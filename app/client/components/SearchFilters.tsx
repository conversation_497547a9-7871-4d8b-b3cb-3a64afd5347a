import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Pressable,
  Modal,
  TextInput,
  Platform,
  Dimensions,
  Animated,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import Colors from '../constants/Colors';

interface SearchFiltersProps {
  onLocationChange?: (location: string) => void;
  onDateChange?: (startDate: string, endDate?: string) => void;
  selectedLocation?: string;
  selectedDate?: string;
  isCompact?: boolean; // For mobile layout
}

const SearchFilters: React.FC<SearchFiltersProps> = ({
  onLocationChange,
  onDateChange,
  selectedLocation = '',
  selectedDate = '',
  isCompact = false,
}) => {
  const [showLocationModal, setShowLocationModal] = useState(false);
  const [showDateModal, setShowDateModal] = useState(false);
  const [tempLocation, setTempLocation] = useState(selectedLocation);
  const [tempDate, setTempDate] = useState(selectedDate);

  const { width } = Dimensions.get('window');
  const isWeb = Platform.OS === 'web';

  const handleLocationSave = () => {
    onLocationChange?.(tempLocation);
    setShowLocationModal(false);
  };

  const handleDateSave = () => {
    onDateChange?.(tempDate);
    setShowDateModal(false);
  };

  const FilterButton: React.FC<{
    icon: string;
    label: string;
    value: string;
    onPress: () => void;
    isActive: boolean;
  }> = ({ icon, label, value, onPress, isActive }) => {
    const scaleAnim = useRef(new Animated.Value(1)).current;

    return (
      <Animated.View style={{ transform: [{ scale: scaleAnim }] }}>
        <Pressable
          style={[styles.filterButton, isCompact && styles.filterButtonCompact]}
          onPress={onPress}
          onPressIn={() => {
            Animated.spring(scaleAnim, {
              toValue: 0.96,
              useNativeDriver: true,
              tension: 300,
              friction: 10,
            }).start();
          }}
          onPressOut={() => {
            Animated.spring(scaleAnim, {
              toValue: 1,
              useNativeDriver: true,
              tension: 300,
              friction: 10,
            }).start();
          }}
        >
          <LinearGradient
            colors={isActive ? ['#FF5A5F', '#FF7B7F'] : ['#F7F7F7', '#F7F7F7']}
            style={styles.filterGradient}
          >
            <View style={styles.filterContent}>
              <Ionicons
                name={icon as any}
                size={16}
                color={isActive ? Colors.text.light : Colors.text.secondary}
              />
              <View style={styles.filterText}>
                <Text style={[
                  styles.filterLabel,
                  { color: isActive ? Colors.text.light : Colors.text.secondary }
                ]}>
                  {label}
                </Text>
                <Text style={[
                  styles.filterValue,
                  { color: isActive ? Colors.text.light : Colors.text.primary }
                ]}>
                  {value || `Any ${label.toLowerCase()}`}
                </Text>
              </View>
            </View>
          </LinearGradient>
        </Pressable>
      </Animated.View>
    );
  };

  const LocationModal = () => (
    <Modal
      visible={showLocationModal}
      transparent
      animationType="fade"
      onRequestClose={() => setShowLocationModal(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Select Location</Text>
            <Pressable onPress={() => setShowLocationModal(false)}>
              <Ionicons name="close" size={24} color={Colors.text.primary} />
            </Pressable>
          </View>
          
          <TextInput
            style={styles.modalInput}
            placeholder="Enter city or area..."
            value={tempLocation}
            onChangeText={setTempLocation}
            autoFocus
          />
          
          <View style={styles.modalActions}>
            <Pressable style={styles.modalButton} onPress={() => setShowLocationModal(false)}>
              <Text style={styles.modalButtonText}>Cancel</Text>
            </Pressable>
            <Pressable style={[styles.modalButton, styles.modalButtonPrimary]} onPress={handleLocationSave}>
              <Text style={[styles.modalButtonText, styles.modalButtonTextPrimary]}>Save</Text>
            </Pressable>
          </View>
        </View>
      </View>
    </Modal>
  );

  const DateModal = () => (
    <Modal
      visible={showDateModal}
      transparent
      animationType="fade"
      onRequestClose={() => setShowDateModal(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Select Date</Text>
            <Pressable onPress={() => setShowDateModal(false)}>
              <Ionicons name="close" size={24} color={Colors.text.primary} />
            </Pressable>
          </View>
          
          <TextInput
            style={styles.modalInput}
            placeholder="Enter preferred date..."
            value={tempDate}
            onChangeText={setTempDate}
            autoFocus
          />
          
          <Text style={styles.modalNote}>
            Note: Date picker will be enhanced in future updates
          </Text>
          
          <View style={styles.modalActions}>
            <Pressable style={styles.modalButton} onPress={() => setShowDateModal(false)}>
              <Text style={styles.modalButtonText}>Cancel</Text>
            </Pressable>
            <Pressable style={[styles.modalButton, styles.modalButtonPrimary]} onPress={handleDateSave}>
              <Text style={[styles.modalButtonText, styles.modalButtonTextPrimary]}>Save</Text>
            </Pressable>
          </View>
        </View>
      </View>
    </Modal>
  );

  if (isCompact) {
    // Mobile layout - horizontal scroll
    return (
      <View style={styles.compactContainer}>
        <FilterButton
          icon="location"
          label="Location"
          value={selectedLocation}
          onPress={() => setShowLocationModal(true)}
          isActive={!!selectedLocation}
        />
        <FilterButton
          icon="calendar"
          label="Date"
          value={selectedDate}
          onPress={() => setShowDateModal(true)}
          isActive={!!selectedDate}
        />
        <LocationModal />
        <DateModal />
      </View>
    );
  }

  // Web layout - integrated filters
  return (
    <View style={styles.webContainer}>
      <FilterButton
        icon="location"
        label="Location"
        value={selectedLocation}
        onPress={() => setShowLocationModal(true)}
        isActive={!!selectedLocation}
      />
      <View style={styles.divider} />
      <FilterButton
        icon="calendar"
        label="Date"
        value={selectedDate}
        onPress={() => setShowDateModal(true)}
        isActive={!!selectedDate}
      />
      <LocationModal />
      <DateModal />
    </View>
  );
};

const styles = StyleSheet.create({
  compactContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 8,
    gap: 12,
  },
  webContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  filterButton: {
    borderRadius: 20,
    overflow: 'hidden',
    minWidth: 120,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  filterButtonCompact: {
    flex: 1,
    minWidth: 100,
  },
  filterGradient: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  filterContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  filterText: {
    flex: 1,
  },
  filterLabel: {
    fontSize: 12,
    fontWeight: '500',
  },
  filterValue: {
    fontSize: 14,
    fontWeight: '600',
    marginTop: 2,
  },
  divider: {
    width: 1,
    height: 30,
    backgroundColor: Colors.border,
    marginHorizontal: 8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: Colors.background.primary,
    borderRadius: 16,
    padding: 24,
    width: '90%',
    maxWidth: 400,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text.primary,
  },
  modalInput: {
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    marginBottom: 16,
    outlineStyle: 'none',
  },
  modalNote: {
    fontSize: 12,
    color: Colors.text.secondary,
    fontStyle: 'italic',
    marginBottom: 20,
  },
  modalActions: {
    flexDirection: 'row',
    gap: 12,
  },
  modalButton: {
    flex: 1,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    backgroundColor: Colors.background.secondary,
  },
  modalButtonPrimary: {
    backgroundColor: Colors.primary,
  },
  modalButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text.primary,
  },
  modalButtonTextPrimary: {
    color: Colors.text.light,
  },
});

export default SearchFilters;
