package com.yotelohago.service.infrastructure;

import com.yotelohago.service.domain.ServiceCategory;
import com.yotelohago.service.domain.ServiceCategoryRepository;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.NoResultException;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * PostgreSQL implementation of ServiceCategoryRepository
 */
@ApplicationScoped
public class ServiceCategoryRepositoryPostgres implements ServiceCategoryRepository {

    @PersistenceContext
    EntityManager entityManager;

    @Override
    public void persist(ServiceCategory category) {
        entityManager.persist(category);
    }

    @Override
    public Optional<ServiceCategory> findById(UUID id) {
        ServiceCategory category = entityManager.find(ServiceCategory.class, id);
        return Optional.ofNullable(category);
    }

    @Override
    public Optional<ServiceCategory> findByName(String name) {
        try {
            ServiceCategory category = entityManager
                .createQuery("FROM ServiceCategory WHERE name = :name", ServiceCategory.class)
                .setParameter("name", name)
                .getSingleResult();
            return Optional.of(category);
        } catch (NoResultException e) {
            return Optional.empty();
        }
    }

    @Override
    public List<ServiceCategory> findAll() {
        return entityManager
            .createQuery("FROM ServiceCategory ORDER BY name", ServiceCategory.class)
            .getResultList();
    }

    @Override
    public ServiceCategory update(ServiceCategory category) {
        return entityManager.merge(category);
    }

    @Override
    public void deleteById(UUID id) {
        ServiceCategory category = entityManager.find(ServiceCategory.class, id);
        if (category != null) {
            entityManager.remove(category);
        }
    }

    @Override
    public boolean existsByName(String name) {
        Long count = entityManager
            .createQuery("SELECT COUNT(c) FROM ServiceCategory c WHERE c.name = :name", Long.class)
            .setParameter("name", name)
            .getSingleResult();
        return count > 0;
    }

    @Override
    public long count() {
        return entityManager
            .createQuery("SELECT COUNT(c) FROM ServiceCategory c", Long.class)
            .getSingleResult();
    }

    @Override
    public void flush() {
        entityManager.flush();
    }
}
