#!/bin/bash

echo "🚀 Starting direct image upload test..."

# Step 1: Get authentication token
echo "🔑 Getting authentication token..."
TOKEN=$(curl -s -X POST "http://keycloak.yotelohago.co/realms/yotelohago/protocol/openid-connect/token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "client_id=yotelohago-app-dev" \
  -d "grant_type=password" \
  -d "username=test1client1" \
  -d "password=test1client1" | jq -r '.access_token')

if [[ "$TOKEN" == "null" || -z "$TOKEN" ]]; then
    echo "❌ Failed to get authentication token"
    exit 1
fi

echo "✅ Token obtained: ${TOKEN:0:20}..."

# Step 2: Check if image file exists
IMAGE_FILE="/Users/<USER>/Downloads/profile.jpg"
if [[ ! -f "$IMAGE_FILE" ]]; then
    echo "❌ Image file not found: $IMAGE_FILE"
    exit 1
fi

# Get file size
FILE_SIZE=$(stat -f%z "$IMAGE_FILE" 2>/dev/null || stat -c%s "$IMAGE_FILE" 2>/dev/null)
echo "📁 Image file size: $FILE_SIZE bytes"

# Step 3: Generate presigned URL
echo "🔗 Generating presigned URL..."
RESPONSE=$(curl -s -X POST "http://localhost:8080/v1/media/presigned-url" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d "{
    \"filename\": \"profile.jpg\",
    \"contentType\": \"image/jpeg\",
    \"fileSize\": $FILE_SIZE,
    \"mediaType\": \"professional_profile_picture\"
  }")

echo "📋 Presigned URL Response:"
echo "$RESPONSE" | jq '.'

# Extract upload URL and media ID
UPLOAD_URL=$(echo "$RESPONSE" | jq -r '.uploadUrl')
MEDIA_ID=$(echo "$RESPONSE" | jq -r '.mediaId')
FILE_PATH=$(echo "$RESPONSE" | jq -r '.filePath')

if [[ "$UPLOAD_URL" == "null" || -z "$UPLOAD_URL" ]]; then
    echo "❌ Failed to get presigned URL"
    echo "Response: $RESPONSE"
    exit 1
fi

echo "🔗 Upload URL: ${UPLOAD_URL:0:80}..."
echo "🆔 Media ID: $MEDIA_ID"
echo "📂 File Path: $FILE_PATH"

# Step 4: Upload the file
echo "📤 Uploading image to MinIO..."
UPLOAD_RESPONSE=$(curl -s -w "HTTPSTATUS:%{http_code}" -X PUT "$UPLOAD_URL" \
  -H "Content-Type: image/jpeg" \
  --data-binary @"$IMAGE_FILE")

HTTP_STATUS=$(echo "$UPLOAD_RESPONSE" | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
UPLOAD_BODY=$(echo "$UPLOAD_RESPONSE" | sed 's/HTTPSTATUS:[0-9]*$//')

echo "📊 Upload HTTP Status: $HTTP_STATUS"

if [[ "$HTTP_STATUS" == "200" ]]; then
    echo "✅ Upload successful!"
else
    echo "❌ Upload failed with status: $HTTP_STATUS"
    echo "Response: $UPLOAD_BODY"
    exit 1
fi

# Step 5: Verify the upload in database
echo "🔍 Verifying media metadata in database..."
VERIFY_RESPONSE=$(curl -s -X GET "http://localhost:8080/v1/media/$MEDIA_ID" \
  -H "Authorization: Bearer $TOKEN")

echo "📋 Media metadata:"
echo "$VERIFY_RESPONSE" | jq '.'

# Step 6: Test public access
PUBLIC_URL="https://minio.yotelohago.co/public/$FILE_PATH"
echo "🌐 Testing public access to: $PUBLIC_URL"

PUBLIC_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$PUBLIC_URL")
echo "📊 Public access status: $PUBLIC_STATUS"

if [[ "$PUBLIC_STATUS" == "200" ]]; then
    echo "✅ File is publicly accessible!"
    echo "🔗 Public URL: $PUBLIC_URL"
else
    echo "⚠️  File may not be publicly accessible (status: $PUBLIC_STATUS)"
fi

# Step 7: List all user media
echo "📋 All your media files:"
curl -s -X GET "http://localhost:8080/v1/media/my-media" \
  -H "Authorization: Bearer $TOKEN" | jq '.[] | {id, originalFilename, mediaType, filePath, publicUrl}'

echo ""
echo "🎉 Upload test completed!"
