package com.yotelohago.debug;

import com.yotelohago.common.ApiVersion;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;

/**
 * Debug endpoints for development and testing
 * Only available in development mode
 */
@Path(ApiVersion.BASE + "/debug")
@Produces(MediaType.TEXT_HTML)
public class DebugResource {

    /**
     * WebSocket test page for messaging functionality
     */
    @GET
    @Path("/websocket-test")
    public Response getWebSocketTestPage() {
        String html = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YoteLoHago WebSocket Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .connected { background-color: #d4edda; color: #155724; }
        .disconnected { background-color: #f8d7da; color: #721c24; }
        .message-log {
            height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background-color: #f9f9f9;
            font-family: monospace;
            font-size: 12px;
        }
        .controls {
            display: flex;
            gap: 10px;
            margin: 10px 0;
            flex-wrap: wrap;
        }
        input, button, select {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background-color: #007bff;
            color: white;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .user-section {
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .user1 { border-left: 4px solid #007bff; }
        .user2 { border-left: 4px solid #28a745; }
    </style>
</head>
<body>
    <h1>🔌 YoteLoHago WebSocket Messaging Test</h1>
    
    <div class="container">
        <h2>Connection Settings</h2>
        <div class="controls">
            <label>Booking ID: <input type="number" id="bookingId" value="1001" min="1"></label>
            <label>WebSocket URL: <input type="text" id="wsUrl" value="ws://localhost:8080/v1/messaging/ws" readonly style="width: 300px;"></label>
        </div>
    </div>

    <div class="container user-section user1">
        <h3>👤 User 1 (Client)</h3>
        <div class="controls">
            <label>User ID: <input type="text" id="user1Id" value="f62a04b6-a0a7-4f21-8e3a-7948eee69ed3" placeholder="<EMAIL>"></label>
            <button id="connect1" onclick="connectUser1()">Connect</button>
            <button id="disconnect1" onclick="disconnectUser1()" disabled>Disconnect</button>
        </div>
        <div id="status1" class="status disconnected">Disconnected</div>
        <div class="controls">
            <input type="text" id="message1" placeholder="Type a message..." style="flex: 1; min-width: 200px;">
            <button onclick="sendMessage1()" id="send1" disabled>Send Message</button>
            <button onclick="sendTyping1()" id="typing1" disabled>Send Typing</button>
            <button onclick="sendPing1()" id="ping1" disabled>Ping</button>
        </div>
        <h4>Messages:</h4>
        <div id="log1" class="message-log"></div>
    </div>

    <div class="container user-section user2">
        <h3>👤 User 2 (Professional)</h3>
        <div class="controls">
            <label>User ID: <input type="text" id="user2Id" value="8dbbddae-c53c-4a1d-b326-a776bca4ff4e" placeholder="<EMAIL>"></label>
            <button id="connect2" onclick="connectUser2()">Connect</button>
            <button id="disconnect2" onclick="disconnectUser2()" disabled>Disconnect</button>
        </div>
        <div id="status2" class="status disconnected">Disconnected</div>
        <div class="controls">
            <input type="text" id="message2" placeholder="Type a message..." style="flex: 1; min-width: 200px;">
            <button onclick="sendMessage2()" id="send2" disabled>Send Message</button>
            <button onclick="sendTyping2()" id="typing2" disabled>Send Typing</button>
            <button onclick="sendPing2()" id="ping2" disabled>Ping</button>
        </div>
        <h4>Messages:</h4>
        <div id="log2" class="message-log"></div>
    </div>

    <script>
        let ws1 = null;
        let ws2 = null;

        function log(user, message, type = 'info') {
            const logElement = document.getElementById(`log${user}`);
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? 'red' : type === 'sent' ? 'blue' : type === 'received' ? 'green' : 'black';
            logElement.innerHTML += `<div style="color: ${color};">[${timestamp}] ${message}</div>`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function updateStatus(user, connected) {
            const statusElement = document.getElementById(`status${user}`);
            const connectBtn = document.getElementById(`connect${user}`);
            const disconnectBtn = document.getElementById(`disconnect${user}`);
            const sendBtn = document.getElementById(`send${user}`);
            const typingBtn = document.getElementById(`typing${user}`);
            const pingBtn = document.getElementById(`ping${user}`);

            if (connected) {
                statusElement.textContent = 'Connected';
                statusElement.className = 'status connected';
                connectBtn.disabled = true;
                disconnectBtn.disabled = false;
                sendBtn.disabled = false;
                typingBtn.disabled = false;
                pingBtn.disabled = false;
            } else {
                statusElement.textContent = 'Disconnected';
                statusElement.className = 'status disconnected';
                connectBtn.disabled = false;
                disconnectBtn.disabled = true;
                sendBtn.disabled = true;
                typingBtn.disabled = true;
                pingBtn.disabled = true;
            }
        }

        function connectUser1() {
            const bookingId = document.getElementById('bookingId').value;
            const userId = document.getElementById('user1Id').value;
            const wsUrl = document.getElementById('wsUrl').value;
            
            ws1 = new WebSocket(`${wsUrl}/${bookingId}?userId=${userId}`);
            
            ws1.onopen = function() {
                log(1, 'WebSocket connection opened', 'info');
                updateStatus(1, true);
            };
            
            ws1.onmessage = function(event) {
                const data = JSON.parse(event.data);
                log(1, `Received: ${data.type} - ${data.message}`, 'received');
                if (data.data) {
                    log(1, `Data: ${JSON.stringify(data.data)}`, 'received');
                }
            };
            
            ws1.onclose = function() {
                log(1, 'WebSocket connection closed', 'error');
                updateStatus(1, false);
            };
            
            ws1.onerror = function(error) {
                log(1, `WebSocket error: ${error}`, 'error');
                updateStatus(1, false);
            };
        }

        function connectUser2() {
            const bookingId = document.getElementById('bookingId').value;
            const userId = document.getElementById('user2Id').value;
            const wsUrl = document.getElementById('wsUrl').value;
            
            ws2 = new WebSocket(`${wsUrl}/${bookingId}?userId=${userId}`);
            
            ws2.onopen = function() {
                log(2, 'WebSocket connection opened', 'info');
                updateStatus(2, true);
            };
            
            ws2.onmessage = function(event) {
                const data = JSON.parse(event.data);
                log(2, `Received: ${data.type} - ${data.message}`, 'received');
                if (data.data) {
                    log(2, `Data: ${JSON.stringify(data.data)}`, 'received');
                }
            };
            
            ws2.onclose = function() {
                log(2, 'WebSocket connection closed', 'error');
                updateStatus(2, false);
            };
            
            ws2.onerror = function(error) {
                log(2, `WebSocket error: ${error}`, 'error');
                updateStatus(2, false);
            };
        }

        function disconnectUser1() {
            if (ws1) {
                ws1.close();
                ws1 = null;
            }
        }

        function disconnectUser2() {
            if (ws2) {
                ws2.close();
                ws2 = null;
            }
        }

        function sendMessage1() {
            const message = document.getElementById('message1').value;
            const user1Id = document.getElementById('user1Id').value;
            const user2Id = document.getElementById('user2Id').value;
            
            if (ws1 && message) {
                const data = {
                    type: 'send_message',
                    message: 'Sending test message',
                    data: {
                        senderKeycloakId: user1Id,
                        receiverKeycloakId: user2Id,
                        content: message
                    }
                };
                ws1.send(JSON.stringify(data));
                log(1, `Sent: ${message}`, 'sent');
                document.getElementById('message1').value = '';
            }
        }

        function sendMessage2() {
            const message = document.getElementById('message2').value;
            const user1Id = document.getElementById('user1Id').value;
            const user2Id = document.getElementById('user2Id').value;
            
            if (ws2 && message) {
                const data = {
                    type: 'send_message',
                    message: 'Sending test message',
                    data: {
                        senderKeycloakId: user2Id,
                        receiverKeycloakId: user1Id,
                        content: message
                    }
                };
                ws2.send(JSON.stringify(data));
                log(2, `Sent: ${message}`, 'sent');
                document.getElementById('message2').value = '';
            }
        }

        function sendTyping1() {
            if (ws1) {
                const data = { type: 'typing', message: 'User is typing', data: true };
                ws1.send(JSON.stringify(data));
                log(1, 'Sent typing indicator', 'sent');
            }
        }

        function sendTyping2() {
            if (ws2) {
                const data = { type: 'typing', message: 'User is typing', data: true };
                ws2.send(JSON.stringify(data));
                log(2, 'Sent typing indicator', 'sent');
            }
        }

        function sendPing1() {
            if (ws1) {
                const data = { type: 'ping', message: 'Ping test', data: null };
                ws1.send(JSON.stringify(data));
                log(1, 'Sent ping', 'sent');
            }
        }

        function sendPing2() {
            if (ws2) {
                const data = { type: 'ping', message: 'Ping test', data: null };
                ws2.send(JSON.stringify(data));
                log(2, 'Sent ping', 'sent');
            }
        }

        // Allow Enter key to send messages
        document.getElementById('message1').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') sendMessage1();
        });
        
        document.getElementById('message2').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') sendMessage2();
        });
    </script>
</body>
</html>
                """;
        
        return Response.ok(html).build();
    }

    /**
     * Simple debug info endpoint
     */
    @GET
    @Path("/info")
    @Produces(MediaType.APPLICATION_JSON)
    public Response getDebugInfo() {
        return Response.ok("{\"status\": \"Debug endpoints available\", \"websocket_test\": \"/v1/debug/websocket-test\"}").build();
    }
}
