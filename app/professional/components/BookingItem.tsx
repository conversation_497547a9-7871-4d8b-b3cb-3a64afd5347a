import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Booking, Service, User } from '@/types';
import Colors from '../constants/Colors';

interface BookingItemProps {
  booking: Booking;
  service: Service;
  provider: User;
  onPress: (booking: Booking) => void;
  onMessagePress: (providerId: string) => void;
}

const BookingItem: React.FC<BookingItemProps> = ({
  booking,
  service,
  provider,
  onPress,
  onMessagePress,
}) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open':
        return Colors.warning;
      case 'accepted':
        return Colors.success;
      case 'completed':
        return Colors.secondary;
      case 'cancelled':
        return Colors.error;
      default:
        return Colors.text.secondary;
    }
  };

  const statusColor = getStatusColor(booking.status);

  return (
    <TouchableOpacity
      style={styles.container}
      onPress={() => onPress(booking)}
    >
      <View style={styles.headerRow}>
        <Text style={styles.title} numberOfLines={1}>
          {service.title}
        </Text>
        <View style={[styles.statusBadge, { backgroundColor: statusColor }]}>
          <Text style={styles.statusText}>
            {booking.status.charAt(0).toUpperCase() + booking.status.slice(1)}
          </Text>
        </View>
      </View>

      <View style={styles.infoRow}>
        <Text style={styles.providerName}>{provider.name}</Text>
        <View style={styles.dateTimeContainer}>
          <Ionicons name="calendar" size={16} color={Colors.text.secondary} />
          <Text style={styles.dateTime}>
            {booking.date} • {booking.time}
          </Text>
        </View>
      </View>

      <View style={styles.footer}>
        <Text style={styles.price}>
          ${booking.price} • {booking.duration} {booking.duration === 1 ? 'hour' : 'hours'}
        </Text>
        
        <TouchableOpacity
          style={styles.messageButton}
          onPress={() => onMessagePress(provider.id)}
        >
          <Ionicons name="chatbubble" size={16} color={Colors.primary} />
          <Text style={styles.messageText}>Message</Text>
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.background.primary,
    borderRadius: 12,
    padding: 16,
    width: '100%',
    borderWidth: 1,
    borderColor: Colors.border,
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text.primary,
    flex: 1,
    marginRight: 8,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  statusText: {
    color: Colors.text.light,
    fontSize: 12,
    fontWeight: '500',
  },
  infoRow: {
    marginBottom: 12,
  },
  providerName: {
    fontSize: 14,
    color: Colors.text.secondary,
    marginBottom: 4,
  },
  dateTimeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dateTime: {
    fontSize: 14,
    color: Colors.text.secondary,
    marginLeft: 4,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 4,
  },
  price: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.text.primary,
  },
  messageButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 6,
    paddingHorizontal: 10,
    borderRadius: 16,
    backgroundColor: Colors.background.secondary,
  },
  messageText: {
    fontSize: 12,
    fontWeight: '500',
    color: Colors.primary,
    marginLeft: 4,
  },
});

export default BookingItem;