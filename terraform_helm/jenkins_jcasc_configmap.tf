# resource "kubernetes_config_map" "jcasc_config" {
#   metadata {
#     name      = "jenkins-jcasc"
#     namespace = "jenkins"
#   }
#
#   data = {
#     for file in fileset("${path.module}/../jenkins/config.generated", "*.generated.yaml") :
#     basename(file) => file("${path.module}/../jenkins/config.generated/${file}")
#   }
# }

# resource "kubernetes_config_map" "jenkins_jcasc" {
#   metadata {
#     name      = "jenkins-jcasc-config"
#     namespace = "jenkins"
#   }
#
#   data = {
#     "jenkins.yaml" = file("${path.module}/modules/jenkins/jenkins-casc.yaml")
#   }
# }