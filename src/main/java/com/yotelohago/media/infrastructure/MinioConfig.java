package com.yotelohago.media.infrastructure;

import io.minio.MinioClient;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.enterprise.inject.Produces;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Configuration class for MinIO client
 * Creates and configures the MinIO client bean for dependency injection
 */
@ApplicationScoped
public class MinioConfig {

    private static final Logger logger = LoggerFactory.getLogger(MinioConfig.class);

    @ConfigProperty(name = "minio.endpoint")
    String endpoint;

    @ConfigProperty(name = "minio.access-key")
    String accessKey;

    @ConfigProperty(name = "minio.secret-key")
    String secretKey;

    @ConfigProperty(name = "minio.region", defaultValue = "us-east-1")
    String region;

    /**
     * Produces a configured MinIO client for dependency injection
     * @return Configured MinioClient instance
     */
    @Produces
    @ApplicationScoped
    public MinioClient minioClient() {
        logger.info("🔧 Configuring MinIO client for endpoint: {}", endpoint);
        
        try {
            MinioClient client = MinioClient.builder()
                    .endpoint(endpoint)
                    .credentials(accessKey, secretKey)
                    .region(region)
                    .build();
            
            logger.info("✅ MinIO client configured successfully");
            return client;
        } catch (Exception e) {
            logger.error("❌ Failed to configure MinIO client", e);
            throw new RuntimeException("Failed to configure MinIO client", e);
        }
    }
}
