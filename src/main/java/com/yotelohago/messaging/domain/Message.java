package com.yotelohago.messaging.domain;

import io.quarkus.hibernate.orm.panache.PanacheEntityBase;
import jakarta.persistence.*;
import org.hibernate.annotations.CreationTimestamp;
import com.yotelohago.booking.domain.Booking;
import com.yotelohago.user.domain.User;

import java.time.Instant;
import java.util.UUID;

@Entity
@Table(name = "messages",
       indexes = {
           @Index(name = "idx_messages_booking_id", columnList = "booking_id"),
           @Index(name = "idx_messages_sender_id", columnList = "sender_id"),
           @Index(name = "idx_messages_receiver_id", columnList = "receiver_id"),
           @Index(name = "idx_messages_sent_at", columnList = "sent_at")
       })
public class Message extends PanacheEntityBase {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "messages_seq")
    @SequenceGenerator(name = "messages_seq", sequenceName = "messages_id_seq", allocationSize = 1)
    public Long id;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "booking_id", nullable = false,
                foreignKey = @ForeignKey(name = "fk_messages_booking"))
    public Booking booking;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "sender_id", nullable = false,
                foreignKey = @ForeignKey(name = "fk_messages_sender"))
    public User sender;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "receiver_id", nullable = false,
                foreignKey = @ForeignKey(name = "fk_messages_receiver"))
    public User receiver;

    @Column(nullable = false, columnDefinition = "TEXT")
    public String content;

    @CreationTimestamp
    @Column(name = "sent_at", nullable = false, updatable = false)
    public Instant sentAt;

    @Column(name = "is_read", nullable = false)
    public boolean isRead;

    public Message() {
        // JPA
    }

    public Message(Booking booking, User sender, User receiver, String content, boolean isRead) {
        this.booking = booking;
        this.sender = sender;
        this.receiver = receiver;
        this.content = content;
        this.isRead = isRead;
        // sentAt will be set automatically by @CreationTimestamp
    }

    // Getters
    public Long getId() { return id; }
    public Booking getBooking() { return booking; }
    public User getSender() { return sender; }
    public User getReceiver() { return receiver; }
    public String getContent() { return content; }
    public Instant getSentAt() { return sentAt; }
    public boolean isRead() { return isRead; }



    // Setters
    public void setId(Long id) { this.id = id; }
    public void setBooking(Booking booking) { this.booking = booking; }
    public void setSender(User sender) { this.sender = sender; }
    public void setReceiver(User receiver) { this.receiver = receiver; }
    public void setContent(String content) { this.content = content; }
    public void setSentAt(Instant sentAt) { this.sentAt = sentAt; }
    public void setRead(boolean read) { isRead = read; }
}