package com.yotelohago.review.api;

import com.yotelohago.common.ApiVersion;
import com.yotelohago.review.application.ReviewManagementService;
import com.yotelohago.review.domain.Review;
import jakarta.annotation.security.RolesAllowed;
import jakarta.inject.Inject;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Path(ApiVersion.BASE + "/reviews")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public class ReviewResource {

    @Inject
    ReviewManagementService service;

    @Inject
    ReviewMapper reviewMapper;

    /**
     * Get all reviews - Public access
     * TODO: Add pagination for performance
     */
    @GET
    public Response getAllReviews() {
        try {
            List<ReviewDTO> reviews = service.findAll().stream()
                    .map(reviewMapper::toDTO)
                    .collect(Collectors.toList());
            return Response.ok(reviews).build();
        } catch (Exception e) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("{\"error\": \"Failed to retrieve reviews\"}")
                    .build();
        }
    }

    /**
     * Get review by ID - Public access
     */
    @GET
    @Path("/{id}")
    public Response getReviewById(@PathParam("id") Long id) {
        try {
            return service.findById(id)
                    .map(reviewMapper::toDTO)
                    .map(dto -> Response.ok(dto).build())
                    .orElse(Response.status(Response.Status.NOT_FOUND)
                            .entity("{\"error\": \"Review not found\"}")
                            .build());
        } catch (IllegalArgumentException e) {
            return Response.status(Response.Status.BAD_REQUEST)
                    .entity("{\"error\": \"Invalid review ID format\"}")
                    .build();
        } catch (Exception e) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("{\"error\": \"Failed to retrieve review\"}")
                    .build();
        }
    }

    /**
     * Get reviews for a professional - Public access
     * TODO: Add pagination for performance
     */
    @GET
    @Path("/professional/{professionalUserId}")
    public Response getByProfessional(@PathParam("professionalUserId") UUID professionalUserId) {
        try {
            List<ReviewDTO> reviews = service.findByProfessionalUserId(professionalUserId).stream()
                    .map(reviewMapper::toDTO)
                    .collect(Collectors.toList());
            return Response.ok(reviews).build();
        } catch (IllegalArgumentException e) {
            return Response.status(Response.Status.BAD_REQUEST)
                    .entity("{\"error\": \"Invalid professional ID format\"}")
                    .build();
        } catch (Exception e) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("{\"error\": \"Failed to retrieve professional reviews\"}")
                    .build();
        }
    }

    /**
     * Get review for a booking - Public access
     */
    @GET
    @Path("/booking/{bookingId}")
    public Response getByBooking(@PathParam("bookingId") Long bookingId) {
        try {
            return service.findByBookingId(bookingId)
                    .map(reviewMapper::toDTO)
                    .map(dto -> Response.ok(dto).build())
                    .orElse(Response.status(Response.Status.NOT_FOUND)
                            .entity("{\"error\": \"Review not found for this booking\"}")
                            .build());
        } catch (IllegalArgumentException e) {
            return Response.status(Response.Status.BAD_REQUEST)
                    .entity("{\"error\": \"Invalid booking ID format\"}")
                    .build();
        } catch (Exception e) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("{\"error\": \"Failed to retrieve booking review\"}")
                    .build();
        }
    }

    /**
     * Create a review - Only the client who made the booking can review after completion
     */
    @POST
    @RolesAllowed({"user", "admin"})
    public Response create(ReviewDTO dto) {
        try {
            if (dto == null) {
                return Response.status(Response.Status.BAD_REQUEST)
                        .entity("{\"error\": \"Review data is required\"}")
                        .build();
            }

            if (dto.bookingId == null) {
                return Response.status(Response.Status.BAD_REQUEST)
                        .entity("{\"error\": \"Booking ID is required\"}")
                        .build();
            }

            if (dto.rating < 1 || dto.rating > 5) {
                return Response.status(Response.Status.BAD_REQUEST)
                        .entity("{\"error\": \"Rating must be between 1 and 5 stars\"}")
                        .build();
            }

            Review createdReview = service.create(reviewMapper.toEntity(dto));
            return Response.status(Response.Status.CREATED)
                    .entity(reviewMapper.toDTO(createdReview))
                    .build();
        } catch (ForbiddenException e) {
            return Response.status(Response.Status.FORBIDDEN)
                    .entity("{\"error\": \"" + e.getMessage() + "\"}")
                    .build();
        } catch (IllegalArgumentException e) {
            return Response.status(Response.Status.BAD_REQUEST)
                    .entity("{\"error\": \"" + e.getMessage() + "\"}")
                    .build();
        } catch (Exception e) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("{\"error\": \"Failed to create review\"}")
                    .build();
        }
    }

    /**
     * Delete a review - Admin only
     */
    @DELETE
    @Path("/{id}")
    @RolesAllowed("admin")
    public Response delete(@PathParam("id") Long id) {
        try {
            if (!service.delete(id)) {
                return Response.status(Response.Status.NOT_FOUND)
                        .entity("{\"error\": \"Review not found\"}")
                        .build();
            }
            return Response.noContent().build();
        } catch (ForbiddenException e) {
            return Response.status(Response.Status.FORBIDDEN)
                    .entity("{\"error\": \"" + e.getMessage() + "\"}")
                    .build();
        } catch (IllegalArgumentException e) {
            return Response.status(Response.Status.BAD_REQUEST)
                    .entity("{\"error\": \"Invalid review ID format\"}")
                    .build();
        } catch (Exception e) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("{\"error\": \"Failed to delete review\"}")
                    .build();
        }
    }
}