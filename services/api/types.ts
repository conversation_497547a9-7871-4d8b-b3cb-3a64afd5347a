// Re-export all API types for easy access
export * from '../../types/api';
export * from '../../types/auth';

// Additional service-specific types
export interface ApiServiceConfig {
  baseURL: string;
  timeout: number;
  retryAttempts: number;
  retryDelay: number;
}

export interface RequestOptions {
  skipAuth?: boolean;
  customHeaders?: Record<string, string>;
  timeout?: number;
}

// API Endpoints enum for type safety
export enum ApiEndpoints {
  // Auth endpoints
  AUTH_LOGIN = '/auth/login',
  AUTH_LOGOUT = '/auth/logout',
  AUTH_REFRESH = '/auth/refresh',

  // User endpoints
  USERS = '/users',
  USER_REGISTER = '/users/register',
  
  // Professional endpoints
  PROFESSIONALS = '/professionals',
  PROFESSIONAL_PROFILE = '/professionals/profile',
  
  // Service endpoints
  SERVICES = '/services',
  SERVICE_CATEGORIES = '/service-categories',
  
  // Booking endpoints
  BOOKINGS = '/bookings',
  
  // Review endpoints
  REVIEWS = '/reviews',

  // Favorite endpoints
  FAVORITES = '/favorites',

  // Messaging endpoints
  MESSAGES = '/messages',
  CONVERSATIONS = '/messages/conversations',
  UNREAD_COUNT = '/messages/unread-count',

  // Media endpoints
  MEDIA = '/media',
  MEDIA_PRESIGNED_URL = '/media/presigned-url',
  MEDIA_MY_MEDIA = '/media/my-media',
  MEDIA_USER = '/media/user',

  // Health check
  HEALTH = '/health',
}

// HTTP Status Codes for better error handling
export enum HttpStatusCode {
  OK = 200,
  CREATED = 201,
  NO_CONTENT = 204,
  BAD_REQUEST = 400,
  UNAUTHORIZED = 401,
  FORBIDDEN = 403,
  NOT_FOUND = 404,
  CONFLICT = 409,
  INTERNAL_SERVER_ERROR = 500,
  SERVICE_UNAVAILABLE = 503,
}
