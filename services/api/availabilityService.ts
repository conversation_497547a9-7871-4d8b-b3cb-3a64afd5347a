import { apiGet, apiPut, apiDelete, apiGetPublic } from './client';
import { 
  WorkScheduleDTO, 
  UpdateWorkScheduleRequest, 
  AvailabilityCheckResponse,
  DayOfWeek 
} from '../../types/api';

/**
 * Availability API Service
 * Handles all availability/work schedule related API calls
 * 
 * Follows the same patterns as other services in the project
 */
export class AvailabilityService {
  private readonly basePath = '/availability';

  /**
   * Get work schedule for a professional (public endpoint)
   * Creates empty schedule if none exists for easier UI binding
   * @param professionalId Professional's internal user ID
   * @returns Work schedule data
   */
  async getWorkScheduleByProfessional(professionalId: string): Promise<WorkScheduleDTO> {
    try {
      console.log(`🗓️ Fetching work schedule for professional: ${professionalId}`);

      const workSchedule = await apiGetPublic<WorkScheduleDTO>(
        `${this.basePath}/professionals/${professionalId}`
      );

      console.log('✅ Work schedule data fetched:', workSchedule);
      return workSchedule;
    } catch (error) {
      console.error('❌ Failed to fetch work schedule:', error);
      throw error;
    }
  }

  /**
   * Get work schedule by ID (authenticated endpoint)
   * @param professionalId Professional's internal user ID
   * @returns Work schedule data or null if not found
   */
  async getWorkScheduleById(professionalId: string): Promise<WorkScheduleDTO | null> {
    try {
      console.log(`🗓️ Fetching work schedule by ID: ${professionalId}`);

      const workSchedule = await apiGet<WorkScheduleDTO>(`${this.basePath}/${professionalId}`);
      
      console.log('✅ Work schedule fetched by ID:', workSchedule);
      return workSchedule;
    } catch (error: any) {
      if (error.status === 404) {
        console.log('ℹ️ Work schedule not found');
        return null;
      }
      console.error('❌ Failed to fetch work schedule by ID:', error);
      throw error;
    }
  }

  /**
   * Update work schedule for a professional (authenticated endpoint)
   * Only professionals can update their own schedules
   * @param professionalId Professional's internal user ID
   * @param request Update request data
   * @returns Updated work schedule
   */
  async updateWorkSchedule(
    professionalId: string, 
    request: UpdateWorkScheduleRequest
  ): Promise<WorkScheduleDTO> {
    try {
      console.log(`🗓️ Updating work schedule for professional: ${professionalId}`, request);

      const updatedSchedule = await apiPut<WorkScheduleDTO>(
        `${this.basePath}/professionals/${professionalId}`,
        request
      );

      console.log('✅ Work schedule updated successfully:', updatedSchedule);
      return updatedSchedule;
    } catch (error) {
      console.error('❌ Failed to update work schedule:', error);
      throw error;
    }
  }

  /**
   * Delete work schedule for a professional (authenticated endpoint)
   * Only professionals can delete their own schedules
   * @param professionalId Professional's internal user ID
   * @returns Success status
   */
  async deleteWorkSchedule(professionalId: string): Promise<boolean> {
    try {
      console.log(`🗓️ Deleting work schedule for professional: ${professionalId}`);

      await apiDelete(`${this.basePath}/${professionalId}`);

      console.log('✅ Work schedule deleted successfully');
      return true;
    } catch (error: any) {
      if (error.status === 404) {
        console.log('ℹ️ Work schedule not found for deletion');
        return false;
      }
      console.error('❌ Failed to delete work schedule:', error);
      throw error;
    }
  }

  /**
   * Find professionals available on a specific day (public endpoint)
   * @param dayOfWeek Day of week to check
   * @returns List of work schedules for professionals available on that day
   */
  async getProfessionalsAvailableOnDay(dayOfWeek: DayOfWeek): Promise<WorkScheduleDTO[]> {
    try {
      console.log(`🗓️ Finding professionals available on: ${dayOfWeek}`);

      const schedules = await apiGetPublic<WorkScheduleDTO[]>(
        `${this.basePath}/day/${dayOfWeek}`
      );

      console.log(`✅ Found ${schedules.length} professionals available on ${dayOfWeek}`);
      return schedules;
    } catch (error) {
      console.error('❌ Failed to find professionals by day:', error);
      throw error;
    }
  }

  /**
   * Check if professional is available at specific day and time (public endpoint)
   * @param professionalId Professional's internal user ID
   * @param dayOfWeek Day of week to check
   * @param time Time to check (HH:mm format)
   * @returns Availability status
   */
  async checkAvailability(
    professionalId: string,
    dayOfWeek: DayOfWeek,
    time: string
  ): Promise<boolean> {
    try {
      console.log(`🗓️ Checking availability for ${professionalId} on ${dayOfWeek} at ${time}`);

      const response = await apiGetPublic<AvailabilityCheckResponse>(
        `${this.basePath}/professionals/${professionalId}/check?dayOfWeek=${dayOfWeek}&time=${time}`
      );

      console.log(`✅ Availability check result: ${response.available}`);
      return response.available;
    } catch (error) {
      console.error('❌ Failed to check availability:', error);
      throw error;
    }
  }

  /**
   * Get current user's work schedule (authenticated endpoint)
   * Convenience method for professionals to get their own schedule
   * Note: This requires the current user's ID to be passed from the frontend
   * @param currentUserId Current user's ID from auth context
   * @returns Current user's work schedule
   */
  async getMyWorkSchedule(currentUserId: string): Promise<WorkScheduleDTO | null> {
    try {
      console.log('🗓️ Fetching current user work schedule');

      const workSchedule = await apiGet<WorkScheduleDTO>(`${this.basePath}/professionals/${currentUserId}`);

      console.log('✅ Current user work schedule fetched:', workSchedule);
      return workSchedule;
    } catch (error: any) {
      if (error.status === 404) {
        console.log('ℹ️ Current user has no work schedule');
        return null;
      }
      console.error('❌ Failed to fetch current user work schedule:', error);
      throw error;
    }
  }

  /**
   * Update current user's work schedule (authenticated endpoint)
   * Convenience method for professionals to update their own schedule
   * @param currentUserId Current user's ID from auth context
   * @param request Update request data
   * @returns Updated work schedule
   */
  async updateMyWorkSchedule(currentUserId: string, request: UpdateWorkScheduleRequest): Promise<WorkScheduleDTO> {
    try {
      console.log('🗓️ Updating current user work schedule', request);

      const updatedSchedule = await apiPut<WorkScheduleDTO>(
        `${this.basePath}/professionals/${currentUserId}`,
        request
      );

      console.log('✅ Current user work schedule updated successfully:', updatedSchedule);
      return updatedSchedule;
    } catch (error) {
      console.error('❌ Failed to update current user work schedule:', error);
      throw error;
    }
  }
}

// Create and export singleton instance
export const availabilityService = new AvailabilityService();
export default availabilityService;
