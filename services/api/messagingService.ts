import { apiGet, apiPost, apiPut, apiPatch } from './client';
import { 
  MessageDTO, 
  ConversationSummaryDTO, 
  SendMessageRequest,
  ApiResponse 
} from '../../types/api';

/**
 * Messaging Service - REST API calls for messaging functionality
 * Integrates with backend messaging endpoints
 */

// Base messaging endpoint
const MESSAGING_BASE = '/messaging';

/**
 * Get all conversations for the current user
 */
export const getConversations = async (): Promise<ConversationSummaryDTO[]> => {
  try {
    console.log('🔄 Fetching conversations...');
    const response = await apiGet<any>(`${MESSAGING_BASE}/conversations`);
    console.log('📡 Raw conversations API response:', response);

    // Handle paginated response - extract content array
    const conversationsArray = response.content || response || [];
    console.log('📡 Conversations array extracted:', conversationsArray);

    if (!Array.isArray(conversationsArray)) {
      console.warn('⚠️ Expected conversations array, got:', typeof conversationsArray);
      return [];
    }

    console.log('✅ Conversations fetched:', conversationsArray.length);
    return conversationsArray;
  } catch (error) {
    console.error('❌ Failed to fetch conversations:', error);
    throw error;
  }
};

/**
 * Get messages for a specific booking/conversation
 */
export const getMessagesForBooking = async (bookingId: number): Promise<MessageDTO[]> => {
  try {
    console.log('🔄 Fetching messages for booking:', bookingId);
    const messages = await apiGet<MessageDTO[]>(`${MESSAGING_BASE}/booking/${bookingId}`);
    console.log('✅ Messages fetched:', messages.length);
    return messages;
  } catch (error) {
    console.error('❌ Failed to fetch messages for booking:', bookingId, error);
    throw error;
  }
};

/**
 * Get conversation info for a booking (participant details)
 */
export const getConversationInfo = async (bookingId: number): Promise<{
  bookingId: number;
  clientId: string;
  clientName: string;
  clientEmail: string;
  professionalId: string;
  professionalName: string;
  professionalEmail: string;
}> => {
  try {
    console.log('🔄 Fetching conversation info for booking:', bookingId);
    const info = await apiGet<{
      bookingId: number;
      clientId: string;
      clientName: string;
      clientEmail: string;
      professionalId: string;
      professionalName: string;
      professionalEmail: string;
    }>(`${MESSAGING_BASE}/conversations/${bookingId}/info`);
    console.log('✅ Conversation info fetched:', info);
    return info;
  } catch (error) {
    console.error('❌ Failed to fetch conversation info:', bookingId, error);
    throw error;
  }
};

/**
 * Get conversation detail (messages + participant info)
 */
export const getConversationDetail = async (bookingId: number): Promise<{
  messages: MessageDTO[];
  conversation: ConversationSummaryDTO | null;
}> => {
  try {
    console.log('🔄 Fetching conversation detail for booking:', bookingId);

    // Fetch messages and conversations in parallel
    const [messages, conversations] = await Promise.all([
      getMessagesForBooking(bookingId),
      getConversations()
    ]);

    // Find the conversation for this booking
    const conversation = conversations.find(conv => conv.bookingId === bookingId) || null;

    console.log('🔍 Conversation search result:', {
      bookingId,
      conversationsCount: conversations.length,
      conversationFound: !!conversation,
      conversation: conversation,
      allConversations: conversations
    });

    // If no conversation found in list, try to get conversation info
    if (!conversation) {
      try {
        const info = await getConversationInfo(bookingId);
        // Create a temporary conversation object
        const tempConversation: ConversationSummaryDTO = {
          bookingId: info.bookingId,
          clientId: info.clientId,
          clientName: info.clientName,
          clientEmail: info.clientEmail,
          professionalId: info.professionalId,
          professionalName: info.professionalName,
          professionalEmail: info.professionalEmail,
          lastMessageContent: '',
          lastMessageTime: new Date().toISOString(),
          lastMessageFromCurrentUser: false,
          unreadCount: 0
        };
        console.log('✅ Created temporary conversation from booking info');
        return { messages, conversation: tempConversation };
      } catch (infoError) {
        console.warn('⚠️ Could not fetch conversation info, returning null conversation');
      }
    } else {
      console.log('✅ Conversation found in list, using existing conversation data');
    }

    console.log('✅ Conversation detail fetched - Final result:', {
      messagesCount: messages.length,
      conversationExists: !!conversation,
      hasParticipantInfo: !!(conversation?.clientId && conversation?.professionalId)
    });
    return { messages, conversation };
  } catch (error) {
    console.error('❌ Failed to fetch conversation detail:', bookingId, error);
    throw error;
  }
};

/**
 * Send a message via REST API (fallback when WebSocket is not available)
 */
export const sendMessage = async (request: SendMessageRequest): Promise<void> => {
  try {
    console.log('🔄 Sending message via REST API:', request);
    
    const messageDTO: Omit<MessageDTO, 'id' | 'sentAt'> = {
      bookingId: request.bookingId,
      senderId: '', // Will be set by backend from auth token
      receiverId: request.receiverUserId,
      content: request.content,
      isRead: false
    };
    
    await apiPost<void>(MESSAGING_BASE, messageDTO);
    console.log('✅ Message sent via REST API');
  } catch (error) {
    console.error('❌ Failed to send message via REST API:', error);
    throw error;
  }
};

/**
 * Mark a specific message as read
 */
export const markMessageAsRead = async (messageId: number): Promise<void> => {
  try {
    console.log('🔄 Marking message as read:', messageId);
    await apiPatch<void>(`${MESSAGING_BASE}/${messageId}/read`);
    console.log('✅ Message marked as read');
  } catch (error) {
    console.error('❌ Failed to mark message as read:', messageId, error);
    throw error;
  }
};

/**
 * Mark all messages in a conversation as read
 */
export const markConversationAsRead = async (bookingId: number): Promise<void> => {
  try {
    console.log('🔄 Marking conversation as read:', bookingId);
    await apiPut<void>(`${MESSAGING_BASE}/conversations/${bookingId}/read`);
    console.log('✅ Conversation marked as read');
  } catch (error) {
    console.error('❌ Failed to mark conversation as read:', bookingId, error);
    throw error;
  }
};

/**
 * Get total unread message count for current user
 */
export const getUnreadMessageCount = async (): Promise<number> => {
  try {
    console.log('🔄 Fetching unread message count...');
    const response = await apiGet<{ unreadCount: number }>(`${MESSAGING_BASE}/unread-count`);
    const count = response.unreadCount;
    console.log('✅ Unread message count:', count);
    return count;
  } catch (error) {
    console.error('❌ Failed to fetch unread message count:', error);
    throw error;
  }
};

/**
 * Get WebSocket URL for real-time messaging
 */
export const getWebSocketUrl = (bookingId: number, userId: string): string => {
  // Convert HTTP(S) URL to WebSocket URL and use the same versioning as API_BASE_URL
  const baseUrl = process.env.NODE_ENV === 'development'
    ? 'ws://localhost:8080/v1'
    : 'wss://api.yotelohago.co/v1';

  return `${baseUrl}/messaging/ws/${bookingId}?userId=${userId}`;
};
