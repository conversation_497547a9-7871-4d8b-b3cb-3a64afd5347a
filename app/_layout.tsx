import { useEffect } from 'react';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useFrameworkReady } from '@/hooks/useFrameworkReady';
import { useDeepLinkHandler } from '@/hooks/useDeepLinkHandler';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { AuthProvider } from '@/contexts/AuthContext';
import { MessagingProvider } from '@/contexts/MessagingContext';

export default function RootLayout() {
  useFrameworkReady();
  useDeepLinkHandler();

  return (
    <AuthProvider>
      <MessagingProvider>
        <GestureHandlerRootView style={{ flex: 1 }}>
          <>
            <Stack screenOptions={{ headerShown: false }}>
              <Stack.Screen name="index" options={{ headerShown: false }} />
              <Stack.Screen name="mode-selection" options={{ headerShown: false }} />
              <Stack.Screen name="client" options={{ headerShown: false }} />
              <Stack.Screen name="professional" options={{ headerShown: false }} />
              <Stack.Screen name="+not-found" />
            </Stack>
            <StatusBar style="auto" />
          </>
        </GestureHandlerRootView>
      </MessagingProvider>
    </AuthProvider>
  );
}