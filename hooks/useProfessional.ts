import { useState, useEffect } from 'react';
import { professionalService, ProfessionalWithUser } from '../services/api/professionalService';
import { ApiError } from '../types/api';

/**
 * Hook for fetching and managing professional data
 * @param userId Professional's internal user ID
 * @returns Professional data, loading state, and error state
 */
export const useProfessional = (userId: string | null) => {
  const [professional, setProfessional] = useState<ProfessionalWithUser | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchProfessional = async (id: string) => {
    try {
      setIsLoading(true);
      setError(null);

      console.log(`🔍 useProfessional: Fetching professional with ID: ${id}`);

      const professionalData = await professionalService.getProfessionalByUserId(id);

      console.log('✅ useProfessional: Professional data fetched successfully:', professionalData);
      setProfessional(professionalData);
    } catch (err) {
      console.error('❌ useProfessional: Failed to fetch professional:', err);

      const apiError = err as ApiError;
      let errorMessage = 'Failed to load professional profile';

      if (apiError.status === 404) {
        errorMessage = 'Professional not found';
      } else if (apiError.status === 500) {
        errorMessage = 'Server error - please try again later';
      } else if (apiError.error) {
        errorMessage = apiError.error;
      }

      setError(errorMessage);
      setProfessional(null);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (userId) {
      fetchProfessional(userId);
    } else {
      setProfessional(null);
      setError(null);
      setIsLoading(false);
    }
  }, [userId]);

  const refetch = () => {
    if (userId) {
      fetchProfessional(userId);
    }
  };

  return {
    professional,
    isLoading,
    error,
    refetch
  };
};

export default useProfessional;
