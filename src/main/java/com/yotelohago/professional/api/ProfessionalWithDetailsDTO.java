package com.yotelohago.professional.api;

import java.util.UUID;

/**
 * Consolidated DTO that combines all professional and user information
 * Follows the same coding convention as Service, Booking, Media, and Favorite domains:
 * - Profile image URLs are built exclusively in repository SQL queries using CONCAT()
 * - No URL construction in service or mapper layers
 * - Single comprehensive DTO to avoid multiple API calls
 */
public class ProfessionalWithDetailsDTO {
    
    // Professional domain data
    public UUID userId;
    public String bio;
    public String city;
    public Double avgRating;
    public Integer ratingCount;
    public String professionalProfileImageUrl; // Built in SQL using CONCAT pattern
    public boolean available;
    
    // User domain data (aggregated for complete profile)
    public String firstName;
    public String lastName;
    public String fullName; // Computed field for convenience
    public String email;
    public String phone;
    public String username;

    /**
     * Default constructor for JSON serialization/deserialization
     */
    public ProfessionalWithDetailsDTO() {}

    /**
     * Constructor for JPQL queries with all fields
     * Used in repository SQL queries to build DTOs directly from database
     *
     * @param userId professional user ID (Professional domain)
     * @param bio professional bio (Professional domain)
     * @param city professional city (Professional domain)
     * @param avgRating average rating (Professional domain)
     * @param ratingCount rating count (Professional domain)
     * @param available availability status (Professional domain)
     * @param professionalProfileImageUrl profile image URL (built in SQL)
     * @param firstName user first name (User domain)
     * @param lastName user last name (User domain)
     * @param email user email (User domain)
     * @param phone user phone (User domain)
     * @param username user username (User domain)
     */
    public ProfessionalWithDetailsDTO(
            UUID userId, String bio, String city, Double avgRating, Integer ratingCount,
            boolean available, String professionalProfileImageUrl,
            String firstName, String lastName, String email, String phone, String username) {

        // Professional domain data
        this.userId = userId;
        this.bio = bio;
        this.city = city;
        this.avgRating = avgRating;
        this.ratingCount = ratingCount;
        this.available = available;
        this.professionalProfileImageUrl = professionalProfileImageUrl;

        // User domain data
        this.firstName = firstName;
        this.lastName = lastName;
        this.email = email;
        this.phone = phone;
        this.username = username;

        // Compute full name for convenience
        this.fullName = computeFullName(firstName, lastName);
    }

    /**
     * Compute full name from first and last name
     * Handles null values gracefully
     */
    private String computeFullName(String firstName, String lastName) {
        if (firstName == null && lastName == null) {
            return null;
        }
        if (firstName == null) {
            return lastName;
        }
        if (lastName == null) {
            return firstName;
        }
        return firstName + " " + lastName;
    }



    @Override
    public String toString() {
        return "ProfessionalWithDetailsDTO{" +
                "userId=" + userId +
                ", bio='" + bio + '\'' +
                ", city='" + city + '\'' +
                ", avgRating=" + avgRating +
                ", ratingCount=" + ratingCount +
                ", available=" + available +
                ", firstName='" + firstName + '\'' +
                ", lastName='" + lastName + '\'' +
                ", fullName='" + fullName + '\'' +
                ", email='" + email + '\'' +
                ", phone='" + phone + '\'' +
                ", username='" + username + '\'' +
                '}';
    }
}
