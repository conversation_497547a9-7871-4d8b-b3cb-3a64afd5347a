#!/bin/bash

# Yo<PERSON>LoHago Media API Library
# Functions for testing Media domain endpoints

# Function to test media endpoints without authentication (should return 401)
test_media_protected_endpoints() {
    local base_url=${1:-$BACKEND_URL}
    
    print_status $CYAN "🔒 Testing media protected endpoints (should return 401) on $base_url..."
    
    echo ""
    print_status $BLUE "Media Endpoints (without authentication):"
    
    # Test presigned URL generation
    test_api_endpoint "/v1/media/presigned-url" "" "POST" '{"filename":"test.jpg","contentType":"image/jpeg","fileSize":1024,"mediaType":"client_profile_picture"}' "$base_url"
    
    # Test media retrieval
    test_api_endpoint "/v1/media/00000000-0000-0000-0000-000000000801" "" "GET" "" "$base_url"
    
    # Test user media listing
    test_api_endpoint "/v1/media/my-media" "" "GET" "" "$base_url"
    test_api_endpoint "/v1/media/user/00000000-0000-0000-0000-000000000101" "" "GET" "" "$base_url"
    
    # Test media deletion
    test_api_endpoint "/v1/media/00000000-0000-0000-0000-000000000801" "" "DELETE" "" "$base_url"
}

# Function to test media endpoints with authentication
test_media_authenticated_endpoints() {
    local username=${1:-$DEFAULT_USERNAME}
    local password=${2:-$DEFAULT_PASSWORD}
    local base_url=${3:-$BACKEND_URL}
    
    print_status $CYAN "🔐 Testing media endpoints with authentication on $base_url..."
    
    # Get authentication token
    local token
    token=$(get_user_token "$username" "$password")
    
    if [[ -z "$token" ]]; then
        print_status $RED "❌ Failed to get authentication token"
        return 1
    fi
    
    echo ""
    print_status $BLUE "Media Endpoints (with authentication):"
    
    # Test getting user's media
    test_api_endpoint "/v1/media/my-media" "$token" "GET" "" "$base_url"
    
    # Test getting specific media by ID (using sample data)
    test_api_endpoint "/v1/media/00000000-0000-0000-0000-000000000801" "$token" "GET" "" "$base_url"
    
    # Test presigned URL generation for different media types
    echo ""
    print_status $BLUE "Presigned URL Generation Tests:"
    
    # Client profile picture
    test_api_endpoint "/v1/media/presigned-url" "$token" "POST" '{"filename":"profile.jpg","contentType":"image/jpeg","fileSize":512000,"mediaType":"client_profile_picture"}' "$base_url"
    
    # Professional profile picture (requires professional role)
    test_api_endpoint "/v1/media/presigned-url" "$token" "POST" '{"filename":"pro_profile.jpg","contentType":"image/jpeg","fileSize":512000,"mediaType":"professional_profile_picture"}' "$base_url"
    
    # Service photo (requires professional role and service ID)
    test_api_endpoint "/v1/media/presigned-url" "$token" "POST" '{"filename":"service_photo.jpg","contentType":"image/jpeg","fileSize":1024000,"mediaType":"service_photo","associatedEntityId":"00000000-0000-0000-0000-000000000501"}' "$base_url"
    
    # Document upload
    test_api_endpoint "/v1/media/presigned-url" "$token" "POST" '{"filename":"document.pdf","contentType":"application/pdf","fileSize":2048000,"mediaType":"document"}' "$base_url"
    
    # Test validation errors
    echo ""
    print_status $BLUE "Validation Error Tests:"
    
    # Invalid content type
    test_api_endpoint "/v1/media/presigned-url" "$token" "POST" '{"filename":"test.exe","contentType":"application/exe","fileSize":1024,"mediaType":"client_profile_picture"}' "$base_url"
    
    # File too large
    test_api_endpoint "/v1/media/presigned-url" "$token" "POST" '{"filename":"huge.jpg","contentType":"image/jpeg","fileSize":50000000,"mediaType":"client_profile_picture"}' "$base_url"
    
    # Invalid media type
    test_api_endpoint "/v1/media/presigned-url" "$token" "POST" '{"filename":"test.jpg","contentType":"image/jpeg","fileSize":1024,"mediaType":"invalid_type"}' "$base_url"
    
    # Missing required fields
    test_api_endpoint "/v1/media/presigned-url" "$token" "POST" '{"filename":"test.jpg"}' "$base_url"
}

# Function to test media endpoints with professional user
test_media_professional_endpoints() {
    local username=${1:-"<EMAIL>"}
    local password=${2:-"1234"}
    local base_url=${3:-$BACKEND_URL}
    
    print_status $CYAN "👨‍🔧 Testing media endpoints with professional user on $base_url..."
    
    # Get authentication token for professional
    local token
    token=$(get_user_token "$username" "$password")
    
    if [[ -z "$token" ]]; then
        print_status $RED "❌ Failed to get professional authentication token"
        return 1
    fi
    
    echo ""
    print_status $BLUE "Professional Media Endpoints:"
    
    # Test professional profile picture upload
    test_api_endpoint "/v1/media/presigned-url" "$token" "POST" '{"filename":"professional_profile.jpg","contentType":"image/jpeg","fileSize":512000,"mediaType":"professional_profile_picture"}' "$base_url"
    
    # Test service photo upload
    test_api_endpoint "/v1/media/presigned-url" "$token" "POST" '{"filename":"service_work.jpg","contentType":"image/jpeg","fileSize":1024000,"mediaType":"service_photo","associatedEntityId":"00000000-0000-0000-0000-000000000501"}' "$base_url"
    
    # Test getting professional's media
    test_api_endpoint "/v1/media/my-media" "$token" "GET" "" "$base_url"
    
    # Test getting specific professional media (using sample data)
    test_api_endpoint "/v1/media/00000000-0000-0000-0000-000000000803" "$token" "GET" "" "$base_url"
}

# Function to test media admin endpoints
test_media_admin_endpoints() {
    local admin_username=${1:-"admin"}
    local admin_password=${2:-"yotelohago"}
    local base_url=${3:-$BACKEND_URL}
    
    print_status $CYAN "👑 Testing media admin endpoints on $base_url..."
    
    # Get admin authentication token
    local token
    token=$(get_user_token "$admin_username" "$admin_password")
    
    if [[ -z "$token" ]]; then
        print_status $RED "❌ Failed to get admin authentication token"
        return 1
    fi
    
    echo ""
    print_status $BLUE "Admin Media Endpoints:"
    
    # Test getting any user's media
    test_api_endpoint "/v1/media/user/00000000-0000-0000-0000-000000000101" "$token" "GET" "" "$base_url"
    test_api_endpoint "/v1/media/user/00000000-0000-0000-0000-000000000201" "$token" "GET" "" "$base_url"
    
    # Test accessing any media by ID
    test_api_endpoint "/v1/media/00000000-0000-0000-0000-000000000801" "$token" "GET" "" "$base_url"
    test_api_endpoint "/v1/media/00000000-0000-0000-0000-000000000803" "$token" "GET" "" "$base_url"
}

# Function to test media data consistency
test_media_data_consistency() {
    local base_url=${1:-$BACKEND_URL}
    
    print_status $CYAN "🔍 Testing media data consistency on $base_url..."
    
    echo ""
    print_status $BLUE "Media Data Consistency Checks:"
    
    # Test that sample media exists
    local token
    token=$(get_user_token "$DEFAULT_USERNAME" "$DEFAULT_PASSWORD")
    
    if [[ -n "$token" ]]; then
        # Check if sample media data is accessible
        test_api_endpoint "/v1/media/00000000-0000-0000-0000-000000000801" "$token" "GET" "" "$base_url"
        test_api_endpoint "/v1/media/00000000-0000-0000-0000-000000000802" "$token" "GET" "" "$base_url"
        
        # Check user media listings
        test_api_endpoint "/v1/media/my-media" "$token" "GET" "" "$base_url"
    fi
}

# Function to run comprehensive media tests
test_media_comprehensive() {
    local base_url=${1:-$BACKEND_URL}
    
    print_status $CYAN "🧪 Running comprehensive media API tests on $base_url..."
    
    # Test protected endpoints without auth
    test_media_protected_endpoints "$base_url"
    
    echo ""
    
    # Test with regular user authentication
    test_media_authenticated_endpoints "$DEFAULT_USERNAME" "$DEFAULT_PASSWORD" "$base_url"
    
    echo ""
    
    # Test with professional user
    test_media_professional_endpoints "<EMAIL>" "1234" "$base_url"
    
    echo ""
    
    # Test admin endpoints
    test_media_admin_endpoints "admin" "yotelohago" "$base_url"
    
    echo ""
    
    # Test data consistency
    test_media_data_consistency "$base_url"
    
    echo ""
    print_status $GREEN "✅ Comprehensive media API tests completed"
}

# Help function for media API testing
media_help() {
    cat << EOF

📸 MEDIA API TESTING COMMANDS:

BASIC TESTS:
    protected                           Test protected endpoints (should return 401)
    authenticated [username] [password] Test endpoints with authentication
    professional [username] [password] Test professional-specific endpoints
    admin [username] [password]        Test admin endpoints
    consistency                        Test media data consistency
    comprehensive                      Run all media tests

EXAMPLES:
    $0 media protected
    $0 <NAME_EMAIL> 1234
    $0 <NAME_EMAIL> 1234
    $0 media admin admin yotelohago
    $0 media comprehensive

ENDPOINT COVERAGE:
    POST /v1/media/presigned-url       Generate presigned upload URL
    GET  /v1/media/{mediaId}           Get media metadata
    GET  /v1/media/my-media            List current user's media
    GET  /v1/media/user/{userId}       List user's media (admin/own)
    DELETE /v1/media/{mediaId}         Delete media and file

MEDIA TYPES TESTED:
    - client_profile_picture           Client profile photos
    - professional_profile_picture     Professional profile photos
    - service_photo                    Service work photos
    - document                         General documents

VALIDATION TESTS:
    - File size limits (max 10MB)
    - Content type restrictions
    - Required field validation
    - Role-based authorization

EOF
}

# Main media function
media_main() {
    local command=$1
    shift

    case "$command" in
        "protected")
            test_media_protected_endpoints "$1"
            ;;
        "authenticated")
            test_media_authenticated_endpoints "$1" "$2" "$3"
            ;;
        "professional")
            test_media_professional_endpoints "$1" "$2" "$3"
            ;;
        "admin")
            test_media_admin_endpoints "$1" "$2" "$3"
            ;;
        "consistency")
            test_media_data_consistency "$1"
            ;;
        "comprehensive")
            test_media_comprehensive "$1"
            ;;
        *)
            print_status $RED "❌ Unknown media command: $command"
            media_help
            exit 1
            ;;
    esac
}
