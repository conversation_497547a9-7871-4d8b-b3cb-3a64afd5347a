import { useState, useEffect, useCallback } from 'react';
import {
  WorkScheduleDTO,
  UpdateWorkScheduleRequest,
  AvailabilityState,
  DayOfWeek,
  ApiError,
  AuthUser
} from '../types/api';
import { availabilityService } from '../services/api/availabilityService';
import { tokenStorage } from '../services/auth/tokenStorage';

/**
 * Custom hook for managing availability/work schedule state
 * Follows the same patterns as other hooks in the project (useBookings, useProfessional)
 */
export const useAvailability = (professionalId?: string) => {
  const [state, setState] = useState<AvailabilityState>({
    workSchedule: null,
    isLoading: false,
    isSaving: false,
    error: null,
    lastUpdated: null,
  });

  // Load work schedule
  const loadWorkSchedule = useCallback(async (id?: string) => {
    const targetId = id || professionalId;
    if (!targetId) {
      console.warn('⚠️ No professional ID provided for loading work schedule');
      return;
    }

    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      console.log(`🗓️ Loading work schedule for professional: ${targetId}`);
      
      const workSchedule = await availabilityService.getWorkScheduleByProfessional(targetId);
      
      setState(prev => ({
        ...prev,
        workSchedule,
        isLoading: false,
        lastUpdated: new Date(),
      }));

      console.log('✅ Work schedule loaded successfully');
    } catch (error) {
      console.error('❌ Failed to load work schedule:', error);
      
      const apiError = error as ApiError;
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: apiError.error || 'Failed to load work schedule',
      }));
    }
  }, [professionalId]);

  // Update work schedule
  const updateWorkSchedule = useCallback(async (
    request: UpdateWorkScheduleRequest,
    id?: string
  ) => {
    const targetId = id || professionalId;
    if (!targetId) {
      console.warn('⚠️ No professional ID provided for updating work schedule');
      return;
    }

    setState(prev => ({ ...prev, isSaving: true, error: null }));

    try {
      console.log(`🗓️ Updating work schedule for professional: ${targetId}`, request);
      
      const updatedSchedule = await availabilityService.updateWorkSchedule(targetId, request);
      
      setState(prev => ({
        ...prev,
        workSchedule: updatedSchedule,
        isSaving: false,
        lastUpdated: new Date(),
      }));

      console.log('✅ Work schedule updated successfully');
      return updatedSchedule;
    } catch (error) {
      console.error('❌ Failed to update work schedule:', error);
      
      const apiError = error as ApiError;
      setState(prev => ({
        ...prev,
        isSaving: false,
        error: apiError.error || 'Failed to update work schedule',
      }));
      throw error;
    }
  }, [professionalId]);

  // Delete work schedule
  const deleteWorkSchedule = useCallback(async (id?: string) => {
    const targetId = id || professionalId;
    if (!targetId) {
      console.warn('⚠️ No professional ID provided for deleting work schedule');
      return false;
    }

    setState(prev => ({ ...prev, isSaving: true, error: null }));

    try {
      console.log(`🗓️ Deleting work schedule for professional: ${targetId}`);
      
      const success = await availabilityService.deleteWorkSchedule(targetId);
      
      if (success) {
        setState(prev => ({
          ...prev,
          workSchedule: null,
          isSaving: false,
          lastUpdated: new Date(),
        }));
        console.log('✅ Work schedule deleted successfully');
      } else {
        setState(prev => ({ ...prev, isSaving: false }));
        console.log('ℹ️ Work schedule not found for deletion');
      }

      return success;
    } catch (error) {
      console.error('❌ Failed to delete work schedule:', error);
      
      const apiError = error as ApiError;
      setState(prev => ({
        ...prev,
        isSaving: false,
        error: apiError.error || 'Failed to delete work schedule',
      }));
      return false;
    }
  }, [professionalId]);

  // Check availability at specific time
  const checkAvailability = useCallback(async (
    dayOfWeek: DayOfWeek,
    time: string,
    id?: string
  ) => {
    const targetId = id || professionalId;
    if (!targetId) {
      console.warn('⚠️ No professional ID provided for checking availability');
      return false;
    }

    try {
      return await availabilityService.checkAvailability(targetId, dayOfWeek, time);
    } catch (error) {
      console.error('❌ Failed to check availability:', error);
      return false;
    }
  }, [professionalId]);

  // Find professionals available on specific day
  const findProfessionalsAvailableOnDay = useCallback(async (dayOfWeek: DayOfWeek) => {
    try {
      return await availabilityService.getProfessionalsAvailableOnDay(dayOfWeek);
    } catch (error) {
      console.error('❌ Failed to find professionals by day:', error);
      return [];
    }
  }, []);

  // Clear error
  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  // Refresh work schedule
  const refresh = useCallback(() => {
    if (professionalId) {
      loadWorkSchedule(professionalId);
    }
  }, [loadWorkSchedule, professionalId]);

  // Load work schedule on mount if professionalId is provided
  useEffect(() => {
    if (professionalId) {
      loadWorkSchedule(professionalId);
    }
  }, [loadWorkSchedule, professionalId]);

  return {
    // State
    workSchedule: state.workSchedule,
    isLoading: state.isLoading,
    isSaving: state.isSaving,
    error: state.error,
    lastUpdated: state.lastUpdated,

    // Actions
    loadWorkSchedule,
    updateWorkSchedule,
    deleteWorkSchedule,
    checkAvailability,
    findProfessionalsAvailableOnDay,
    clearError,
    refresh,

    // Computed properties
    hasWorkSchedule: !!state.workSchedule,
    hasActiveDays: state.workSchedule?.hasAnyActiveDays || false,
    totalWeeklyMinutes: state.workSchedule?.totalWeeklyMinutes || 0,
  };
};

/**
 * Hook for managing current user's availability (professional mode)
 */
export const useMyAvailability = () => {
  const [state, setState] = useState<AvailabilityState>({
    workSchedule: null,
    isLoading: false,
    isSaving: false,
    error: null,
    lastUpdated: null,
  });

  // Get current user ID from token storage
  const getCurrentUserId = useCallback(async (): Promise<string | null> => {
    try {
      const userInfo = await tokenStorage.getUserInfo() as AuthUser;
      return userInfo?.internalId || null;
    } catch (error) {
      console.error('❌ Failed to get current user ID:', error);
      return null;
    }
  }, []);

  // Load current user's work schedule
  const loadMyWorkSchedule = useCallback(async () => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      console.log('🗓️ Loading current user work schedule');

      const currentUserId = await getCurrentUserId();
      if (!currentUserId) {
        throw new Error('User not authenticated');
      }

      const workSchedule = await availabilityService.getMyWorkSchedule(currentUserId);

      setState(prev => ({
        ...prev,
        workSchedule,
        isLoading: false,
        lastUpdated: new Date(),
      }));

      console.log('✅ Current user work schedule loaded successfully');
    } catch (error) {
      console.error('❌ Failed to load current user work schedule:', error);

      const apiError = error as ApiError;
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: apiError.error || 'Failed to load work schedule',
      }));
    }
  }, [getCurrentUserId]);

  // Update current user's work schedule
  const updateMyWorkSchedule = useCallback(async (request: UpdateWorkScheduleRequest) => {
    setState(prev => ({ ...prev, isSaving: true, error: null }));

    try {
      console.log('🗓️ Updating current user work schedule', request);

      const currentUserId = await getCurrentUserId();
      if (!currentUserId) {
        throw new Error('User not authenticated');
      }

      const updatedSchedule = await availabilityService.updateMyWorkSchedule(currentUserId, request);

      setState(prev => ({
        ...prev,
        workSchedule: updatedSchedule,
        isSaving: false,
        lastUpdated: new Date(),
      }));

      console.log('✅ Current user work schedule updated successfully');
      return updatedSchedule;
    } catch (error) {
      console.error('❌ Failed to update current user work schedule:', error);

      const apiError = error as ApiError;
      setState(prev => ({
        ...prev,
        isSaving: false,
        error: apiError.error || 'Failed to update work schedule',
      }));
      throw error;
    }
  }, [getCurrentUserId]);

  // Clear error
  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  // Refresh work schedule
  const refresh = useCallback(() => {
    loadMyWorkSchedule();
  }, [loadMyWorkSchedule]);

  // Load work schedule on mount
  useEffect(() => {
    loadMyWorkSchedule();
  }, [loadMyWorkSchedule]);

  return {
    // State
    workSchedule: state.workSchedule,
    isLoading: state.isLoading,
    isSaving: state.isSaving,
    error: state.error,
    lastUpdated: state.lastUpdated,

    // Actions
    loadMyWorkSchedule,
    updateMyWorkSchedule,
    clearError,
    refresh,

    // Computed properties
    hasWorkSchedule: !!state.workSchedule,
    hasActiveDays: state.workSchedule?.hasAnyActiveDays || false,
    totalWeeklyMinutes: state.workSchedule?.totalWeeklyMinutes || 0,
  };
};

export default useAvailability;
