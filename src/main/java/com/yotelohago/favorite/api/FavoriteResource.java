package com.yotelohago.favorite.api;

import com.yotelohago.common.ApiVersion;
import com.yotelohago.favorite.application.FavoriteManagementService;
import com.yotelohago.service.api.ServiceWithProfessionalDTO;
import jakarta.annotation.security.RolesAllowed;
import jakarta.inject.Inject;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;

import java.util.List;
import java.util.UUID;

@Path(ApiVersion.BASE + "/favorites")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@RolesAllowed({"user", "professional", "admin"})
public class FavoriteResource {

    @Inject
    FavoriteManagementService favoriteService;

    /**
     * Get all favorite services for the current authenticated user
     * Returns services with professional information
     */
    @GET
    public Response getFavorites() {
        try {
            List<ServiceWithProfessionalDTO> favorites = favoriteService.getCurrentUserFavorites();
            return Response.ok(favorites).build();
        } catch (ForbiddenException e) {
            return Response.status(Response.Status.FORBIDDEN)
                    .entity("{\"error\": \"" + e.getMessage() + "\"}")
                    .build();
        } catch (Exception e) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("{\"error\": \"Failed to retrieve favorites\"}")
                    .build();
        }
    }

    /**
     * Get favorite service IDs for the current authenticated user
     * Lightweight endpoint for checking favorite status
     */
    @GET
    @Path("/service-ids")
    public Response getFavoriteServiceIds() {
        try {
            List<UUID> serviceIds = favoriteService.getCurrentUserFavoriteServiceIds();
            return Response.ok(serviceIds).build();
        } catch (ForbiddenException e) {
            return Response.status(Response.Status.FORBIDDEN)
                    .entity("{\"error\": \"" + e.getMessage() + "\"}")
                    .build();
        } catch (Exception e) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("{\"error\": \"Failed to retrieve favorite service IDs\"}")
                    .build();
        }
    }

    /**
     * Add a service to current user's favorites
     */
    @POST
    @Path("/{serviceId}")
    public Response addToFavorites(@PathParam("serviceId") String serviceIdStr) {
        try {
            UUID serviceId = UUID.fromString(serviceIdStr);
            boolean added = favoriteService.addToFavorites(serviceId);
            
            if (added) {
                return Response.status(Response.Status.CREATED)
                        .entity("{\"message\": \"Service added to favorites\", \"favorited\": true}")
                        .build();
            } else {
                return Response.ok()
                        .entity("{\"message\": \"Service already in favorites\", \"favorited\": true}")
                        .build();
            }
        } catch (IllegalArgumentException e) {
            return Response.status(Response.Status.BAD_REQUEST)
                    .entity("{\"error\": \"Invalid service ID format\"}")
                    .build();
        } catch (ForbiddenException e) {
            return Response.status(Response.Status.FORBIDDEN)
                    .entity("{\"error\": \"" + e.getMessage() + "\"}")
                    .build();
        } catch (Exception e) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("{\"error\": \"Failed to add service to favorites\"}")
                    .build();
        }
    }

    /**
     * Remove a service from current user's favorites
     */
    @DELETE
    @Path("/{serviceId}")
    public Response removeFromFavorites(@PathParam("serviceId") String serviceIdStr) {
        try {
            UUID serviceId = UUID.fromString(serviceIdStr);
            boolean removed = favoriteService.removeFromFavorites(serviceId);
            
            if (removed) {
                return Response.ok()
                        .entity("{\"message\": \"Service removed from favorites\", \"favorited\": false}")
                        .build();
            } else {
                return Response.ok()
                        .entity("{\"message\": \"Service was not in favorites\", \"favorited\": false}")
                        .build();
            }
        } catch (IllegalArgumentException e) {
            return Response.status(Response.Status.BAD_REQUEST)
                    .entity("{\"error\": \"Invalid service ID format\"}")
                    .build();
        } catch (ForbiddenException e) {
            return Response.status(Response.Status.FORBIDDEN)
                    .entity("{\"error\": \"" + e.getMessage() + "\"}")
                    .build();
        } catch (Exception e) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("{\"error\": \"Failed to remove service from favorites\"}")
                    .build();
        }
    }

    /**
     * Check if a service is favorited by the current user
     */
    @GET
    @Path("/check/{serviceId}")
    public Response checkFavoriteStatus(@PathParam("serviceId") String serviceIdStr) {
        try {
            UUID serviceId = UUID.fromString(serviceIdStr);
            boolean isFavorited = favoriteService.isFavorited(serviceId);
            
            return Response.ok()
                    .entity("{\"serviceId\": \"" + serviceId + "\", \"favorited\": " + isFavorited + "}")
                    .build();
        } catch (IllegalArgumentException e) {
            return Response.status(Response.Status.BAD_REQUEST)
                    .entity("{\"error\": \"Invalid service ID format\"}")
                    .build();
        } catch (Exception e) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("{\"error\": \"Failed to check favorite status\"}")
                    .build();
        }
    }

    /**
     * Toggle favorite status for a service
     */
    @PUT
    @Path("/{serviceId}/toggle")
    public Response toggleFavorite(@PathParam("serviceId") String serviceIdStr) {
        try {
            UUID serviceId = UUID.fromString(serviceIdStr);
            boolean isFavorited = favoriteService.toggleFavorite(serviceId);
            
            String message = isFavorited ? "Service added to favorites" : "Service removed from favorites";
            return Response.ok()
                    .entity("{\"message\": \"" + message + "\", \"favorited\": " + isFavorited + "}")
                    .build();
        } catch (IllegalArgumentException e) {
            return Response.status(Response.Status.BAD_REQUEST)
                    .entity("{\"error\": \"Invalid service ID format\"}")
                    .build();
        } catch (ForbiddenException e) {
            return Response.status(Response.Status.FORBIDDEN)
                    .entity("{\"error\": \"" + e.getMessage() + "\"}")
                    .build();
        } catch (Exception e) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("{\"error\": \"Failed to toggle favorite status\"}")
                    .build();
        }
    }

    /**
     * Get the count of favorites for the current user
     */
    @GET
    @Path("/count")
    public Response getFavoritesCount() {
        try {
            long count = favoriteService.getCurrentUserFavoritesCount();
            return Response.ok()
                    .entity("{\"count\": " + count + "}")
                    .build();
        } catch (Exception e) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("{\"error\": \"Failed to get favorites count\"}")
                    .build();
        }
    }
}
