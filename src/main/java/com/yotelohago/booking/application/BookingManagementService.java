package com.yotelohago.booking.application;

import com.yotelohago.auth.TokenIdentityProvider;
import com.yotelohago.booking.api.BookingWithDetailsDTO;
import com.yotelohago.booking.domain.Booking;
import com.yotelohago.booking.domain.BookingRepository;
import com.yotelohago.booking.domain.BookingStatus;
import com.yotelohago.common.dto.PageRequest;
import com.yotelohago.common.dto.PageResponse;
import com.yotelohago.user.application.UserSyncService;
import com.yotelohago.user.domain.User;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;
import jakarta.ws.rs.ForbiddenException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@ApplicationScoped
public class BookingManagementService {

    private static final Logger logger = LoggerFactory.getLogger(BookingManagementService.class);

    @Inject
    BookingRepository repository;

    @Inject
    TokenIdentityProvider tokenIdentityProvider;

    @Inject
    UserSyncService userSyncService;

    /**
     * Find booking by ID with authorization check
     * Users can only access bookings they are involved in (client or professional)
     */
    public Optional<Booking> findById(Long id) {
        // Ensure current user exists in database
        userSyncService.ensureCurrentUserExists();

        Optional<Booking> bookingOpt = repository.findById(id);
        if (bookingOpt.isEmpty()) {
            return Optional.empty();
        }

        Booking booking = bookingOpt.get();

        // Check authorization
        if (!tokenIdentityProvider.canAccessBooking(booking.getClient().id,
                booking.getProfessional() != null ? booking.getProfessional().id : null)) {
            throw new ForbiddenException("Access denied to booking data");
        }

        return bookingOpt;
    }

    /**
     * Find all bookings - Admin only, or filtered by user access
     * TODO: Add pagination for performance
     */
    public List<Booking> findAll() {
        // Ensure current user exists in database
        userSyncService.ensureCurrentUserExists();

        if (tokenIdentityProvider.hasRole("admin")) {
            return repository.findAll();
        }

        // For non-admin users, return only bookings they are involved in
        UUID currentUserId = tokenIdentityProvider.getCurrentUserId();
        if (currentUserId == null) {
            throw new ForbiddenException("Authentication required");
        }

        // Filter bookings where user is either client or professional
        return repository.findAll().stream()
                .filter(booking -> currentUserId.equals(booking.getClient().id) ||
                                 (booking.getProfessional() != null && currentUserId.equals(booking.getProfessional().id)))
                .toList();
    }

    /**
     * Create a booking - Only authenticated users can create bookings
     */
    @Transactional
    public Booking create(Booking booking) {
        // Ensure current user exists in database
        userSyncService.ensureCurrentUserExists();

        UUID currentUserId = tokenIdentityProvider.getCurrentUserId();
        if (currentUserId == null) {
            throw new ForbiddenException("Authentication required");
        }

        // Load the current user entity and set as client (security measure)
        User currentUser = User.findById(currentUserId);
        if (currentUser == null) {
            throw new IllegalStateException("Current user not found in database");
        }
        booking.setClient(currentUser);

        // Set creation timestamp
        booking.setCreatedAt(Instant.now());
        booking.setUpdatedAt(Instant.now());

        repository.persist(booking);
        repository.flush();

        logger.info("Booking created: {} by client: {}", booking.getId(), currentUserId);
        return booking;
    }

    /**
     * Update a booking - Only users involved in the booking can update it
     */
    @Transactional
    public Booking update(Booking updatedBooking) {
        // Ensure current user exists in database
        userSyncService.ensureCurrentUserExists();

        // Get existing booking to check authorization
        Booking existingBooking = repository.findById(updatedBooking.getId())
                .orElseThrow(() -> new IllegalArgumentException("Booking not found"));

        // Check authorization
        if (!tokenIdentityProvider.canAccessBooking(existingBooking.getClient().id,
                existingBooking.getProfessional() != null ? existingBooking.getProfessional().id : null)) {
            throw new ForbiddenException("Access denied to update booking");
        }

        // Preserve immutable fields (security measures)
        updatedBooking.setClient(existingBooking.getClient());
        updatedBooking.setCreatedAt(existingBooking.getCreatedAt());
        updatedBooking.setUpdatedAt(Instant.now());

        Booking result = repository.update(updatedBooking);
        logger.info("Booking updated: {}", updatedBooking.getId());
        return result;
    }

    /**
     * Delete a booking - Only the client who created it or admin can delete
     */
    @Transactional
    public boolean delete(Long id) {
        // Ensure current user exists in database
        userSyncService.ensureCurrentUserExists();

        // Get booking to check authorization
        Optional<Booking> bookingOpt = repository.findById(id);
        if (bookingOpt.isEmpty()) {
            return false;
        }

        Booking booking = bookingOpt.get();
        UUID currentUserId = tokenIdentityProvider.getCurrentUserId();

        // Only the client who created the booking or admin can delete it
        if (!tokenIdentityProvider.hasRole("admin") &&
            !currentUserId.equals(booking.getClient().id)) {
            throw new ForbiddenException("Only the client who created the booking or admin can delete it");
        }

        repository.deleteById(id);
        logger.info("Booking deleted: {} by user: {}", id, currentUserId);
        return true;
    }

    /**
     * Find all bookings with pagination and authorization
     */
    public PageResponse<Booking> findAllPaginated(PageRequest pageRequest) {
        // Ensure current user exists in database
        userSyncService.ensureCurrentUserExists();

        if (tokenIdentityProvider.hasRole("admin")) {
            return repository.findAllPaginated(pageRequest);
        }

        // For non-admin users, return only bookings they are involved in
        UUID currentUserId = tokenIdentityProvider.getCurrentUserId();
        if (currentUserId == null) {
            throw new ForbiddenException("Authentication required");
        }

        return repository.findByUserPaginated(currentUserId, pageRequest);
    }

    /**
     * Find bookings by status with pagination and authorization
     */
    public PageResponse<Booking> findByStatusPaginated(BookingStatus status, PageRequest pageRequest) {
        // Ensure current user exists in database
        userSyncService.ensureCurrentUserExists();

        if (tokenIdentityProvider.hasRole("admin")) {
            return repository.findByStatusPaginated(status, pageRequest);
        }

        // For non-admin users, return only bookings they are involved in
        UUID currentUserId = tokenIdentityProvider.getCurrentUserId();
        if (currentUserId == null) {
            throw new ForbiddenException("Authentication required");
        }

        return repository.findByUserAndStatusPaginated(currentUserId, status, pageRequest);
    }

    /**
     * Find all bookings with details (professional and service data) with pagination
     */
    public PageResponse<BookingWithDetailsDTO> findAllWithDetailsPaginated(PageRequest pageRequest) {
        // Ensure current user exists in database
        userSyncService.ensureCurrentUserExists();

        if (tokenIdentityProvider.hasRole("admin")) {
            return repository.findAllWithDetailsPaginated(pageRequest);
        }

        // For non-admin users, return only bookings they are involved in
        UUID currentUserId = tokenIdentityProvider.getCurrentUserId();
        if (currentUserId == null) {
            throw new ForbiddenException("Authentication required");
        }

        return repository.findByUserWithDetailsPaginated(currentUserId, pageRequest);
    }

    /**
     * Find bookings by status with details (professional and service data) with pagination
     */
    public PageResponse<BookingWithDetailsDTO> findByStatusWithDetailsPaginated(BookingStatus status, PageRequest pageRequest) {
        // Ensure current user exists in database
        userSyncService.ensureCurrentUserExists();

        if (tokenIdentityProvider.hasRole("admin")) {
            return repository.findByStatusWithDetailsPaginated(status, pageRequest);
        }

        // For non-admin users, return only bookings they are involved in
        UUID currentUserId = tokenIdentityProvider.getCurrentUserId();
        if (currentUserId == null) {
            throw new ForbiddenException("Authentication required");
        }

        return repository.findByUserAndStatusWithDetailsPaginated(currentUserId, status, pageRequest);
    }
}