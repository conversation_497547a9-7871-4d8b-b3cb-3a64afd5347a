import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronRight, ChevronLeft, Zap, Wrench, Truck, Shield, ArrowRight, MapPin } from 'lucide-react';
import HeroPanel from './components/HeroPanel';
import ServicePanel from './components/ServicePanel';
import CTAPanel from './components/CTAPanel';
import ScrollIndicator from './components/ScrollIndicator';

const services = [
  {
    id: 'electricians',
    title: 'Electricians',
    icon: Zap,
    color: 'from-coral-400 to-coral-600',
    bgColor: 'bg-gradient-to-br from-coral-50 to-orange-50',
    features: ['Instant Quotes', 'Licensed & Insured', '24/7 Emergency', 'Smart Home Setup'],
    description: 'Professional electrical services you can trust',
    animation: 'spark'
  },
  {
    id: 'plumbers',
    title: 'Plumbers',
    icon: Wrench,
    color: 'from-coral-400 to-coral-600',
    bgColor: 'bg-gradient-to-br from-blue-50 to-coral-50',
    features: ['Emergency Repairs', 'Drain Cleaning', 'Water Heaters', 'Pipe Installation'],
    description: 'Expert plumbing solutions for every need',
    animation: 'wave'
  },
  {
    id: 'movers',
    title: 'Movers',
    icon: Truck,
    color: 'from-coral-400 to-coral-600',
    bgColor: 'bg-gradient-to-br from-green-50 to-coral-50',
    features: ['Full Packing', 'Local & Long Distance', 'Piano Moving', 'Storage Solutions'],
    description: 'Stress-free moving with professional care',
    animation: 'slide'
  },
  {
    id: 'locksmiths',
    title: 'Locksmiths',
    icon: Shield,
    color: 'from-coral-400 to-coral-600',
    bgColor: 'bg-gradient-to-br from-purple-50 to-coral-50',
    features: ['24/7 Lockouts', 'Key Duplication', 'Security Systems', 'Safe Installation'],
    description: 'Secure your home with trusted professionals',
    animation: 'lock'
  }
];

function App() {
  const [currentPanel, setCurrentPanel] = useState(0);
  const [isScrolling, setIsScrolling] = useState(false);
  const totalPanels = services.length + 2; // Hero + Services + CTA

  useEffect(() => {
    const handleWheel = (e: WheelEvent) => {
      if (isScrolling) return;
      
      e.preventDefault();
      setIsScrolling(true);
      
      // Reduced threshold for more sensitive scrolling
      if (e.deltaX > 20 || e.deltaY > 20) {
        setCurrentPanel(prev => Math.min(prev + 1, totalPanels - 1));
      } else if (e.deltaX < -20 || e.deltaY < -20) {
        setCurrentPanel(prev => Math.max(prev - 1, 0));
      }
      
      // Reduced timeout for faster transitions
      setTimeout(() => setIsScrolling(false), 400);
    };

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'ArrowRight') {
        setCurrentPanel(prev => Math.min(prev + 1, totalPanels - 1));
      } else if (e.key === 'ArrowLeft') {
        setCurrentPanel(prev => Math.max(prev - 1, 0));
      }
    };

    // Touch handling for mobile
    let touchStartX = 0;
    let touchStartY = 0;

    const handleTouchStart = (e: TouchEvent) => {
      touchStartX = e.touches[0].clientX;
      touchStartY = e.touches[0].clientY;
    };

    const handleTouchEnd = (e: TouchEvent) => {
      if (isScrolling) return;
      
      const touchEndX = e.changedTouches[0].clientX;
      const touchEndY = e.changedTouches[0].clientY;
      const deltaX = touchStartX - touchEndX;
      const deltaY = touchStartY - touchEndY;
      
      // Reduced threshold for more sensitive touch scrolling
      if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 30) {
        setIsScrolling(true);
        if (deltaX > 0) {
          setCurrentPanel(prev => Math.min(prev + 1, totalPanels - 1));
        } else {
          setCurrentPanel(prev => Math.max(prev - 1, 0));
        }
        setTimeout(() => setIsScrolling(false), 400);
      }
    };

    window.addEventListener('wheel', handleWheel, { passive: false });
    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('touchstart', handleTouchStart, { passive: true });
    window.addEventListener('touchend', handleTouchEnd, { passive: true });
    
    return () => {
      window.removeEventListener('wheel', handleWheel);
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('touchstart', handleTouchStart);
      window.removeEventListener('touchend', handleTouchEnd);
    };
  }, [isScrolling, totalPanels]);

  const nextPanel = () => {
    setCurrentPanel(prev => Math.min(prev + 1, totalPanels - 1));
  };

  const prevPanel = () => {
    setCurrentPanel(prev => Math.max(prev - 1, 0));
  };

  return (
    <div className="relative w-full h-screen overflow-hidden bg-slate-900">
      {/* Main Content Container */}
      <motion.div
        className="flex h-full w-full"
        animate={{ x: `${-currentPanel * 100}vw` }}
        transition={{ 
          type: "spring", 
          stiffness: 400, 
          damping: 40,
          mass: 0.8
        }}
        style={{ width: `${totalPanels * 100}vw` }}
      >
        {/* Hero Panel */}
        <div className="w-screen h-full flex-shrink-0">
          <HeroPanel onNext={nextPanel} />
        </div>
        
        {/* Service Panels */}
        {services.map((service, index) => (
          <div key={service.id} className="w-screen h-full flex-shrink-0">
            <ServicePanel
              service={service}
              isActive={currentPanel === index + 1}
            />
          </div>
        ))}
        
        {/* CTA Panel */}
        <div className="w-screen h-full flex-shrink-0">
          <CTAPanel />
        </div>
      </motion.div>

      {/* Navigation Controls */}
      <div className="fixed bottom-8 left-1/2 transform -translate-x-1/2 z-50">
        <div className="flex items-center space-x-4">
          <button
            onClick={prevPanel}
            disabled={currentPanel === 0}
            className="p-3 rounded-full bg-white/20 backdrop-blur-md text-white hover:bg-coral-500/30 transition-all disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <ChevronLeft className="w-6 h-6" />
          </button>
          
          <div className="flex space-x-2">
            {Array.from({ length: totalPanels }).map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentPanel(index)}
                className={`w-3 h-3 rounded-full transition-all ${
                  currentPanel === index
                    ? 'bg-coral-500 scale-125'
                    : 'bg-white/50 hover:bg-coral-300'
                }`}
              />
            ))}
          </div>
          
          <button
            onClick={nextPanel}
            disabled={currentPanel === totalPanels - 1}
            className="p-3 rounded-full bg-white/20 backdrop-blur-md text-white hover:bg-coral-500/30 transition-all disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <ChevronRight className="w-6 h-6" />
          </button>
        </div>
      </div>

      {/* Scroll Indicator */}
      <ScrollIndicator currentPanel={currentPanel} totalPanels={totalPanels} />
    </div>
  );
}

export default App;