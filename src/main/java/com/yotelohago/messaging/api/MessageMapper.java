package com.yotelohago.messaging.api;

import com.yotelohago.messaging.domain.Message;
import com.yotelohago.booking.domain.Booking;
import com.yotelohago.user.domain.User;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

@ApplicationScoped
public class MessageMapper {

    @PersistenceContext
    EntityManager entityManager; // CDI pattern like we use on ServiceMapper class

    public MessageDTO toDTO(Message m) {
        MessageDTO dto = new MessageDTO();
        dto.id = m.id;
        dto.bookingId = m.getBooking().id;
        dto.senderId = m.getSender().id;  // UUID type matches MessageDTO.senderId
        dto.receiverId = m.getReceiver().id;  // UUID type matches MessageDTO.receiverId
        dto.content = m.content;
        dto.sentAt = m.sentAt;
        dto.isRead = m.isRead;
        return dto;
    }

    public Message toEntity(MessageDTO dto) {
        // Validate required fields
        if (dto.bookingId == null) {
            throw new IllegalArgumentException("Booking ID is required");
        }
        if (dto.senderId == null) {
            throw new IllegalArgumentException("Sender ID is required");
        }
        if (dto.receiverId == null) {
            throw new IllegalArgumentException("Receiver ID is required");
        }

        // Load the required entities from database
        Booking booking = Booking.findById(dto.bookingId);
        User sender = User.findById(dto.senderId);
        User receiver = User.findById(dto.receiverId);

        if (booking == null) {
            throw new IllegalArgumentException("Booking not found: " + dto.bookingId);
        }
        if (sender == null) {
            throw new IllegalArgumentException("Sender not found: " + dto.senderId);
        }
        if (receiver == null) {
            throw new IllegalArgumentException("Receiver not found: " + dto.receiverId);
        }

        // Create message with proper entity relationships
        Message message = new Message(booking, sender, receiver, dto.content, dto.isRead);

        // Only set ID if it exists (for updates), let JPA generate it for new messages
        if (dto.id != null) {
            message.id = dto.id;
        }

        if (dto.sentAt != null) {
            message.setSentAt(dto.sentAt);
        }

        return message;
    }
}