package com.yotelohago.service.api;

import com.yotelohago.service.domain.Service;
import com.yotelohago.service.domain.ServiceCategory;
import com.yotelohago.service.domain.ServiceStatus;
import com.yotelohago.user.domain.User;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

@ApplicationScoped
public class ServiceMapper {

    @PersistenceContext
    EntityManager entityManager;

    public ServiceDTO toDTO(Service service) {
        return new ServiceDTO(
                service.getId(),
                service.getProfessionalUser().id,
                service.getTitle(),
                service.getDescription(),
                service.getPrice(),
                service.getCategory().getId(),
                service.getCategory().getName(),
                service.getStatus().toString(),
                service.getCreatedAt(),
                service.getUpdatedAt()
        );
    }

    public Service toEntity(ServiceDTO dto) {
        ServiceCategory category = entityManager.find(ServiceCategory.class, dto.categoryId);
        if (category == null) {
            throw new IllegalArgumentException("Invalid service category ID: " + dto.categoryId);
        }

        // Load the professional user entity
        User professionalUser = User.findById(dto.professionalUserId);
        if (professionalUser == null) {
            throw new IllegalArgumentException("Professional user not found: " + dto.professionalUserId);
        }

        ServiceStatus status = ServiceStatus.DRAFT; // Default status
        if (dto.status != null && !dto.status.isEmpty()) {
            try {
                status = ServiceStatus.fromValue(dto.status);
            } catch (IllegalArgumentException e) {
                throw new IllegalArgumentException("Invalid service status: " + dto.status);
            }
        }

        return new Service(
                null, // Let JPA auto-generate the ID
                dto.title,
                dto.description,
                category,
                professionalUser,
                dto.price,
                status
        );
    }

    /**
     * Convert DTO to entity for updates, using existing service's professional user
     * This method is used when updating services to avoid requiring professionalUserId in the DTO
     * and to prevent changing the service ownership
     */
    public Service toEntityForUpdate(ServiceDTO dto, Service existingService) {
        ServiceCategory category = entityManager.find(ServiceCategory.class, dto.categoryId);
        if (category == null) {
            throw new IllegalArgumentException("Invalid service category ID: " + dto.categoryId);
        }

        ServiceStatus status = ServiceStatus.DRAFT; // Default status
        if (dto.status != null && !dto.status.isEmpty()) {
            try {
                status = ServiceStatus.fromValue(dto.status);
            } catch (IllegalArgumentException e) {
                throw new IllegalArgumentException("Invalid service status: " + dto.status);
            }
        }

        return new Service(
                dto.id,
                dto.title,
                dto.description,
                category,
                existingService.getProfessionalUser(), // Use existing service's professional user
                dto.price,
                status
        );
    }
}