import React from 'react';
import { View, Text, StyleSheet, Image, Pressable, Dimensions, Platform } from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import Colors from '@/constants/Colors';
import { Service } from '@/types';

const { width } = Dimensions.get('window');
const isWeb = Platform.OS === 'web';
const cardWidth = isWeb 
  ? (width >= 768 ? (width / 3) - 24 : (width / 2) - 24)
  : width - 32; // Full width minus padding on mobile

interface ServiceCardProps {
  service: Service;
  onPress?: (service: Service) => void;
  onFavoritePress?: (service: Service) => void;
  isFavorite?: boolean;
}

export default function ServiceCard({ 
  service, 
  onPress,
  onFavoritePress,
  isFavorite = false
}: ServiceCardProps) {
  const router = useRouter();

  const handlePress = () => {
    console.log('ServiceCard pressed:', { 
      serviceId: service.id,
      providerId: service.providerId,
      hasOnPress: !!onPress 
    });

    if (onPress) {
      onPress(service);
    } else if (service.providerId) {
      console.log('Navigating to professional profile:', service.providerId);
      router.push({
        pathname: '/client/professional/[id]',
        params: { id: service.providerId }
      });
    } else {
      console.log('No providerId found for service:', service.id);
    }
  };

  return (
    <Pressable 
      style={[styles.container, { width: cardWidth }]} 
      onPress={handlePress}
    >
      <View style={styles.content}>
        <View style={styles.providerInfo}>
          <Image
            source={{ uri: service.images[0] }}
            style={styles.providerAvatar}
          />
          <View style={styles.providerDetails}>
            <Text style={styles.providerName}>{service.title}</Text>
            <View style={styles.ratingContainer}>
              <Ionicons name="star" size={14} color={Colors.warning} />
              <Text style={styles.rating}>{service.rating}</Text>
            </View>
          </View>
        </View>
        
        <View style={styles.serviceInfo}>
          <Text style={styles.title} numberOfLines={1}>
            {service.title}
          </Text>
          <Text style={styles.category}>{service.category}</Text>
          <Text style={styles.location} numberOfLines={1}>{service.location}</Text>
        </View>
        
        <View style={styles.footer}>
          <Text style={styles.price}>
            <Text style={styles.priceBold}>${service.price}</Text>
            {service.category === 'Web Design' ? ' per project' : ' per hour'}
          </Text>
          
          <View style={styles.badges}>
            {service.instantBook && (
              <View style={styles.instantBookBadge}>
                <Text style={styles.instantBookText}>Instant</Text>
              </View>
            )}
            {service.featured && (
              <View style={styles.featuredBadge}>
                <Text style={styles.featuredText}>Featured</Text>
              </View>
            )}
          </View>
        </View>

        {onFavoritePress && (
          <Pressable 
            style={styles.favoriteButton}
            onPress={() => onFavoritePress(service)}
          >
            <Ionicons name="heart" size={20} color={isFavorite ? Colors.error : Colors.text.secondary} />
          </Pressable>
        )}
      </View>
    </Pressable>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.background.primary,
    borderRadius: 12,
    marginBottom: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  content: {
    padding: 8,
  },
  providerInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  providerAvatar: {
    width: 36,
    height: 36,
    borderRadius: 18,
    marginRight: 8,
  },
  providerDetails: {
    flex: 1,
  },
  providerName: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.text.primary,
    marginBottom: 2,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rating: {
    marginLeft: 4,
    fontSize: 12,
    fontWeight: '500',
    color: Colors.text.secondary,
  },
  serviceInfo: {
    marginBottom: 8,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text.primary,
    marginBottom: 2,
  },
  category: {
    fontSize: 14,
    color: Colors.text.secondary,
    marginBottom: 2,
  },
  location: {
    fontSize: 14,
    color: Colors.text.secondary,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  price: {
    fontSize: 14,
    color: Colors.text.primary,
  },
  priceBold: {
    fontWeight: '600',
  },
  badges: {
    flexDirection: 'row',
    gap: 6,
  },
  instantBookBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    backgroundColor: Colors.secondary,
    borderRadius: 4,
  },
  instantBookText: {
    color: Colors.text.light,
    fontSize: 10,
    fontWeight: '500',
  },
  featuredBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    backgroundColor: Colors.primary,
    borderRadius: 4,
  },
  featuredText: {
    color: Colors.text.light,
    fontSize: 10,
    fontWeight: '500',
  },
  favoriteButton: {
    position: 'absolute',
    top: 8,
    right: 8,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: Colors.background.secondary,
    justifyContent: 'center',
    alignItems: 'center',
  },
});