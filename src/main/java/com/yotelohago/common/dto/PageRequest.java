package com.yotelohago.common.dto;

import jakarta.ws.rs.DefaultValue;
import jakarta.ws.rs.QueryParam;

/**
 * Standard pagination request parameters
 * Follows Spring Data conventions for consistency
 */
public class PageRequest {

    @QueryParam("page")
    @DefaultValue("0")
    private int page;

    @QueryParam("size")
    @DefaultValue("20")
    private int size;

    @QueryParam("sort")
    private String sort;

    @QueryParam("direction")
    @DefaultValue("asc")
    private String direction;

    // Default constructor
    public PageRequest() {}

    // Constructor with parameters
    public PageRequest(int page, int size, String sort, String direction) {
        this.page = page;
        this.size = size;
        this.sort = sort;
        this.direction = direction;
    }

    // Getters and setters
    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = Math.max(0, page); // Ensure page is not negative
    }

    public int getSize() {
        // Always validate size when accessed to handle JAX-RS direct field setting
        return Math.min(Math.max(1, size), 100);
    }

    public void setSize(int size) {
        // Limit page size to prevent performance issues
        this.size = Math.min(Math.max(1, size), 100);
    }

    public String getSort() {
        return sort;
    }

    public void setSort(String sort) {
        this.sort = sort;
    }

    public String getDirection() {
        return direction;
    }

    public void setDirection(String direction) {
        // Validate direction
        if ("desc".equalsIgnoreCase(direction)) {
            this.direction = "desc";
        } else {
            this.direction = "asc";
        }
    }

    // Utility methods
    public int getOffset() {
        return page * size;
    }

    public boolean isAscending() {
        return "asc".equalsIgnoreCase(direction);
    }

    public boolean isDescending() {
        return "desc".equalsIgnoreCase(direction);
    }

    // Validation
    public boolean isValid() {
        return page >= 0 && size > 0 && size <= 100;
    }

    @Override
    public String toString() {
        return String.format("PageRequest{page=%d, size=%d, sort='%s', direction='%s'}", 
                           page, size, sort, direction);
    }
}
