import * as AuthSession from 'expo-auth-session';
import * as Crypto from 'expo-crypto';
import { Platform } from 'react-native';
import { ENV, getAuthUrl, getTokenUrl, getUserInfoUrl, getWebAppUrl } from '../../config/environment';
import { AuthTokens, KeycloakTokenResponse, KeycloakUserInfo, AuthUser } from '../../types/auth';
import crossPlatformStorage from '../storage/crossPlatformStorage';

class KeycloakServiceMobile {
  private redirectUri: string;
  private processedCodes: Set<string> = new Set();

  constructor() {
    // Configure redirect URI based on platform
    if (Platform.OS === 'web') {
      this.redirectUri = getWebAppUrl('/auth/callback');
    } else {
      this.redirectUri = AuthSession.makeRedirectUri({
        scheme: ENV.APP_SCHEME,
        path: 'auth/callback',
      });
    }

    console.log('🔧 Keycloak redirect URI:', this.redirectUri);
  }

  /**
   * Start the authentication flow - uses custom login for mobile, OAuth for web
   */
  async login(username?: string, password?: string): Promise<{ user: AuthUser; tokens: AuthTokens }> {
    try {
      console.log('🔄 Starting Keycloak login...');

      // For web platform, use OAuth redirect flow
      if (Platform.OS === 'web') {
        return this.loginWithRedirect();
      }

      // For mobile platforms, use custom username/password authentication
      // if (username && password) {
      //   return this.loginWithCredentials(username, password);
      // } else {
      //   // Fallback to popup flow if no credentials provided
      //   return this.loginWithPopup();
      // }
      console.log(' ✅ loginWithPopup...');
      return this.loginWithPopup();
    } catch (error) {
      console.error('❌ Keycloak login failed:', error);
      throw error;
    }
  }

  /**
   * Login using username and password (for mobile)
   */
  async loginWithCredentials(username: string, password: string): Promise<{ user: AuthUser; tokens: AuthTokens }> {
    try {
      console.log('🔄 Starting username/password authentication...');

      // Use Resource Owner Password Credentials Grant
      const tokenParams = new URLSearchParams({
        grant_type: 'password',
        client_id: ENV.KEYCLOAK_CLIENT_ID,
        username,
        password,
        scope: 'openid profile email',
      });

      const tokenUrl = `${ENV.KEYCLOAK_URL}/realms/${ENV.KEYCLOAK_REALM}/protocol/openid-connect/token`;

      console.log('🔄 Requesting tokens with credentials...');

      const response = await fetch(tokenUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: tokenParams.toString(),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Credential authentication failed:', response.status, errorText);

        // Parse error for better user feedback
        try {
          const errorData = JSON.parse(errorText);
          if (errorData.error === 'invalid_grant') {
            throw new Error('Invalid username or password');
          } else if (errorData.error_description) {
            throw new Error(errorData.error_description);
          }
        } catch (parseError) {
          // If we can't parse the error, use a generic message
        }

        throw new Error('Authentication failed. Please check your credentials.');
      }

      const tokenResponse: KeycloakTokenResponse = await response.json();

      console.log('✅ Tokens received successfully');

      // Convert to our token format
      const tokens: AuthTokens = {
        accessToken: tokenResponse.access_token,
        refreshToken: tokenResponse.refresh_token,
        idToken: tokenResponse.id_token,
        tokenType: tokenResponse.token_type || 'Bearer',
        expiresIn: tokenResponse.expires_in,
      };

      // Get user info
      const userInfo = await this.getUserInfo(tokens.accessToken);

      // Convert to our auth user format
      const user = this.mapKeycloakUserToAuthUser(userInfo);

      console.log('✅ Mobile credential login successful:', {
        user: { ...user, id: user.id.substring(0, 8) + '...' },
        tokenLength: tokens.accessToken.length
      });

      return { user, tokens };
    } catch (error) {
      console.error('❌ Credential authentication failed:', error);
      throw error;
    }
  }

  /**
   * Login using direct redirect (for web)
   */
  private async loginWithRedirect(): Promise<{ user: AuthUser; tokens: AuthTokens }> {
    // Generate PKCE code verifier and challenge
    const { codeVerifier, codeChallenge } = await this.generatePKCE();

    // Store code verifier using cross-platform storage
    try {
      const timestamp = Date.now().toString();
      const storageKey = `yotelohago_pkce_${timestamp}`;

      await Promise.all([
        crossPlatformStorage.setItem('yotelohago_pkce_current', codeVerifier),
        crossPlatformStorage.setItem('yotelohago_pkce_key', storageKey),
        crossPlatformStorage.setItemWithPrefix('yotelohago_pkce', storageKey, codeVerifier),
      ]);

      console.log('✅ Code verifier stored with cross-platform storage');
    } catch (error) {
      console.error('❌ Failed to store code verifier:', error);
      throw new Error('Failed to store authentication state');
    }

    // Build the authorization URL
    const authUrl = `${getAuthUrl()}?${new URLSearchParams({
      client_id: ENV.KEYCLOAK_CLIENT_ID,
      redirect_uri: this.redirectUri,
      response_type: 'code',
      scope: 'openid profile email',
      code_challenge: codeChallenge,
      code_challenge_method: 'S256',
    }).toString()}`;

    console.log('🔄 Redirecting to Keycloak for authentication...');

    // For web, redirect in the same window
    if (Platform.OS === 'web' && typeof window !== 'undefined') {
      window.location.href = authUrl;
    }

    // This will never resolve for web (redirect happens)
    throw new Error('Redirect initiated');
  }

  /**
   * Login using popup/modal (for mobile)
   */
  private async loginWithPopup(): Promise<{ user: AuthUser; tokens: AuthTokens }> {
    // Generate PKCE code verifier and challenge
    const { codeVerifier, codeChallenge } = await this.generatePKCE();

    console.log('✅ Base64 challenge generated');

    // Configure the auth request
    const authRequest = new AuthSession.AuthRequest({
      clientId: ENV.KEYCLOAK_CLIENT_ID,
      scopes: ['openid', 'profile', 'email'],
      redirectUri: this.redirectUri,
      responseType: AuthSession.ResponseType.Code,
      codeChallenge,
      codeChallengeMethod: AuthSession.CodeChallengeMethod.S256,
    });

    // Discover the auth endpoints
    const discovery = await AuthSession.fetchDiscoveryAsync(
      `${ENV.KEYCLOAK_URL}/realms/${ENV.KEYCLOAK_REALM}`
    );

    console.log('🔍 Keycloak discovery:', discovery);

    // Start the auth session
    const authResult = await authRequest.promptAsync(discovery);

    console.log('📱 Auth result:', authResult);

    if (authResult.type === 'dismiss') {
      throw new Error('Authentication was cancelled by user');
    }

    if (authResult.type === 'cancel') {
      throw new Error('Authentication was cancelled');
    }

    if (authResult.type !== 'success') {
      throw new Error(`Authentication failed: ${authResult.type}`);
    }

    if (!authResult.params.code) {
      throw new Error('No authorization code received');
    }

    // Exchange the authorization code for tokens
    const tokens = await this.exchangeCodeForTokens(
      authResult.params.code,
      codeVerifier,
      discovery.tokenEndpoint!
    );

    // Get user info
    const userInfo = await this.getUserInfo(tokens.accessToken);

    // Convert to our auth user format
    const user = this.mapKeycloakUserToAuthUser(userInfo);

    console.log('✅ Keycloak login successful:', {
      user: { ...user, id: user.id.substring(0, 8) + '...' },
      tokenLength: tokens.accessToken.length
    });

    return { user, tokens };
  }

  /**
   * Generate PKCE code verifier and challenge
   */
  private async generatePKCE(): Promise<{ codeVerifier: string; codeChallenge: string }> {
    // Generate a random code verifier (43-128 characters)
    const randomBytes = await Crypto.getRandomBytesAsync(32);
    const codeVerifier = randomBytes
      .reduce((str, byte) => str + String.fromCharCode(byte), '')
      .replace(/[^A-Za-z0-9\-._~]/g, '')
      .substring(0, 128);

    console.log('🔐 Code verifier generated:', codeVerifier.substring(0, 10) + '...');

    // Create SHA256 hash of the code verifier
    const codeVerifierHash = await Crypto.digestStringAsync(
      Crypto.CryptoDigestAlgorithm.SHA256,
      codeVerifier,
      { encoding: Crypto.CryptoEncoding.BASE64 }
    );

    console.log('🔐 PKCE generated:', {
      codeVerifier: codeVerifier.substring(0, 10) + '...',
      codeChallenge: codeVerifierHash.substring(0, 10) + '...'
    });

    // Convert to base64url format for PKCE
    const codeChallenge = codeVerifierHash
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=/g, '');

    return {
      codeVerifier,
      codeChallenge
    };
  }

  /**
   * Exchange authorization code for tokens
   */
  private async exchangeCodeForTokens(
    code: string,
    codeVerifier: string,
    tokenEndpoint: string
  ): Promise<AuthTokens> {
    const tokenParams = new URLSearchParams({
      grant_type: 'authorization_code',
      client_id: ENV.KEYCLOAK_CLIENT_ID,
      code,
      redirect_uri: this.redirectUri,
      code_verifier: codeVerifier,
    });

    console.log('🔄 Exchanging code for tokens...');
    console.log('🔍 Token exchange params:', {
      grant_type: 'authorization_code',
      client_id: ENV.KEYCLOAK_CLIENT_ID,
      code: code.substring(0, 10) + '...',
      redirect_uri: this.redirectUri,
      code_verifier: codeVerifier.substring(0, 10) + '...',
    });

    const response = await fetch(tokenEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: tokenParams.toString(),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Token exchange failed:', response.status, errorText);
      throw new Error(`Token exchange failed: ${response.status} ${errorText}`);
    }

    const tokenResponse: KeycloakTokenResponse = await response.json();

    console.log('✅ Tokens received successfully');

    return {
      accessToken: tokenResponse.access_token,
      refreshToken: tokenResponse.refresh_token,
      idToken: tokenResponse.id_token,
      tokenType: tokenResponse.token_type || 'Bearer',
      expiresIn: tokenResponse.expires_in,
    };
  }

  /**
   * Get user information from Keycloak
   */
  private async getUserInfo(accessToken: string): Promise<KeycloakUserInfo> {
    console.log('🔄 Fetching user info...');

    const response = await fetch(getUserInfoUrl(), {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ User info fetch failed:', response.status, errorText);
      throw new Error(`Failed to fetch user info: ${response.status}`);
    }

    const userInfo = await response.json();
    console.log('✅ User info fetched successfully:', {
      sub: userInfo.sub?.substring(0, 8) + '...',
      email: userInfo.email,
      username: userInfo.preferred_username
    });

    return userInfo;
  }

  /**
   * Map Keycloak user info to our AuthUser format
   */
  private mapKeycloakUserToAuthUser(userInfo: KeycloakUserInfo): AuthUser {
    // Extract roles from Keycloak token
    const realmRoles = userInfo.realm_access?.roles || [];
    const clientRoles = userInfo.resource_access?.[ENV.KEYCLOAK_CLIENT_ID]?.roles || [];
    const allRoles = [...realmRoles, ...clientRoles];

    return {
      id: userInfo.sub,
      username: userInfo.preferred_username,
      email: userInfo.email,
      firstName: userInfo.given_name,
      lastName: userInfo.family_name,
      roles: allRoles,
      isProfessional: allRoles.includes('professional'),
    };
  }

  /**
   * Refresh access token using refresh token
   */
  async refreshToken(refreshToken: string): Promise<AuthTokens> {
    try {
      console.log('🔄 Refreshing access token...');

      const tokenParams = new URLSearchParams({
        grant_type: 'refresh_token',
        client_id: ENV.KEYCLOAK_CLIENT_ID,
        refresh_token: refreshToken,
      });

      const tokenUrl = getTokenUrl();

      const response = await fetch(tokenUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: tokenParams.toString(),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Token refresh failed:', response.status, errorText);
        throw new Error(`Token refresh failed: ${response.status}`);
      }

      const tokenResponse: KeycloakTokenResponse = await response.json();

      console.log('✅ Token refreshed successfully');

      return {
        accessToken: tokenResponse.access_token,
        refreshToken: tokenResponse.refresh_token,
        idToken: tokenResponse.id_token,
        tokenType: tokenResponse.token_type || 'Bearer',
        expiresIn: tokenResponse.expires_in,
      };
    } catch (error) {
      console.error('❌ Token refresh failed:', error);
      throw error;
    }
  }

  /**
   * Handle authentication callback (for web redirects)
   */
  async handleCallback(code: string): Promise<{ user: AuthUser; tokens: AuthTokens }> {
    try {
      console.log('🔄 Handling authentication callback...');

      // Try to get stored code verifier
      let codeVerifier: string | null = null;

      try {
        // Try multiple storage keys for backward compatibility
        codeVerifier = await crossPlatformStorage.getItem('yotelohago_pkce_current');

        if (!codeVerifier) {
          const storageKey = await crossPlatformStorage.getItem('yotelohago_pkce_key');
          if (storageKey) {
            codeVerifier = await crossPlatformStorage.getItem(storageKey);
          }
        }
      } catch (error) {
        console.warn('⚠️ Could not retrieve code verifier from storage:', error);
      }

      if (!codeVerifier) {
        console.warn('⚠️ No code verifier found, attempting callback without PKCE');
        return this.handleCallbackWithoutPKCE(code);
      }

      // Exchange the authorization code for tokens
      const tokens = await this.exchangeCodeForTokens(
        code,
        codeVerifier,
        getTokenUrl()
      );

      // Get user info
      const userInfo = await this.getUserInfo(tokens.accessToken);

      // Convert to our auth user format
      const user = this.mapKeycloakUserToAuthUser(userInfo);

      console.log('✅ Callback handled successfully:', {
        user: { ...user, id: user.id.substring(0, 8) + '...' },
        tokenLength: tokens.accessToken.length
      });

      // Clean up stored code verifier
      try {
        await Promise.all([
          crossPlatformStorage.removeItem('yotelohago_pkce_current'),
          crossPlatformStorage.removeItem('yotelohago_pkce_key'),
        ]);
      } catch (error) {
        console.warn('⚠️ Failed to clean up code verifier:', error);
      }

      return { user, tokens };
    } catch (error) {
      console.error('❌ Callback handling failed:', error);
      throw error;
    }
  }

  /**
   * Handle callback without PKCE (fallback for development)
   */
  private async handleCallbackWithoutPKCE(code: string): Promise<{ user: AuthUser; tokens: AuthTokens }> {
    try {
      console.log('🔄 Handling callback without PKCE (fallback mode)...');

      // Exchange the authorization code for tokens without PKCE
      const tokens = await this.exchangeCodeForTokensWithoutPKCE(code);

      // Get user info
      const userInfo = await this.getUserInfo(tokens.accessToken);

      // Convert to our auth user format
      const user = this.mapKeycloakUserToAuthUser(userInfo);

      console.log('✅ Callback handled successfully (without PKCE):', {
        user: { ...user, id: user.id.substring(0, 8) + '...' },
        tokenLength: tokens.accessToken.length
      });

      return { user, tokens };
    } catch (error) {
      console.error('❌ Callback handling failed (without PKCE):', error);
      throw error;
    }
  }

  /**
   * Exchange authorization code for tokens without PKCE
   */
  private async exchangeCodeForTokensWithoutPKCE(code: string): Promise<AuthTokens> {
    const tokenParams = new URLSearchParams({
      grant_type: 'authorization_code',
      client_id: ENV.KEYCLOAK_CLIENT_ID,
      code,
      redirect_uri: this.redirectUri,
    });

    console.log('🔄 Exchanging code for tokens (without PKCE)...');

    const response = await fetch(getTokenUrl(), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: tokenParams.toString(),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Token exchange failed:', response.status, errorText);
      throw new Error(`Token exchange failed: ${response.status} ${errorText}`);
    }

    const tokenResponse: KeycloakTokenResponse = await response.json();

    return {
      accessToken: tokenResponse.access_token,
      refreshToken: tokenResponse.refresh_token,
      idToken: tokenResponse.id_token,
      tokenType: tokenResponse.token_type || 'Bearer',
      expiresIn: tokenResponse.expires_in,
    };
  }

  /**
   * Logout from Keycloak
   */
  async logout(refreshToken?: string): Promise<void> {
    try {
      console.log('🔄 Logging out from Keycloak...');

      if (refreshToken) {
        // Try to revoke the refresh token
        const logoutParams = new URLSearchParams({
          client_id: ENV.KEYCLOAK_CLIENT_ID,
          refresh_token: refreshToken,
        });

        const logoutUrl = `${ENV.KEYCLOAK_URL}/realms/${ENV.KEYCLOAK_REALM}/protocol/openid-connect/logout`;

        await fetch(logoutUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          body: logoutParams.toString(),
        });
      }

      // Clear any stored authentication state
      try {
        await Promise.all([
          crossPlatformStorage.removeItem('yotelohago_pkce_current'),
          crossPlatformStorage.removeItem('yotelohago_pkce_key'),
        ]);
      } catch (error) {
        console.warn('⚠️ Failed to clear stored auth state:', error);
      }

      console.log('✅ Logout completed');
    } catch (error) {
      console.error('❌ Logout failed:', error);
      // Don't throw error for logout - we'll clear local tokens anyway
    }
  }

  /**
   * Check if the current platform supports the authentication method
   */
  isSupported(): boolean {
    return true; // This service supports all platforms
  }

  /**
   * Get the redirect URI for the current platform
   */
  getRedirectUri(): string {
    return this.redirectUri;
  }
}

export default new KeycloakServiceMobile();
