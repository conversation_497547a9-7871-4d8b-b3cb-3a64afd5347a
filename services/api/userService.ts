import { apiGet, apiGetPublic, apiPut } from './client';
import { UserWithDetailsDTO } from '../../types/api';
import { UserMode } from '../../types/auth';

/**
 * User API Service
 * Handles user data fetching and management
 */
export class UserService {
  private readonly basePath = '/users';

  /**
   * Get user profile data by ID with enhanced information
   * Returns complete profile including professional data and media URLs
   */
  async getUserById(userId: string): Promise<UserWithDetailsDTO> {
    try {
      console.log('🔍 Fetching enhanced user profile from /users/{id}');

      const response = await apiGet<UserWithDetailsDTO>(`${this.basePath}/${userId}`);

      console.log('✅ Enhanced user profile fetched successfully:', {
        id: response.id,
        username: response.username,
        isProfessional: response.isProfessional,
        hasClientPhoto: !!response.clientProfilePhotoUrl,
        hasProfessionalPhoto: !!response.professionalProfilePhotoUrl,
        professionalData: response.isProfessional ? {
          bio: !!response.bio,
          city: !!response.city,
          avgRating: response.avgRating,
          available: response.available
        } : null
      });

      return response;
    } catch (error) {
      console.error('❌ Failed to fetch user profile:', error);
      throw new Error('Failed to fetch user profile. Please try again.');
    }
  }

  /**
   * Get current user profile data with enhanced information
   * Returns complete profile including professional data and media URLs
   * @deprecated Use getUserById with the current user's internal ID instead
   */
  async getCurrentUser(): Promise<UserWithDetailsDTO> {
    throw new Error('getCurrentUser is deprecated. Use getUserById with the current user\'s internal ID instead.');
  }

  /**
   * Get profile photo URL for current user based on mode
   * Uses the enhanced UserWithDetailsDTO data instead of separate Media API calls
   */
  getProfilePhotoUrl(userProfile: UserWithDetailsDTO, userMode: UserMode): string | null {
    if (!userProfile) return null;

    const photoUrl = userMode === UserMode.PROFESSIONAL
      ? userProfile.professionalProfilePhotoUrl
      : userProfile.clientProfilePhotoUrl;

    return photoUrl || null;
  }

  /**
   * Update user profile by ID
   */
  async updateUserById(userId: string, userData: Partial<UserWithDetailsDTO>): Promise<UserWithDetailsDTO> {
    try {
      console.log('🔄 Updating user profile...');

      const response = await apiPut<UserWithDetailsDTO>(`${this.basePath}/${userId}`, userData);

      console.log('✅ User profile updated successfully');
      return response;
    } catch (error) {
      console.error('❌ Failed to update user profile:', error);
      throw new Error('Failed to update profile. Please try again.');
    }
  }

  /**
   * Update user profile
   * @deprecated Use updateUserById with the current user's internal ID instead
   */
  async updateProfile(userData: Partial<UserWithDetailsDTO>): Promise<UserWithDetailsDTO> {
    throw new Error('updateProfile is deprecated. Use updateUserById with the current user\'s internal ID instead.');
  }
}

// Export singleton instance
export const userService = new UserService();
export default userService;
