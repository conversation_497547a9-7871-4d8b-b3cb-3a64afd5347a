import keycloakService from './keycloakService';
import dataTransformService from './dataTransformService';

class ApiService {
  private getBaseUrl(): string {
    return keycloakService.getApiBaseUrl();
  }

  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const url = `${this.getBaseUrl()}${endpoint}`;
    const headers = {
      'Content-Type': 'application/json',
      ...keycloakService.getAuthHeaders(),
      ...options.headers,
    };

    const response = await fetch(url, {
      ...options,
      headers,
    });

    if (!response.ok) {
      throw new Error(`API request failed: ${response.status} ${response.statusText}`);
    }

    return response.json();
  }

  // Users API - Admin only endpoints
  async getUsers() {
    // Get all users (admin endpoint)
    const response = await this.request('/users');
    return dataTransformService.transformUsers(response);
  }

  async getUserById(id: string) {
    const response = await this.request(`/users/internal/${id}`);
    return dataTransformService.transformUser(response);
  }

  // Professionals API - Now uses User domain with professional filtering
  async getProfessionals() {
    try {
      // Use the User domain with professional filtering for better performance and consistency
      const response = await this.request('/users?isProfessional=true&page=0&size=1000');
      console.log('Professionals API response (from User domain):', response);

      // Transform the paginated user response to professional format
      return dataTransformService.transformUsersToProfessionals(response);
    } catch (error) {
      console.error('Error fetching professionals:', error);
      throw error;
    }
  }

  async getProfessionalById(id: string) {
    const response = await this.request(`/professionals/${id}`);
    return dataTransformService.transformProfessional(response);
  }

  async updateProfessional(id: string, data: any) {
    const response = await this.request(`/professionals`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
    return dataTransformService.transformProfessional(response);
  }

  async deleteProfessional(id: string) {
    return this.request(`/professionals/${id}`, {
      method: 'DELETE',
    });
  }

  // Services API
  async getServices() {
    try {
      // Get services with professional details for admin panel
      const response = await this.request('/services?withProfessional=true');
      console.log('Services API response:', response);

      // Handle both paginated and direct array responses
      const servicesArray = response.content || response || [];
      return dataTransformService.transformServices(Array.isArray(servicesArray) ? servicesArray : []);
    } catch (error) {
      console.error('Error fetching services:', error);
      throw error;
    }
  }

  async getServiceById(id: string) {
    const response = await this.request(`/services/${id}`);
    return dataTransformService.transformService(response);
  }

  async createService(data: any) {
    const response = await this.request('/services', {
      method: 'POST',
      body: JSON.stringify(data),
    });
    return dataTransformService.transformService(response);
  }

  async updateService(id: string, data: any) {
    const response = await this.request(`/services/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
    return dataTransformService.transformService(response);
  }

  async deleteService(id: string) {
    return this.request(`/services/${id}`, {
      method: 'DELETE',
    });
  }

  // Service Categories API
  async getServiceCategories() {
    const response = await this.request('/service-categories');
    return dataTransformService.transformServiceCategories(response);
  }

  async createServiceCategory(data: any) {
    const response = await this.request('/service-categories', {
      method: 'POST',
      body: JSON.stringify(data),
    });
    return dataTransformService.transformServiceCategory(response);
  }

  async updateServiceCategory(id: string, data: any) {
    const response = await this.request(`/service-categories/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
    return dataTransformService.transformServiceCategory(response);
  }

  async deleteServiceCategory(id: string) {
    return this.request(`/service-categories/${id}`, {
      method: 'DELETE',
    });
  }

  // Bookings API
  async getBookings() {
    try {
      // Get bookings (try with and without details parameter)
      let response;
      try {
        response = await this.request('/bookings?withDetails=true');
      } catch (error) {
        console.warn('Failed to get bookings with details, trying without:', error);
        response = await this.request('/bookings');
      }

      console.log('Bookings API response:', response);

      // Handle both paginated and direct array responses
      const bookingsArray = response.content || response || [];
      return dataTransformService.transformBookings(Array.isArray(bookingsArray) ? bookingsArray : []);
    } catch (error) {
      console.error('Error fetching bookings:', error);
      throw error;
    }
  }

  async getBookingById(id: string) {
    const response = await this.request(`/bookings/${id}`);
    return dataTransformService.transformBooking(response);
  }

  async updateBooking(id: string, data: any) {
    const response = await this.request(`/bookings/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
    return dataTransformService.transformBooking(response);
  }

  // Reviews API
  async getReviews() {
    const response = await this.request('/reviews');
    return dataTransformService.transformReviews(response);
  }

  // Messages API
  async getConversations() {
    const response = await this.request('/messaging/conversations');
    console.log('📡 Conversations API response:', response);

    // Handle paginated response - admin gets conversation summaries
    const conversationsArray = response.content || response || [];
    console.log('📡 Conversations array:', conversationsArray);

    return dataTransformService.transformConversations(Array.isArray(conversationsArray) ? conversationsArray : []);
  }

  async getConversationMessages(bookingId: string) {
    const response = await this.request(`/messaging/conversations/${bookingId}/messages`);
    console.log('📡 Conversation messages API response:', response);

    // Handle paginated response - get individual messages for specific conversation
    const messagesArray = response.content || response || [];
    console.log('📡 Messages array for booking:', bookingId, messagesArray);

    return dataTransformService.transformMessagesWithDetails(Array.isArray(messagesArray) ? messagesArray : []);
  }

  // Dashboard Stats API
  async getDashboardStats() {
    // This might need to be a custom endpoint for admin dashboard
    return this.request('/admin/dashboard-stats');
  }
}

// Export singleton instance
const apiService = new ApiService();
export default apiService;
