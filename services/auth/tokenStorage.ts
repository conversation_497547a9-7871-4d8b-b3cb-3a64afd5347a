import * as SecureStore from 'expo-secure-store';
import { Platform } from 'react-native';
import { AuthTokens, TokenStorageKeys } from '../../types/auth';

// Secure storage wrapper that works across platforms
class TokenStorage {
  private isWeb = Platform.OS === 'web';

  // Store a value securely
  private async setItem(key: string, value: string): Promise<void> {
    try {
      if (this.isWeb) {
        // For web, use localStorage (less secure but functional for development)
        localStorage.setItem(key, value);
      } else {
        // For mobile, use SecureStore
        await SecureStore.setItemAsync(key, value);
      }
    } catch (error) {
      console.error(`Failed to store ${key}:`, error);
      throw new Error(`Failed to store ${key}`);
    }
  }

  // Retrieve a value securely
  private async getItem(key: string): Promise<string | null> {
    try {
      if (this.isWeb) {
        return localStorage.getItem(key);
      } else {
        return await SecureStore.getItemAsync(key);
      }
    } catch (error) {
      console.error(`Failed to retrieve ${key}:`, error);
      return null;
    }
  }

  // Remove a value securely
  private async removeItem(key: string): Promise<void> {
    try {
      if (this.isWeb) {
        localStorage.removeItem(key);
      } else {
        await SecureStore.deleteItemAsync(key);
      }
    } catch (error) {
      console.error(`Failed to remove ${key}:`, error);
    }
  }

  // Store authentication tokens
  async storeTokens(tokens: AuthTokens): Promise<void> {
    try {
      console.log('🔄 Storing tokens...', {
        accessTokenLength: tokens.accessToken.length,
        refreshTokenLength: tokens.refreshToken.length,
        expiresIn: tokens.expiresIn
      });

      await Promise.all([
        this.setItem(TokenStorageKeys.ACCESS_TOKEN, tokens.accessToken),
        this.setItem(TokenStorageKeys.REFRESH_TOKEN, tokens.refreshToken),
        this.setItem(TokenStorageKeys.TOKEN_EXPIRY,
          (Date.now() + tokens.expiresIn * 1000).toString()
        ),
        tokens.idToken ? this.setItem(TokenStorageKeys.ID_TOKEN, tokens.idToken) : Promise.resolve(),
      ]);
      console.log('✅ Tokens stored successfully');
    } catch (error) {
      console.error('❌ Failed to store tokens:', error);
      throw error;
    }
  }

  // Retrieve authentication tokens
  async getTokens(): Promise<AuthTokens | null> {
    try {
      const [accessToken, refreshToken, idToken, expiryStr] = await Promise.all([
        this.getItem(TokenStorageKeys.ACCESS_TOKEN),
        this.getItem(TokenStorageKeys.REFRESH_TOKEN),
        this.getItem(TokenStorageKeys.ID_TOKEN),
        this.getItem(TokenStorageKeys.TOKEN_EXPIRY),
      ]);

      if (!accessToken || !refreshToken) {
        return null;
      }

      const expiry = expiryStr ? parseInt(expiryStr, 10) : 0;
      const expiresIn = Math.max(0, Math.floor((expiry - Date.now()) / 1000));

      return {
        accessToken,
        refreshToken,
        idToken: idToken || undefined,
        tokenType: 'Bearer',
        expiresIn,
      };
    } catch (error) {
      console.error('❌ Failed to retrieve tokens:', error);
      return null;
    }
  }

  // Get access token only
  async getAccessToken(): Promise<string | null> {
    return this.getItem(TokenStorageKeys.ACCESS_TOKEN);
  }

  // Get refresh token only
  async getRefreshToken(): Promise<string | null> {
    return this.getItem(TokenStorageKeys.REFRESH_TOKEN);
  }

  // Check if tokens are expired
  async isTokenExpired(): Promise<boolean> {
    try {
      const expiryStr = await this.getItem(TokenStorageKeys.TOKEN_EXPIRY);
      if (!expiryStr) return true;

      const expiry = parseInt(expiryStr, 10);
      const now = Date.now();
      const bufferTime = 1 * 60 * 1000; // 1 minute buffer (reduced from 5 minutes)

      const isExpired = now >= (expiry - bufferTime);

      // Debug logging
      console.log('🔍 Token expiry check:', {
        now: new Date(now).toISOString(),
        expiry: new Date(expiry).toISOString(),
        bufferTime: bufferTime / 1000 + 's',
        timeLeft: Math.max(0, Math.floor((expiry - now) / 1000)) + 's',
        isExpired
      });

      return isExpired;
    } catch (error) {
      console.error('❌ Failed to check token expiry:', error);
      return true;
    }
  }

  // Store user info
  async storeUserInfo(userInfo: any): Promise<void> {
    try {
      await this.setItem(TokenStorageKeys.USER_INFO, JSON.stringify(userInfo));
    } catch (error) {
      console.error('❌ Failed to store user info:', error);
      throw error;
    }
  }

  // Get user info
  async getUserInfo(): Promise<any | null> {
    try {
      const userInfoStr = await this.getItem(TokenStorageKeys.USER_INFO);
      return userInfoStr ? JSON.parse(userInfoStr) : null;
    } catch (error) {
      console.error('❌ Failed to retrieve user info:', error);
      return null;
    }
  }

  // Store user mode
  async storeUserMode(mode: string): Promise<void> {
    try {
      await this.setItem(TokenStorageKeys.USER_MODE, mode);
      console.log('✅ User mode stored:', mode);
    } catch (error) {
      console.error('❌ Failed to store user mode:', error);
      throw error;
    }
  }

  // Get user mode
  async getUserMode(): Promise<string | null> {
    try {
      return await this.getItem(TokenStorageKeys.USER_MODE);
    } catch (error) {
      console.error('❌ Failed to retrieve user mode:', error);
      return null;
    }
  }

  // Clear all stored data
  async clearAll(): Promise<void> {
    try {
      await Promise.all([
        this.removeItem(TokenStorageKeys.ACCESS_TOKEN),
        this.removeItem(TokenStorageKeys.REFRESH_TOKEN),
        this.removeItem(TokenStorageKeys.ID_TOKEN),
        this.removeItem(TokenStorageKeys.TOKEN_EXPIRY),
        this.removeItem(TokenStorageKeys.USER_INFO),
        this.removeItem(TokenStorageKeys.USER_MODE),
      ]);
      console.log('✅ All tokens cleared');
    } catch (error) {
      console.error('❌ Failed to clear tokens:', error);
    }
  }

  // Check if user is logged in (has valid tokens)
  async hasValidSession(): Promise<boolean> {
    const tokens = await this.getTokens();
    if (!tokens) return false;

    const isExpired = await this.isTokenExpired();
    return !isExpired;
  }
}

// Export singleton instance
export const tokenStorage = new TokenStorage();
export default tokenStorage;
