package com.yotelohago.service.api;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.UUID;

public class ServiceDTO {

    public UUID id;
    public UUID professionalUserId;
    public String title;
    public String description;
    public BigDecimal price;

    public UUID categoryId;
    public String categoryName;
    public String status;
    public Instant createdAt;
    public Instant updatedAt;

    public ServiceDTO() {}

    public ServiceDTO(UUID id, UUID professionalUserId, String title, String description, BigDecimal price,
                      UUID categoryId, String categoryName, String status, Instant createdAt, Instant updatedAt) {
        this.id = id;
        this.professionalUserId = professionalUserId;
        this.title = title;
        this.description = description;
        this.price = price;
        this.categoryId = categoryId;
        this.categoryName = categoryName;
        this.status = status;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
    }
}