import React, { useState, useEffect } from 'react';
import { Typography, Button, Space, Tag, Avatar, Dropdown, Modal, Descriptions, Rate, message } from 'antd';
import {
  EyeOutlined,
  DeleteOutlined,
  MoreOutlined,
  UserOutlined,
  StarOutlined,
  ExclamationCircleOutlined,
  CalendarOutlined
} from '@ant-design/icons';
import { ColumnsType } from 'antd/es/table';
import DataTable from '../components/common/DataTable';
import { Review } from '../types';
import apiService from '../services/apiService';
import { useUrlHighlight } from '../hooks/useUrlHighlight';
import { useNavigate } from 'react-router-dom';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { confirm } = Modal;

const Reviews: React.FC = () => {
  const [reviews, setReviews] = useState<Review[]>([]);
  const [loading, setLoading] = useState(true);

  // Load reviews from API
  useEffect(() => {
    const loadReviews = async () => {
      try {
        setLoading(true);
        const reviewsData = await apiService.getReviews();
        setReviews(reviewsData);
      } catch (error) {
        console.error('Failed to load reviews:', error);
        message.error('Failed to load reviews');
      } finally {
        setLoading(false);
      }
    };

    loadReviews();
  }, []);
  const [detailsVisible, setDetailsVisible] = useState(false);
  const [selectedReview, setSelectedReview] = useState<Review | null>(null);
  const highlightId = useUrlHighlight();
  const navigate = useNavigate();

  // Get enriched reviews with user and booking data
  const reviewsWithDetails = reviews.map(review => ({
    ...review,
    // User and booking data should come from backend if needed
    client: null, // Will be handled by backend
    professional: null, // Will be handled by backend
    booking: null // Will be handled by backend
  }));

  const handleViewReview = (review: Review) => {
    setSelectedReview(review);
    setDetailsVisible(true);
  };

  const handleDeleteReview = (review: Review) => {
    confirm({
      title: 'Are you sure you want to delete this review?',
      icon: <ExclamationCircleOutlined />,
      content: 'This action cannot be undone. The review will be permanently deleted.',
      okText: 'Yes, Delete',
      okType: 'danger',
      cancelText: 'Cancel',
      onOk() {
        setReviews(reviews.filter(r => r.id !== review.id));
        message.success('Review deleted successfully');
      },
    });
  };

  const getRatingColor = (rating: number) => {
    if (rating >= 4) return 'green';
    if (rating >= 3) return 'orange';
    return 'red';
  };

  const columns: ColumnsType<Review & { client?: any; professional?: any; booking?: any }> = [
    {
      title: 'Review',
      key: 'review',
      width: 300,
      render: (_, record) => (
        <div className={highlightId === record.id ? 'bg-coral-100 p-2 rounded animate-pulse' : ''}>
          <div className="flex items-center space-x-2 mb-2">
            <Rate disabled defaultValue={record.rating} className="text-sm" />
            <Tag color={getRatingColor(record.rating)}>
              {record.rating}/5
            </Tag>
          </div>
          <div className="text-sm text-gray-600 line-clamp-2">
            {record.comment}
          </div>
        </div>
      ),
    },
    {
      title: 'Client',
      key: 'client',
      width: 180,
      render: (_, record) => (
        <div className="flex items-center space-x-2">
          <Avatar icon={<UserOutlined />} size="small" />
          <span 
            className="text-coral-500 hover:text-coral-600 cursor-pointer font-medium"
            onClick={() => navigate(`/users?highlight=${record.clientId}`)}
          >
            {record.client 
              ? `${record.client.firstName} ${record.client.lastName}`
              : 'Unknown'
            }
          </span>
        </div>
      ),
    },
    {
      title: 'Professional',
      key: 'professional',
      width: 180,
      render: (_, record) => (
        <div className="flex items-center space-x-2">
          <Avatar icon={<UserOutlined />} size="small" />
          <span 
            className="text-coral-500 hover:text-coral-600 cursor-pointer font-medium"
            onClick={() => navigate(`/professionals?highlight=${record.professionalId}`)}
          >
            {record.professional 
              ? `${record.professional.firstName} ${record.professional.lastName}`
              : 'Unknown'
            }
          </span>
        </div>
      ),
    },
    {
      title: 'Booking',
      key: 'booking',
      width: 180,
      render: (_, record) => (
        <div className="flex items-center space-x-2">
          <CalendarOutlined className="text-gray-400" />
          <span 
            className="text-coral-500 hover:text-coral-600 cursor-pointer font-medium"
            onClick={() => navigate(`/bookings?highlight=${record.bookingId}`)}
          >
            {record.booking?.title || 'Unknown Booking'}
          </span>
        </div>
      ),
    },
    {
      title: 'Rating',
      dataIndex: 'rating',
      key: 'rating',
      width: 100,
      render: (rating) => (
        <div className="flex items-center space-x-2">
          <StarOutlined className="text-yellow-500" />
          <span className="font-medium">{rating}</span>
        </div>
      ),
      sorter: (a, b) => a.rating - b.rating,
      filters: [
        { text: '5 Stars', value: 5 },
        { text: '4 Stars', value: 4 },
        { text: '3 Stars', value: 3 },
        { text: '2 Stars', value: 2 },
        { text: '1 Star', value: 1 },
      ],
      onFilter: (value, record) => record.rating === value,
    },
    {
      title: 'Date',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 120,
      render: (createdAt) => (
        <span className="text-sm">
          {dayjs(createdAt).format('MMM DD, YYYY')}
        </span>
      ),
      sorter: (a, b) => dayjs(a.createdAt).unix() - dayjs(b.createdAt).unix(),
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 120,
      render: (_, record) => (
        <Dropdown
          menu={{
            items: [
              {
                key: 'view',
                icon: <EyeOutlined />,
                label: 'View Details',
                onClick: () => handleViewReview(record),
              },
              {
                type: 'divider',
              },
              {
                key: 'delete',
                icon: <DeleteOutlined />,
                label: 'Delete Review',
                danger: true,
                onClick: () => handleDeleteReview(record),
              },
            ],
          }}
          trigger={['click']}
        >
          <Button type="text" icon={<MoreOutlined />} />
        </Dropdown>
      ),
    },
  ];

  const filterOptions = [
    {
      key: 'rating',
      label: 'Rating',
      options: [
        { value: '5', label: '5 Stars' },
        { value: '4', label: '4 Stars' },
        { value: '3', label: '3 Stars' },
        { value: '2', label: '2 Stars' },
        { value: '1', label: '1 Star' },
      ],
    },
  ];

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <Title level={2} className="m-0">Review Management</Title>
        <Text type="secondary">Monitor and moderate customer reviews</Text>
      </div>

      <DataTable
        columns={columns}
        dataSource={reviewsWithDetails}
        rowKey="id"
        loading={loading}
        filterOptions={filterOptions}
        onSearch={(value) => {
          console.log('Search:', value);
        }}
        onFilter={(filters) => {
          console.log('Filters:', filters);
        }}
        onExport={() => {
          console.log('Export reviews');
        }}
      />

      <Modal
        title="Review Details"
        open={detailsVisible}
        onCancel={() => setDetailsVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailsVisible(false)}>
            Close
          </Button>,
          <Button 
            key="delete" 
            danger 
            icon={<DeleteOutlined />}
            onClick={() => selectedReview && handleDeleteReview(selectedReview)}
          >
            Delete Review
          </Button>,
        ]}
        width={600}
      >
        {selectedReview && (
          <div className="space-y-6">
            <div className="text-center">
              <Rate disabled defaultValue={selectedReview.rating} className="text-2xl mb-2" />
              <div className="text-lg font-medium">{selectedReview.rating}/5 Stars</div>
            </div>

            <Descriptions column={1} bordered>
              <Descriptions.Item label="Client">
                <span 
                  className="text-coral-500 hover:text-coral-600 cursor-pointer font-medium"
                  onClick={() => {
                    setDetailsVisible(false);
                    navigate(`/users?highlight=${selectedReview.clientId}`);
                  }}
                >
                  {selectedReview.client 
                    ? `${selectedReview.client.firstName} ${selectedReview.client.lastName}`
                    : 'Unknown Client'
                  }
                </span>
              </Descriptions.Item>
              <Descriptions.Item label="Professional">
                <span 
                  className="text-coral-500 hover:text-coral-600 cursor-pointer font-medium"
                  onClick={() => {
                    setDetailsVisible(false);
                    navigate(`/professionals?highlight=${selectedReview.professionalId}`);
                  }}
                >
                  {selectedReview.professional 
                    ? `${selectedReview.professional.firstName} ${selectedReview.professional.lastName}`
                    : 'Unknown Professional'
                  }
                </span>
              </Descriptions.Item>
              <Descriptions.Item label="Booking">
                <span 
                  className="text-coral-500 hover:text-coral-600 cursor-pointer font-medium"
                  onClick={() => {
                    setDetailsVisible(false);
                    navigate(`/bookings?highlight=${selectedReview.bookingId}`);
                  }}
                >
                  {selectedReview.booking?.title || 'Unknown Booking'}
                </span>
              </Descriptions.Item>
              <Descriptions.Item label="Rating">
                <div className="flex items-center space-x-2">
                  <Rate disabled defaultValue={selectedReview.rating} />
                  <span>{selectedReview.rating}/5</span>
                </div>
              </Descriptions.Item>
              <Descriptions.Item label="Comment">
                <div className="whitespace-pre-wrap">
                  {selectedReview.comment}
                </div>
              </Descriptions.Item>
              <Descriptions.Item label="Date">
                {dayjs(selectedReview.createdAt).format('MMM DD, YYYY HH:mm')}
              </Descriptions.Item>
            </Descriptions>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default Reviews;