FROM registry.access.redhat.com/ubi9/ubi-minimal

USER root

ARG RHEL_USERNAME
ARG RHEL_PASSWORD

# Install system tools + sudo
RUN microdnf install -y subscription-manager && \
    subscription-manager register --username=$RHEL_USERNAME --password=$RHEL_PASSWORD && \
    microdnf install -y \
        java-21-openjdk-devel \
        vi \
        buildah \
        podman \
        skopeo \
        iproute \
        git \
        jq \
        tar \
        unzip \
        shadow-utils \
        sudo && \
    microdnf clean all && \
    subscription-manager unregister && \
    subscription-manager clean && \
    microdnf remove -y subscription-manager

# Create Jenkins user with sudo access
RUN useradd -m -d /home/<USER>/bin/bash jenkins && \
    echo 'jenkins ALL=(ALL) NOPASSWD:ALL' > /etc/sudoers.d/jenkins && \
    chmod 0440 /etc/sudoers.d/jenkins

# Set workdir and switch to Jenkins user
USER jenkins
WORKDIR /home/<USER>

# No entrypoint needed — <PERSON> will inject agent command