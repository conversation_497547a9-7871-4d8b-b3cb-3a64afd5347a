package com.yotelohago.professional.infrastructure;

import com.yotelohago.professional.api.ProfessionalWithDetailsDTO;
import com.yotelohago.professional.domain.Professional;
import com.yotelohago.professional.domain.ProfessionalRepository;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;
import org.eclipse.microprofile.config.inject.ConfigProperty;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * PostgreSQL implementation of ProfessionalRepository
 * Follows the same coding convention as Service, Booking, Media, and Favorite domains:
 * - URLs are built exclusively in SQL queries using CONCAT()
 * - DTO-returning methods include pre-built URLs to avoid N+1 problems
 */
@ApplicationScoped
public class ProfessionalRepositoryPostgres implements ProfessionalRepository {

    @PersistenceContext
    EntityManager entityManager;

    @ConfigProperty(name = "media.url-resolver")
    String mediaBaseUrl;

    @Override
    public void persist(Professional professional) {
        entityManager.persist(professional);
    }

    @Override
    public Optional<Professional> findById(UUID userId) {
        Professional professional = entityManager.find(Professional.class, userId);
        return Optional.ofNullable(professional);
    }

    @Override
    public List<Professional> findAll() {
        return entityManager.createQuery("FROM Professional ORDER BY avgRating DESC", Professional.class).getResultList();
    }

    @Override
    public Professional update(Professional professional) {
        return entityManager.merge(professional);
    }

    @Override
    public void deleteById(UUID userId) {
        Professional professional = entityManager.find(Professional.class, userId);
        if (professional != null) {
            entityManager.remove(professional);
        }
    }

    @Override
    public void flush() {
        entityManager.flush();
    }

    // =====================================================
    // DTO operations with SQL-based URL building
    // Following the same convention as Service, Booking, Media, and Favorite domains
    // =====================================================

    @Override
    public Optional<ProfessionalWithDetailsDTO> findWithDetailsByIdWithUrl(UUID userId) {
        String jpql = String.format("""
            SELECT new com.yotelohago.professional.api.ProfessionalWithDetailsDTO(
                p.user.id, p.bio, p.city, p.avgRating, p.ratingCount,
                p.available,
                (SELECT CASE
                    WHEN pm.filePath IS NOT NULL
                    THEN CONCAT('%s', pm.filePath)
                    ELSE NULL
                 END
                 FROM Media pm
                 WHERE pm.user.id = p.user.id AND pm.mediaType = 'PROFESSIONAL_PROFILE_PICTURE'
                 ORDER BY pm.createdAt DESC LIMIT 1),
                u.firstName, u.lastName, u.email, u.phone, u.username
            )
            FROM Professional p
            JOIN p.user u
            WHERE p.user.id = :userId
            """, mediaBaseUrl);

        TypedQuery<ProfessionalWithDetailsDTO> query = entityManager.createQuery(jpql, ProfessionalWithDetailsDTO.class);
        query.setParameter("userId", userId);

        List<ProfessionalWithDetailsDTO> results = query.getResultList();
        return results.isEmpty() ? Optional.empty() : Optional.of(results.get(0));
    }


}
