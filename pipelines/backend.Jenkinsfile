pipeline {
    agent { label 'jenkins-builder-v0' }

    parameters {
        string(name: 'GIT_BRANCH', defaultValue: 'main', description: 'Branch or PR source branch to build')
    }

    environment {
        GIT_CREDENTIALS_ID = 'yotelohago-read-github-token'
        GIT_REPO_URL = 'https://github.com/yotelohago/yotelohago-backend.git'
        REGISTRY_URL = 'registry.yotelohago.co'
    }

    stages {
        stage('Clone Repository') {
            steps {
                git credentialsId: "${GIT_CREDENTIALS_ID}", url: "${GIT_REPO_URL}", branch: "${params.GIT_BRANCH}"
            }
        }

        stage('Build and Push') {
            steps {
                withCredentials([usernamePassword(credentialsId: 'docker-registry', usernameVariable: 'REG_USER', passwordVariable: 'REG_PASS')]) {
                    script {
                        def safeTag = params.GIT_BRANCH.toLowerCase().replaceAll('[^a-z0-9\\-]', '-')
                        env.IMAGE_NAME = "${env.REGISTRY_URL}/backend/${safeTag}:v${env.BUILD_NUMBER}"

                        sh """
                            echo "🔍 Detecting Java 21..."
                            JAVA_HOME=\$(dirname \$(dirname \$(readlink -f \$(which java))))
                            echo "JAVA_HOME is \$JAVA_HOME"
                            export JAVA_HOME
                            export PATH=\$JAVA_HOME/bin:\$PATH

                            echo "🔧 Java version:"
                            java -version
                            echo "Using javac from: \$(which javac)"

                            echo "Maven version:"
                            ./mvnw --version

                            echo "Compiling maven project..."
                            \$JAVA_HOME/bin/java -version
                            ./mvnw clean package -Dquarkus.profile=prod -Dmaven.compiler.fork=true -Dmaven.compiler.executable=\$JAVA_HOME/bin/javac

                            echo "🔐 Logging into registry..."
                            podman login ${env.REGISTRY_URL} -u ${env.REG_USER} -p ${env.REG_PASS}

                            echo "🛠️ Building image ${env.IMAGE_NAME}"
                            podman build -f src/main/docker/Containerfile.jvm -t ${env.IMAGE_NAME} .

                            echo "📤 Pushing image ${env.IMAGE_NAME} to registry..."
                            podman push ${env.IMAGE_NAME}

                            echo "🚪 Logging out from registry..."
                            podman logout ${env.REGISTRY_URL}
                        """
                    }
                }
            }
        }
    }
}