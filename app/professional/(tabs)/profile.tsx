import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, Image, TouchableOpacity, ScrollView, StatusBar } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Colors from '../constants/Colors';
import { useRouter } from 'expo-router';
import { useAuth } from '../../../contexts/AuthContext';
import { UserMode } from '../../../types/auth';
import { UserWithDetailsDTO } from '../../../types';
import { AuthGuard } from '../../../components/auth';
import ProfilePhotoUploadModal from '../../../components/media/ProfilePhotoUploadModal';
import { mediaService } from '../../../services/api/mediaService';
import { userService } from '../../../services/api/userService';

// Default user image
const defaultUserImage = require('../../../assets/images/user.png');



interface MenuItemProps {
  icon: React.ReactNode;
  title: string;
  onPress: () => void;
}

const MenuItem: React.FC<MenuItemProps> = ({ icon, title, onPress }) => (
  <TouchableOpacity style={styles.menuItem} onPress={onPress}>
    <View style={styles.menuIcon}>{icon}</View>
    <Text style={styles.menuTitle}>{title}</Text>
    <Ionicons name="chevron-forward" size={20} color={Colors.text.secondary} />
  </TouchableOpacity>
);

export default function ProfileScreen() {
  const router = useRouter();
  const { state: authState, setUserMode } = useAuth();
  const [userProfile, setUserProfile] = useState<UserWithDetailsDTO | null>(null);
  const [profilePhotoUrl, setProfilePhotoUrl] = useState<string>(defaultUserImage);
  const [showUploadModal, setShowUploadModal] = useState(false);

  // Load user profile data on component mount
  useEffect(() => {
    loadUserProfile();
  }, [authState.currentMode]);

  const loadUserProfile = async () => {
    try {
      console.log('🔄 Loading enhanced user profile...');

      // Get current user's internal ID from auth state
      const currentUserId = authState.user?.internalId;
      if (!currentUserId) {
        console.error('❌ No internal user ID available');
        setProfilePhotoUrl(defaultUserImage);
        return;
      }

      const userData = await userService.getUserById(currentUserId);
      console.log('✅ Enhanced user profile loaded:', userData);

      setUserProfile(userData);

      // Get profile photo URL based on current mode
      const photoUrl = userService.getProfilePhotoUrl(userData, authState.currentMode);
      if (photoUrl) {
        console.log('📸 Using profile photo from enhanced data:', photoUrl);
        setProfilePhotoUrl(photoUrl);
      } else {
        console.log('📷 No profile photo found, using default image');
        setProfilePhotoUrl(defaultUserImage);
      }
    } catch (error) {
      console.error('❌ Failed to load user profile:', error);
      setProfilePhotoUrl(defaultUserImage);
    }
  };

  const handleUploadSuccess = async (newImageUrl: string) => {
    console.log('📸 Profile photo upload successful, refreshing profile...');

    // Update the local state immediately
    setProfilePhotoUrl(newImageUrl);

    // Reload the user profile from backend to ensure consistency
    setTimeout(() => {
      loadUserProfile();
    }, 1000); // Small delay to ensure backend has processed the upload
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={Colors.background.primary} />
      <View style={styles.header}>
        <Text style={styles.title}>Profile</Text>
      </View>

      <AuthGuard
        actionType="general"
        promptTitle="Access Your Professional Profile"
        promptMessage="Log in as a professional to view and manage your profile, settings, and account information."
        tabIcon="person-outline"
      >
        <ScrollView
          style={styles.content}
          contentContainerStyle={styles.contentContainer}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.userInfoSection}>
            <View style={styles.userInfoContainer}>
              <Image source={{ uri: profilePhotoUrl }} style={styles.avatar} />
              <View style={styles.userInfo}>
                {userProfile ? (
                  <>
                    <Text style={styles.userName}>
                      {userProfile.fullName || `${userProfile.firstName || ''} ${userProfile.lastName || ''}`.trim() || 'Professional'}
                    </Text>
                    <Text style={styles.userEmail}>{userProfile.email || 'No email'}</Text>
                    {userProfile.phone && (
                      <Text style={styles.userPhone}>{userProfile.phone}</Text>
                    )}
                    {userProfile.isProfessional && userProfile.city && (
                      <Text style={styles.userCity}>{userProfile.city}</Text>
                    )}
                    {userProfile.isProfessional && userProfile.avgRating && (
                      <Text style={styles.userRating}>⭐ {userProfile.avgRating.toFixed(1)} ({userProfile.ratingCount || 0} reviews)</Text>
                    )}
                  </>
                ) : (
                  <>
                    <Text style={styles.userName}>Loading...</Text>
                    <Text style={styles.userEmail}>Loading professional data...</Text>
                  </>
                )}
              </View>
            </View>
            <TouchableOpacity
              style={styles.settingsButton}
              onPress={() => setShowUploadModal(true)}
            >
              <Ionicons name="settings" size={24} color={Colors.text.primary} />
            </TouchableOpacity>
          </View>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Account</Text>
          <View style={styles.menuGroup}>
            <MenuItem
              icon={<Ionicons name="heart" size={20} color={Colors.primary} />}
              title="Saved Services"
              onPress={() => console.log('Saved services')}
            />
            <MenuItem
              icon={<Ionicons name="star" size={20} color={Colors.warning} />}
              title="Reviews & Ratings"
              onPress={() => console.log('Reviews')}
            />
            <MenuItem
              icon={<Ionicons name="calendar" size={20} color={Colors.secondary} />}
              title="Service History"
              onPress={() => console.log('Service history')}
            />
            <MenuItem
              icon={<Ionicons name="chatbubble" size={20} color={Colors.primary} />}
              title="Messages"
              onPress={() => console.log('Messages')}
            />
          </View>
        </View>
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Preferences</Text>
          <View style={styles.menuGroup}>
            <MenuItem
              icon={<Ionicons name="shield" size={20} color={Colors.success} />}
              title="Personal Information"
              onPress={() => console.log('Personal info')}
            />
            <MenuItem
              icon={<Ionicons name="wallet" size={20} color={Colors.text.primary} />}
              title="Payment Methods"
              onPress={() => console.log('Payment methods')}
            />
          </View>
        </View>
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Support</Text>
          <View style={styles.menuGroup}>
            <MenuItem
              icon={<Ionicons name="help-circle" size={20} color={Colors.primary} />}
              title="Help Center"
              onPress={() => console.log('Help center')}
            />
            <MenuItem
              icon={<Ionicons name="shield-checkmark" size={20} color={Colors.primary} />}
              title="Privacy Policy"
              onPress={() => console.log('Privacy policy')}
            />
            <MenuItem
              icon={<Ionicons name="document-text" size={20} color={Colors.primary} />}
              title="Terms of Service"
              onPress={() => console.log('Terms of service')}
            />
            <MenuItem
              icon={<Ionicons name="person" size={20} color={Colors.primary} />}
              title="Switch to Client Mode"
              onPress={async () => {
                await setUserMode(UserMode.CLIENT);
                router.push('/client/(tabs)');
              }}
            />
          </View>
        </View>
        
        <TouchableOpacity style={styles.logoutButton}>
          <Ionicons name="log-out" size={24} color={Colors.error} />
          <Text style={styles.logoutText}>Log out</Text>
        </TouchableOpacity>

        <Text style={styles.versionText}>Version 1.0.0</Text>
        </ScrollView>
      </AuthGuard>

      <ProfilePhotoUploadModal
        visible={showUploadModal}
        onClose={() => setShowUploadModal(false)}
        onUploadSuccess={handleUploadSuccess}
        currentImageUrl={profilePhotoUrl}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.primary,
  },
  header: {
    backgroundColor: Colors.background.primary,
    paddingTop: 60,
    paddingBottom: 20,
    paddingHorizontal: 16,
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: Colors.text.primary,
    textAlign: 'center',
  },
  userInfoSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 24,
    paddingVertical: 16,
    paddingHorizontal: 16,
    backgroundColor: Colors.background.secondary,
    borderRadius: 12,
  },
  userInfoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    width: 64,
    height: 64,
    borderRadius: 32,
    borderWidth: 2,
    borderColor: Colors.primary,
  },
  userInfo: {
    marginLeft: 16,
  },
  userName: {
    fontSize: 20,
    fontWeight: '600',
    color: Colors.text.primary,
    marginBottom: 4,
  },
  userEmail: {
    fontSize: 14,
    color: Colors.text.secondary,
  },
  userPhone: {
    fontSize: 14,
    color: Colors.text.secondary,
    marginTop: 2,
  },
  userCity: {
    fontSize: 14,
    color: Colors.text.secondary,
    marginTop: 2,
  },
  userRating: {
    fontSize: 14,
    color: Colors.warning,
    marginTop: 2,
    fontWeight: '500',
  },
  settingsButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.background.secondary,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.border,
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text.primary,
    marginBottom: 12,
  },
  menuGroup: {
    backgroundColor: Colors.background.secondary,
    borderRadius: 12,
    overflow: 'hidden',
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  menuIcon: {
    marginRight: 16,
    width: 24,
    alignItems: 'center',
  },
  menuTitle: {
    flex: 1,
    fontSize: 16,
    color: Colors.text.primary,
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    backgroundColor: Colors.background.secondary,
    borderRadius: 12,
    marginVertical: 16,
  },
  logoutText: {
    marginLeft: 8,
    fontSize: 16,
    fontWeight: '500',
    color: Colors.error,
  },
  versionText: {
    textAlign: 'center',
    fontSize: 14,
    color: Colors.text.secondary,
    marginBottom: 16,
  },
});