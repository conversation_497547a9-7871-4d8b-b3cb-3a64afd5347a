from flask import Flask, request, jsonify, render_template

app = Flask(__name__)

# Shared variable
number = 0


@app.route('/', methods=['POST'])
def home():
    global number
    data = request.get_json()

    if not data or "num" not in data:
        return jsonify({"error": "Invalid request, 'num' required"}), 400

    try:
        number = int(data["num"])
        return jsonify({"message": "Value updated", "new_value": number})
    except ValueError:
        return jsonify({"error": "Invalid number format"}), 400


@app.route('/', methods=['GET'])
def get_value():
    return str(number)


if __name__ == '__main__':
    app.run(debug=True, port=80, host="0.0.0.0")  # App will be Expose in Port 80
