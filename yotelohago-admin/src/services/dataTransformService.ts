import { User, Professional, Service, Booking, ServiceCategory, Review, Message } from '../types';

// Paginated response interface
interface PageResponse<T> {
  content: T[];
  totalElements: number;
  totalPages: number;
  size: number;
  number: number;
  first: boolean;
  last: boolean;
  empty: boolean;
  numberOfElements: number;
}

// Backend DTO interfaces (based on the OpenAPI specification)
interface BackendUserWithDetailsDTO {
  // Basic user data
  id: string;
  username: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  isProfessional: boolean;
  createdAt: string;
  updatedAt: string;

  // Professional data (only populated if isProfessional = true)
  bio?: string;
  city?: string;
  avgRating?: number;
  ratingCount?: number;
  available?: boolean;

  // Profile photo URLs (derived from Media entities)
  clientProfilePhotoUrl?: string;
  professionalProfilePhotoUrl?: string;

  // Computed convenience field
  fullName?: string;
}

// Legacy interface for backward compatibility
interface BackendUserDTO {
  id: string;
  username: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  isProfessional: boolean;
  createdAt: string;
  updatedAt: string;
}

interface BackendProfessionalDTO {
  userId: string;
  bio: string;
  city: string;
  avgRating: number;
  ratingCount: number;
  profileImageUrl: string;
  available: boolean;
  serviceIds: string[];
}

interface BackendProfessionalProfileDTO {
  userId: string;
  bio: string;
  city: string;
  avgRating: number;
  ratingCount: number;
  profileImageUrl: string;
  available: boolean;
  serviceIds: string[];
  firstName: string;
  lastName: string;
  fullName: string;
  email: string;
  phone: string;
  username: string;
}

interface BackendServiceDTO {
  id: string;
  professionalUserId: string;
  title: string;
  description: string;
  price: number;
  categoryId: string;
  categoryName: string;
  status: string;
  createdAt: string;
  updatedAt: string;
}

interface BackendBookingDTO {
  id: number;
  clientId: string;
  professionalId: string;
  serviceId: string;
  title: string;
  description: string;
  requestedDate: string;
  scheduledAt: string;
  price: number;
  status: string;
  createdAt: string;
  updatedAt: string;
}

interface BackendServiceCategoryDTO {
  id: string;
  name: string;
  description: string;
  iconUrl: string;
}

interface BackendReviewDTO {
  id: number;
  bookingId: number;
  professionalId: string;
  clientId: string;
  rating: number;
  comment: string;
  createdAt: string;
}

interface BackendMessageDTO {
  id: number;
  bookingId: number;
  senderId: string;
  receiverId: string;
  content: string;
  sentAt: string;
  isRead: boolean;
}

// New interface for admin messages with full details
interface BackendMessageWithDetailsDTO {
  id: number;
  bookingId: number;
  senderId: string;
  receiverId: string;
  content: string;
  sentAt: string;
  isRead: boolean;
  sender: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    isProfessional: boolean;
  };
  receiver: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    isProfessional: boolean;
  };
  booking: {
    id: string;
    title: string;
    description: string;
    status: string;
  };
}

// Interface for conversation summaries
interface BackendConversationSummaryDTO {
  bookingId: number;
  bookingTitle: string;
  bookingDescription: string;
  lastMessageContent: string;
  lastMessageTime: string;
  lastMessageFromCurrentUser: boolean;
  unreadCount: number;
  // Participant details - always populated for both client and professional
  clientId: string;
  clientName: string;
  clientEmail: string;
  professionalId: string;
  professionalName: string;
  professionalEmail: string;
}

class DataTransformService {
  
  // Transform backend user to admin panel user
  transformUser(user: BackendUserWithDetailsDTO | BackendUserDTO): User {
    return {
      id: user.id,
      username: user.username,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      phone: user.phone,
      isProfessional: user.isProfessional,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    };
  }

  // Transform backend professional to admin panel professional
  transformProfessional(professional: BackendProfessionalDTO): Professional {
    return {
      userId: professional.userId,
      available: professional.available,
      avgRating: professional.avgRating,
      ratingCount: professional.ratingCount,
      city: professional.city,
      bio: professional.bio,
      profileImageUrl: professional.profileImageUrl,
      status: 'approved', // Assuming approved if in the system
      verificationStatus: 'verified', // Assuming verified
    };
  }

  // Transform backend professional profile (with user data) to admin panel professional
  transformProfessionalProfile(profile: BackendProfessionalProfileDTO): Professional & { user: User } {
    return {
      userId: profile.userId,
      available: profile.available,
      avgRating: profile.avgRating,
      ratingCount: profile.ratingCount,
      city: profile.city,
      bio: profile.bio,
      profileImageUrl: profile.profileImageUrl,
      status: 'approved', // Assuming approved if in the system
      verificationStatus: 'verified', // Assuming verified
      user: {
        id: profile.userId,
        username: profile.username,
        email: profile.email,
        firstName: profile.firstName,
        lastName: profile.lastName,
        phone: profile.phone,
        isProfessional: true,
        createdAt: new Date().toISOString(), // Not available
        updatedAt: new Date().toISOString(), // Not available
      }
    };
  }

  // Transform backend service to admin panel service
  transformService(service: BackendServiceDTO): Service {
    return {
      id: service.id,
      title: service.title,
      description: service.description,
      price: service.price,
      status: service.status as 'active' | 'inactive',
      categoryId: service.categoryId,
      professionalUserId: service.professionalUserId,
      createdAt: service.createdAt,
      updatedAt: service.updatedAt,
    };
  }

  // Transform backend booking to admin panel booking
  transformBooking(booking: BackendBookingDTO): Booking {
    return {
      id: booking.id.toString(),
      title: booking.title,
      description: booking.description,
      price: booking.price,
      status: booking.status as 'OPEN' | 'ACCEPTED' | 'COMPLETED' | 'CANCELLED',
      clientId: booking.clientId,
      professionalId: booking.professionalId,
      serviceId: booking.serviceId,
      requestedDate: booking.requestedDate,
      scheduledAt: booking.scheduledAt,
      createdAt: booking.createdAt,
      updatedAt: booking.updatedAt,
    };
  }

  // Transform backend service category to admin panel service category
  transformServiceCategory(category: BackendServiceCategoryDTO): ServiceCategory {
    return {
      id: category.id,
      name: category.name,
      description: category.description,
      iconUrl: category.iconUrl,
      createdAt: new Date().toISOString(), // Not available in backend DTO
      updatedAt: new Date().toISOString(), // Not available in backend DTO
    };
  }

  // Transform backend review to admin panel review
  transformReview(review: BackendReviewDTO): Review {
    return {
      id: review.id.toString(),
      bookingId: review.bookingId.toString(),
      professionalId: review.professionalId,
      clientId: review.clientId,
      rating: review.rating,
      comment: review.comment,
      createdAt: review.createdAt,
    };
  }

  // Transform backend message to admin panel message
  transformMessage(message: BackendMessageDTO): Message {
    return {
      id: message.id.toString(),
      content: message.content,
      senderId: message.senderId,
      receiverId: message.receiverId,
      bookingId: message.bookingId.toString(),
      isRead: message.isRead,
      sentAt: message.sentAt,
    };
  }

  // Transform backend message with details to admin panel message (for admin panel)
  transformMessageWithDetails(message: BackendMessageWithDetailsDTO): Message {
    console.log('🔄 Transforming message with details:', message);
    console.log('📧 Sender data:', message.sender);
    console.log('📧 Receiver data:', message.receiver);
    console.log('📧 Booking data:', message.booking);

    return {
      id: message.id.toString(),
      content: message.content,
      senderId: message.senderId,
      receiverId: message.receiverId,
      bookingId: message.bookingId.toString(),
      isRead: message.isRead,
      sentAt: message.sentAt,
      // Include nested objects for admin panel display
      sender: {
        id: message.sender.id,
        firstName: message.sender.firstName,
        lastName: message.sender.lastName,
        email: message.sender.email,
        isProfessional: message.sender.isProfessional,
        username: message.sender.email, // Use email as username fallback
        phone: '', // Not available in message context
        createdAt: new Date().toISOString(), // Not available
        updatedAt: new Date().toISOString(), // Not available
      },
      receiver: {
        id: message.receiver.id,
        firstName: message.receiver.firstName,
        lastName: message.receiver.lastName,
        email: message.receiver.email,
        isProfessional: message.receiver.isProfessional,
        username: message.receiver.email, // Use email as username fallback
        phone: '', // Not available in message context
        createdAt: new Date().toISOString(), // Not available
        updatedAt: new Date().toISOString(), // Not available
      },
      booking: {
        id: message.booking.id,
        title: message.booking.title,
        description: message.booking.description,
        status: message.booking.status as 'OPEN' | 'ACCEPTED' | 'COMPLETED' | 'CANCELLED',
        price: 0, // Not available in message context
        clientId: message.receiver.isProfessional ? message.receiver.id : message.sender.id,
        professionalId: message.sender.isProfessional ? message.sender.id : message.receiver.id,
        serviceId: '', // Not available in message context
        requestedDate: new Date().toISOString(), // Not available
        createdAt: new Date().toISOString(), // Not available
        updatedAt: new Date().toISOString(), // Not available
      },
    };
  }

  // Transform arrays with error handling
  transformUsers(response: PageResponse<BackendUserWithDetailsDTO> | BackendUserDTO[]): User[] {
    // Handle paginated response (new format)
    if (response && typeof response === 'object' && 'content' in response) {
      if (!Array.isArray(response.content)) {
        console.warn('Expected users content array, got:', response.content);
        return [];
      }
      return response.content.map(u => this.transformUser(u));
    }

    // Handle legacy array format
    if (!Array.isArray(response)) {
      console.warn('Expected users array or paginated response, got:', response);
      return [];
    }
    return response.map(u => this.transformUser(u));
  }

  // Transform paginated users to professionals format for admin panel
  transformUsersToProfessionals(response: PageResponse<BackendUserWithDetailsDTO>): Professional[] {
    if (!response || !response.content || !Array.isArray(response.content)) {
      console.warn('Expected paginated users response, got:', response);
      return [];
    }

    return response.content
      .filter(user => user.isProfessional) // Extra safety filter
      .map(user => ({
        userId: user.id,
        user: this.transformUser(user),
        available: user.available || false,
        avgRating: user.avgRating || 0,
        ratingCount: user.ratingCount || 0,
        city: user.city || '',
        bio: user.bio || '',
        profileImageUrl: user.professionalProfilePhotoUrl,
        status: 'approved' as const, // Default status since we don't have this in UserWithDetailsDTO
        verificationStatus: 'verified' as const, // Default status
      }));
  }

  transformProfessionals(professionals: BackendProfessionalDTO[]): Professional[] {
    if (!Array.isArray(professionals)) {
      console.warn('Expected professionals array, got:', professionals);
      return [];
    }
    return professionals.map(p => this.transformProfessional(p));
  }

  transformProfessionalProfiles(profiles: BackendProfessionalProfileDTO[]): (Professional & { user: User })[] {
    if (!Array.isArray(profiles)) {
      console.warn('Expected professional profiles array, got:', profiles);
      return [];
    }
    return profiles.map(p => this.transformProfessionalProfile(p));
  }

  transformServices(services: BackendServiceDTO[]): Service[] {
    if (!Array.isArray(services)) {
      console.warn('Expected services array, got:', services);
      return [];
    }
    return services.map(s => this.transformService(s));
  }

  transformBookings(bookings: BackendBookingDTO[]): Booking[] {
    return bookings.map(b => this.transformBooking(b));
  }

  transformServiceCategories(categories: BackendServiceCategoryDTO[]): ServiceCategory[] {
    return categories.map(c => this.transformServiceCategory(c));
  }

  transformReviews(reviews: BackendReviewDTO[]): Review[] {
    return reviews.map(r => this.transformReview(r));
  }

  transformMessages(messages: BackendMessageDTO[]): Message[] {
    return messages.map(m => this.transformMessage(m));
  }

  transformMessagesWithDetails(messages: BackendMessageWithDetailsDTO[]): Message[] {
    if (!Array.isArray(messages)) {
      console.warn('Expected messages array, got:', messages);
      return [];
    }
    return messages.map(m => this.transformMessageWithDetails(m));
  }

  // Transform conversation summaries for admin panel
  transformConversation(conversation: BackendConversationSummaryDTO): any {
    console.log('🔄 Transforming conversation:', conversation);
    return {
      bookingId: conversation.bookingId.toString(),
      bookingTitle: conversation.bookingTitle,
      bookingDescription: conversation.bookingDescription,
      lastMessageContent: conversation.lastMessageContent,
      lastMessageTime: conversation.lastMessageTime,
      lastMessageFromCurrentUser: conversation.lastMessageFromCurrentUser,
      unreadCount: conversation.unreadCount,
      // Participant details
      clientId: conversation.clientId,
      clientName: conversation.clientName,
      clientEmail: conversation.clientEmail,
      professionalId: conversation.professionalId,
      professionalName: conversation.professionalName,
      professionalEmail: conversation.professionalEmail,
    };
  }

  transformConversations(conversations: BackendConversationSummaryDTO[]): any[] {
    if (!Array.isArray(conversations)) {
      console.warn('Expected conversations array, got:', conversations);
      return [];
    }
    return conversations.map(c => this.transformConversation(c));
  }
}

// Export singleton instance
const dataTransformService = new DataTransformService();
export default dataTransformService;
