import { apiPost, apiGet, apiDelete } from './client';
import {
  MediaDTO,
  PresignedUrlRequestDTO,
  PresignedUrlResponseDTO,
  MediaType,
  ApiError
} from '../../types/api';
import { UserMode } from '../../types/auth';

/**
 * Media API Service
 * Handles media upload, retrieval, and management operations
 */
export class MediaService {
  private readonly basePath = '/media';

  /**
   * Generate presigned URL for uploading media
   */
  async generatePresignedUrl(request: PresignedUrlRequestDTO): Promise<PresignedUrlResponseDTO> {
    try {
      console.log('🔗 Generating presigned URL for:', request.filename);

      const response = await apiPost<PresignedUrlResponseDTO>(
        `${this.basePath}/presigned-url`,
        request
      );

      console.log('✅ Presigned URL generated successfully');
      return response;
    } catch (error) {
      console.error('❌ Failed to generate presigned URL:', error);
      throw new Error('Failed to generate presigned URL. Please try again.');
    }
  }

  /**
   * Upload file to MinIO using presigned URL
   */
  async uploadToMinIO(uploadUrl: string, file: Blob | File, contentType: string): Promise<void> {
    try {
      console.log('📤 Uploading file to MinIO...');

      const response = await fetch(uploadUrl, {
        method: 'PUT',
        body: file,
        headers: {
          'Content-Type': contentType,
        },
      });

      if (!response.ok) {
        throw new Error(`Upload failed with status: ${response.status}`);
      }

      console.log('✅ File uploaded successfully to MinIO');
    } catch (error) {
      console.error('❌ Failed to upload file to MinIO:', error);
      throw new Error('Failed to upload file. Please try again.');
    }
  }

  /**
   * Get media by ID
   */
  async getMediaById(mediaId: string): Promise<MediaDTO> {
    try {
      console.log('🔍 Fetching media by ID:', mediaId);

      const response = await apiGet<MediaDTO>(`${this.basePath}/${mediaId}`);

      console.log('✅ Media fetched successfully');
      return response;
    } catch (error) {
      console.error('❌ Failed to fetch media:', error);
      throw new Error('Failed to fetch media. Please try again.');
    }
  }

  /**
   * Get all media for current user
   */
  async getUserMedia(): Promise<MediaDTO[]> {
    try {
      console.log('🔍 Fetching user media...');

      const response = await apiGet<MediaDTO[]>(`${this.basePath}/my-media`);

      console.log('✅ User media fetched successfully');
      return response;
    } catch (error) {
      console.error('❌ Failed to fetch user media:', error);
      throw new Error('Failed to fetch user media. Please try again.');
    }
  }

  /**
   * Delete media by ID
   */
  async deleteMedia(mediaId: string): Promise<void> {
    try {
      console.log('🗑️ Deleting media:', mediaId);

      await apiDelete(`${this.basePath}/${mediaId}`);

      console.log('✅ Media deleted successfully');
    } catch (error) {
      console.error('❌ Failed to delete media:', error);
      throw new Error('Failed to delete media. Please try again.');
    }
  }

  /**
   * Get profile photo for current user based on mode
   */
  async getProfilePhoto(userMode: UserMode): Promise<MediaDTO | null> {
    try {
      console.log('🔍 Fetching profile photo for mode:', userMode);

      const mediaType = userMode === UserMode.PROFESSIONAL
        ? MediaType.PROFESSIONAL_PROFILE_PICTURE
        : MediaType.CLIENT_PROFILE_PICTURE;

      const allMedia = await this.getUserMedia();
      const profilePhotos = allMedia.filter(media => media.mediaType === mediaType);

      // Return the most recent profile photo
      const profilePhoto = profilePhotos.length > 0 ? profilePhotos[0] : null;

      console.log('✅ Profile photo fetched:', profilePhoto ? 'found' : 'not found');
      return profilePhoto;
    } catch (error) {
      console.error('❌ Failed to fetch profile photo:', error);
      return null; // Don't throw error for profile photo, just return null
    }
  }

  /**
   * Complete profile photo upload flow
   */
  async uploadProfilePhoto(
    file: Blob | File,
    filename: string,
    userMode: UserMode
  ): Promise<MediaDTO> {
    try {
      console.log('📸 Starting profile photo upload flow...');

      const mediaType = userMode === UserMode.PROFESSIONAL
        ? MediaType.PROFESSIONAL_PROFILE_PICTURE
        : MediaType.CLIENT_PROFILE_PICTURE;

      // Step 1: Generate presigned URL
      const presignedUrlRequest: PresignedUrlRequestDTO = {
        filename,
        contentType: file.type || 'image/jpeg',
        fileSize: file.size,
        mediaType,
      };

      const presignedResponse = await this.generatePresignedUrl(presignedUrlRequest);

      // Step 2: Upload to MinIO
      await this.uploadToMinIO(presignedResponse.uploadUrl, file, presignedUrlRequest.contentType);

      // Step 3: Get the created media record
      const mediaRecord = await this.getMediaById(presignedResponse.mediaId);

      console.log('✅ Profile photo upload completed successfully');
      return mediaRecord;
    } catch (error) {
      console.error('❌ Profile photo upload failed:', error);
      throw error;
    }
  }

  /**
   * Construct profile photo URL for display using correct MinIO format
   */
  constructProfilePhotoUrl(userId: string, userMode: UserMode, cacheBust: boolean = true): string {
    const path = userMode === UserMode.PROFESSIONAL
      ? `users/${userId}/professional/profile.jpg`
      : `users/${userId}/client/profile.jpg`;

    let url = `https://minio.yotelohago.co/api/v1/buckets/public/objects/download?preview=true&prefix=${encodeURIComponent(path)}`;

    // Add cache busting parameter to force refresh
    if (cacheBust) {
      url += `&t=${Date.now()}`;
    }

    return url;
  }

  /**
   * Get profile photo URL for current user
   * First tries to get from backend, falls back to constructed URL
   */
  async getProfilePhotoUrl(userId: string, userMode: UserMode): Promise<string | null> {
    try {
      console.log('🔍 Getting profile photo URL for user:', userId, 'mode:', userMode);

      // Try to get from backend first
      const profilePhoto = await this.getProfilePhoto(userMode);
      console.log('📸 Backend profile photo result:', profilePhoto);

      if (profilePhoto && profilePhoto.publicUrl) {
        console.log('✅ Using backend profile photo URL:', profilePhoto.publicUrl);
        return profilePhoto.publicUrl;
      }

      // Fallback to constructed URL (this should now use the correct format)
      const constructedUrl = this.constructProfilePhotoUrl(userId, userMode);
      console.log('🔧 Using constructed profile photo URL:', constructedUrl);
      return constructedUrl;
    } catch (error) {
      console.error('❌ Failed to get profile photo URL:', error);
      return null;
    }
  }
}

// Export singleton instance
export const mediaService = new MediaService();
export default mediaService;