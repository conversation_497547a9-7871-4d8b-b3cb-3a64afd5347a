import React from 'react';
import { motion } from 'framer-motion';
import { DivideIcon as LucideIcon, Check } from 'lucide-react';
import LightningBolt from './LightningBolt';
import BrokenPipe from './BrokenPipe';
import MovingTruck from './MovingTruck';
import AnimatedLock from './AnimatedLock';

interface Service {
  id: string;
  title: string;
  icon: LucideIcon;
  color: string;
  bgColor: string;
  features: string[];
  description: string;
  animation: string;
}

interface ServicePanelProps {
  service: Service;
  isActive: boolean;
}

const ServicePanel: React.FC<ServicePanelProps> = ({ service, isActive }) => {
  const Icon = service.icon;

  const getAnimationVariant = () => {
    switch (service.animation) {
      case 'spark':
        return {
          animate: { rotate: [0, 10, -10, 0], scale: [1, 1.1, 1] },
          transition: { repeat: Infinity, duration: 2, delay: 1 }
        };
      case 'wave':
        return {
          animate: { y: [0, -10, 0], rotate: [0, 5, -5, 0] },
          transition: { repeat: Infinity, duration: 3, ease: "easeInOut" }
        };
      case 'slide':
        return {
          animate: { x: [0, -20, 0] },
          transition: { repeat: Infinity, duration: 2.5, ease: "easeInOut" }
        };
      case 'lock':
        return {
          animate: { rotate: [0, -15, 15, 0], scale: [1, 0.95, 1.05, 1] },
          transition: { repeat: Infinity, duration: 3, delay: 0.5 }
        };
      default:
        return {};
    }
  };

  return (
    <div className={`w-full h-full ${service.bgColor} flex items-center justify-center relative overflow-hidden`}>
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-20 left-20 w-32 h-32 border border-coral-400 rounded-full"></div>
        <div className="absolute bottom-32 right-32 w-48 h-48 border border-coral-400 rounded-full"></div>
        <div className="absolute top-1/2 left-10 w-24 h-24 border border-coral-400 rounded-full"></div>
      </div>

      <div className="relative z-10 max-w-6xl mx-auto px-4 sm:px-6 grid md:grid-cols-2 gap-8 md:gap-12 items-center">
        {/* Icon and Title Section */}
        <motion.div
          initial={{ opacity: 0, x: -100 }}
          animate={isActive ? { opacity: 1, x: 0 } : { opacity: 0, x: -100 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="text-center md:text-left"
        >
          <motion.div
            {...getAnimationVariant()}
            className="w-24 h-24 sm:w-28 sm:h-28 md:w-32 md:h-32 mx-auto md:mx-0 rounded-full bg-gradient-to-br from-coral-400 to-coral-600 flex items-center justify-center shadow-2xl mb-6 md:mb-8"
          >
            <div className="w-16 h-16 sm:w-18 sm:h-18 md:w-20 md:h-20 flex items-center justify-center">
              {service.id === 'electricians' ? (
                <LightningBolt />
              ) : service.id === 'plumbers' ? (
                <BrokenPipe />
              ) : service.id === 'movers' ? (
                <MovingTruck />
              ) : service.id === 'locksmiths' ? (
                <AnimatedLock />
              ) : (
                <Icon className="w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16 text-white" />
              )}
            </div>
          </motion.div>
          
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            animate={isActive ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
            transition={{ duration: 0.6, delay: 0.5 }}
            className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-gray-800 mb-3 md:mb-4"
          >
            {service.title}
          </motion.h2>
          
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={isActive ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
            transition={{ duration: 0.6, delay: 0.7 }}
            className="text-lg sm:text-xl text-gray-600 mb-6 md:mb-8"
          >
            {service.description}
          </motion.p>
        </motion.div>

        {/* Features Section */}
        <motion.div
          initial={{ opacity: 0, x: 100 }}
          animate={isActive ? { opacity: 1, x: 0 } : { opacity: 0, x: 100 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="space-y-4 md:space-y-6"
        >
          {service.features.map((feature, index) => (
            <motion.div
              key={feature}
              initial={{ opacity: 0, x: 50 }}
              animate={isActive ? { opacity: 1, x: 0 } : { opacity: 0, x: 50 }}
              transition={{ duration: 0.6, delay: 0.6 + index * 0.1 }}
              className="flex items-center space-x-3 md:space-x-4 bg-white/70 backdrop-blur-sm rounded-xl p-3 md:p-4 shadow-lg hover:shadow-xl transition-shadow"
            >
              <div className="w-10 h-10 md:w-12 md:h-12 rounded-full bg-gradient-to-br from-coral-400 to-coral-600 flex items-center justify-center shadow-lg flex-shrink-0">
                <Check className="w-5 h-5 md:w-6 md:h-6 text-white" />
              </div>
              <span className="text-base md:text-lg font-semibold text-gray-800">{feature}</span>
            </motion.div>
          ))}
          
          <motion.button
            initial={{ opacity: 0, y: 30 }}
            animate={isActive ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
            transition={{ duration: 0.6, delay: 1.2 }}
            whileHover={{ scale: 1.05, y: -2 }}
            whileTap={{ scale: 0.95 }}
            className="w-full mt-6 md:mt-8 bg-gradient-to-r from-coral-500 to-coral-600 text-white py-3 md:py-4 px-6 md:px-8 rounded-xl text-base md:text-lg font-semibold shadow-2xl hover:shadow-xl transition-all duration-300"
          >
            Book {service.title.slice(0, -1)} Now
          </motion.button>
        </motion.div>
      </div>

      {/* Floating Elements */}
      {service.animation === 'wave' && (
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <motion.div
            animate={{ 
              y: [0, -20, 0],
              opacity: [0.3, 0.6, 0.3]
            }}
            transition={{ repeat: Infinity, duration: 4, ease: "easeInOut" }}
            className="absolute bottom-20 left-1/4 w-64 h-2 bg-coral-400/30 rounded-full blur-sm"
          />
          <motion.div
            animate={{ 
              y: [0, -15, 0],
              opacity: [0.2, 0.5, 0.2]
            }}
            transition={{ repeat: Infinity, duration: 3.5, delay: 1, ease: "easeInOut" }}
            className="absolute bottom-16 right-1/3 w-48 h-2 bg-coral-300/25 rounded-full blur-sm"
          />
        </div>
      )}

      {/* Electric Energy Effects for Electricians Panel */}
      {service.id === 'electricians' && (
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <motion.div
            animate={{ 
              opacity: [0, 0.4, 0],
              scale: [0.8, 1.2, 0.8]
            }}
            transition={{ repeat: Infinity, duration: 2, ease: "easeInOut" }}
            className="absolute top-1/4 left-1/4 w-32 h-1 bg-yellow-400/40 rounded-full blur-sm transform rotate-45"
          />
          <motion.div
            animate={{ 
              opacity: [0, 0.3, 0],
              scale: [1, 1.5, 1]
            }}
            transition={{ repeat: Infinity, duration: 2.5, delay: 0.8, ease: "easeInOut" }}
            className="absolute bottom-1/3 right-1/4 w-24 h-1 bg-yellow-300/30 rounded-full blur-sm transform -rotate-45"
          />
        </div>
      )}

      {/* Water Effects for Plumbers Panel */}
      {service.id === 'plumbers' && (
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <motion.div
            animate={{ 
              opacity: [0, 0.5, 0],
              scale: [0.5, 1.5, 0.5],
              y: [0, 20, 40]
            }}
            transition={{ repeat: Infinity, duration: 3, ease: "easeOut" }}
            className="absolute top-1/3 right-1/4 w-4 h-4 bg-blue-400/60 rounded-full blur-sm"
          />
          <motion.div
            animate={{ 
              opacity: [0, 0.4, 0],
              scale: [0.3, 1.2, 0.3],
              y: [0, 15, 30]
            }}
            transition={{ repeat: Infinity, duration: 2.5, delay: 0.5, ease: "easeOut" }}
            className="absolute top-1/4 right-1/3 w-3 h-3 bg-blue-300/50 rounded-full blur-sm"
          />
          <motion.div
            animate={{ 
              opacity: [0, 0.6, 0],
              scaleX: [0.5, 2, 0.5],
              scaleY: [1, 0.3, 1]
            }}
            transition={{ repeat: Infinity, duration: 2, delay: 1, ease: "easeInOut" }}
            className="absolute bottom-1/4 right-1/5 w-16 h-2 bg-blue-200/40 rounded-full blur-sm"
          />
        </div>
      )}

      {/* Moving Effects for Movers Panel - Updated for Opposite Direction */}
      {service.id === 'movers' && (
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          {/* Speed Dust Clouds - Moving Left to Right */}
          <motion.div
            animate={{ 
              x: [-50, 50],
              scale: [0.5, 1.5, 0.5],
              opacity: [0, 0.4, 0]
            }}
            transition={{ repeat: Infinity, duration: 1.5, ease: "easeOut" }}
            className="absolute bottom-1/3 right-1/4 w-12 h-6 bg-gray-300/40 rounded-full blur-md"
          />
          
          <motion.div
            animate={{ 
              x: [-40, 40],
              scale: [0.3, 1.2, 0.3],
              opacity: [0, 0.3, 0]
            }}
            transition={{ repeat: Infinity, duration: 1.8, delay: 0.3, ease: "easeOut" }}
            className="absolute bottom-1/5 left-1/3 w-8 h-4 bg-gray-400/30 rounded-full blur-md"
          />
          
          {/* Motion Lines - Moving Left to Right */}
          <motion.div
            animate={{ 
              x: [-60, 60],
              scaleX: [0.5, 2, 0.5],
              opacity: [0, 0.5, 0]
            }}
            transition={{ repeat: Infinity, duration: 1.2, ease: "linear" }}
            className="absolute top-1/3 right-1/4 w-20 h-0.5 bg-gradient-to-r from-transparent via-green-400 to-transparent opacity-40"
          />
          
          <motion.div
            animate={{ 
              x: [-80, 80],
              scaleX: [0.3, 1.8, 0.3],
              opacity: [0, 0.4, 0]
            }}
            transition={{ repeat: Infinity, duration: 1, delay: 0.2, ease: "linear" }}
            className="absolute top-1/2 right-1/5 w-16 h-0.5 bg-gradient-to-r from-transparent via-green-300 to-transparent opacity-35"
          />
        </div>
      )}

      {/* Security Effects for Locksmiths Panel */}
      {service.id === 'locksmiths' && (
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          {/* Security Shield Rings */}
          <motion.div
            animate={{ 
              scale: [0.8, 1.2, 0.8],
              opacity: [0.1, 0.3, 0.1],
              rotate: [0, 360]
            }}
            transition={{ repeat: Infinity, duration: 8, ease: "linear" }}
            className="absolute top-1/4 left-1/4 w-24 h-24 border border-yellow-400/30 rounded-full"
          />
          
          <motion.div
            animate={{ 
              scale: [1, 1.4, 1],
              opacity: [0.05, 0.2, 0.05],
              rotate: [0, -360]
            }}
            transition={{ repeat: Infinity, duration: 12, ease: "linear" }}
            className="absolute bottom-1/3 right-1/4 w-32 h-32 border border-yellow-300/20 rounded-full"
          />
          
          {/* Security Sparkles */}
          <motion.div
            animate={{ 
              opacity: [0, 1, 0],
              scale: [0, 1.5, 0],
              rotate: [0, 180, 360]
            }}
            transition={{ repeat: Infinity, duration: 3, ease: "easeOut" }}
            className="absolute top-1/3 right-1/5 w-2 h-2 bg-yellow-300/60 rounded-full blur-sm"
          />
          
          <motion.div
            animate={{ 
              opacity: [0, 0.8, 0],
              scale: [0, 1.2, 0],
              rotate: [0, -180, -360]
            }}
            transition={{ repeat: Infinity, duration: 2.5, delay: 1, ease: "easeOut" }}
            className="absolute bottom-1/4 left-1/3 w-1.5 h-1.5 bg-yellow-400/50 rounded-full blur-sm"
          />
          
          {/* Digital Lock Pattern */}
          <motion.div
            animate={{ 
              opacity: [0.2, 0.6, 0.2],
              scaleY: [0.5, 1.5, 0.5]
            }}
            transition={{ repeat: Infinity, duration: 2, ease: "easeInOut" }}
            className="absolute top-1/2 left-1/5 w-0.5 h-8 bg-gradient-to-b from-transparent via-yellow-400 to-transparent opacity-40"
          />
          
          <motion.div
            animate={{ 
              opacity: [0.2, 0.5, 0.2],
              scaleY: [0.3, 1.2, 0.3]
            }}
            transition={{ repeat: Infinity, duration: 1.8, delay: 0.3, ease: "easeInOut" }}
            className="absolute top-1/2 right-1/6 w-0.5 h-6 bg-gradient-to-b from-transparent via-yellow-300 to-transparent opacity-35"
          />
        </div>
      )}
    </div>
  );
};

export default ServicePanel;