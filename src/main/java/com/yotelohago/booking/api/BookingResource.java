package com.yotelohago.booking.api;

import com.yotelohago.booking.application.BookingManagementService;
import com.yotelohago.booking.domain.Booking;
import com.yotelohago.booking.domain.BookingStatus;
import com.yotelohago.common.ApiVersion;
import com.yotelohago.common.dto.PageRequest;
import com.yotelohago.common.dto.PageResponse;
import jakarta.annotation.security.RolesAllowed;
import jakarta.inject.Inject;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;

import java.util.List;
import java.util.stream.Collectors;

@Path(ApiVersion.BASE + "/bookings")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@RolesAllowed({"user", "professional", "admin"})
public class BookingResource {

    @Inject
    BookingManagementService service;

    @Inject
    BookingMapper bookingMapper;

    /**
     * Get all bookings with pagination and optional filtering
     * Admin gets all, users get only their bookings
     * Includes professional and service details to avoid N+1 queries
     */
    @GET
    public Response listAll(@BeanParam PageRequest pageRequest,
                           @QueryParam("status") String status,
                           @QueryParam("withDetails") @DefaultValue("true") boolean withDetails) {
        try {
            if (withDetails) {
                // Use enriched DTOs with professional and service data (recommended)
                PageResponse<BookingWithDetailsDTO> pageResponse;

                if (status != null && !status.trim().isEmpty()) {
                    // Parse and validate status
                    BookingStatus bookingStatus;
                    try {
                        bookingStatus = BookingStatus.valueOf(status.toUpperCase());
                    } catch (IllegalArgumentException e) {
                        return Response.status(Response.Status.BAD_REQUEST)
                                .entity("{\"error\": \"Invalid status. Valid values: OPEN, ACCEPTED, COMPLETED, CANCELLED\"}")
                                .build();
                    }

                    // Filter by status with details
                    pageResponse = service.findByStatusWithDetailsPaginated(bookingStatus, pageRequest);
                } else {
                    // No filters, just pagination with details
                    pageResponse = service.findAllWithDetailsPaginated(pageRequest);
                }

                return Response.ok(pageResponse).build();
            } else {
                // Fallback to basic DTOs for backward compatibility
                PageResponse<Booking> pageResponse;

                if (status != null && !status.trim().isEmpty()) {
                    BookingStatus bookingStatus;
                    try {
                        bookingStatus = BookingStatus.valueOf(status.toUpperCase());
                    } catch (IllegalArgumentException e) {
                        return Response.status(Response.Status.BAD_REQUEST)
                                .entity("{\"error\": \"Invalid status. Valid values: OPEN, ACCEPTED, COMPLETED, CANCELLED\"}")
                                .build();
                    }

                    pageResponse = service.findByStatusPaginated(bookingStatus, pageRequest);
                } else {
                    pageResponse = service.findAllPaginated(pageRequest);
                }

                // Convert to DTOs
                List<BookingDTO> bookingDTOs = pageResponse.getContent().stream()
                        .map(bookingMapper::toDTO)
                        .collect(Collectors.toList());

                // Create paginated response with DTOs
                PageResponse<BookingDTO> dtoPageResponse = PageResponse.of(
                    bookingDTOs,
                    pageResponse.getTotalElements(),
                    pageResponse.getNumber(),
                    pageResponse.getSize()
                );

                return Response.ok(dtoPageResponse).build();
            }
        } catch (ForbiddenException e) {
            return Response.status(Response.Status.FORBIDDEN)
                    .entity("{\"error\": \"" + e.getMessage() + "\"}")
                    .build();
        } catch (Exception e) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("{\"error\": \"Failed to retrieve bookings: " + e.getMessage() + "\"}")
                    .build();
        }
    }

    /**
     * Get booking by ID - Only users involved in the booking can access it
     */
    @GET
    @Path("/{id}")
    public Response getById(@PathParam("id") Long id) {
        try {
            return service.findById(id)
                    .map(bookingMapper::toDTO)
                    .map(dto -> Response.ok(dto).build())
                    .orElse(Response.status(Response.Status.NOT_FOUND)
                            .entity("{\"error\": \"Booking not found\"}")
                            .build());
        } catch (ForbiddenException e) {
            return Response.status(Response.Status.FORBIDDEN)
                    .entity("{\"error\": \"" + e.getMessage() + "\"}")
                    .build();
        } catch (IllegalArgumentException e) {
            return Response.status(Response.Status.BAD_REQUEST)
                    .entity("{\"error\": \"Invalid booking ID format\"}")
                    .build();
        } catch (Exception e) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("{\"error\": \"Failed to retrieve booking\"}")
                    .build();
        }
    }

    /**
     * Create a booking - Authenticated users only
     */
    @POST
    public Response create(BookingDTO dto) {
        try {
            if (dto == null) {
                return Response.status(Response.Status.BAD_REQUEST)
                        .entity("{\"error\": \"Booking data is required\"}")
                        .build();
            }

            if (dto.professionalId == null || dto.serviceId == null || dto.title == null) {
                return Response.status(Response.Status.BAD_REQUEST)
                        .entity("{\"error\": \"Professional, service, and title are required\"}")
                        .build();
            }

            Booking createdBooking = service.create(bookingMapper.toEntity(dto));
            return Response.status(Response.Status.CREATED)
                    .entity(bookingMapper.toDTO(createdBooking))
                    .build();
        } catch (ForbiddenException e) {
            return Response.status(Response.Status.FORBIDDEN)
                    .entity("{\"error\": \"" + e.getMessage() + "\"}")
                    .build();
        } catch (IllegalArgumentException e) {
            return Response.status(Response.Status.BAD_REQUEST)
                    .entity("{\"error\": \"" + e.getMessage() + "\"}")
                    .build();
        } catch (Exception e) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("{\"error\": \"Failed to create booking\"}")
                    .build();
        }
    }

    /**
     * Update a booking - Only users involved in the booking can update it
     */
    @PUT
    @Path("/{id}")
    public Response update(@PathParam("id") Long id, BookingDTO dto) {
        try {
            if (dto == null) {
                return Response.status(Response.Status.BAD_REQUEST)
                        .entity("{\"error\": \"Booking data is required\"}")
                        .build();
            }

            // Ensure the ID in the path matches the DTO
            dto.id = id;

            Booking updatedBooking = service.update(bookingMapper.toEntity(dto));
            return Response.ok(bookingMapper.toDTO(updatedBooking)).build();
        } catch (ForbiddenException e) {
            return Response.status(Response.Status.FORBIDDEN)
                    .entity("{\"error\": \"" + e.getMessage() + "\"}")
                    .build();
        } catch (IllegalArgumentException e) {
            return Response.status(Response.Status.NOT_FOUND)
                    .entity("{\"error\": \"" + e.getMessage() + "\"}")
                    .build();
        } catch (RuntimeException e) {
            if (e.getMessage() != null && e.getMessage().contains("not found")) {
                return Response.status(Response.Status.NOT_FOUND)
                        .entity("{\"error\": \"" + e.getMessage() + "\"}")
                        .build();
            }
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("{\"error\": \"Failed to update booking\"}")
                    .build();
        } catch (Exception e) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("{\"error\": \"Failed to update booking\"}")
                    .build();
        }
    }

    /**
     * Delete a booking - Only the client who created it or admin can delete
     */
    @DELETE
    @Path("/{id}")
    public Response delete(@PathParam("id") Long id) {
        try {
            if (!service.delete(id)) {
                return Response.status(Response.Status.NOT_FOUND)
                        .entity("{\"error\": \"Booking not found\"}")
                        .build();
            }
            return Response.noContent().build();
        } catch (ForbiddenException e) {
            return Response.status(Response.Status.FORBIDDEN)
                    .entity("{\"error\": \"" + e.getMessage() + "\"}")
                    .build();
        } catch (IllegalArgumentException e) {
            return Response.status(Response.Status.BAD_REQUEST)
                    .entity("{\"error\": \"Invalid booking ID format\"}")
                    .build();
        } catch (Exception e) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("{\"error\": \"Failed to delete booking\"}")
                    .build();
        }
    }
}