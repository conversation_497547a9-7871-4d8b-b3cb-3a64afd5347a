// API Client exports
export { default as apiClient } from './api/client';
export {
  apiGet,
  apiGetPublic,
  apiPost,
  apiPut,
  apiDelete,
  apiPatch,
  checkApiHealth,
} from './api/client';

// API Services exports
export { default as userRegistrationService, registerUser } from './api/userRegistrationService';
export { default as serviceService } from './api/serviceService';
export { default as categoryService } from './api/categoryService';
export { default as messagingService } from './api/messagingService';
export { default as bookingService } from './api/bookingService';
export { default as availabilityService } from './api/availabilityService';
export { default as professionalServiceManagementService } from './api/professionalServiceManagement';
export { default as mediaService } from './api/mediaService';
export { default as userService } from './api/userService';

// Service API functions exports
export {
  getServices,
  getServiceById,
  searchServices,
  getServicesByProfessional,
  getFeaturedServices,
} from './api/serviceService';

export {
  getCategories,
  getCategoryById,
  createCategory,
  updateCategory,
  deleteCategory,
  getCategoryByName,
  getCategoriesWithCache,
  clearCategoriesCache,
  getFrontendCategories,
} from './api/categoryService';

// Messaging API functions exports
export {
  getConversations,
  getMessagesForBooking,
  getConversationDetail,
  sendMessage,
  markMessageAsRead,
  markConversationAsRead,
  getUnreadMessageCount,
  getWebSocketUrl,
} from './api/messagingService';

// Booking API functions exports
export {
  getBookings,
  getBookingsByStatus,
  getBookingsPaginated,
  getBookingById,
  createBooking,
  updateBooking,
  deleteBooking,
  updateBookingStatus,
  transformBookingForFrontend,
  transformBookingForBackend,
} from './api/bookingService';

// API Types exports
export * from './api/types';

// Auth exports
export { default as tokenStorage } from './auth/tokenStorage';

// Types exports
export * from '../types/api';
export * from '../types/auth';

// Configuration exports
export { ENV, getApiUrl, getKeycloakUrl } from '../config/environment';
