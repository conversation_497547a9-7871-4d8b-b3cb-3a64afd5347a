package com.yotelohago.review.domain;

import io.quarkus.hibernate.orm.panache.PanacheEntityBase;
import jakarta.persistence.*;
import org.hibernate.annotations.CreationTimestamp;
import com.yotelohago.booking.domain.Booking;
import com.yotelohago.user.domain.User;

import java.time.Instant;
import java.util.UUID;

@Entity
@Table(name = "reviews",
       indexes = {
           @Index(name = "idx_reviews_professional_id", columnList = "professional_id"),
           @Index(name = "idx_reviews_client_id", columnList = "client_id"),
           @Index(name = "idx_reviews_created_at", columnList = "created_at"),
           @Index(name = "idx_reviews_booking_id", columnList = "booking_id")
       })
public class Review extends PanacheEntityBase {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "reviews_seq")
    @SequenceGenerator(name = "reviews_seq", sequenceName = "reviews_id_seq", allocationSize = 1)
    public Long id;

    @OneToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "booking_id", nullable = false, unique = true,
                foreignKey = @ForeignKey(name = "fk_reviews_booking"))
    public Booking booking;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "professional_id", nullable = false,
                foreignKey = @ForeignKey(name = "fk_reviews_professional"))
    public User professional;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "client_id", nullable = false,
                foreignKey = @ForeignKey(name = "fk_reviews_client"))
    public User client;

    @Column(nullable = false)
    public int rating; // 1-5 stars

    @Column(columnDefinition = "TEXT")
    public String comment;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    public Instant createdAt;

    public Review() {
        // JPA
    }

    public Review(Booking booking, User professional, User client, int rating, String comment) {
        this.booking = booking;
        this.professional = professional;
        this.client = client;
        this.rating = rating;
        this.comment = comment;
        // createdAt will be set automatically by @CreationTimestamp
    }

    // Getters
    public Long getId() { return id; }
    public Booking getBooking() { return booking; }
    public User getProfessional() { return professional; }
    public User getClient() { return client; }
    public int getRating() { return rating; }
    public String getComment() { return comment; }
    public Instant getCreatedAt() { return createdAt; }



    // Setters
    public void setId(Long id) { this.id = id; }
    public void setBooking(Booking booking) { this.booking = booking; }
    public void setProfessional(User professional) { this.professional = professional; }
    public void setClient(User client) { this.client = client; }
    public void setRating(int rating) { this.rating = rating; }
    public void setComment(String comment) { this.comment = comment; }
    public void setCreatedAt(Instant createdAt) { this.createdAt = createdAt; }

    // Validation helper
    public boolean isValidRating() {
        return rating >= 1 && rating <= 5;
    }
}