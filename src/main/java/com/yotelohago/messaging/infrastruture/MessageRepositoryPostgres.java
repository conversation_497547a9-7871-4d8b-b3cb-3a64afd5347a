package com.yotelohago.messaging.infrastructure;

import com.yotelohago.common.dto.PageRequest;
import com.yotelohago.common.dto.PageResponse;
import com.yotelohago.messaging.api.ConversationDTO;
import com.yotelohago.messaging.api.MessageWithDetailsDTO;
import com.yotelohago.messaging.domain.Message;
import com.yotelohago.messaging.domain.MessageRepository;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@ApplicationScoped
public class MessageRepositoryPostgres implements MessageRepository {

    @PersistenceContext
    EntityManager entityManager;

    @Override
    public void persist(Message message) {
        entityManager.persist(message);
    }

    @Override
    public Optional<Message> findById(Long id) {
        Message message = entityManager.find(Message.class, id);
        return Optional.ofNullable(message);
    }

    @Override
    public List<Message> findByBookingId(Long bookingId) {
        return entityManager.createQuery("FROM Message m WHERE m.booking.id = :bookingId ORDER BY m.sentAt", Message.class)
                .setParameter("bookingId", bookingId)
                .getResultList();
    }

    @Override
    public void markAsRead(Long messageId) {
        Message msg = entityManager.find(Message.class, messageId);
        if (msg != null) {
            msg.isRead = true;
            entityManager.merge(msg);
        }
    }

    @Override
    public void deleteById(Long id) {
        Message msg = entityManager.find(Message.class, id);
        if (msg != null) {
            entityManager.remove(msg);
        }
    }

    @Override
    public void flush() {
        entityManager.flush();
    }

    @Override
    public List<Message> findByParticipant(UUID participantId) {
        return entityManager.createQuery(
                "FROM Message m WHERE m.sender.id = :participantId OR m.receiver.id = :participantId ORDER BY m.sentAt DESC",
                Message.class)
                .setParameter("participantId", participantId)
                .getResultList();
    }

    @Override
    public int countUnreadForUser(UUID userId) {
        Long count = entityManager.createQuery(
                "SELECT COUNT(m) FROM Message m WHERE m.receiver.id = :userId AND m.isRead = false",
                Long.class)
                .setParameter("userId", userId)
                .getSingleResult();
        return count.intValue();
    }

    @Override
    public PageResponse<ConversationDTO.ConversationSummary> findAllConversationsPaginated(PageRequest pageRequest) {
        // Get latest message for each booking (admin view)
        String jpql = """
            SELECT m FROM Message m
            WHERE m.id IN (
                SELECT MAX(m2.id) FROM Message m2 GROUP BY m2.booking.id
            )
            ORDER BY m.sentAt DESC
            """;

        TypedQuery<Message> query = entityManager.createQuery(jpql, Message.class);
        query.setFirstResult(pageRequest.getOffset());
        query.setMaxResults(pageRequest.getSize());

        List<Message> latestMessages = query.getResultList();

        // Convert to ConversationSummary DTOs for admin
        List<ConversationDTO.ConversationSummary> conversations = latestMessages.stream()
                .map(this::convertToConversationSummaryForAdmin)
                .toList();

        // Count total conversations (distinct booking IDs)
        Long totalElements = entityManager.createQuery(
                "SELECT COUNT(DISTINCT m.booking.id) FROM Message m", Long.class)
                .getSingleResult();

        return PageResponse.of(conversations, totalElements, pageRequest.getPage(), pageRequest.getSize());
    }

    @Override
    public PageResponse<MessageWithDetailsDTO> findMessagesByBookingPaginated(Long bookingId, PageRequest pageRequest) {
        // Get all messages for a specific booking with full details
        String jpql = """
            SELECT m FROM Message m
            JOIN FETCH m.sender sender
            JOIN FETCH m.receiver receiver
            JOIN FETCH m.booking booking
            WHERE m.booking.id = :bookingId
            ORDER BY m.sentAt ASC
            """;

        TypedQuery<Message> query = entityManager.createQuery(jpql, Message.class);
        query.setParameter("bookingId", bookingId);
        query.setFirstResult(pageRequest.getOffset());
        query.setMaxResults(pageRequest.getSize());

        List<Message> messages = query.getResultList();

        // Convert to DTOs with nested objects
        List<MessageWithDetailsDTO> messageDTOs = messages.stream()
                .map(this::convertToMessageWithDetailsDTO)
                .toList();

        // Count total messages for this booking
        Long totalElements = entityManager.createQuery(
                "SELECT COUNT(m) FROM Message m WHERE m.booking.id = :bookingId", Long.class)
                .setParameter("bookingId", bookingId)
                .getSingleResult();

        return PageResponse.of(messageDTOs, totalElements, pageRequest.getPage(), pageRequest.getSize());
    }

    private MessageWithDetailsDTO convertToMessageWithDetailsDTO(Message message) {
        System.out.println("🔄 Converting message ID: " + message.id);
        System.out.println("📧 Sender: " + message.getSender().firstName + " " + message.getSender().lastName + " (Professional: " + message.getSender().isProfessional + ")");
        System.out.println("📧 Receiver: " + message.getReceiver().firstName + " " + message.getReceiver().lastName + " (Professional: " + message.getReceiver().isProfessional + ")");
        System.out.println("📧 Booking: " + message.getBooking().title + " (ID: " + message.getBooking().id + ")");

        // Create sender info
        MessageWithDetailsDTO.UserInfo sender = new MessageWithDetailsDTO.UserInfo(
                message.getSender().id.toString(),
                message.getSender().firstName,
                message.getSender().lastName,
                message.getSender().email,
                message.getSender().isProfessional
        );

        // Create receiver info
        MessageWithDetailsDTO.UserInfo receiver = new MessageWithDetailsDTO.UserInfo(
                message.getReceiver().id.toString(),
                message.getReceiver().firstName,
                message.getReceiver().lastName,
                message.getReceiver().email,
                message.getReceiver().isProfessional
        );

        // Create booking info
        MessageWithDetailsDTO.BookingInfo booking = new MessageWithDetailsDTO.BookingInfo(
                message.getBooking().id.toString(),
                message.getBooking().title,
                message.getBooking().description,
                message.getBooking().status.toString()
        );

        MessageWithDetailsDTO dto = new MessageWithDetailsDTO(
                message.id,
                message.getBooking().id,
                message.getSender().id.toString(),
                message.getReceiver().id.toString(),
                message.content,
                message.sentAt.toString(),
                message.isRead,
                sender,
                receiver,
                booking
        );

        System.out.println("✅ Created DTO with booking title: " + dto.booking.title);
        return dto;
    }

    @Override
    public PageResponse<ConversationDTO.ConversationSummary> findConversationsByParticipantPaginated(UUID participantId, PageRequest pageRequest) {
        // Get latest message for each booking where user is participant
        String jpql = """
            SELECT m FROM Message m
            WHERE m.id IN (
                SELECT MAX(m2.id) FROM Message m2
                WHERE m2.sender.id = :participantId OR m2.receiver.id = :participantId
                GROUP BY m2.booking.id
            )
            ORDER BY m.sentAt DESC
            """;

        TypedQuery<Message> query = entityManager.createQuery(jpql, Message.class);
        query.setParameter("participantId", participantId);
        query.setFirstResult(pageRequest.getOffset());
        query.setMaxResults(pageRequest.getSize());

        List<Message> latestMessages = query.getResultList();

        // Convert to ConversationSummary DTOs
        List<ConversationDTO.ConversationSummary> conversations = latestMessages.stream()
                .map(message -> convertToConversationSummaryForUser(message, participantId))
                .toList();

        // Count total conversations for this user
        Long totalElements = entityManager.createQuery(
                "SELECT COUNT(DISTINCT m.booking.id) FROM Message m WHERE m.sender.id = :participantId OR m.receiver.id = :participantId", Long.class)
                .setParameter("participantId", participantId)
                .getSingleResult();

        return PageResponse.of(conversations, totalElements, pageRequest.getPage(), pageRequest.getSize());
    }



    private ConversationDTO.ConversationSummary convertToConversationSummaryForUser(Message message, UUID currentUserId) {
        ConversationDTO.ConversationSummary summary = new ConversationDTO.ConversationSummary();
        summary.bookingId = message.getBooking().id;
        summary.bookingTitle = message.getBooking().title;
        summary.bookingDescription = message.getBooking().description;

        // Always populate both client and professional details from booking
        summary.clientId = message.getBooking().getClient().id;
        summary.clientName = message.getBooking().getClient().firstName + " " + message.getBooking().getClient().lastName;
        summary.clientEmail = message.getBooking().getClient().email;

        summary.professionalId = message.getBooking().getProfessional().id;
        summary.professionalName = message.getBooking().getProfessional().firstName + " " + message.getBooking().getProfessional().lastName;
        summary.professionalEmail = message.getBooking().getProfessional().email;

        // Determine if last message was from current user
        summary.lastMessageFromCurrentUser = message.getSender().id.equals(currentUserId);

        summary.lastMessageContent = message.content;
        summary.lastMessageTime = message.sentAt;

        // Count unread messages for this conversation
        Long unreadCount = entityManager.createQuery(
                "SELECT COUNT(m) FROM Message m WHERE m.booking.id = :bookingId AND m.receiver.id = :userId AND m.isRead = false",
                Long.class)
                .setParameter("bookingId", message.getBooking().id)
                .setParameter("userId", currentUserId)
                .getSingleResult();
        summary.unreadCount = unreadCount.intValue();

        return summary;
    }

    private ConversationDTO.ConversationSummary convertToConversationSummaryForAdmin(Message message) {
        // For admin view - show conversation summary with both participants
        ConversationDTO.ConversationSummary summary = new ConversationDTO.ConversationSummary();
        summary.bookingId = message.getBooking().id;
        summary.bookingTitle = message.getBooking().title;
        summary.bookingDescription = message.getBooking().description;

        // Always populate both client and professional details from booking
        // This is more reliable than inferring from message participants
        summary.clientId = message.getBooking().getClient().id;
        summary.clientName = message.getBooking().getClient().firstName + " " + message.getBooking().getClient().lastName;
        summary.clientEmail = message.getBooking().getClient().email;

        summary.professionalId = message.getBooking().getProfessional().id;
        summary.professionalName = message.getBooking().getProfessional().firstName + " " + message.getBooking().getProfessional().lastName;
        summary.professionalEmail = message.getBooking().getProfessional().email;

        summary.lastMessageContent = message.content;
        summary.lastMessageTime = message.sentAt;
        summary.lastMessageFromCurrentUser = false; // Admin view doesn't have current user context
        summary.unreadCount = 0; // Admin doesn't need unread count

        return summary;
    }
}