import React from 'react';
import { motion } from 'framer-motion';

const BrokenPipe: React.FC = () => {
  return (
    <div className="relative w-full h-full flex items-center justify-center">
      {/* Main Pipe Structure - Responsive sizing */}
      <motion.svg
        width="100%"
        height="100%"
        viewBox="0 0 96 96"
        className="relative z-10 max-w-20 max-h-20 sm:max-w-24 sm:max-h-24"
        animate={{
          y: [0, -0.5, 0, 0.5, 0],
        }}
        transition={{
          duration: 4,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      >
        <defs>
          {/* Water Glow Filter */}
          <filter id="water-glow" x="-200%" y="-200%" width="400%" height="400%">
            <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
            <feFlood floodColor="#3B82F6" floodOpacity="0.8"/>
            <feComposite in2="coloredBlur" operator="in"/>
            <feMerge> 
              <feMergeNode/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
          
          {/* Pipe Metal Gradient */}
          <linearGradient id="pipe-metal" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#E5E7EB" />
            <stop offset="20%" stopColor="#9CA3AF" />
            <stop offset="60%" stopColor="#6B7280" />
            <stop offset="100%" stopColor="#4B5563" />
          </linearGradient>
          
          {/* Pipe Shadow */}
          <linearGradient id="pipe-shadow" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#6B7280" />
            <stop offset="100%" stopColor="#374151" />
          </linearGradient>
          
          {/* Water Stream Gradient */}
          <linearGradient id="water-stream" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#DBEAFE" />
            <stop offset="40%" stopColor="#60A5FA" />
            <stop offset="80%" stopColor="#3B82F6" />
            <stop offset="100%" stopColor="#1E40AF" />
          </linearGradient>
        </defs>
        
        {/* Vertical Pipe Section - Much Thicker */}
        <rect
          x="36"
          y="8"
          width="24"
          height="80"
          fill="url(#pipe-metal)"
          rx="2"
        />
        
        {/* Pipe Shadow/Depth - Thicker */}
        <rect
          x="52"
          y="8"
          width="8"
          height="80"
          fill="url(#pipe-shadow)"
          rx="1"
        />
        
        {/* Pipe Joint - Top - Thicker */}
        <rect
          x="30"
          y="22"
          width="36"
          height="12"
          rx="6"
          fill="url(#pipe-metal)"
        />
        
        {/* Pipe Joint - Bottom - Thicker */}
        <rect
          x="30"
          y="62"
          width="36"
          height="12"
          rx="6"
          fill="url(#pipe-metal)"
        />
        
        {/* Break/Crack in Pipe - Larger */}
        <motion.path
          d="M60 44 L66 46 L64 48 L70 50 L68 52 L74 54 L72 56"
          stroke="#DC2626"
          strokeWidth="2"
          fill="none"
          animate={{
            strokeWidth: [1.5, 3, 1.5],
            opacity: [0.7, 1, 0.7]
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        
        {/* MAJOR WATER STREAMS - Much Larger and More Dramatic */}
        
        {/* Primary Main Stream - Huge */}
        <motion.path
          d="M60 48 Q75 48 88 52 Q95 55 100 58"
          stroke="url(#water-stream)"
          strokeWidth="6"
          fill="none"
          filter="url(#water-glow)"
          animate={{
            pathLength: [0, 1],
            strokeWidth: [4, 8, 4],
            opacity: [0.7, 1, 0.7]
          }}
          transition={{
            duration: 1.2,
            repeat: Infinity,
            ease: "easeOut"
          }}
        />
        
        {/* Secondary Main Stream */}
        <motion.path
          d="M58 46 Q72 44 85 42 Q92 40 98 38"
          stroke="url(#water-stream)"
          strokeWidth="5"
          fill="none"
          filter="url(#water-glow)"
          animate={{
            pathLength: [0, 1],
            strokeWidth: [3, 6, 3],
            opacity: [0.6, 0.9, 0.6]
          }}
          transition={{
            duration: 1.4,
            repeat: Infinity,
            ease: "easeOut",
            delay: 0.1
          }}
        />
        
        {/* Third Main Stream */}
        <motion.path
          d="M58 50 Q72 54 85 58 Q92 62 98 66"
          stroke="url(#water-stream)"
          strokeWidth="5"
          fill="none"
          filter="url(#water-glow)"
          animate={{
            pathLength: [0, 1],
            strokeWidth: [3, 6, 3],
            opacity: [0.6, 0.9, 0.6]
          }}
          transition={{
            duration: 1.3,
            repeat: Infinity,
            ease: "easeOut",
            delay: 0.2
          }}
        />
        
        {/* Upper Arc Stream - Large */}
        <motion.path
          d="M60 46 Q70 36 80 28 Q88 22 94 18"
          stroke="url(#water-stream)"
          strokeWidth="4"
          fill="none"
          filter="url(#water-glow)"
          animate={{
            pathLength: [0, 1],
            strokeWidth: [2, 5, 2],
            opacity: [0.5, 0.8, 0.5]
          }}
          transition={{
            duration: 1.8,
            repeat: Infinity,
            ease: "easeOut",
            delay: 0.3
          }}
        />
        
        {/* Lower Arc Stream - Large */}
        <motion.path
          d="M60 50 Q70 60 80 68 Q88 74 94 78"
          stroke="url(#water-stream)"
          strokeWidth="4"
          fill="none"
          filter="url(#water-glow)"
          animate={{
            pathLength: [0, 1],
            strokeWidth: [2, 5, 2],
            opacity: [0.5, 0.8, 0.5]
          }}
          transition={{
            duration: 1.7,
            repeat: Infinity,
            ease: "easeOut",
            delay: 0.4
          }}
        />
        
        {/* High Pressure Upper Stream */}
        <motion.path
          d="M58 44 Q68 32 78 22 Q86 14 92 8"
          stroke="url(#water-stream)"
          strokeWidth="3.5"
          fill="none"
          filter="url(#water-glow)"
          animate={{
            pathLength: [0, 1],
            strokeWidth: [2, 4.5, 2],
            opacity: [0.4, 0.7, 0.4]
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: "easeOut",
            delay: 0.5
          }}
        />
        
        {/* High Pressure Lower Stream */}
        <motion.path
          d="M58 52 Q68 64 78 74 Q86 82 92 88"
          stroke="url(#water-stream)"
          strokeWidth="3.5"
          fill="none"
          filter="url(#water-glow)"
          animate={{
            pathLength: [0, 1],
            strokeWidth: [2, 4.5, 2],
            opacity: [0.4, 0.7, 0.4]
          }}
          transition={{
            duration: 1.9,
            repeat: Infinity,
            ease: "easeOut",
            delay: 0.6
          }}
        />
        
        {/* Medium Pressure Streams */}
        <motion.path
          d="M59 47 Q68 42 76 38 Q82 35 88 32"
          stroke="url(#water-stream)"
          strokeWidth="3"
          fill="none"
          filter="url(#water-glow)"
          animate={{
            pathLength: [0, 1],
            strokeWidth: [1.5, 3.5, 1.5],
            opacity: [0.4, 0.6, 0.4]
          }}
          transition={{
            duration: 1.6,
            repeat: Infinity,
            ease: "easeOut",
            delay: 0.7
          }}
        />
        
        <motion.path
          d="M59 49 Q68 54 76 58 Q82 61 88 64"
          stroke="url(#water-stream)"
          strokeWidth="3"
          fill="none"
          filter="url(#water-glow)"
          animate={{
            pathLength: [0, 1],
            strokeWidth: [1.5, 3.5, 1.5],
            opacity: [0.4, 0.6, 0.4]
          }}
          transition={{
            duration: 1.5,
            repeat: Infinity,
            ease: "easeOut",
            delay: 0.8
          }}
        />
        
        {/* Additional Smaller Streams */}
        <motion.path
          d="M57 45 Q64 40 70 36 Q75 33 80 30"
          stroke="url(#water-stream)"
          strokeWidth="2.5"
          fill="none"
          filter="url(#water-glow)"
          animate={{
            pathLength: [0, 1],
            strokeWidth: [1, 3, 1],
            opacity: [0.3, 0.5, 0.3]
          }}
          transition={{
            duration: 2.2,
            repeat: Infinity,
            ease: "easeOut",
            delay: 0.9
          }}
        />
        
        <motion.path
          d="M57 51 Q64 56 70 60 Q75 63 80 66"
          stroke="url(#water-stream)"
          strokeWidth="2.5"
          fill="none"
          filter="url(#water-glow)"
          animate={{
            pathLength: [0, 1],
            strokeWidth: [1, 3, 1],
            opacity: [0.3, 0.5, 0.3]
          }}
          transition={{
            duration: 2.1,
            repeat: Infinity,
            ease: "easeOut",
            delay: 1
          }}
        />
        
        {/* Fine Spray Streams */}
        <motion.path
          d="M58 46.5 Q65 43 71 40 Q76 38 81 36"
          stroke="url(#water-stream)"
          strokeWidth="2"
          fill="none"
          filter="url(#water-glow)"
          animate={{
            pathLength: [0, 1],
            strokeWidth: [1, 2.5, 1],
            opacity: [0.3, 0.4, 0.3]
          }}
          transition={{
            duration: 1.8,
            repeat: Infinity,
            ease: "easeOut",
            delay: 1.1
          }}
        />
        
        <motion.path
          d="M58 49.5 Q65 53 71 56 Q76 58 81 60"
          stroke="url(#water-stream)"
          strokeWidth="2"
          fill="none"
          filter="url(#water-glow)"
          animate={{
            pathLength: [0, 1],
            strokeWidth: [1, 2.5, 1],
            opacity: [0.3, 0.4, 0.3]
          }}
          transition={{
            duration: 1.7,
            repeat: Infinity,
            ease: "easeOut",
            delay: 1.2
          }}
        />
      </motion.svg>

      {/* REALISTIC WATER DROPS - Various Sizes and Shapes */}
      
      {/* Large Water Drops - Primary Direction */}
      <motion.div
        className="absolute top-6 right-0"
        animate={{
          scale: [0, 1, 0],
          opacity: [0, 1, 0],
          x: [0, 35, 50],
          y: [0, -3, -6]
        }}
        transition={{
          duration: 1.8,
          repeat: Infinity,
          ease: "easeOut",
          delay: 0.2
        }}
      >
        <div className="w-4 h-5 bg-gradient-to-b from-blue-300 to-blue-500 rounded-full transform rotate-12 shadow-lg" 
             style={{ borderRadius: '50% 50% 50% 50% / 60% 60% 40% 40%' }} />
      </motion.div>
      
      <motion.div
        className="absolute top-8 right-1"
        animate={{
          scale: [0, 1, 0],
          opacity: [0, 1, 0],
          x: [0, 40, 55],
          y: [0, 2, 4]
        }}
        transition={{
          duration: 2,
          repeat: Infinity,
          ease: "easeOut",
          delay: 0.3
        }}
      >
        <div className="w-3.5 h-4 bg-gradient-to-b from-blue-200 to-blue-500 rounded-full transform -rotate-6 shadow-md" 
             style={{ borderRadius: '50% 50% 50% 50% / 60% 60% 40% 40%' }} />
      </motion.div>
      
      <motion.div
        className="absolute top-10 right-2"
        animate={{
          scale: [0, 1, 0],
          opacity: [0, 1, 0],
          x: [0, 30, 45],
          y: [0, 4, 8]
        }}
        transition={{
          duration: 1.9,
          repeat: Infinity,
          ease: "easeOut",
          delay: 0.4
        }}
      >
        <div className="w-3 h-3.5 bg-gradient-to-b from-blue-300 to-blue-400 rounded-full transform rotate-8 shadow-md" 
             style={{ borderRadius: '50% 50% 50% 50% / 60% 60% 40% 40%' }} />
      </motion.div>
      
      {/* Medium Water Drops - Upper Arc */}
      <motion.div
        className="absolute top-4 right-3"
        animate={{
          scale: [0, 1, 0],
          opacity: [0, 1, 0],
          x: [0, 25, 35],
          y: [0, -12, -18]
        }}
        transition={{
          duration: 2.2,
          repeat: Infinity,
          ease: "easeOut",
          delay: 0.5
        }}
      >
        <div className="w-2.5 h-3 bg-gradient-to-b from-blue-200 to-blue-500 rounded-full transform rotate-15 shadow-sm" 
             style={{ borderRadius: '50% 50% 50% 50% / 65% 65% 35% 35%' }} />
      </motion.div>
      
      <motion.div
        className="absolute top-2 right-5"
        animate={{
          scale: [0, 1, 0],
          opacity: [0, 1, 0],
          x: [0, 20, 30],
          y: [0, -16, -24]
        }}
        transition={{
          duration: 2.5,
          repeat: Infinity,
          ease: "easeOut",
          delay: 0.6
        }}
      >
        <div className="w-2 h-2.5 bg-gradient-to-b from-blue-100 to-blue-400 rounded-full transform -rotate-10 shadow-sm" 
             style={{ borderRadius: '50% 50% 50% 50% / 65% 65% 35% 35%' }} />
      </motion.div>
      
      <motion.div
        className="absolute top-1 right-7"
        animate={{
          scale: [0, 1, 0],
          opacity: [0, 0.9, 0],
          x: [0, 15, 25],
          y: [0, -20, -30]
        }}
        transition={{
          duration: 2.8,
          repeat: Infinity,
          ease: "easeOut",
          delay: 0.7
        }}
      >
        <div className="w-1.5 h-2 bg-gradient-to-b from-blue-100 to-blue-300 rounded-full transform rotate-20 shadow-sm" 
             style={{ borderRadius: '50% 50% 50% 50% / 70% 70% 30% 30%' }} />
      </motion.div>
      
      {/* Medium Water Drops - Lower Arc */}
      <motion.div
        className="absolute top-12 right-1"
        animate={{
          scale: [0, 1, 0],
          opacity: [0, 1, 0],
          x: [0, 28, 40],
          y: [0, 10, 16]
        }}
        transition={{
          duration: 1.9,
          repeat: Infinity,
          ease: "easeOut",
          delay: 0.8
        }}
      >
        <div className="w-2.5 h-3 bg-gradient-to-b from-blue-300 to-blue-500 rounded-full transform -rotate-8 shadow-sm" 
             style={{ borderRadius: '50% 50% 50% 50% / 65% 65% 35% 35%' }} />
      </motion.div>
      
      <motion.div
        className="absolute top-14 right-3"
        animate={{
          scale: [0, 1, 0],
          opacity: [0, 1, 0],
          x: [0, 22, 32],
          y: [0, 14, 22]
        }}
        transition={{
          duration: 2.1,
          repeat: Infinity,
          ease: "easeOut",
          delay: 0.9
        }}
      >
        <div className="w-2 h-2.5 bg-gradient-to-b from-blue-200 to-blue-400 rounded-full transform rotate-12 shadow-sm" 
             style={{ borderRadius: '50% 50% 50% 50% / 65% 65% 35% 35%' }} />
      </motion.div>
      
      <motion.div
        className="absolute top-16 right-5"
        animate={{
          scale: [0, 1, 0],
          opacity: [0, 0.8, 0],
          x: [0, 18, 28],
          y: [0, 18, 28]
        }}
        transition={{
          duration: 2.3,
          repeat: Infinity,
          ease: "easeOut",
          delay: 1
        }}
      >
        <div className="w-1.5 h-2 bg-gradient-to-b from-blue-100 to-blue-300 rounded-full transform -rotate-15 shadow-sm" 
             style={{ borderRadius: '50% 50% 50% 50% / 70% 70% 30% 30%' }} />
      </motion.div>
      
      {/* Small Water Drops - Scattered */}
      <motion.div
        className="absolute top-7 right-4"
        animate={{
          scale: [0, 1, 0],
          opacity: [0, 0.7, 0],
          x: [0, 24, 36],
          y: [0, -2, -4]
        }}
        transition={{
          duration: 2,
          repeat: Infinity,
          ease: "easeOut",
          delay: 1.1
        }}
      >
        <div className="w-1.5 h-2 bg-gradient-to-b from-blue-100 to-blue-200 rounded-full transform rotate-5 shadow-sm" 
             style={{ borderRadius: '50% 50% 50% 50% / 70% 70% 30% 30%' }} />
      </motion.div>
      
      <motion.div
        className="absolute top-9 right-6"
        animate={{
          scale: [0, 1, 0],
          opacity: [0, 0.6, 0],
          x: [0, 20, 30],
          y: [0, 1, 2]
        }}
        transition={{
          duration: 1.8,
          repeat: Infinity,
          ease: "easeOut",
          delay: 1.2
        }}
      >
        <div className="w-1 h-1.5 bg-gradient-to-b from-blue-50 to-blue-200 rounded-full transform -rotate-8 shadow-sm" 
             style={{ borderRadius: '50% 50% 50% 50% / 75% 75% 25% 25%' }} />
      </motion.div>
      
      <motion.div
        className="absolute top-11 right-4"
        animate={{
          scale: [0, 1, 0],
          opacity: [0, 0.8, 0],
          x: [0, 26, 38],
          y: [0, 6, 10]
        }}
        transition={{
          duration: 1.7,
          repeat: Infinity,
          ease: "easeOut",
          delay: 1.3
        }}
      >
        <div className="w-1 h-1.5 bg-gradient-to-b from-blue-100 to-blue-300 rounded-full transform rotate-10 shadow-sm" 
             style={{ borderRadius: '50% 50% 50% 50% / 75% 75% 25% 25%' }} />
      </motion.div>
      
      {/* Tiny Water Drops - Fine Spray */}
      <motion.div
        className="absolute top-5 right-8"
        animate={{
          scale: [0, 1, 0],
          opacity: [0, 0.5, 0],
          x: [0, 12, 18],
          y: [0, -8, -12]
        }}
        transition={{
          duration: 2.4,
          repeat: Infinity,
          ease: "easeOut",
          delay: 1.4
        }}
      >
        <div className="w-0.5 h-1 bg-gradient-to-b from-blue-50 to-blue-100 rounded-full shadow-sm" 
             style={{ borderRadius: '50% 50% 50% 50% / 80% 80% 20% 20%' }} />
      </motion.div>
      
      <motion.div
        className="absolute top-13 right-7"
        animate={{
          scale: [0, 1, 0],
          opacity: [0, 0.5, 0],
          x: [0, 14, 20],
          y: [0, 12, 18]
        }}
        transition={{
          duration: 2.2,
          repeat: Infinity,
          ease: "easeOut",
          delay: 1.5
        }}
      >
        <div className="w-0.5 h-1 bg-gradient-to-b from-blue-50 to-blue-100 rounded-full shadow-sm" 
             style={{ borderRadius: '50% 50% 50% 50% / 80% 80% 20% 20%' }} />
      </motion.div>

      {/* Additional Fine Mist Drops */}
      <motion.div
        className="absolute top-6 right-9"
        animate={{
          scale: [0, 1, 0],
          opacity: [0, 0.4, 0],
          x: [0, 8, 12],
          y: [0, -10, -16]
        }}
        transition={{
          duration: 3,
          repeat: Infinity,
          ease: "easeOut",
          delay: 1.6
        }}
      >
        <div className="w-0.5 h-0.5 bg-blue-50 rounded-full opacity-60" />
      </motion.div>
      
      <motion.div
        className="absolute top-12 right-8"
        animate={{
          scale: [0, 1, 0],
          opacity: [0, 0.4, 0],
          x: [0, 10, 16],
          y: [0, 8, 14]
        }}
        transition={{
          duration: 2.7,
          repeat: Infinity,
          ease: "easeOut",
          delay: 1.8
        }}
      >
        <div className="w-0.5 h-0.5 bg-blue-50 rounded-full opacity-50" />
      </motion.div>

      {/* Extra Large Drops for More Impact */}
      <motion.div
        className="absolute top-9 right-0"
        animate={{
          scale: [0, 1, 0],
          opacity: [0, 1, 0],
          x: [0, 45, 65],
          y: [0, 0, 0]
        }}
        transition={{
          duration: 1.6,
          repeat: Infinity,
          ease: "easeOut",
          delay: 0.1
        }}
      >
        <div className="w-5 h-6 bg-gradient-to-b from-blue-200 to-blue-600 rounded-full transform rotate-3 shadow-lg" 
             style={{ borderRadius: '50% 50% 50% 50% / 55% 55% 45% 45%' }} />
      </motion.div>

      <motion.div
        className="absolute top-11 right-1"
        animate={{
          scale: [0, 1, 0],
          opacity: [0, 1, 0],
          x: [0, 38, 52],
          y: [0, 6, 12]
        }}
        transition={{
          duration: 1.7,
          repeat: Infinity,
          ease: "easeOut",
          delay: 0.15
        }}
      >
        <div className="w-4 h-5 bg-gradient-to-b from-blue-300 to-blue-500 rounded-full transform -rotate-5 shadow-lg" 
             style={{ borderRadius: '50% 50% 50% 50% / 55% 55% 45% 45%' }} />
      </motion.div>
    </div>
  );
};

export default BrokenPipe;