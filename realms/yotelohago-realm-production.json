{"realm": "yo<PERSON>ohago", "enabled": true, "sslRequired": "external", "registrationAllowed": false, "registrationEmailAsUsername": false, "rememberMe": false, "verifyEmail": false, "loginWithEmailAllowed": true, "duplicateEmailsAllowed": false, "resetPasswordAllowed": false, "editUsernameAllowed": false, "bruteForceProtected": false, "accessTokenLifespan": 300, "accessTokenLifespanForImplicitFlow": 900, "ssoSessionIdleTimeout": 1800, "ssoSessionMaxLifespan": 36000, "offlineSessionIdleTimeout": 2592000, "accessCodeLifespan": 60, "accessCodeLifespanUserAction": 300, "accessCodeLifespanLogin": 1800, "roles": {"realm": [{"name": "professional", "description": "Professional service provider role", "composite": false, "clientRole": false}, {"name": "user", "description": "Regular user role", "composite": false, "clientRole": false}, {"name": "admin", "description": "Administrator role", "composite": false, "clientRole": false}, {"name": "offline_access", "description": "${role_offline-access}", "composite": false, "clientRole": false}, {"name": "uma_authorization", "description": "${role_uma_authorization}", "composite": false, "clientRole": false}, {"name": "default-roles-yo<PERSON><PERSON><PERSON>", "description": "${role_default-roles}", "composite": true, "composites": {"realm": ["offline_access", "uma_authorization"], "client": {"account": ["view-profile", "manage-account"]}}, "clientRole": false}]}, "clients": [{"clientId": "yotelohago-app-prod", "name": "Yotelohago Production App", "description": "Production client for YoteLoHago platform", "enabled": true, "alwaysDisplayInConsole": true, "clientAuthenticatorType": "client-secret", "redirectUris": ["https://yotelohago.co/*", "yotelohago://auth/callback", "yotelohago://*"], "webOrigins": ["https://yotelohago.co", "yotelohago://"], "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": true, "publicClient": true, "frontchannelLogout": true, "protocol": "openid-connect", "attributes": {"oidc.ciba.grant.enabled": "false", "backchannel.logout.session.required": "true", "post.logout.redirect.uris": "https://yotelohago.co/auth/callback+yotelohago://auth/callback", "display.on.consent.screen": "false", "oauth2.device.authorization.grant.enabled": "false", "pkce.code.challenge.method": "S256", "backchannel.logout.revoke.offline.tokens": "false"}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "defaultClientScopes": ["web-origins", "acr", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"clientId": "yotelohago-app-dev", "name": "Yotelohago Development App", "description": "Development client for YoteLoHago platform (localhost testing)", "enabled": true, "alwaysDisplayInConsole": true, "clientAuthenticatorType": "client-secret", "redirectUris": ["http://localhost:8082/*", "http://localhost:19006/*", "yotelohago://auth/callback", "http://localhost:8082/auth/callback", "exp://localhost:19000/*", "yotelohago://*"], "webOrigins": ["http://localhost:8082", "http://localhost:19006", "yotelohago://"], "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": true, "publicClient": true, "frontchannelLogout": true, "protocol": "openid-connect", "attributes": {"oidc.ciba.grant.enabled": "false", "backchannel.logout.session.required": "true", "post.logout.redirect.uris": "http://localhost:8082/auth/callback+yotelohago://auth/callback", "display.on.consent.screen": "false", "oauth2.device.authorization.grant.enabled": "false", "pkce.code.challenge.method": "S256", "backchannel.logout.revoke.offline.tokens": "false"}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "defaultClientScopes": ["web-origins", "acr", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"clientId": "yotelohago-backend", "name": "Yotel<PERSON>ago <PERSON>end Service", "description": "Backend service client for admin operations (user creation, role management)", "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "yotelohago-backend-secret-change-in-production", "redirectUris": [], "webOrigins": [], "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": false, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": true, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"oidc.ciba.grant.enabled": "false", "backchannel.logout.session.required": "false", "display.on.consent.screen": "false", "oauth2.device.authorization.grant.enabled": "false", "backchannel.logout.revoke.offline.tokens": "false"}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "defaultClientScopes": ["web-origins", "acr", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}], "defaultDefaultClientScopes": ["role_list", "profile", "email", "roles", "web-origins", "acr"], "defaultOptionalClientScopes": ["offline_access", "address", "phone", "microprofile-jwt"], "browserSecurityHeaders": {"contentSecurityPolicyReportOnly": "", "xContentTypeOptions": "nosniff", "referrerPolicy": "no-referrer", "xRobotsTag": "none", "xFrameOptions": "SAMEORIGIN", "contentSecurityPolicy": "frame-src 'self'; frame-ancestors 'self'; object-src 'none';", "xXSSProtection": "1; mode=block", "strictTransportSecurity": "max-age=31536000; includeSubDomains"}, "eventsEnabled": false, "eventsListeners": ["jboss-logging"], "enabledEventTypes": [], "adminEventsEnabled": false, "adminEventsDetailsEnabled": false, "identityProviders": [], "identityProviderMappers": [], "internationalizationEnabled": false, "supportedLocales": [], "requiredCredentials": ["password"], "otpPolicyType": "totp", "otpPolicyAlgorithm": "HmacSHA1", "otpPolicyInitialCounter": 0, "otpPolicyDigits": 6, "otpPolicyLookAheadWindow": 1, "otpPolicyPeriod": 30, "otpPolicyCodeReusable": false, "otpSupportedApplications": ["totpAppFreeOTPName", "totpAppGoogleName", "totpAppMicrosoftAuthenticatorName"]}