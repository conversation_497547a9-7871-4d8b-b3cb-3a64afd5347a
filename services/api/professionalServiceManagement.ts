import { apiGet, apiPost, apiPut, apiDelete } from './client';
import {
  ServiceDTO,
  CreateServiceRequest,
  UpdateServiceRequest,
  ApiError
} from '../../types/api';
import tokenStorage from '../auth/tokenStorage';
import { ENV } from '../../config/environment';

/**
 * Professional Service Management API Service
 * Handles service management operations for professionals
 * 
 * This service provides CRUD operations for professionals to manage their own services
 */
export class ProfessionalServiceManagementService {
  private readonly basePath = '/services';

  /**
   * Get all services for the current professional (authenticated endpoint)
   * Returns ALL services (DRAFT + PUBLISHED) for the professional to manage
   * @returns List of all services owned by the current professional
   */
  async getMyServices(): Promise<ServiceDTO[]> {
    try {
      console.log('🗓️ Fetching current professional services');

      const services = await apiGet<ServiceDTO[]>(`${this.basePath}/my-services`);
      
      console.log(`✅ Professional services fetched: ${services.length} services`);
      return services;
    } catch (error) {
      console.error('❌ Failed to fetch professional services:', error);
      throw error;
    }
  }

  /**
   * Create a new service for the current professional (authenticated endpoint)
   * @param request Service creation data
   * @returns Created service
   */
  async createService(request: CreateServiceRequest): Promise<ServiceDTO> {
    try {
      console.log('🗓️ Creating new service', request);

      // Get current user info to include professionalUserId
      const userInfo = await tokenStorage.getUserInfo();
      if (!userInfo?.internalId) {
        throw new Error('User not authenticated or missing internal ID');
      }

      // Convert CreateServiceRequest to ServiceDTO with professionalUserId
      const serviceDTO: Partial<ServiceDTO> = {
        title: request.title.trim(),
        description: request.description.trim(),
        price: request.price,
        categoryId: request.categoryId,
        professionalUserId: userInfo.internalId,
        status: 'draft' // Backend expects lowercase status
      };

      console.log('🗓️ Sending service DTO to backend:', serviceDTO);
      console.log('🔍 User info:', userInfo);
      console.log('🔍 Request data:', request);

      const service = await apiPost<ServiceDTO>(this.basePath, serviceDTO);

      console.log('✅ Service created successfully:', service);
      return service;
    } catch (error) {
      console.error('❌ Failed to create service:', error);
      throw error;
    }
  }

  /**
   * Update an existing service (authenticated endpoint)
   * Only the professional who owns the service can update it
   * @param serviceId Service ID to update
   * @param request Update data
   * @returns Updated service
   */
  async updateService(serviceId: string, request: UpdateServiceRequest): Promise<ServiceDTO> {
    try {
      console.log(`🗓️ Updating service ${serviceId}`, request);
      console.log(`🔗 API URL: ${this.basePath}/${serviceId}`);

      const service = await apiPut<ServiceDTO>(`${this.basePath}/${serviceId}`, request);

      console.log('✅ Service updated successfully:', service);
      return service;
    } catch (error: any) {
      console.error('❌ Failed to update service:', error);
      console.error('❌ Error details:', {
        message: error.message,
        status: error.status,
        response: error.response?.data,
        url: `${this.basePath}/${serviceId}`,
        request: request
      });
      throw error;
    }
  }

  /**
   * Delete a service (authenticated endpoint)
   * Only the professional who owns the service can delete it
   * @param serviceId Service ID to delete
   * @returns Success status
   */
  async deleteService(serviceId: string): Promise<boolean> {
    try {
      console.log(`🗓️ Starting delete service process for ID: ${serviceId}`);
      console.log(`🔗 DELETE URL: ${this.basePath}/${serviceId}`);
      console.log(`🔗 Full URL: ${ENV.API_BASE_URL}${this.basePath}/${serviceId}`);

      const result = await apiDelete(`${this.basePath}/${serviceId}`);

      console.log('✅ Service deleted successfully, result:', result);
      return true;
    } catch (error: any) {
      console.error('❌ Delete service error details:', {
        serviceId,
        url: `${this.basePath}/${serviceId}`,
        fullUrl: `${ENV.API_BASE_URL}${this.basePath}/${serviceId}`,
        status: error.status,
        statusCode: error.response?.status,
        message: error.message,
        response: error.response?.data,
        headers: error.response?.headers,
        config: error.config,
        error: error
      });

      if (error.status === 404 || error.response?.status === 404) {
        console.log('ℹ️ Service not found for deletion (404)');
        return false;
      }
      console.error('❌ Failed to delete service:', error);
      throw error;
    }
  }

  /**
   * Get a specific service by ID (authenticated endpoint)
   * Only the professional who owns the service can access it
   * @param serviceId Service ID
   * @returns Service data
   */
  async getServiceById(serviceId: string): Promise<ServiceDTO | null> {
    try {
      console.log(`🗓️ Fetching service by ID: ${serviceId}`);

      const service = await apiGet<ServiceDTO>(`${this.basePath}/${serviceId}`);
      
      console.log('✅ Service fetched by ID:', service);
      return service;
    } catch (error: any) {
      if (error.status === 404) {
        console.log('ℹ️ Service not found');
        return null;
      }
      console.error('❌ Failed to fetch service by ID:', error);
      throw error;
    }
  }

  /**
   * Toggle service status (publish/unpublish)
   * This is a convenience method that updates the service status
   * @param serviceId Service ID
   * @param isActive New active status (true = published, false = draft)
   * @param currentService Current service data to preserve other fields
   * @returns Updated service
   */
  async toggleServiceStatus(serviceId: string, isActive: boolean, currentService?: ServiceDTO): Promise<ServiceDTO> {
    try {
      const newStatus = isActive ? 'published' : 'draft';
      console.log(`🗓️ Toggling service ${serviceId} status to: ${newStatus}`);

      // If we don't have current service data, fetch it first
      if (!currentService) {
        console.log('🔍 Fetching current service data for status toggle...');
        currentService = await this.getServiceById(serviceId);
        if (!currentService) {
          throw new Error('Service not found');
        }
      }

      // Create a complete update request with all current data plus new status
      const request: UpdateServiceRequest = {
        title: currentService.title,
        description: currentService.description,
        price: currentService.price,
        categoryId: currentService.categoryId,
        status: newStatus
      };

      const service = await this.updateService(serviceId, request);

      console.log('✅ Service status toggled successfully');
      return service;
    } catch (error) {
      console.error('❌ Failed to toggle service status:', error);
      throw error;
    }
  }
}

// Create and export singleton instance
export const professionalServiceManagementService = new ProfessionalServiceManagementService();
export default professionalServiceManagementService;
