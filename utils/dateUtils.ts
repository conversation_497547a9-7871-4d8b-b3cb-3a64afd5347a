/**
 * Format a date as a relative time string (e.g., "2 hours ago", "yesterday")
 */
export function formatDistanceToNow(date: Date): string {
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
  
  // Less than a minute
  if (diffInSeconds < 60) {
    return 'just now';
  }
  
  // Less than an hour
  const diffInMinutes = Math.floor(diffInSeconds / 60);
  if (diffInMinutes < 60) {
    return `${diffInMinutes}m ago`;
  }
  
  // Less than a day
  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) {
    return `${diffInHours}h ago`;
  }
  
  // Less than a week
  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays < 7) {
    if (diffInDays === 1) {
      return 'yesterday';
    }
    return `${diffInDays}d ago`;
  }
  
  // Format as date
  const month = date.toLocaleString('default', { month: 'short' });
  const day = date.getDate();
  
  if (date.getFullYear() === now.getFullYear()) {
    return `${month} ${day}`;
  }
  
  return `${month} ${day}, ${date.getFullYear()}`;
}

/**
 * Format a date string to a readable format (e.g., "May 15, 2023")
 */
export function formatDate(dateString: string): string {
  const date = new Date(dateString);
  const options: Intl.DateTimeFormatOptions = { 
    month: 'long', 
    day: 'numeric',
    year: 'numeric'
  };
  
  return date.toLocaleDateString('en-US', options);
}

/**
 * Format a date for display in bookings (e.g., "Monday, May 15")
 */
export function formatBookingDate(dateString: string): string {
  const date = new Date(dateString);
  const options: Intl.DateTimeFormatOptions = { 
    weekday: 'long',
    month: 'long', 
    day: 'numeric'
  };
  
  return date.toLocaleDateString('en-US', options);
}