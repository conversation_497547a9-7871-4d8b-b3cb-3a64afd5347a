import React, { useState, useEffect } from 'react';
import { Typography, Button, Space, Tag, Avatar, Dropdown, Modal, Descriptions, Select, message } from 'antd';
import {
  EyeOutlined,
  EditOutlined,
  CalendarOutlined,
  MoreOutlined,
  UserOutlined,
  ExclamationCircleOutlined,
  ToolOutlined
} from '@ant-design/icons';
import { ColumnsType } from 'antd/es/table';
import DataTable from '../components/common/DataTable';
import { Booking } from '../types';
import apiService from '../services/apiService';
import { useUrlHighlight } from '../hooks/useUrlHighlight';
import { useNavigate } from 'react-router-dom';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { Option } = Select;
const { confirm } = Modal;

const Bookings: React.FC = () => {
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [loading, setLoading] = useState(true);
  const [detailsVisible, setDetailsVisible] = useState(false);
  const [selectedBooking, setSelectedBooking] = useState<Booking | null>(null);
  const highlightId = useUrlHighlight();
  const navigate = useNavigate();

  // Load bookings from API
  useEffect(() => {
    const loadBookings = async () => {
      try {
        setLoading(true);
        const bookingsData = await apiService.getBookings();
        setBookings(bookingsData);
      } catch (error) {
        console.error('Failed to load bookings:', error);
        message.error('Failed to load bookings');
      } finally {
        setLoading(false);
      }
    };

    loadBookings();
  }, []);

  // Get enriched bookings with user and service data (simplified for now)
  const bookingsWithDetails = bookings.map(booking => ({
    ...booking,
    // Professional and service data should come from BookingWithDetailsDTO
    professional: null, // Will be handled by backend
    service: null // Will be handled by backend
  }));

  const handleViewBooking = (booking: Booking) => {
    setSelectedBooking(booking);
    setDetailsVisible(true);
  };

  const handleUpdateBookingStatus = (booking: Booking, newStatus: string) => {
    confirm({
      title: 'Update Booking Status?',
      icon: <ExclamationCircleOutlined />,
      content: `Change booking status to ${newStatus}?`,
      onOk() {
        setBookings(bookings.map(b => 
          b.id === booking.id 
            ? { ...b, status: newStatus as any, updatedAt: new Date().toISOString() }
            : b
        ));
        message.success('Booking status updated successfully');
      },
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'OPEN': return 'orange';
      case 'ACCEPTED': return 'blue';
      case 'COMPLETED': return 'green';
      case 'CANCELLED': return 'red';
      default: return 'default';
    }
  };

  const columns: ColumnsType<Booking & { client?: any; professional?: any; service?: any }> = [
    {
      title: 'Booking',
      key: 'booking',
      width: 200,
      render: (_, record) => (
        <div className={highlightId === record.id ? 'bg-coral-100 p-2 rounded animate-pulse' : ''}>
          <div className="font-medium text-gray-900">{record.title}</div>
          <div className="text-sm text-gray-500">
            ID: {record.id}
          </div>
          <div className="text-sm text-gray-500">
            {dayjs(record.createdAt).format('MMM DD, YYYY')}
          </div>
        </div>
      ),
    },
    {
      title: 'Client',
      key: 'client',
      width: 180,
      render: (_, record) => (
        <div className="flex items-center space-x-2">
          <Avatar icon={<UserOutlined />} size="small" />
          <span
            className="text-coral-500 hover:text-coral-600 cursor-pointer font-medium"
            onClick={() => navigate(`/users?highlight=${record.clientId}`)}
          >
            Client {record.clientId.slice(0, 8)}...
          </span>
        </div>
      ),
    },
    {
      title: 'Professional',
      key: 'professional',
      width: 180,
      render: (_, record) => (
        <div className="flex items-center space-x-2">
          <Avatar icon={<UserOutlined />} size="small" />
          <span
            className="text-coral-500 hover:text-coral-600 cursor-pointer font-medium"
            onClick={() => navigate(`/professionals?highlight=${record.professionalId}`)}
          >
            Professional {record.professionalId.slice(0, 8)}...
          </span>
        </div>
      ),
    },
    {
      title: 'Service',
      key: 'service',
      width: 180,
      render: (_, record) => (
        <div className="flex items-center space-x-2">
          <ToolOutlined className="text-gray-400" />
          <span
            className="text-coral-500 hover:text-coral-600 cursor-pointer font-medium"
            onClick={() => navigate(`/services?highlight=${record.serviceId}`)}
          >
            Service {record.serviceId.slice(0, 8)}...
          </span>
        </div>
      ),
    },
    {
      title: 'Price',
      dataIndex: 'price',
      key: 'price',
      width: 100,
      render: (price) => (
        <span className="font-medium">${price.toFixed(2)}</span>
      ),
      sorter: (a, b) => a.price - b.price,
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status) => (
        <Tag color={getStatusColor(status)}>
          {status}
        </Tag>
      ),
      filters: [
        { text: 'Open', value: 'OPEN' },
        { text: 'Accepted', value: 'ACCEPTED' },
        { text: 'Completed', value: 'COMPLETED' },
        { text: 'Cancelled', value: 'CANCELLED' },
      ],
      onFilter: (value, record) => record.status === value,
    },
    {
      title: 'Scheduled',
      key: 'scheduled',
      width: 120,
      render: (_, record) => (
        <span className="text-sm">
          {record.scheduledAt 
            ? dayjs(record.scheduledAt).format('MMM DD, HH:mm')
            : 'Not scheduled'
          }
        </span>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 120,
      render: (_, record) => (
        <Dropdown
          menu={{
            items: [
              {
                key: 'view',
                icon: <EyeOutlined />,
                label: 'View Details',
                onClick: () => handleViewBooking(record),
              },
              {
                key: 'edit',
                icon: <EditOutlined />,
                label: 'Edit Booking',
              },
              {
                type: 'divider',
              },
              {
                key: 'status-open',
                label: 'Mark as Open',
                disabled: record.status === 'OPEN',
                onClick: () => handleUpdateBookingStatus(record, 'OPEN'),
              },
              {
                key: 'status-accepted',
                label: 'Mark as Accepted',
                disabled: record.status === 'ACCEPTED',
                onClick: () => handleUpdateBookingStatus(record, 'ACCEPTED'),
              },
              {
                key: 'status-completed',
                label: 'Mark as Completed',
                disabled: record.status === 'COMPLETED',
                onClick: () => handleUpdateBookingStatus(record, 'COMPLETED'),
              },
              {
                key: 'status-cancelled',
                label: 'Mark as Cancelled',
                disabled: record.status === 'CANCELLED',
                danger: true,
                onClick: () => handleUpdateBookingStatus(record, 'CANCELLED'),
              },
            ],
          }}
          trigger={['click']}
        >
          <Button type="text" icon={<MoreOutlined />} />
        </Dropdown>
      ),
    },
  ];

  const filterOptions = [
    {
      key: 'status',
      label: 'Status',
      options: [
        { value: 'OPEN', label: 'Open' },
        { value: 'ACCEPTED', label: 'Accepted' },
        { value: 'COMPLETED', label: 'Completed' },
        { value: 'CANCELLED', label: 'Cancelled' },
      ],
    },
  ];

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <Title level={2} className="m-0">Booking Management</Title>
        <Text type="secondary">Monitor and manage all service bookings</Text>
      </div>

      <DataTable
        columns={columns}
        dataSource={bookingsWithDetails}
        rowKey="id"
        loading={loading}
        filterOptions={filterOptions}
        onSearch={(value) => {
          console.log('Search:', value);
        }}
        onFilter={(filters) => {
          console.log('Filters:', filters);
        }}
        onExport={() => {
          console.log('Export bookings');
        }}
      />

      <Modal
        title={`Booking Details - ${selectedBooking?.title}`}
        open={detailsVisible}
        onCancel={() => setDetailsVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailsVisible(false)}>
            Close
          </Button>,
          <Button key="edit" type="primary" icon={<EditOutlined />}>
            Edit Booking
          </Button>,
        ]}
        width={700}
      >
        {selectedBooking && (
          <div className="space-y-6">
            <Descriptions column={2} bordered>
              <Descriptions.Item label="Booking ID">
                {selectedBooking.id}
              </Descriptions.Item>
              <Descriptions.Item label="Status">
                <Tag color={getStatusColor(selectedBooking.status)}>
                  {selectedBooking.status}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="Title">
                {selectedBooking.title}
              </Descriptions.Item>
              <Descriptions.Item label="Price">
                ${selectedBooking.price.toFixed(2)}
              </Descriptions.Item>
              <Descriptions.Item label="Client" span={2}>
                <span 
                  className="text-coral-500 hover:text-coral-600 cursor-pointer font-medium"
                  onClick={() => {
                    setDetailsVisible(false);
                    navigate(`/users?highlight=${selectedBooking.clientId}`);
                  }}
                >
                  {selectedBooking.client 
                    ? `${selectedBooking.client.firstName} ${selectedBooking.client.lastName} (${selectedBooking.client.email})`
                    : 'Unknown Client'
                  }
                </span>
              </Descriptions.Item>
              <Descriptions.Item label="Professional" span={2}>
                <span 
                  className="text-coral-500 hover:text-coral-600 cursor-pointer font-medium"
                  onClick={() => {
                    setDetailsVisible(false);
                    navigate(`/professionals?highlight=${selectedBooking.professionalId}`);
                  }}
                >
                  {selectedBooking.professional 
                    ? `${selectedBooking.professional.firstName} ${selectedBooking.professional.lastName} (${selectedBooking.professional.email})`
                    : 'Unknown Professional'
                  }
                </span>
              </Descriptions.Item>
              <Descriptions.Item label="Service" span={2}>
                <span 
                  className="text-coral-500 hover:text-coral-600 cursor-pointer font-medium"
                  onClick={() => {
                    setDetailsVisible(false);
                    navigate(`/services?highlight=${selectedBooking.serviceId}`);
                  }}
                >
                  {selectedBooking.service?.title || 'Unknown Service'}
                </span>
              </Descriptions.Item>
              <Descriptions.Item label="Description" span={2}>
                {selectedBooking.description}
              </Descriptions.Item>
              <Descriptions.Item label="Requested Date">
                {dayjs(selectedBooking.requestedDate).format('MMM DD, YYYY HH:mm')}
              </Descriptions.Item>
              <Descriptions.Item label="Scheduled Date">
                {selectedBooking.scheduledAt 
                  ? dayjs(selectedBooking.scheduledAt).format('MMM DD, YYYY HH:mm')
                  : 'Not scheduled'
                }
              </Descriptions.Item>
              <Descriptions.Item label="Created">
                {dayjs(selectedBooking.createdAt).format('MMM DD, YYYY HH:mm')}
              </Descriptions.Item>
              <Descriptions.Item label="Last Updated">
                {dayjs(selectedBooking.updatedAt).format('MMM DD, YYYY HH:mm')}
              </Descriptions.Item>
            </Descriptions>

            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div>
                <Text strong>Update Status</Text>
                <div className="text-sm text-gray-500">
                  Change the booking status
                </div>
              </div>
              <Select
                value={selectedBooking.status}
                style={{ width: 120 }}
                onChange={(value) => handleUpdateBookingStatus(selectedBooking, value)}
              >
                <Option value="OPEN">Open</Option>
                <Option value="ACCEPTED">Accepted</Option>
                <Option value="COMPLETED">Completed</Option>
                <Option value="CANCELLED">Cancelled</Option>
              </Select>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default Bookings;