# variable "resource_base_name" {
#   description = "Base value all resource to identify them on AWS console"
#   default     = "jenkins"
# }
#
# variable "bucket_name" {
#   description = "The name of the S3 bucket to store Terraform state."
#   type        = string
# }
#
variable "region" {
  description = "The AWS region to deploy the backend."
  type        = string
  default     = "us-east-1"
}
#
# variable "instance_type" {
#   description = "Value for Instance type"
#   default     = "t3.medium"
# }
#
# variable "ec2_key_path" {
#   description = "The path of your key locally"
#   default     = "/Users/<USER>/.ssh/aws/key"
# }
#
# variable "ec2_user" {
#   type        = string
#   default     = "ec2_user"
# }
#
# variable "vpc_cidr" {
#   description = "Value for cidr"
#   default     = "10.0.0.0/16"
# }
#
# # Define a list of CIDR blocks for the subnets
# variable "subnet_cidrs" {
#   type    = list(string)
#   default = ["********/24", "********/24"]
# }
#
# # Define a list of Availability Zones
# variable "availability_zones" {
#   type    = list(string)
#   default = ["us-east-1a", "us-east-1b"]
# }

# Helm charts
variable "k8s_kubeconfig_path" {
  default     = "./config/ansible_kubeconfig-k3s.generated"
  type        = string
}

variable "helm_argocd_version" {
  type        = string
}

variable "image_pull_secret_name" {
  default = "ecr-secret"
}

variable "k8s_namespace" {
  default = "default"
}

variable "repository_name" {
  description = "Elastic container registry repository name."
  type        = string
}

variable "image_tag" {
  default = "latest"
}


