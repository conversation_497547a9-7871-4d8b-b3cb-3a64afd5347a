package com.yotelohago.common.dto;

import java.util.List;

/**
 * Standard pagination response wrapper
 * Follows Spring Data Page conventions for consistency with frontend expectations
 */
public class PageResponse<T> {

    private List<T> content;
    private long totalElements;
    private int totalPages;
    private int size;
    private int number;
    private boolean first;
    private boolean last;
    private boolean empty;
    private int numberOfElements;

    // Default constructor
    public PageResponse() {}

    // Constructor with all parameters
    public PageResponse(List<T> content, long totalElements, int page, int size) {
        this.content = content;
        this.totalElements = totalElements;
        this.size = size;
        this.number = page;
        this.numberOfElements = content.size();
        this.totalPages = (int) Math.ceil((double) totalElements / size);
        this.first = page == 0;
        this.last = page >= totalPages - 1;
        this.empty = content.isEmpty();
    }

    // Static factory method for easier creation
    public static <T> PageResponse<T> of(List<T> content, long totalElements, int page, int size) {
        return new PageResponse<>(content, totalElements, page, size);
    }

    // Static factory method for empty page
    public static <T> PageResponse<T> empty(int page, int size) {
        return new PageResponse<>(List.of(), 0, page, size);
    }

    // Getters and setters
    public List<T> getContent() {
        return content;
    }

    public void setContent(List<T> content) {
        this.content = content;
        this.numberOfElements = content != null ? content.size() : 0;
        this.empty = content == null || content.isEmpty();
    }

    public long getTotalElements() {
        return totalElements;
    }

    public void setTotalElements(long totalElements) {
        this.totalElements = totalElements;
        this.totalPages = (int) Math.ceil((double) totalElements / size);
        this.last = number >= totalPages - 1;
    }

    public int getTotalPages() {
        return totalPages;
    }

    public void setTotalPages(int totalPages) {
        this.totalPages = totalPages;
    }

    public int getSize() {
        return size;
    }

    public void setSize(int size) {
        this.size = size;
        this.totalPages = (int) Math.ceil((double) totalElements / size);
        this.last = number >= totalPages - 1;
    }

    public int getNumber() {
        return number;
    }

    public void setNumber(int number) {
        this.number = number;
        this.first = number == 0;
        this.last = number >= totalPages - 1;
    }

    public boolean isFirst() {
        return first;
    }

    public void setFirst(boolean first) {
        this.first = first;
    }

    public boolean isLast() {
        return last;
    }

    public void setLast(boolean last) {
        this.last = last;
    }

    public boolean isEmpty() {
        return empty;
    }

    public void setEmpty(boolean empty) {
        this.empty = empty;
    }

    public int getNumberOfElements() {
        return numberOfElements;
    }

    public void setNumberOfElements(int numberOfElements) {
        this.numberOfElements = numberOfElements;
    }

    // Utility methods
    public boolean hasContent() {
        return !empty;
    }

    public boolean hasNext() {
        return !last;
    }

    public boolean hasPrevious() {
        return !first;
    }

    @Override
    public String toString() {
        return String.format("PageResponse{content=%d items, totalElements=%d, totalPages=%d, number=%d, size=%d}", 
                           numberOfElements, totalElements, totalPages, number, size);
    }
}
