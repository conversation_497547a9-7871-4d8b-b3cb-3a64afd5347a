package com.yotelohago.user.infrastructure;

import com.yotelohago.user.api.UserWithDetailsDTO;
import com.yotelohago.user.domain.User;
import com.yotelohago.user.domain.UserRepository;
import com.yotelohago.common.dto.PageRequest;
import com.yotelohago.common.dto.PageResponse;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;
import jakarta.inject.Inject;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@ApplicationScoped
public class UserRepositoryPostgres implements UserRepository {

    private static final Logger logger = LoggerFactory.getLogger(UserRepositoryPostgres.class);

    @PersistenceContext
    EntityManager entityManager;

    @Inject
    @ConfigProperty(name = "media.url-resolver")
    String mediaUrlResolver;

    @Override
    public void persist(User user) {
        entityManager.persist(user);
    }

    @Override
    public Optional<UserWithDetailsDTO> findById(UUID id) {
        String jpql = """
            SELECT new com.yotelohago.user.api.UserWithDetailsDTO(
                u.id, u.username, u.firstName, u.lastName, u.email, u.phone,
                u.isProfessional, u.createdAt, u.updatedAt,
                p.bio, p.city, p.avgRating, p.ratingCount, p.available,
                CONCAT('%s', clientMedia.filePath), CONCAT('%s', profMedia.filePath),
                CONCAT(u.firstName, ' ', u.lastName)
            )
            FROM User u
            LEFT JOIN Professional p ON u.id = p.user.id
            LEFT JOIN Media clientMedia ON u.id = clientMedia.user.id AND clientMedia.mediaType = com.yotelohago.media.domain.MediaType.CLIENT_PROFILE_PICTURE
            LEFT JOIN Media profMedia ON u.id = profMedia.user.id AND profMedia.mediaType = com.yotelohago.media.domain.MediaType.PROFESSIONAL_PROFILE_PICTURE
            WHERE u.id = :id
            """.formatted(mediaUrlResolver, mediaUrlResolver);

        return entityManager.createQuery(jpql, UserWithDetailsDTO.class)
                .setParameter("id", id)
                .getResultStream()
                .findFirst();
    }

    @Override
    public Optional<UserWithDetailsDTO> findByEmail(String email) {
        String jpql = """
            SELECT new com.yotelohago.user.api.UserWithDetailsDTO(
                u.id, u.username, u.firstName, u.lastName, u.email, u.phone,
                u.isProfessional, u.createdAt, u.updatedAt,
                p.bio, p.city, p.avgRating, p.ratingCount, p.available,
                CONCAT('%s', clientMedia.filePath), CONCAT('%s', profMedia.filePath),
                CONCAT(u.firstName, ' ', u.lastName)
            )
            FROM User u
            LEFT JOIN Professional p ON u.id = p.user.id
            LEFT JOIN Media clientMedia ON u.id = clientMedia.user.id AND clientMedia.mediaType = com.yotelohago.media.domain.MediaType.CLIENT_PROFILE_PICTURE
            LEFT JOIN Media profMedia ON u.id = profMedia.user.id AND profMedia.mediaType = com.yotelohago.media.domain.MediaType.PROFESSIONAL_PROFILE_PICTURE
            WHERE u.email = :email
            """.formatted(mediaUrlResolver, mediaUrlResolver);

        return entityManager.createQuery(jpql, UserWithDetailsDTO.class)
                .setParameter("email", email)
                .getResultStream()
                .findFirst();
    }

    @Override
    public List<UserWithDetailsDTO> findAll() {
        String jpql = """
            SELECT new com.yotelohago.user.api.UserWithDetailsDTO(
                u.id, u.username, u.firstName, u.lastName, u.email, u.phone,
                u.isProfessional, u.createdAt, u.updatedAt,
                p.bio, p.city, p.avgRating, p.ratingCount, p.available,
                CONCAT('%s', clientMedia.filePath), CONCAT('%s', profMedia.filePath),
                CONCAT(u.firstName, ' ', u.lastName)
            )
            FROM User u
            LEFT JOIN Professional p ON u.id = p.user.id
            LEFT JOIN Media clientMedia ON u.id = clientMedia.user.id AND clientMedia.mediaType = com.yotelohago.media.domain.MediaType.CLIENT_PROFILE_PICTURE
            LEFT JOIN Media profMedia ON u.id = profMedia.user.id AND profMedia.mediaType = com.yotelohago.media.domain.MediaType.PROFESSIONAL_PROFILE_PICTURE
            ORDER BY u.createdAt DESC
            """.formatted(mediaUrlResolver, mediaUrlResolver);

        return entityManager.createQuery(jpql, UserWithDetailsDTO.class)
                .getResultList();
    }

    @Override
    public User update(User user) {
        return entityManager.merge(user);
    }

    @Override
    public void deleteById(UUID id) {
        User user = entityManager.find(User.class, id);
        if (user != null) {
            entityManager.remove(user);
        }
    }

    @Override
    public void flush() {
        entityManager.flush();
    }

    // Entity methods for internal operations
    @Override
    public Optional<User> findEntityById(UUID id) {
        User user = entityManager.find(User.class, id);
        return Optional.ofNullable(user);
    }

    @Override
    public Optional<User> findEntityByEmail(String email) {
        return entityManager.createQuery("FROM User WHERE email = :email", User.class)
                .setParameter("email", email)
                .getResultStream()
                .findFirst();
    }

    @Override
    public List<User> findAllEntities() {
        return entityManager.createQuery("FROM User ORDER BY createdAt DESC", User.class)
                .getResultList();
    }

    // Pagination methods for enriched users with professional and media details
    @Override
    public PageResponse<UserWithDetailsDTO> findAllWithDetailsPaginated(PageRequest pageRequest) {
        logger.info("Executing findAllWithDetailsPaginated with page={}, size={}", pageRequest.getPage(), pageRequest.getSize());

        String jpql = """
            SELECT new com.yotelohago.user.api.UserWithDetailsDTO(
                u.id, u.username, u.firstName, u.lastName, u.email, u.phone,
                u.isProfessional, u.createdAt, u.updatedAt,
                p.bio, p.city, p.avgRating, p.ratingCount, p.available,
                CONCAT('%s', clientMedia.filePath), CONCAT('%s', profMedia.filePath),
                CONCAT(u.firstName, ' ', u.lastName)
            )
            FROM User u
            LEFT JOIN Professional p ON u.id = p.user.id
            LEFT JOIN Media clientMedia ON u.id = clientMedia.user.id AND clientMedia.mediaType = com.yotelohago.media.domain.MediaType.CLIENT_PROFILE_PICTURE
            LEFT JOIN Media profMedia ON u.id = profMedia.user.id AND profMedia.mediaType = com.yotelohago.media.domain.MediaType.PROFESSIONAL_PROFILE_PICTURE
            ORDER BY u.createdAt DESC
            """.formatted(mediaUrlResolver, mediaUrlResolver);

        logger.debug("Generated JPQL query: {}", jpql);

        try {
            TypedQuery<UserWithDetailsDTO> query = entityManager.createQuery(jpql, UserWithDetailsDTO.class);
            query.setFirstResult(pageRequest.getOffset());
            query.setMaxResults(pageRequest.getSize());

            logger.debug("Executing query with offset={}, maxResults={}", pageRequest.getOffset(), pageRequest.getSize());
            List<UserWithDetailsDTO> content = query.getResultList();
            logger.info("Query returned {} users", content.size());

            // Count total elements
            logger.debug("Counting total users");
            Long totalElements = entityManager.createQuery("SELECT COUNT(u) FROM User u", Long.class)
                    .getSingleResult();
            logger.info("Total users in database: {}", totalElements);

            PageResponse<UserWithDetailsDTO> result = PageResponse.of(content, totalElements, pageRequest.getPage(), pageRequest.getSize());
            logger.info("Returning page response with {} items out of {} total", content.size(), totalElements);

            return result;
        } catch (Exception e) {
            logger.error("Error executing findAllWithDetailsPaginated query", e);
            throw e;
        }
    }

    @Override
    public PageResponse<UserWithDetailsDTO> findByProfessionalStatusWithDetailsPaginated(boolean isProfessional, PageRequest pageRequest) {
        String jpql = """
            SELECT new com.yotelohago.user.api.UserWithDetailsDTO(
                u.id, u.username, u.firstName, u.lastName, u.email, u.phone,
                u.isProfessional, u.createdAt, u.updatedAt,
                p.bio, p.city, p.avgRating, p.ratingCount, p.available,
                CONCAT('%s', clientMedia.filePath), CONCAT('%s', profMedia.filePath),
                CONCAT(u.firstName, ' ', u.lastName)
            )
            FROM User u
            LEFT JOIN Professional p ON u.id = p.user.id
            LEFT JOIN Media clientMedia ON u.id = clientMedia.user.id AND clientMedia.mediaType = com.yotelohago.media.domain.MediaType.CLIENT_PROFILE_PICTURE
            LEFT JOIN Media profMedia ON u.id = profMedia.user.id AND profMedia.mediaType = com.yotelohago.media.domain.MediaType.PROFESSIONAL_PROFILE_PICTURE
            WHERE u.isProfessional = :isProfessional
            ORDER BY u.createdAt DESC
            """.formatted(mediaUrlResolver, mediaUrlResolver);

        TypedQuery<UserWithDetailsDTO> query = entityManager.createQuery(jpql, UserWithDetailsDTO.class);
        query.setParameter("isProfessional", isProfessional);
        query.setFirstResult(pageRequest.getOffset());
        query.setMaxResults(pageRequest.getSize());

        List<UserWithDetailsDTO> content = query.getResultList();

        // Count total elements
        Long totalElements = entityManager.createQuery(
                "SELECT COUNT(u) FROM User u WHERE u.isProfessional = :isProfessional", Long.class)
                .setParameter("isProfessional", isProfessional)
                .getSingleResult();

        return PageResponse.of(content, totalElements, pageRequest.getPage(), pageRequest.getSize());
    }
}