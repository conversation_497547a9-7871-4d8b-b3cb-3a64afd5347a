.PHONY: check_requirements load_secrets \
		parse_terraform_backend_vars parse_terraform_backend_vars parse_terraform_main_backend_vars generate_all \
		tf_init_backend tf_backend_destroy \
		tf_infra_init tf_infra_plan tf_infra_apply tf_infra_output \
		tf_helm_init tf_helm_install_crds tf_helm_plan tf_helm_apply tf_helm_destroy tf_helm_uninstall_crds tf_helm_output\
		deploy_all destroy_all \
		deploy_k3s_ec2 deploy_k3s_macos \
		clean help\

REQUIRED_TOOLS := terraform helm kubectl jq yq direnv hcp ansible-playbook kubectx

JENKINS_CONFIG_FILE_INPUT := ./config/jenkins.yaml

TERRAFORM_INFRA_DIR := ./terraform_infra

BACKEND_INFRA_CONFIG_FILE_DIR := ./terraform_infra/backend
BACKEND_INFRA_CONFIG_FILE_OUTPUT := config/terraform_infra_backend.hcl.generated
TERRAFORM_BACKEND_VARS := ./config/terraform_backend_vars.generated
TERRAFORM_BACKEND_VARS_LIST := region bucket_name profile bucket_key_terraform_infra bucket_key_terraform_helm dynamodb_table_name # Define variables we want to use for each stage

TERRAFORM_MAIN_BACKEND_VARS := ./config/terraform_main_backend_vars.generated
TERRAFORM_MAIN_VARS_LIST := region resource_base_name instance_type ec2_key_path ec2_user repository_name tailscale_dns_nameserver# Define variables we want to use for each stage

TERRAFORM_HELM_DIR := ./terraform_helm
BACKEND_HELM_CONFIG_FILE_OUTPUT := config/terraform_helm_backend.hcl.generated
TERRAFORM_HELM_VARS := ./config/terraform_helm_vars.generated
TERRAFORM_HELM_VARS_LIST := helm_argocd_version k8s_kubeconfig_path repository_name image_tag region# Define variables we want to use for each stage


# ANSIBLE vars
ANSIBLE_HOSTS_FILE := ./config/ansible_hosts.ini.generated
ANSIBLE_PLAYBOOK := ./ansible/site.yml
ANSIBLE_INVENTORY := ./ansible/inventory

# Configurable mode: "vault" or "local"
SECRETS_MODE ?= vault
SECRETS_GROUPS := TF_VAR_cloudflare_api_token TF_VAR_tailscale_api_key #Please use the same format from vault and local so terraform can detect the secret correctly
SECRETS_DIR := config/secrets
SECRETS_FILE := $(SECRETS_DIR)/.env.generated
ENVRC_FILE := .envrc


# DRY function to check if a tool exists
define check_tool
	@for tool in $(1); do \
		if command -v $$tool >/dev/null 2>&1; then \
			echo "✅ Tool '$$tool' is installed."; \
		else \
			echo "❌ Required tool '$$tool' is not installed. Aborting."; \
			exit 1; \
		fi; \
	done
endef

# Function to convert a space-separated list into yq filter format
define format_keys
$(foreach key,$(1),.key == "$(key)" or) .key == "dummy"
endef

define safe_remove
    @if [ -f "$(1)" ]; then \
        rm "$(1)"; \
        echo "File removed: $(1)"; \
    else \
        echo "File did not exist: $(1)"; \
    fi
endef

# Preparing keys to be used in the yq command (no quotes, properly formatted)
SELECT_BACKEND_KEYS := $(call format_keys,$(TERRAFORM_BACKEND_VARS_LIST))
SELECT_MAIN_KEYS := $(call format_keys,$(TERRAFORM_MAIN_VARS_LIST))

# Function to parse yaml based on a list of keys and section
define parse_yaml
	@echo "Parsing YAML file: $(JENKINS_CONFIG_FILE_INPUT) for $(1)"
	yq eval '.${1} | to_entries | map(select(${2})) | .[] | "-var " + .key + "=" + .value' $(JENKINS_CONFIG_FILE_INPUT) | xargs
endef

help: ## Show this help.
	@awk 'BEGIN {FS = ":.*##"; printf "\n\033[1mAvailable targets:\033[0m\n"} \
		/^[a-zA-Z0-9_-]+:.*##/ { printf "  \033[36m%-25s\033[0m %s\n", $$1, $$2 }' $(MAKEFILE_LIST)

parse_terraform_backend_vars:
	$(call safe_remove,$(TERRAFORM_BACKEND_VARS))
	$(call parse_yaml,terraform,$(call format_keys,$(TERRAFORM_BACKEND_VARS_LIST))) > $(TERRAFORM_BACKEND_VARS)

parse_terraform_main_backend_vars:
	$(call safe_remove,$(TERRAFORM_MAIN_BACKEND_VARS))
	$(call parse_yaml,terraform,$(call format_keys,$(TERRAFORM_MAIN_VARS_LIST))) > $(TERRAFORM_MAIN_BACKEND_VARS)

parse_terraform_helm_vars:
	$(call safe_remove,$(TERRAFORM_HELM_VARS))
	$(call parse_yaml,terraform,$(call format_keys,$(TERRAFORM_HELM_VARS_LIST))) > $(TERRAFORM_HELM_VARS)

generate_all: parse_terraform_backend_vars parse_terraform_main_backend_vars parse_terraform_helm_vars

test:
	echo "bucket = \"$(shell terraform -chdir=$(BACKEND_INFRA_CONFIG_FILE_DIR) output -raw bucket)\""


### TERRAFORM INFRA FOR EC2, NETWORKING, STORAGE, ETC. ###

tf_init_backend: generate_all
	@echo "Initializing Terraform backend"
	terraform -chdir=$(BACKEND_INFRA_CONFIG_FILE_DIR) init -reconfigure
	terraform -chdir=$(BACKEND_INFRA_CONFIG_FILE_DIR) apply --auto-approve $$(cat $(TERRAFORM_BACKEND_VARS))

tf_backend_destroy: generate_all
	terraform -chdir=$(TERRAFORM_HELM_DIR) destroy  --auto-approve $$(cat $(TERRAFORM_HELM_VARS))

tf_infra_init: tf_init_backend ## Initialize terraform.
	$(call safe_remove,$(BACKEND_INFRA_CONFIG_FILE_OUTPUT))
	echo "bucket = \"$(shell terraform -chdir=$(BACKEND_INFRA_CONFIG_FILE_DIR) output -raw bucket)\"" >> $(BACKEND_INFRA_CONFIG_FILE_OUTPUT)
	echo "dynamodb_table = \"$(shell terraform -chdir=$(BACKEND_INFRA_CONFIG_FILE_DIR) output -raw dynamodb_table)\"" >> $(BACKEND_INFRA_CONFIG_FILE_OUTPUT)
	echo "region = \"$(shell terraform -chdir=$(BACKEND_INFRA_CONFIG_FILE_DIR) output -raw region)\"" >> $(BACKEND_INFRA_CONFIG_FILE_OUTPUT)
	echo "key = \"$(shell terraform -chdir=$(BACKEND_INFRA_CONFIG_FILE_DIR) output -raw bucket_key_terraform_infra)\"" >> $(BACKEND_INFRA_CONFIG_FILE_OUTPUT)

	@echo "Initializing Terraform main deployment"
	terraform -chdir=$(TERRAFORM_INFRA_DIR) init -backend-config=../$(BACKEND_INFRA_CONFIG_FILE_OUTPUT) -reconfigure
	echo "Terraform Initialization Completed"

tf_infra_plan: generate_all ## Plan terraform.
	terraform -chdir=$(TERRAFORM_INFRA_DIR) plan $$(cat $(TERRAFORM_MAIN_BACKEND_VARS))

tf_infra_apply: generate_all ## Apply terraform.
	terraform -chdir=$(TERRAFORM_INFRA_DIR) apply --auto-approve $$(cat $(TERRAFORM_MAIN_BACKEND_VARS))

tf_infra_destroy: generate_all ## Destroy terraform.
	terraform -chdir=$(TERRAFORM_INFRA_DIR) destroy $$(cat $(TERRAFORM_MAIN_BACKEND_VARS))

tf_infra_output: generate_all ## Output terraform.
	terraform -chdir=$(TERRAFORM_INFRA_DIR) output

# DANGEROUS ZONE !!! DELETE ALL RESOURCE RELATED TO THE PLATFORM
destroy_all: generate_all ## Destroy Helm Terraform and Terraform backend.
	@$(MAKE) tf_helm_destroy
	@$(MAKE) tf_infra_destroy
	@$(MAKE) tf_backend_destroy


deploy_k3s_macos: ## Deploy k3s on MacOS using ansible role.
	#ansible-playbook -K $(ANSIBLE_PLAYBOOK) -i $(ANSIBLE_INVENTORY) --limit "remote" --tags "macos_k8s" --extra-vars "host=************* user=macmini1" --ask-pass
	#ansible-playbook -K $(ANSIBLE_PLAYBOOK) -i $(ANSIBLE_INVENTORY) --limit "remote" --tags "k3s" --extra-vars "key=/~/.lima/_config/user port=52591 host=************* user=macmini1" --ask-pass
	ansible-playbook $(ANSIBLE_PLAYBOOK) -i $(ANSIBLE_INVENTORY) --limit "remote" --tags "k3s" --extra-vars "host=macmini1-vm" --ask-pass

deploy_k3s_ec2: ## Deploy k3s on EC2 using ansible role.
	ansible-playbook $(ANSIBLE_PLAYBOOK) -i $(ANSIBLE_INVENTORY) --limit "ec2" --tags "k3s" --extra-vars "host=$$(terraform -chdir=$(TERRAFORM_INFRA_DIR) output -raw ec2_ip) user=ec2-user key=$$(terraform -chdir=$(TERRAFORM_INFRA_DIR) output -raw ec2_key_path)"

### TERRAFORM HELM FOR ARGOCD, JENKINS AND K8S RESOURCES ###
tf_helm_init: tf_init_backend ## Init Helm Terraform.
	$(call safe_remove,$(BACKEND_HELM_CONFIG_FILE_OUTPUT))
	echo "bucket = \"$(shell terraform -chdir=$(BACKEND_INFRA_CONFIG_FILE_DIR) output -raw bucket)\"" >> $(BACKEND_HELM_CONFIG_FILE_OUTPUT)
	echo "dynamodb_table = \"$(shell terraform -chdir=$(BACKEND_INFRA_CONFIG_FILE_DIR) output -raw dynamodb_table)\"" >> $(BACKEND_HELM_CONFIG_FILE_OUTPUT)
	echo "region = \"$(shell terraform -chdir=$(BACKEND_INFRA_CONFIG_FILE_DIR) output -raw region)\"" >> $(BACKEND_HELM_CONFIG_FILE_OUTPUT)
	echo "key = \"$(shell terraform -chdir=$(BACKEND_INFRA_CONFIG_FILE_DIR) output -raw bucket_key_terraform_helm)\"" >> $(BACKEND_HELM_CONFIG_FILE_OUTPUT)

	@echo "Initializing Terraform HELM deployment"
	terraform -chdir=$(TERRAFORM_HELM_DIR) init -backend-config=../$(BACKEND_HELM_CONFIG_FILE_OUTPUT) -reconfigure
	echo "Terraform HELM Initialization Completed"

tf_helm_install_crds: ## Install CRDs. Hint: Prerequisite for installing some Helm Terraform charts.
	@echo "🚀 Installing cert-manager with CRDs..."
	helm repo add jetstack https://charts.jetstack.io
	helm repo add cnpg https://cloudnative-pg.github.io/charts
	helm repo update
	helm upgrade --install cert-manager jetstack/cert-manager \
	  --namespace cert-manager \
	  --create-namespace \
	  --set crds.enabled=true \
	  --reuse-values \
	  --set extraArgs="{--dns01-recursive-nameservers=1.1.1.1:53,8.8.8.8:53,--dns01-recursive-nameservers-only}" \
	  --wait
	helm upgrade --install cnpg-operator cnpg/cloudnative-pg \
        --namespace cnpg-system \
        --create-namespace \
        --wait

tf_helm_plan: generate_all ## Helm Terraform plan.
	terraform -chdir=$(TERRAFORM_HELM_DIR) plan $$(cat $(TERRAFORM_HELM_VARS))

tf_helm_apply: generate_all ## Helm Terraform apply with CRDs.
	#@$(MAKE) tf_helm_install_crds
	terraform -chdir=$(TERRAFORM_HELM_DIR) apply --auto-approve $$(cat $(TERRAFORM_HELM_VARS))

tf_helm_destroy: generate_all ## Helm Terraform destroy with CRDs.
	terraform -chdir=$(TERRAFORM_HELM_DIR) destroy $$(cat $(TERRAFORM_HELM_VARS))
	@$(MAKE) tf_helm_uninstall_crds

tf_helm_uninstall_crds: ## Uninstall CRDs. Hint: Prerequisite for uninstalling some Helm Terraform charts.
	@echo "🔥 Uninstalling cert-manager and CRDs..."
	helm uninstall cnpg-operator -n cnpg-system || true
	helm uninstall cert-manager --namespace cert-manager || true
	kubectl delete crds \
      backups.postgresql.cnpg.io \
      clusters.postgresql.cnpg.io \
      scheduledbackups.postgresql.cnpg.io \
      poolers.postgresql.cnpg.io
	kubectl delete crds \
	  certificaterequests.cert-manager.io \
	  certificates.cert-manager.io \
	  challenges.acme.cert-manager.io \
	  clusterissuers.cert-manager.io \
	  issuers.cert-manager.io \
	  orders.acme.cert-manager.io || true
	kubectl delete namespace cnpg-system || true
	kubectl delete namespace cert-manager || true

tf_helm_output: generate_all  ## Output Helm terraform.
	terraform -chdir=$(TERRAFORM_HELM_DIR) output

jenkins_pass:  ## Print Jenkins password using kubectl command.
	kubectl get secret jenkins -n jenkins -o jsonpath="{.data.jenkins-admin-password}" | base64 -d && echo



### Deploy everything ###
deploy_all: tf_infra_init tf_infra_apply deploy_k3s_ec2 tf_helm_init tf_helm_apply ## Deploy k3s on ec2.


check_requirements: ## Check required tools for proper functioning.
	@echo "🔍 Checking required tools..."
	$(call check_tool,$(REQUIRED_TOOLS))
	@echo "✅ All required tools are available."

load_secrets: check_requirements ## Load secrets. Either from local or Vault Cloud.
	@echo "📁 Creating secrets directory if not exists..."
	@mkdir -p $(SECRETS_DIR)

	$(call safe_remove,$(SECRETS_FILE))
	$(call safe_remove,$(ENVRC_FILE))

	@echo "📥 Generating $(SECRETS_FILE)..."
	@echo "# Auto-generated by make load_secrets" > $(SECRETS_FILE)

ifeq ($(SECRETS_MODE),vault)
	@echo "🔐 Loading secrets from Vault via HCP..."
	@hcp profile init --vault-secrets || true
	@$(foreach group,$(SECRETS_GROUPS),\
		echo "## $(group)" >> $(SECRETS_FILE); \
		VALUE=$$(hcp vault-secrets secrets open $(group) | grep "^Value:" | sed 's/^Value:[[:space:]]*//'); \
		echo "export $(group)=$$VALUE" >> $(SECRETS_FILE);)
else ifeq ($(SECRETS_MODE),local)
	@echo "📦 Loading secrets from local .env files..."
	@$(foreach group,$(SECRETS_GROUPS),\
		[ -f $(SECRETS_DIR)/local/$(group).env ] && \
		cat $(SECRETS_DIR)/local/$(group).env >> $(SECRETS_FILE) || \
		echo "⚠️  Missing $(SECRETS_DIR)/local/$(group).env";)
else
	@echo "❌ Unknown SECRETS_MODE: $(SECRETS_MODE)"; exit 1
endif
	@echo "📝 Setting up .envrc for direnv..."
	@if [ ! -f $(ENVRC_FILE) ]; then \
		echo 'source $(SECRETS_FILE)' > $(ENVRC_FILE); \
		echo "✅ Created .envrc"; \
	fi

	@echo "🔓 Allowing direnv to load secrets..."
	@direnv allow .

	@echo "⚙️  Ensuring direnv is hooked into your shell..."
	@if ! grep -q 'eval "$$(direnv hook zsh)"' ~/.zshrc; then \
		echo '\n# Enable direnv\nexport DIRENV_LOG_FORMAT=""\neval "$$(direnv hook zsh)"' >> ~/.zshrc && \
		echo "✅ Added direnv hook to ~/.zshrc. Please restart your terminal or run 'source ~/.zshrc'."; \
	else \
		echo "ℹ️  direnv hook already present in ~/.zshrc."; \
	fi

	@echo "✅ Secrets loaded via direnv from $(SECRETS_FILE)"


clean: ## Remove all generated files. Terraform files/folders. Config files in ./config
	rm -rf ./config/*.generated "$(TERRAFORM_INFRA_DIR)/.terraform" "$(TERRAFORM_INFRA_DIR)/.terraform.lock.hcl" "$(TERRAFORM_INFRA_DIR)/terraform.tfstate*" "$(TERRAFORM_HELM_DIR)/.terraform" "$(TERRAFORM_HELM_DIR)/.terraform.lock.hcl" "$(TERRAFORM_HELM_DIR)/terraform.tfstate*" "$(BACKEND_INFRA_CONFIG_FILE_DIR)/.terraform" "$(BACKEND_INFRA_CONFIG_FILE_DIR)/.terraform.lock.hcl" $(BACKEND_INFRA_CONFIG_FILE_DIR)/terraform.tfstate*