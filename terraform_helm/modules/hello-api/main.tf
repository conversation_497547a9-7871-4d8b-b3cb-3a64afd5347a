terraform {
  required_providers {
    kubernetes = {
      source  = "hashicorp/kubernetes"
    }
    helm = {
      source  = "hashicorp/helm"
    }
  }
}


resource "kubernetes_namespace" "hello-api" {
  metadata {
    name = "hello"
  }
}

resource "helm_release" "hello_api" {
  name       = "hello-api"
  chart      = path.module
  namespace  = kubernetes_namespace.hello-api.metadata[0].name
  create_namespace = false

  depends_on = [kubernetes_namespace.hello-api]
}