package com.yotelohago.user.application;

import com.yotelohago.user.api.UserRegistrationDTO;
import com.yotelohago.user.api.UserRegistrationResponseDTO;
import com.yotelohago.user.domain.AuthProvider;
import com.yotelohago.user.domain.User;
import com.yotelohago.user.domain.UserRepository;
import com.yotelohago.user.domain.UserExternalIdentity;
import com.yotelohago.user.domain.UserExternalIdentityRepository;
import com.yotelohago.user.infrastructure.KeycloakAdminService;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Instant;
import java.util.UUID;

/**
 * Service for handling user registration
 * Creates users in both Keycloak and the local database
 */
@ApplicationScoped
public class UserRegistrationService {

    private static final Logger logger = LoggerFactory.getLogger(UserRegistrationService.class);

    @Inject
    UserRepository userRepository;

    @Inject
    UserExternalIdentityRepository externalIdentityRepository;

    @Inject
    KeycloakAdminService keycloakAdminService;

    /**
     * Register a new user
     * 
     * @param registrationDTO User registration data
     * @return Registration response with user details
     * @throws IllegalArgumentException if validation fails
     * @throws RuntimeException if registration fails
     */
    @Transactional
    public UserRegistrationResponseDTO registerUser(UserRegistrationDTO registrationDTO) {
        logger.info("Starting user registration for email: {}", registrationDTO.email);

        // Validate input
        validateRegistrationData(registrationDTO);

        // Check if user already exists in our database
        if (userRepository.findByEmail(registrationDTO.email).isPresent()) {
            throw new IllegalArgumentException("User with this email already exists in our system");
        }

        try {
            // Step 1: Create user in Keycloak
            UUID keycloakUserId = keycloakAdminService.createUser(
                registrationDTO.email,
                registrationDTO.password,
                registrationDTO.firstName,
                registrationDTO.lastName
            );

            // Step 2: Create user in our database
            User newUser = createUserInDatabase(keycloakUserId, registrationDTO);

            // TODO: Step 3: Send email verification when implementing email verification
            // emailVerificationService.sendVerificationEmail(newUser.email, verificationToken);

            logger.info("User registration completed successfully: email={}, keycloakId={}, internalId={}",
                       registrationDTO.email, keycloakUserId, newUser.id);

            return UserRegistrationResponseDTO.success(
                newUser.id,
                newUser.email,
                newUser.firstName,
                newUser.lastName
            );

        } catch (RuntimeException e) {
            logger.error("User registration failed for email: {}", registrationDTO.email, e);
            
            // Re-throw with user-friendly message
            if (e.getMessage().contains("already exists")) {
                throw new IllegalArgumentException("An account with this email already exists");
            } else {
                throw new RuntimeException("Registration failed. Please try again later.");
            }
        }
    }

    private void validateRegistrationData(UserRegistrationDTO registrationDTO) {
        // Basic validation
        if (registrationDTO.email == null || registrationDTO.email.trim().isEmpty()) {
            throw new IllegalArgumentException("Email is required");
        }

        if (registrationDTO.password == null || registrationDTO.password.length() < 8) {
            throw new IllegalArgumentException("Password must be at least 8 characters long");
        }

        if (registrationDTO.firstName == null || registrationDTO.firstName.trim().isEmpty()) {
            throw new IllegalArgumentException("First name is required");
        }

        if (registrationDTO.lastName == null || registrationDTO.lastName.trim().isEmpty()) {
            throw new IllegalArgumentException("Last name is required");
        }

        // Email format validation
        if (!registrationDTO.email.matches("^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$")) {
            throw new IllegalArgumentException("Invalid email format");
        }
    }

    private User createUserInDatabase(UUID keycloakUserId, UserRegistrationDTO registrationDTO) {
        Instant now = Instant.now();
        UUID internalUserId = UUID.randomUUID();

        // Create user with internal ID only
        User newUser = new User(
            internalUserId, // Generate new internal ID
            registrationDTO.email, // Use email as username initially
            registrationDTO.firstName,
            registrationDTO.lastName,
            registrationDTO.email,
            null, // phone will be set later by user
            false, // isProfessional - users start as regular users
            now,   // createdAt
            now    // updatedAt
        );

        userRepository.persist(newUser);

        // Create external identity mapping
        UserExternalIdentity externalIdentity = new UserExternalIdentity(
            newUser,
            AuthProvider.KEYCLOAK,
            keycloakUserId.toString()
        );

        externalIdentityRepository.persist(externalIdentity);
        userRepository.flush(); // Ensure both are persisted immediately

        logger.info("Created user in database: keycloakId={}, internalId={}, email={}",
                   keycloakUserId, internalUserId, registrationDTO.email);

        return newUser;
    }
}
