import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert, StatusBar, ActivityIndicator, Modal } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import Colors from '../constants/Colors';
import { useProfessionalServices } from '../../../hooks/useProfessionalServices';
import { ServiceDTO, CreateServiceRequest, UpdateServiceRequest } from '../../../types/api';
import { AuthGuard } from '../../../components/auth';
import AddListingModal from '../components/AddListingModal';
import EditListingModal from '../components/EditListingModal';

export default function ListingsScreen() {
  // Use the professional services hook for real data
  const {
    services,
    isLoading,
    isSaving,
    error,
    deleteService,
    toggleServiceStatus,
    clearError,
    refresh,
    statistics,
    createService,
    updateService,
  } = useProfessionalServices();

  // Modal states
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedService, setSelectedService] = useState<ServiceDTO | null>(null);
  const [serviceToDelete, setServiceToDelete] = useState<ServiceDTO | null>(null);

  const handleCreateListing = () => {
    setShowAddModal(true);
  };

  const handleEditListing = (serviceId: string) => {
    const service = services.find(s => s.id === serviceId);
    if (service) {
      setSelectedService(service);
      setShowEditModal(true);
    }
  };

  const handleCreateService = async (serviceData: CreateServiceRequest) => {
    try {
      const newService = await createService(serviceData);
      console.log('✅ Service created successfully:', newService);

      // Refresh the listings to show the new service
      await refresh();

      // Show success message
      Alert.alert('Success', 'Listing created successfully!');
    } catch (error) {
      console.error('Failed to create service:', error);
      throw error; // Re-throw to let modal handle the error
    }
  };

  const handleUpdateService = async (serviceId: string, serviceData: UpdateServiceRequest) => {
    try {
      const updatedService = await updateService(serviceId, serviceData);
      console.log('✅ Service updated successfully:', updatedService);

      // Refresh the listings to show the updated service
      await refresh();

      // Show success message
      Alert.alert('Success', 'Listing updated successfully!');
    } catch (error) {
      console.error('Failed to update service:', error);
      throw error; // Re-throw to let modal handle the error
    }
  };

  const handleDeleteListing = async (serviceId: string) => {
    console.log('🗑️ handleDeleteListing called with serviceId:', serviceId);
    console.log('🗑️ Available services:', services.map(s => ({ id: s.id, title: s.title })));

    const service = services.find(s => s.id === serviceId);
    console.log('🗑️ Found service:', service);

    if (service) {
      setServiceToDelete(service);
      setShowDeleteModal(true);
      console.log('🗑️ Showing delete confirmation modal');
    }
  };

  const handleConfirmDelete = async () => {
    if (!serviceToDelete) return;

    console.log('🗑️ User confirmed delete - starting delete process');
    setShowDeleteModal(false);

    try {
      console.log(`🗑️ Starting delete process for service: ${serviceToDelete.id}`);
      const success = await deleteService(serviceToDelete.id);

      if (success) {
        console.log('✅ Delete successful, refreshing listings');

        // Refresh the listings to remove the deleted service
        await refresh();

        // Show success message
        Alert.alert('Success', 'Listing deleted successfully');
      } else {
        console.log('⚠️ Delete returned false, service may not exist');
        Alert.alert('Warning', 'Service may have already been deleted');
      }
    } catch (error) {
      console.error('❌ Delete failed with error:', error);
      Alert.alert('Error', 'Failed to delete listing. Please try again.');
    } finally {
      setServiceToDelete(null);
    }
  };

  const handleCancelDelete = () => {
    console.log('🗑️ User cancelled delete operation');
    setShowDeleteModal(false);
    setServiceToDelete(null);
  };

  const handleToggleListingStatus = async (serviceId: string, currentStatus: string) => {
    try {
      const isCurrentlyPublished = currentStatus === 'published';
      await toggleServiceStatus(serviceId, !isCurrentlyPublished);

      const newStatus = isCurrentlyPublished ? 'draft' : 'published';
      Alert.alert('Success', `Listing ${newStatus} successfully`);
    } catch (error) {
      Alert.alert('Error', 'Failed to update listing status. Please try again.');
    }
  };

  const renderListing = (service: ServiceDTO) => {
    const isPublished = service.status === 'published';

    return (
      <View key={service.id} style={styles.listingCard}>
        <View style={styles.listingHeader}>
          <View style={styles.listingInfo}>
            <Text style={styles.listingTitle}>{service.title}</Text>
            <Text style={styles.listingCategory}>{service.categoryName || 'Service'}</Text>
          </View>
          <View style={styles.listingActions}>
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: Colors.primary + '20' }]}
              onPress={() => {
                console.log('✏️ Edit button pressed for service:', service.id);
                Alert.alert('Debug', `Edit button pressed for service: ${service.id}`);
                handleEditListing(service.id);
              }}
              disabled={isSaving}
            >
              <Ionicons name="pencil" size={20} color={Colors.primary} />
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: Colors.error + '20' }]}
              onPress={() => {
                console.log('🗑️ Delete button pressed for service:', service.id);
                console.log('🗑️ isSaving state:', isSaving);
                Alert.alert('Debug', `Delete button pressed for service: ${service.id}`);
                handleDeleteListing(service.id);
              }}
              disabled={isSaving}
            >
              <Ionicons name="trash" size={20} color={Colors.error} />
            </TouchableOpacity>
          </View>
        </View>

        <Text style={styles.listingDescription}>{service.description}</Text>

        <View style={styles.priceContainer}>
          <Text style={styles.price}>
            ${service.price} fixed price
          </Text>
        </View>

        <View style={styles.listingFooter}>
          <View style={styles.statusContainer}>
            <View style={[
              styles.statusDot,
              { backgroundColor: isPublished ? Colors.success : Colors.gray }
            ]} />
            <Text style={[
              styles.statusText,
              { color: isPublished ? Colors.success : Colors.gray }
            ]}>
              {isPublished ? 'Published' : 'Draft'}
            </Text>
          </View>

          <TouchableOpacity
            style={[
              styles.toggleButton,
              { backgroundColor: isPublished ? Colors.gray + '20' : Colors.primary + '20' }
            ]}
            onPress={() => handleToggleListingStatus(service.id, service.status || 'draft')}
            disabled={isSaving}
          >
            <Text style={[
              styles.toggleButtonText,
              { color: isPublished ? Colors.gray : Colors.primary }
            ]}>
              {isPublished ? 'Unpublish' : 'Publish'}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={Colors.background} />

      {/* Centered Header */}
      <View style={styles.centeredHeader}>
        <Text style={styles.centeredTitle}>Listings</Text>
      </View>

      <AuthGuard
        actionType="general"
        promptTitle="Manage Your Listings"
        promptMessage="Log in as a professional to create and manage your service listings."
        tabIcon="file-tray-stacked-outline"
      >
        {/* Error Message */}
        {error && (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>{error}</Text>
            <TouchableOpacity onPress={clearError}>
              <Text style={styles.errorDismiss}>Dismiss</Text>
            </TouchableOpacity>
          </View>
        )}

      <ScrollView style={styles.content}>
        {/* Statistics */}
        <View style={styles.statsContainer}>
          <View style={styles.statItem}>
            <Text style={styles.statNumber}>{statistics.total}</Text>
            <Text style={styles.statLabel}>Total Listings</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statNumber}>{statistics.published}</Text>
            <Text style={styles.statLabel}>Published</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statNumber}>{statistics.draft}</Text>
            <Text style={styles.statLabel}>Draft</Text>
          </View>
        </View>

        {/* Loading State */}
        {isLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={Colors.primary} />
            <Text style={styles.loadingText}>Loading your listings...</Text>
          </View>
        ) : services.length > 0 ? (
          services.map((service) => renderListing(service))
        ) : (
          <View style={styles.emptyState}>
            <Ionicons name="briefcase-outline" size={64} color={Colors.gray} />
            <Text style={styles.emptyStateText}>No listings yet</Text>
            <Text style={styles.emptyStateSubtext}>Create your first service listing to start receiving requests</Text>
            <TouchableOpacity style={styles.emptyStateButton} onPress={handleCreateListing}>
              <Text style={styles.emptyStateButtonText}>Create Listing</Text>
            </TouchableOpacity>
          </View>
        )}
        </ScrollView>

        {/* Floating Add Button */}
        <TouchableOpacity style={styles.floatingAddButton} onPress={handleCreateListing}>
          <Ionicons name="add" size={24} color="white" />
        </TouchableOpacity>
      </AuthGuard>

      {/* Add Listing Modal */}
      <AddListingModal
        visible={showAddModal}
        onClose={() => setShowAddModal(false)}
        onSave={handleCreateService}
        isLoading={isSaving}
      />

      {/* Edit Listing Modal */}
      <EditListingModal
        visible={showEditModal}
        service={selectedService}
        onClose={() => {
          setShowEditModal(false);
          setSelectedService(null);
        }}
        onSave={handleUpdateService}
        isLoading={isSaving}
      />

      {/* Delete Confirmation Modal */}
      <Modal
        visible={showDeleteModal}
        transparent={true}
        animationType="fade"
        onRequestClose={handleCancelDelete}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.deleteModalContainer}>
            <View style={styles.deleteModalHeader}>
              <Ionicons name="warning" size={24} color={Colors.error} />
              <Text style={styles.deleteModalTitle}>Delete Listing</Text>
            </View>

            <Text style={styles.deleteModalMessage}>
              Are you sure you want to delete "{serviceToDelete?.title}"?
            </Text>
            <Text style={styles.deleteModalSubMessage}>
              This action cannot be undone.
            </Text>

            <View style={styles.deleteModalButtons}>
              <TouchableOpacity
                style={[styles.deleteModalButton, styles.cancelButton]}
                onPress={handleCancelDelete}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.deleteModalButton, styles.deleteButton]}
                onPress={handleConfirmDelete}
                disabled={isSaving}
              >
                {isSaving ? (
                  <ActivityIndicator size="small" color={Colors.white} />
                ) : (
                  <Text style={styles.deleteButtonText}>Delete</Text>
                )}
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  centeredHeader: {
    backgroundColor: Colors.background.primary,
    paddingTop: 60,
    paddingBottom: 16,
    paddingHorizontal: 16,
    alignItems: 'center',
  },
  centeredTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: Colors.text.primary,
    textAlign: 'center',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: Colors.lightGray,
  },
  headerContent: {
    flex: 1,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    color: Colors.gray,
  },
  createButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: Colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  statsContainer: {
    flexDirection: 'row',
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.primary,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 14,
    color: Colors.gray,
  },
  listingCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  listingHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  listingInfo: {
    flex: 1,
  },
  listingTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 4,
  },
  listingCategory: {
    fontSize: 14,
    color: Colors.primary,
    fontWeight: '500',
  },
  listingActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: Colors.lightGray,
    justifyContent: 'center',
    alignItems: 'center',
  },
  listingDescription: {
    fontSize: 14,
    color: Colors.text,
    lineHeight: 20,
    marginBottom: 12,
  },
  priceContainer: {
    backgroundColor: Colors.primary + '20',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
    alignSelf: 'flex-start',
    marginBottom: 16,
  },
  price: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.primary,
  },
  listingFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  statusText: {
    fontSize: 14,
    fontWeight: '500',
  },
  toggleButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
  },
  toggleButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyStateText: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.gray,
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: Colors.gray,
    textAlign: 'center',
    marginBottom: 24,
  },
  emptyStateButton: {
    backgroundColor: Colors.primary,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  emptyStateButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  errorContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFE5E5',
    borderBottomWidth: 1,
    borderBottomColor: '#FFB3B3',
  },
  errorText: {
    flex: 1,
    fontSize: 14,
    color: '#CC0000',
  },
  errorDismiss: {
    fontSize: 14,
    fontWeight: '600',
    color: '#CC0000',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: Colors.gray,
  },
  // Delete Modal Styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  deleteModalContainer: {
    backgroundColor: Colors.white,
    borderRadius: 12,
    padding: 24,
    width: '100%',
    maxWidth: 400,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
  },
  deleteModalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  deleteModalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text.primary,
    marginLeft: 8,
  },
  deleteModalMessage: {
    fontSize: 16,
    color: Colors.text.primary,
    marginBottom: 8,
    lineHeight: 22,
  },
  deleteModalSubMessage: {
    fontSize: 14,
    color: Colors.text.secondary,
    marginBottom: 24,
  },
  deleteModalButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  deleteModalButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 44,
  },
  cancelButton: {
    backgroundColor: Colors.background.secondary,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.text.primary,
  },
  deleteButton: {
    backgroundColor: Colors.error,
  },
  deleteButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.white,
  },
  floatingAddButton: {
    position: 'absolute',
    bottom: 20, // Same distance as right margin
    right: 20,
    backgroundColor: '#000000', // Black for professional mode
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 5, // Android shadow
    shadowColor: '#000', // iOS shadow
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
});
