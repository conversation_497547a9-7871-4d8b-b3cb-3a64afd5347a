import React from 'react';
import { motion } from 'framer-motion';

const AnimatedLock: React.FC = () => {
  return (
    <div className="relative w-full h-full flex items-center justify-center">
      {/* Main Lock SVG - Responsive sizing */}
      <motion.svg
        width="100%"
        height="100%"
        viewBox="0 0 96 96"
        className="relative z-10 max-w-20 max-h-20 sm:max-w-24 sm:max-h-24"
        animate={{
          scale: [1, 1.02, 1],
        }}
        transition={{
          duration: 4,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      >
        <defs>
          {/* Lock Body Gradient - Orange to Yellow */}
          <linearGradient id="lock-body" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#FCD34D" />
            <stop offset="30%" stopColor="#F59E0B" />
            <stop offset="70%" stopColor="#F97316" />
            <stop offset="100%" stopColor="#EA580C" />
          </linearGradient>
          
          {/* Lock Body Shadow */}
          <linearGradient id="lock-shadow" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#EA580C" />
            <stop offset="100%" stopColor="#C2410C" />
          </linearGradient>
          
          {/* Shackle Gradient - Steel Gray */}
          <linearGradient id="shackle-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#E5E7EB" />
            <stop offset="30%" stopColor="#D1D5DB" />
            <stop offset="70%" stopColor="#9CA3AF" />
            <stop offset="100%" stopColor="#6B7280" />
          </linearGradient>
          
          {/* Keyhole Shadow */}
          <radialGradient id="keyhole-shadow" cx="50%" cy="50%" r="50%">
            <stop offset="0%" stopColor="#000000" />
            <stop offset="100%" stopColor="#374151" />
          </radialGradient>
          
          {/* Lock Body Highlight */}
          <linearGradient id="lock-highlight" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#FEF3C7" />
            <stop offset="50%" stopColor="#FCD34D" />
            <stop offset="100%" stopColor="#F59E0B" />
          </linearGradient>
        </defs>
        
        {/* Animated Shackle - Modern Rounded Design */}
        <motion.g
          animate={{
            rotate: [0, 25, 0],
            x: [0, 12, 0],
            y: [0, -4, 0]
          }}
          transition={{
            duration: 6,
            repeat: Infinity,
            ease: "easeInOut",
            times: [0, 0.5, 1]
          }}
          style={{ transformOrigin: '36px 50px' }}
        >
          {/* Shackle - Thick Rounded Design */}
          <path
            d="M36 28 Q48 16 60 28 L60 50 L56 50 L56 30 Q48 22 40 30 L40 50 L36 50 Z"
            fill="url(#shackle-gradient)"
            stroke="#9CA3AF"
            strokeWidth="0.5"
          />
          
          {/* Shackle Inner Highlight */}
          <path
            d="M38 30 Q48 20 58 30 L58 48 L56 48 L56 30 Q48 24 40 30 L40 48 L38 48 Z"
            fill="#F9FAFB"
            opacity="0.6"
          />
          
          {/* Shackle Outer Edge */}
          <path
            d="M36 28 Q48 16 60 28"
            stroke="#6B7280"
            strokeWidth="2"
            fill="none"
            strokeLinecap="round"
          />
        </motion.g>
        
        {/* Lock Body - Rounded Rectangle */}
        <rect
          x="28"
          y="50"
          width="40"
          height="32"
          fill="url(#lock-body)"
          rx="8"
          ry="8"
        />
        
        {/* Lock Body Shadow/Depth */}
        <rect
          x="24"
          y="50"
          width="6"
          height="32"
          fill="url(#lock-shadow)"
          rx="3"
        />
        
        {/* Lock Body Bottom Shadow */}
        <rect
          x="28"
          y="78"
          width="40"
          height="4"
          fill="url(#lock-shadow)"
          rx="8"
          opacity="0.7"
        />
        
        {/* Lock Body Top Highlight */}
        <rect
          x="30"
          y="52"
          width="36"
          height="6"
          fill="url(#lock-highlight)"
          rx="6"
          opacity="0.8"
        />
        
        {/* Lock Body Side Highlight */}
        <rect
          x="62"
          y="52"
          width="6"
          height="28"
          fill="#FEF3C7"
          rx="3"
          opacity="0.5"
        />
        
        {/* Keyhole Circle */}
        <circle
          cx="48"
          cy="64"
          r="4"
          fill="url(#keyhole-shadow)"
        />
        
        {/* Keyhole Slot */}
        <rect
          x="47"
          y="64"
          width="2"
          height="8"
          fill="url(#keyhole-shadow)"
          rx="1"
        />
        
        {/* Lock Mechanism - Internal Pins */}
        <motion.rect
          x="45"
          y="56"
          width="1"
          height="4"
          fill="#6B7280"
          animate={{
            y: [56, 54, 56],
            opacity: [0.5, 1, 0.5]
          }}
          transition={{
            duration: 6,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 2.5
          }}
        />
        
        <motion.rect
          x="47"
          y="56"
          width="1"
          height="3"
          fill="#6B7280"
          animate={{
            y: [56, 55, 56],
            opacity: [0.5, 1, 0.5]
          }}
          transition={{
            duration: 6,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 2.7
          }}
        />
        
        <motion.rect
          x="49"
          y="56"
          width="1"
          height="4"
          fill="#6B7280"
          animate={{
            y: [56, 54, 56],
            opacity: [0.5, 1, 0.5]
          }}
          transition={{
            duration: 6,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 2.9
          }}
        />
        
        <motion.rect
          x="51"
          y="56"
          width="1"
          height="3"
          fill="#6B7280"
          animate={{
            y: [56, 55, 56],
            opacity: [0.5, 1, 0.5]
          }}
          transition={{
            duration: 6,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 3.1
          }}
        />
        
        {/* Lock Brand Indicator */}
        <rect
          x="46"
          y="74"
          width="4"
          height="2"
          fill="#C2410C"
          rx="1"
          opacity="0.8"
        />
        
        {/* Lock Status LED */}
        <motion.circle
          cx="48"
          cy="58"
          r="1"
          animate={{
            fill: ["#EF4444", "#10B981", "#EF4444"],
            opacity: [0.6, 1, 0.6]
          }}
          transition={{
            duration: 6,
            repeat: Infinity,
            ease: "easeInOut",
            times: [0, 0.5, 1]
          }}
        />
      </motion.svg>
    </div>
  );
};

export default AnimatedLock;