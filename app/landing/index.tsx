import React, { useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  StyleSheet,
  Dimensions,
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { LinearGradient } from 'expo-linear-gradient';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withDelay,
} from 'react-native-reanimated';
import { ArrowRight } from 'lucide-react-native';
import { router } from 'expo-router';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

export default function LandingPage() {
  const logoScale = useSharedValue(0);
  const logoRotate = useSharedValue(-180);
  const titleOpacity = useSharedValue(0);
  const titleY = useSharedValue(50);
  const taglineOpacity = useSharedValue(0);
  const taglineY = useSharedValue(30);
  const statsOpacity = useSharedValue(0);
  const statsY = useSharedValue(40);
  const buttonOpacity = useSharedValue(0);
  const buttonScale = useSharedValue(0.8);
  const buttonY = useSharedValue(30);

  // Service icon scales for press animations
  const electricianScale = useSharedValue(1);
  const plumberScale = useSharedValue(1);
  const moverScale = useSharedValue(1);
  const locksmithScale = useSharedValue(1);

  const handleGetStarted = () => {
    // Navigate to client frontend
    router.push('/client');
  };

  const handleServicePress = (category: string) => {
    // Navigate to client frontend with category filter
    router.push(`/client?category=${category.toLowerCase()}`);
  };

  const handleServiceHoverIn = (service: string) => {
    switch(service) {
      case 'electricians':
        electricianScale.value = 1.2;
        break;
      case 'plumbers':
        plumberScale.value = 1.2;
        break;
      case 'movers':
        moverScale.value = 1.2;
        break;
      case 'locksmiths':
        locksmithScale.value = 1.2;
        break;
    }
  };

  const handleServiceHoverOut = (service: string) => {
    switch(service) {
      case 'electricians':
        electricianScale.value = 1;
        break;
      case 'plumbers':
        plumberScale.value = 1;
        break;
      case 'movers':
        moverScale.value = 1;
        break;
      case 'locksmiths':
        locksmithScale.value = 1;
        break;
    }
  };

  useEffect(() => {
    // Logo animation
    logoScale.value = withDelay(200, withSpring(1, { stiffness: 200, damping: 15 }));
    logoRotate.value = withDelay(200, withSpring(0, { stiffness: 200, damping: 15 }));

    // Title animation
    titleOpacity.value = withDelay(500, withSpring(1, { duration: 800 }));
    titleY.value = withDelay(500, withSpring(0, { duration: 800 }));

    // Tagline animation
    taglineOpacity.value = withDelay(800, withSpring(1, { duration: 800 }));
    taglineY.value = withDelay(800, withSpring(0, { duration: 800 }));

    // Stats animation
    statsOpacity.value = withDelay(1100, withSpring(1, { duration: 800 }));
    statsY.value = withDelay(1100, withSpring(0, { duration: 800 }));

    // Button animation
    buttonOpacity.value = withDelay(1400, withSpring(1, { duration: 800 }));
    buttonScale.value = withDelay(1400, withSpring(1, { stiffness: 200, damping: 15 }));
    buttonY.value = withDelay(1400, withSpring(0, { duration: 800 }));
  }, []);

  const logoAnimatedStyle = useAnimatedStyle(() => ({
    transform: [
      { scale: logoScale.value },
      { rotate: `${logoRotate.value}deg` }
    ],
  }));

  const titleAnimatedStyle = useAnimatedStyle(() => ({
    opacity: titleOpacity.value,
    transform: [{ translateY: titleY.value }],
  }));

  const taglineAnimatedStyle = useAnimatedStyle(() => ({
    opacity: taglineOpacity.value,
    transform: [{ translateY: taglineY.value }],
  }));

  const statsAnimatedStyle = useAnimatedStyle(() => ({
    opacity: statsOpacity.value,
    transform: [{ translateY: statsY.value }],
  }));

  const buttonAnimatedStyle = useAnimatedStyle(() => ({
    opacity: buttonOpacity.value,
    transform: [
      { scale: buttonScale.value },
      { translateY: buttonY.value }
    ],
  }));

  const electricianAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: electricianScale.value }],
  }));

  const plumberAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: plumberScale.value }],
  }));

  const moverAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: moverScale.value }],
  }));

  const locksmithAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: locksmithScale.value }],
  }));

  return (
    <View style={styles.container}>
      <StatusBar style="light" />
      <LinearGradient
        colors={['#0f172a', '#881c26', '#c1272d']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.gradient}
      >
        {/* Background Elements */}
        <View style={styles.backgroundElements}>
          <View style={[styles.bgCircle, styles.bgCircle1]} />
          <View style={[styles.bgCircle, styles.bgCircle2]} />
          <LinearGradient
            colors={['transparent', 'rgba(255, 90, 95, 0.05)', 'transparent']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            style={styles.bgGradient}
          />
        </View>

        <View style={styles.content}>
          {/* Logo */}
          <Animated.View style={[styles.logoContainer, logoAnimatedStyle]}>
            <View style={styles.logoWrapper}>
              <LinearGradient
                colors={['rgba(255, 255, 255, 0.2)', 'rgba(255, 255, 255, 0.05)']}
                style={styles.logoBackground}
              >
                <LinearGradient
                  colors={['rgba(255, 90, 95, 0.2)', 'rgba(255, 90, 95, 0.2)']}
                  style={styles.logoInnerGlow}
                />
                <Image
                  source={require('../../assets/images/logo.png')}
                  style={styles.logoImage}
                  resizeMode="cover"
                />
                <LinearGradient
                  colors={['rgba(255, 255, 255, 0.3)', 'transparent', 'transparent']}
                  style={styles.logoGloss}
                />
              </LinearGradient>
              <View style={styles.logoShadow} />
            </View>
          </Animated.View>

          {/* Brand Name */}
          <Animated.Text style={[styles.title, titleAnimatedStyle]}>
            Yotelohago
          </Animated.Text>

          {/* Tagline */}
          <Animated.Text style={[styles.tagline, taglineAnimatedStyle]}>
            Fast, Trusted Home Services
          </Animated.Text>

          {/* Service Icons */}
          <Animated.View style={[styles.statsContainer, statsAnimatedStyle]}>
            <TouchableOpacity
              style={styles.statItem}
              onPress={() => handleServicePress('Electricians')}
              onMouseEnter={() => handleServiceHoverIn('electricians')}
              onMouseLeave={() => handleServiceHoverOut('electricians')}
              activeOpacity={0.9}
            >
              <Animated.View style={[styles.serviceIconWrapper, electricianAnimatedStyle]}>
                <LinearGradient
                  colors={['rgba(255, 255, 255, 0.2)', 'rgba(255, 255, 255, 0.05)']}
                  style={styles.serviceIconBackground}
                >
                  <LinearGradient
                    colors={['rgba(255, 90, 95, 0.2)', 'rgba(255, 90, 95, 0.2)']}
                    style={styles.serviceIconInnerGlow}
                  />
                  <Image
                    source={require('../../assets/images/thunder.png')}
                    style={styles.serviceIcon}
                    resizeMode="contain"
                  />
                  <LinearGradient
                    colors={['rgba(255, 255, 255, 0.3)', 'transparent', 'transparent']}
                    style={styles.serviceIconGloss}
                  />
                </LinearGradient>
                <View style={styles.serviceIconShadow} />
              </Animated.View>
              <Text style={styles.statLabel}>Electricians</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.statItem}
              onPress={() => handleServicePress('Plumbers')}
              onMouseEnter={() => handleServiceHoverIn('plumbers')}
              onMouseLeave={() => handleServiceHoverOut('plumbers')}
              activeOpacity={0.9}
            >
              <Animated.View style={[styles.serviceIconWrapper, plumberAnimatedStyle]}>
                <LinearGradient
                  colors={['rgba(255, 255, 255, 0.2)', 'rgba(255, 255, 255, 0.05)']}
                  style={styles.serviceIconBackground}
                >
                  <LinearGradient
                    colors={['rgba(255, 90, 95, 0.2)', 'rgba(255, 90, 95, 0.2)']}
                    style={styles.serviceIconInnerGlow}
                  />
                  <Image
                    source={require('../../assets/images/water-leak.png')}
                    style={styles.serviceIcon}
                    resizeMode="contain"
                  />
                  <LinearGradient
                    colors={['rgba(255, 255, 255, 0.3)', 'transparent', 'transparent']}
                    style={styles.serviceIconGloss}
                  />
                </LinearGradient>
                <View style={styles.serviceIconShadow} />
              </Animated.View>
              <Text style={styles.statLabel}>Plumbers</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.statItem}
              onPress={() => handleServicePress('Movers')}
              onMouseEnter={() => handleServiceHoverIn('movers')}
              onMouseLeave={() => handleServiceHoverOut('movers')}
              activeOpacity={0.9}
            >
              <Animated.View style={[styles.serviceIconWrapper, moverAnimatedStyle]}>
                <LinearGradient
                  colors={['rgba(255, 255, 255, 0.2)', 'rgba(255, 255, 255, 0.05)']}
                  style={styles.serviceIconBackground}
                >
                  <LinearGradient
                    colors={['rgba(255, 90, 95, 0.2)', 'rgba(255, 90, 95, 0.2)']}
                    style={styles.serviceIconInnerGlow}
                  />
                  <Image
                    source={require('../../assets/images/delivery-truck.png')}
                    style={styles.serviceIcon}
                    resizeMode="contain"
                  />
                  <LinearGradient
                    colors={['rgba(255, 255, 255, 0.3)', 'transparent', 'transparent']}
                    style={styles.serviceIconGloss}
                  />
                </LinearGradient>
                <View style={styles.serviceIconShadow} />
              </Animated.View>
              <Text style={styles.statLabel}>Movers</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.statItem}
              onPress={() => handleServicePress('Locksmiths')}
              onMouseEnter={() => handleServiceHoverIn('locksmiths')}
              onMouseLeave={() => handleServiceHoverOut('locksmiths')}
              activeOpacity={0.9}
            >
              <Animated.View style={[styles.serviceIconWrapper, locksmithAnimatedStyle]}>
                <LinearGradient
                  colors={['rgba(255, 255, 255, 0.2)', 'rgba(255, 255, 255, 0.05)']}
                  style={styles.serviceIconBackground}
                >
                  <LinearGradient
                    colors={['rgba(255, 90, 95, 0.2)', 'rgba(255, 90, 95, 0.2)']}
                    style={styles.serviceIconInnerGlow}
                  />
                  <Image
                    source={require('../../assets/images/unlocked.png')}
                    style={styles.serviceIcon}
                    resizeMode="contain"
                  />
                  <LinearGradient
                    colors={['rgba(255, 255, 255, 0.3)', 'transparent', 'transparent']}
                    style={styles.serviceIconGloss}
                  />
                </LinearGradient>
                <View style={styles.serviceIconShadow} />
              </Animated.View>
              <Text style={styles.statLabel}>Locksmiths</Text>
            </TouchableOpacity>
          </Animated.View>

          {/* CTA Button */}
          <Animated.View style={buttonAnimatedStyle}>
            <TouchableOpacity onPress={handleGetStarted} style={styles.ctaButton}>
              <LinearGradient
                colors={['#FF5A5F', '#e63946']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                style={styles.ctaGradient}
              >
                <Text style={styles.ctaText}>Explore Services</Text>
                <ArrowRight size={20} color="white" style={styles.ctaIcon} />
              </LinearGradient>
            </TouchableOpacity>
          </Animated.View>
        </View>
      </LinearGradient>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    flex: 1,
    position: 'relative',
  },
  backgroundElements: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  bgCircle: {
    position: 'absolute',
    borderRadius: 1000,
    backgroundColor: 'rgba(255, 90, 95, 0.1)',
  },
  bgCircle1: {
    width: 288,
    height: 288,
    top: 80,
    left: 80,
  },
  bgCircle2: {
    width: 384,
    height: 384,
    bottom: 80,
    right: 80,
  },
  bgGradient: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    width: '100%',
    height: '100%',
    transform: [{ translateX: -screenWidth / 2 }, { translateY: -screenHeight / 2 }],
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 24,
    zIndex: 10,
  },
  logoContainer: {
    marginBottom: 32,
  },
  logoWrapper: {
    width: 128,
    height: 128,
    position: 'relative',
  },
  logoBackground: {
    width: '100%',
    height: '100%',
    borderRadius: 24,
    overflow: 'hidden',
    position: 'relative',
  },
  logoInnerGlow: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    borderRadius: 24,
  },
  logoImage: {
    width: '100%',
    height: '100%',
    borderRadius: 24,
  },
  logoGloss: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    borderRadius: 24,
  },
  logoShadow: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 90, 95, 0.2)',
    borderRadius: 24,
    transform: [{ scale: 1.1 }],
    zIndex: -1,
  },
  title: {
    fontSize: 48,
    fontWeight: 'bold',
    color: 'white',
    textAlign: 'center',
    marginBottom: 16,
  },
  tagline: {
    fontSize: 20,
    color: '#fbd9da',
    textAlign: 'center',
    marginBottom: 48,
    fontWeight: '300',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    maxWidth: 500,
    marginBottom: 64,
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  serviceIconWrapper: {
    width: 80,
    height: 80,
    position: 'relative',
    marginBottom: 12,
  },
  serviceIconBackground: {
    width: '100%',
    height: '100%',
    borderRadius: 20,
    overflow: 'hidden',
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
  },
  serviceIconInnerGlow: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    borderRadius: 20,
  },
  serviceIconGloss: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    borderRadius: 20,
  },
  serviceIconShadow: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 90, 95, 0.15)',
    borderRadius: 20,
    transform: [{ scale: 1.05 }],
    zIndex: -1,
  },
  statLabel: {
    fontSize: 16,
    color: '#fbd9da',
    textAlign: 'center',
    fontWeight: '500',
  },
  serviceIcon: {
    width: 40,
    height: 40,
    zIndex: 10,
  },
  ctaButton: {
    borderRadius: 50,
    overflow: 'hidden',
    elevation: 8,
    shadowColor: '#FF5A5F',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.25,
    shadowRadius: 8,
  },
  ctaGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 32,
    paddingVertical: 16,
  },
  ctaText: {
    fontSize: 18,
    fontWeight: '600',
    color: 'white',
  },
  ctaIcon: {
    marginLeft: 8,
  },
});
