import { useEffect } from 'react';
import { Platform } from 'react-native';
import * as Linking from 'expo-linking';
import { useRouter } from 'expo-router';

/**
 * Hook to handle deep links for mobile OAuth callbacks
 * This is essential for mobile authentication flow
 */
export function useDeepLinkHandler() {
  const router = useRouter();

  useEffect(() => {
    // Only handle deep links on mobile platforms
    if (Platform.OS === 'web') {
      return;
    }

    console.log('🔗 Setting up deep link handler for mobile...');

    // Handle initial URL if app was opened via deep link
    const handleInitialURL = async () => {
      try {
        const initialUrl = await Linking.getInitialURL();
        if (initialUrl) {
          console.log('🔗 App opened with initial URL:', initialUrl);
          handleDeepLink(initialUrl);
        }
      } catch (error) {
        console.error('❌ Error getting initial URL:', error);
      }
    };

    // Handle deep links while app is running
    const handleUrlChange = (event: { url: string }) => {
      console.log('🔗 Deep link received:', event.url);
      handleDeepLink(event.url);
    };

    // Parse and handle the deep link
    const handleDeepLink = (url: string) => {
      try {
        const parsed = Linking.parse(url);
        console.log('🔍 Parsed deep link:', parsed);

        // Check if this is an auth callback
        if (parsed.path === 'auth/callback' || parsed.path === '/auth/callback') {
          console.log('🔄 Auth callback deep link detected');
          
          // Extract query parameters
          const queryParams = parsed.queryParams || {};
          
          // Navigate to the auth callback screen with parameters
          const searchParams = new URLSearchParams();
          Object.entries(queryParams).forEach(([key, value]) => {
            if (value !== undefined && value !== null) {
              searchParams.set(key, String(value));
            }
          });

          const callbackUrl = `/auth/callback?${searchParams.toString()}`;
          console.log('🔄 Navigating to callback:', callbackUrl);
          
          router.replace(callbackUrl);
        } else {
          console.log('🔍 Non-auth deep link, handling normally');
          // Handle other deep links if needed
        }
      } catch (error) {
        console.error('❌ Error handling deep link:', error);
      }
    };

    // Set up listeners
    handleInitialURL();
    const subscription = Linking.addEventListener('url', handleUrlChange);

    // Cleanup
    return () => {
      subscription?.remove();
    };
  }, [router]);
}

export default useDeepLinkHandler;
