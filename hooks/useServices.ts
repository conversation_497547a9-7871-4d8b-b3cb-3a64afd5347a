import { useState, useEffect, useCallback, useRef } from 'react';
import { 
  ServiceDTO, 
  ServiceSearchParams, 
  PaginatedResponse,
  ServiceListState,
  LoadingState 
} from '../types/api';
import { getServices, searchServices } from '../services';

/**
 * Custom hook for managing service data with pagination and search
 * Provides efficient state management for service lists with caching
 */
export const useServices = (initialParams: ServiceSearchParams = {}) => {
  // State management
  const [state, setState] = useState<ServiceListState>({
    services: [],
    totalElements: 0,
    currentPage: 0,
    isLoading: false,
    isLoadingMore: false,
    error: null,
    hasMore: true,
  });

  // Search parameters
  const [searchParams, setSearchParams] = useState<ServiceSearchParams>({
    page: 0,
    size: 20,
    ...initialParams
  });

  // Cache for preventing duplicate requests
  const requestCache = useRef<Map<string, Promise<PaginatedResponse<ServiceDTO>>>>(new Map());
  const abortController = useRef<AbortController | null>(null);

  /**
   * Generate cache key for request deduplication
   */
  const getCacheKey = useCallback((params: ServiceSearchParams): string => {
    return JSON.stringify(params);
  }, []);

  /**
   * Load services with caching and deduplication
   */
  const loadServices = useCallback(async (
    params: ServiceSearchParams,
    append = false
  ): Promise<void> => {
    try {
      const cacheKey = getCacheKey(params);
      
      // Cancel previous request if still pending
      if (abortController.current) {
        abortController.current.abort();
      }
      abortController.current = new AbortController();

      // Set loading state
      setState(prev => ({
        ...prev,
        isLoading: !append,
        isLoadingMore: append,
        error: null
      }));

      // Check cache first
      let request = requestCache.current.get(cacheKey);
      
      if (!request) {
        // Create new request
        request = params.query 
          ? searchServices(params.query, params)
          : getServices(params);
        
        // Cache the request
        requestCache.current.set(cacheKey, request);
        
        // Clean up cache after request completes
        request.finally(() => {
          requestCache.current.delete(cacheKey);
        });
      }

      const response = await request;

      setState(prev => ({
        ...prev,
        services: append ? [...prev.services, ...response.content] : response.content,
        totalElements: response.totalElements,
        currentPage: response.number,
        isLoading: false,
        isLoadingMore: false,
        hasMore: !response.last,
        error: null
      }));

    } catch (error: any) {
      // Don't update state if request was aborted
      if (error.name === 'AbortError') {
        return;
      }

      console.error('❌ Failed to load services:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        isLoadingMore: false,
        error: error.message || 'Failed to load services'
      }));
    }
  }, [getCacheKey]);

  /**
   * Initial load effect
   */
  useEffect(() => {
    loadServices(searchParams);
  }, [searchParams, loadServices]);

  /**
   * Refresh services (reload from beginning)
   */
  const refresh = useCallback(async (): Promise<void> => {
    const refreshParams = { ...searchParams, page: 0 };
    setSearchParams(refreshParams);
    await loadServices(refreshParams, false);
  }, [searchParams, loadServices]);

  /**
   * Load more services (pagination)
   */
  const loadMore = useCallback(async (): Promise<void> => {
    if (state.isLoadingMore || !state.hasMore) {
      return;
    }

    const nextPage = state.currentPage + 1;
    const nextParams = { ...searchParams, page: nextPage };
    
    await loadServices(nextParams, true);
  }, [state.isLoadingMore, state.hasMore, state.currentPage, searchParams, loadServices]);

  /**
   * Update search parameters
   */
  const updateSearchParams = useCallback((newParams: Partial<ServiceSearchParams>): void => {
    setSearchParams(prev => ({
      ...prev,
      ...newParams,
      page: 0 // Reset to first page when params change
    }));
  }, []);

  /**
   * Search services with query
   */
  const search = useCallback((query: string): void => {
    updateSearchParams({ query: query.trim() });
  }, [updateSearchParams]);

  /**
   * Filter by category
   */
  const filterByCategory = useCallback((categoryId: string | null): void => {
    updateSearchParams({ category: categoryId || undefined });
  }, [updateSearchParams]);

  /**
   * Apply price filter
   */
  const filterByPrice = useCallback((minPrice?: number, maxPrice?: number): void => {
    updateSearchParams({ minPrice, maxPrice });
  }, [updateSearchParams]);

  /**
   * Clear all filters
   */
  const clearFilters = useCallback((): void => {
    setSearchParams({
      page: 0,
      size: searchParams.size
    });
  }, [searchParams.size]);

  /**
   * Cleanup on unmount
   */
  useEffect(() => {
    return () => {
      if (abortController.current) {
        abortController.current.abort();
      }
      requestCache.current.clear();
    };
  }, []);

  return {
    // State
    services: state.services,
    totalElements: state.totalElements,
    currentPage: state.currentPage,
    isLoading: state.isLoading,
    isLoadingMore: state.isLoadingMore,
    error: state.error,
    hasMore: state.hasMore,
    
    // Search params
    searchParams,
    
    // Actions
    refresh,
    loadMore,
    search,
    filterByCategory,
    filterByPrice,
    clearFilters,
    updateSearchParams,
    
    // Computed values
    isEmpty: state.services.length === 0 && !state.isLoading,
    isFirstLoad: state.isLoading && state.services.length === 0,
  };
};

export default useServices;
