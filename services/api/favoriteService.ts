import { apiGet, apiPost, apiDelete, apiPut } from './client';
import { ApiEndpoints } from './types';
import { ServiceDTO } from '../../types/api';

/**
 * Favorite API Service
 * Handles all favorite-related API calls with authentication
 */

export interface FavoriteResponse {
  message: string;
  favorited: boolean;
}

export interface FavoriteStatusResponse {
  serviceId: string;
  favorited: boolean;
}

export interface FavoriteCountResponse {
  count: number;
}

/**
 * Get all favorite services for the current authenticated user
 * Returns services with professional information
 * 
 * @returns Promise with array of favorite services
 */
export const getFavorites = async (): Promise<ServiceDTO[]> => {
  try {
    console.log('🚀 Fetching user favorites');
    
    const response = await apiGet<ServiceDTO[]>(ApiEndpoints.FAVORITES);
    
    console.log('✅ Favorites fetched successfully:', {
      count: response.length
    });
    
    return response;
  } catch (error) {
    console.error('❌ Failed to fetch favorites:', error);
    throw new Error('Failed to fetch favorites. Please try again.');
  }
};

/**
 * Get favorite service IDs for the current authenticated user
 * Lightweight endpoint for checking favorite status
 * 
 * @returns Promise with array of service IDs
 */
export const getFavoriteServiceIds = async (): Promise<string[]> => {
  try {
    console.log('🚀 Fetching favorite service IDs');
    
    const response = await apiGet<string[]>(`${ApiEndpoints.FAVORITES}/service-ids`);
    
    console.log('✅ Favorite service IDs fetched successfully:', {
      count: response.length
    });
    
    return response;
  } catch (error) {
    console.error('❌ Failed to fetch favorite service IDs:', error);
    throw new Error('Failed to fetch favorite service IDs. Please try again.');
  }
};

/**
 * Add a service to current user's favorites
 * 
 * @param serviceId The service ID to add to favorites
 * @returns Promise with favorite response
 */
export const addToFavorites = async (serviceId: string): Promise<FavoriteResponse> => {
  try {
    console.log('🚀 Adding service to favorites:', serviceId);
    
    const response = await apiPost<FavoriteResponse>(`${ApiEndpoints.FAVORITES}/${serviceId}`, {});
    
    console.log('✅ Service added to favorites successfully:', {
      serviceId,
      favorited: response.favorited
    });
    
    return response;
  } catch (error) {
    console.error('❌ Failed to add service to favorites:', error);
    throw new Error('Failed to add service to favorites. Please try again.');
  }
};

/**
 * Remove a service from current user's favorites
 * 
 * @param serviceId The service ID to remove from favorites
 * @returns Promise with favorite response
 */
export const removeFromFavorites = async (serviceId: string): Promise<FavoriteResponse> => {
  try {
    console.log('🚀 Removing service from favorites:', serviceId);
    
    const response = await apiDelete<FavoriteResponse>(`${ApiEndpoints.FAVORITES}/${serviceId}`);
    
    console.log('✅ Service removed from favorites successfully:', {
      serviceId,
      favorited: response.favorited
    });
    
    return response;
  } catch (error) {
    console.error('❌ Failed to remove service from favorites:', error);
    throw new Error('Failed to remove service from favorites. Please try again.');
  }
};

/**
 * Check if a service is favorited by the current user
 * 
 * @param serviceId The service ID to check
 * @returns Promise with favorite status
 */
export const checkFavoriteStatus = async (serviceId: string): Promise<FavoriteStatusResponse> => {
  try {
    console.log('🚀 Checking favorite status for service:', serviceId);
    
    const response = await apiGet<FavoriteStatusResponse>(`${ApiEndpoints.FAVORITES}/check/${serviceId}`);
    
    console.log('✅ Favorite status checked successfully:', {
      serviceId,
      favorited: response.favorited
    });
    
    return response;
  } catch (error) {
    console.error('❌ Failed to check favorite status:', error);
    throw new Error('Failed to check favorite status. Please try again.');
  }
};

/**
 * Toggle favorite status for a service
 * 
 * @param serviceId The service ID to toggle
 * @returns Promise with favorite response
 */
export const toggleFavorite = async (serviceId: string): Promise<FavoriteResponse> => {
  try {
    console.log('🚀 Toggling favorite status for service:', serviceId);
    
    const response = await apiPut<FavoriteResponse>(`${ApiEndpoints.FAVORITES}/${serviceId}/toggle`, {});
    
    console.log('✅ Favorite status toggled successfully:', {
      serviceId,
      favorited: response.favorited
    });
    
    return response;
  } catch (error) {
    console.error('❌ Failed to toggle favorite status:', error);
    throw new Error('Failed to toggle favorite status. Please try again.');
  }
};

/**
 * Get the count of favorites for the current user
 * 
 * @returns Promise with favorites count
 */
export const getFavoritesCount = async (): Promise<FavoriteCountResponse> => {
  try {
    console.log('🚀 Fetching favorites count');
    
    const response = await apiGet<FavoriteCountResponse>(`${ApiEndpoints.FAVORITES}/count`);
    
    console.log('✅ Favorites count fetched successfully:', {
      count: response.count
    });
    
    return response;
  } catch (error) {
    console.error('❌ Failed to fetch favorites count:', error);
    throw new Error('Failed to fetch favorites count. Please try again.');
  }
};

// Export default service object
export default {
  getFavorites,
  getFavoriteServiceIds,
  addToFavorites,
  removeFromFavorites,
  checkFavoriteStatus,
  toggleFavorite,
  getFavoritesCount,
};
