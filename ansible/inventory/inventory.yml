all:
  children:
    darwin_arm64:             # Group: Apple Silicon macOS machines
      hosts:
        local:                # Local Mac (your laptop)
          #ansible_connection: local
          ansible_host: 127.0.0.1
          #ansible_user: "{{ user }}"
          ansible_port: "{{ port }}"
          ansible_ssh_private_key_file: "{{ key }}"
          ansible_python_interpreter: /usr/bin/python3

        remote:               # Remote Mac mini over SSH
          ansible_host: "{{ host }}"
          #ansible_user: "{{ user }}"
          #ansible_ssh_private_key_file: "{{ key }}"

    rhel_x86_64:              # Group: Red Hat EC2s or VMs
      hosts:
        ec2:                  # EC2 host (dynamic IP)
          ansible_host: "{{ host }}"
          ansible_user: "{{ user }}"
          ansible_ssh_private_key_file: "{{ key }}"
          ansible_python_interpreter: /usr/bin/python