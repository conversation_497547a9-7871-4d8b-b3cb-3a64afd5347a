terraform {
  required_providers {
    kubernetes = {
      source  = "hashicorp/kubernetes"
    }
  }
}

resource "kubernetes_secret" "cloudflare_api_token_secret" {
  metadata {
    name      = "cloudflare-api-token-secret"
    namespace = "cert-manager"
  }

  data = {
    api-token = "TfuOBw6PCouEBZ2U-n5BWxagxOKeKwPfFNaUwdru"
  }

  type = "Opaque"
}

resource "kubernetes_manifest" "cluster_issuer" {
  depends_on = [kubernetes_secret.cloudflare_api_token_secret]
  manifest = yamldecode(file("${path.module}/cluster-issuer.yaml"))
}