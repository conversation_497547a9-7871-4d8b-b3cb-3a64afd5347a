#!/bin/bash

# Yote<PERSON>oHago Keycloak Library
# Functions for managing Keycloak configuration and troubleshooting

# Function to check realm status
check_realm_status() {
    print_status $CYAN "🔍 Checking realm status..."
    
    local response
    response=$(http_request GET "$KEYCLOAK_URL/realms/$REALM_NAME" -s)
    local status_code
    status_code=$(echo "$response" | cut -d'|' -f1)
    
    if [[ "$status_code" == "200" ]]; then
        local body
        body=$(echo "$response" | cut -d'|' -f2-)
        print_status $GREEN "✅ Realm '$REALM_NAME' is accessible"
        echo "$body" | jq -r '{realm, enabled, sslRequired}'
    else
        print_status $RED "❌ Realm not accessible (Status: $status_code)"
    fi
}

# Function to check OIDC configuration
check_oidc_config() {
    print_status $CYAN "🔍 Checking OpenID Connect configuration..."
    
    local response
    response=$(http_request GET "$KEYCLOAK_URL/realms/$REALM_NAME/.well-known/openid_configuration" -s)
    local status_code
    status_code=$(echo "$response" | cut -d'|' -f1)
    
    if [[ "$status_code" == "200" ]]; then
        local body
        body=$(echo "$response" | cut -d'|' -f2-)
        print_status $GREEN "✅ OIDC configuration accessible"
        echo "$body" | jq -r '{
            authorization_endpoint,
            token_endpoint,
            userinfo_endpoint,
            end_session_endpoint
        }'
    else
        print_status $RED "❌ OIDC config not accessible (Status: $status_code)"
    fi
}

# Function to list clients
list_clients() {
    print_status $CYAN "📱 Listing clients..."
    
    local admin_token
    admin_token=$(get_admin_token)
    
    if [[ "$admin_token" == "null" ]]; then
        print_status $RED "❌ Failed to get admin token"
        return 1
    fi
    
    local response
    response=$(http_request GET "$KEYCLOAK_URL/admin/realms/$REALM_NAME/clients" \
        -H "Authorization: Bearer $admin_token" -s)
    local status_code
    status_code=$(echo "$response" | cut -d'|' -f1)
    
    if [[ "$status_code" == "200" ]]; then
        local body
        body=$(echo "$response" | cut -d'|' -f2-)
        echo "$body" | jq -r '.[] | select(.clientId | test("yotelohago")) | {clientId, enabled, publicClient, serviceAccountsEnabled}'
    else
        print_status $RED "❌ Failed to list clients (Status: $status_code)"
    fi
}

# Function to show client scopes
show_client_scopes() {
    local client_id=${1:-$DEV_CLIENT_ID}
    
    print_status $CYAN "📋 Checking client scopes for $client_id..."
    
    local admin_token
    admin_token=$(get_admin_token)
    
    if [[ "$admin_token" == "null" ]]; then
        print_status $RED "❌ Failed to get admin token"
        return 1
    fi
    
    # Get client UUID
    local client_uuid
    client_uuid=$(http_request GET "$KEYCLOAK_URL/admin/realms/$REALM_NAME/clients" \
        -H "Authorization: Bearer $admin_token" -s | \
        cut -d'|' -f2- | jq -r ".[] | select(.clientId==\"$client_id\") | .id")
    
    if [[ -z "$client_uuid" ]] || [[ "$client_uuid" == "null" ]]; then
        print_status $RED "❌ Client '$client_id' not found"
        return 1
    fi
    
    echo "Default scopes:"
    local response
    response=$(http_request GET "$KEYCLOAK_URL/admin/realms/$REALM_NAME/clients/$client_uuid/default-client-scopes" \
        -H "Authorization: Bearer $admin_token" -s)
    local status_code
    status_code=$(echo "$response" | cut -d'|' -f1)
    
    if [[ "$status_code" == "200" ]]; then
        local body
        body=$(echo "$response" | cut -d'|' -f2-)
        echo "$body" | jq -r '.[].name' | sort | sed 's/^/  - /'
    else
        print_status $RED "❌ Failed to get client scopes (Status: $status_code)"
    fi
}

# Function to list realm roles
list_realm_roles() {
    print_status $CYAN "🏰 Listing realm roles..."
    
    local admin_token
    admin_token=$(get_admin_token)
    
    if [[ "$admin_token" == "null" ]]; then
        print_status $RED "❌ Failed to get admin token"
        return 1
    fi
    
    local response
    response=$(http_request GET "$KEYCLOAK_URL/admin/realms/$REALM_NAME/roles" \
        -H "Authorization: Bearer $admin_token" -s)
    local status_code
    status_code=$(echo "$response" | cut -d'|' -f1)
    
    if [[ "$status_code" == "200" ]]; then
        local body
        body=$(echo "$response" | cut -d'|' -f2-)
        echo "$body" | jq -r '.[] | select(.name=="user" or .name=="professional" or .name=="admin") | "- \(.name): \(.description // "No description")"'
    else
        print_status $RED "❌ Failed to list realm roles (Status: $status_code)"
    fi
}

# Function to compare clients
compare_clients() {
    local client1=${1:-$DEV_CLIENT_ID}
    local client2=${2:-$PROD_CLIENT_ID}
    
    print_status $CYAN "🔄 Comparing clients: $client1 vs $client2..."
    
    local admin_token
    admin_token=$(get_admin_token)
    
    if [[ "$admin_token" == "null" ]]; then
        print_status $RED "❌ Failed to get admin token"
        return 1
    fi
    
    # Get client configurations
    local clients_response
    clients_response=$(http_request GET "$KEYCLOAK_URL/admin/realms/$REALM_NAME/clients" \
        -H "Authorization: Bearer $admin_token" -s)
    local status_code
    status_code=$(echo "$clients_response" | cut -d'|' -f1)
    
    if [[ "$status_code" != "200" ]]; then
        print_status $RED "❌ Failed to get clients (Status: $status_code)"
        return 1
    fi
    
    local clients_body
    clients_body=$(echo "$clients_response" | cut -d'|' -f2-)
    
    local client1_config
    client1_config=$(echo "$clients_body" | jq ".[] | select(.clientId==\"$client1\")")
    local client2_config
    client2_config=$(echo "$clients_body" | jq ".[] | select(.clientId==\"$client2\")")
    
    if [[ -z "$client1_config" ]] || [[ "$client1_config" == "null" ]]; then
        print_status $RED "❌ Client '$client1' not found"
        return 1
    fi
    
    if [[ -z "$client2_config" ]] || [[ "$client2_config" == "null" ]]; then
        print_status $RED "❌ Client '$client2' not found"
        return 1
    fi
    
    echo "Comparing key configuration differences:"
    echo ""
    
    # Compare basic settings
    echo "Basic Settings:"
    echo "  Enabled:"
    echo "    $client1: $(echo "$client1_config" | jq -r '.enabled')"
    echo "    $client2: $(echo "$client2_config" | jq -r '.enabled')"
    
    echo "  Public Client:"
    echo "    $client1: $(echo "$client1_config" | jq -r '.publicClient')"
    echo "    $client2: $(echo "$client2_config" | jq -r '.publicClient')"
    
    echo "  Service Accounts Enabled:"
    echo "    $client1: $(echo "$client1_config" | jq -r '.serviceAccountsEnabled')"
    echo "    $client2: $(echo "$client2_config" | jq -r '.serviceAccountsEnabled')"
    
    # Compare redirect URIs
    echo ""
    echo "Redirect URIs:"
    echo "  $client1:"
    echo "$client1_config" | jq -r '.redirectUris[]?' | sed 's/^/    - /'
    echo "  $client2:"
    echo "$client2_config" | jq -r '.redirectUris[]?' | sed 's/^/    - /'
}

# Function to fix missing sub mapper
fix_missing_sub() {
    print_status $CYAN "🔧 Adding missing sub mapper to clients..."
    
    local admin_token
    admin_token=$(get_admin_token)
    
    if [[ "$admin_token" == "null" ]]; then
        print_status $RED "❌ Failed to get admin token"
        return 1
    fi
    
    for client_name in "$DEV_CLIENT_ID" "$PROD_CLIENT_ID"; do
        # Get client UUID
        local client_uuid
        client_uuid=$(http_request GET "$KEYCLOAK_URL/admin/realms/$REALM_NAME/clients" \
            -H "Authorization: Bearer $admin_token" -s | \
            cut -d'|' -f2- | jq -r ".[] | select(.clientId==\"$client_name\") | .id")
        
        if [[ -z "$client_uuid" ]] || [[ "$client_uuid" == "null" ]]; then
            print_status $YELLOW "⚠️ Client '$client_name' not found, skipping..."
            continue
        fi
        
        echo "Adding sub mapper to $client_name..."
        
        local mapper_data='{
            "name": "sub",
            "protocol": "openid-connect",
            "protocolMapper": "oidc-sub-mapper",
            "consentRequired": false,
            "config": {
                "introspection.token.claim": "true",
                "access.token.claim": "true",
                "id.token.claim": "true"
            }
        }'
        
        local response
        response=$(http_request POST "$KEYCLOAK_URL/admin/realms/$REALM_NAME/clients/$client_uuid/protocol-mappers/models" \
            -H "Authorization: Bearer $admin_token" \
            -H "Content-Type: application/json" \
            -d "$mapper_data" -s)
        local status_code
        status_code=$(echo "$response" | cut -d'|' -f1)
        
        if [[ "$status_code" == "201" ]]; then
            print_status $GREEN "✅ Sub mapper added to $client_name"
        else
            print_status $YELLOW "⚠️ Sub mapper may already exist for $client_name (Status: $status_code)"
        fi
    done
}

# Function to add basic scope
add_basic_scope() {
    print_status $CYAN "🔧 Adding basic scope to clients..."
    
    local admin_token
    admin_token=$(get_admin_token)
    
    if [[ "$admin_token" == "null" ]]; then
        print_status $RED "❌ Failed to get admin token"
        return 1
    fi
    
    # Get basic scope ID
    local basic_scope_id
    basic_scope_id=$(http_request GET "$KEYCLOAK_URL/admin/realms/$REALM_NAME/client-scopes" \
        -H "Authorization: Bearer $admin_token" -s | \
        cut -d'|' -f2- | jq -r '.[] | select(.name=="basic") | .id')
    
    if [[ -z "$basic_scope_id" ]] || [[ "$basic_scope_id" == "null" ]]; then
        print_status $RED "❌ Basic scope not found"
        return 1
    fi
    
    for client_name in "$DEV_CLIENT_ID" "$PROD_CLIENT_ID"; do
        # Get client UUID
        local client_uuid
        client_uuid=$(http_request GET "$KEYCLOAK_URL/admin/realms/$REALM_NAME/clients" \
            -H "Authorization: Bearer $admin_token" -s | \
            cut -d'|' -f2- | jq -r ".[] | select(.clientId==\"$client_name\") | .id")
        
        if [[ -z "$client_uuid" ]] || [[ "$client_uuid" == "null" ]]; then
            print_status $YELLOW "⚠️ Client '$client_name' not found, skipping..."
            continue
        fi
        
        echo "Adding basic scope to $client_name..."
        
        local response
        response=$(http_request PUT "$KEYCLOAK_URL/admin/realms/$REALM_NAME/clients/$client_uuid/default-client-scopes/$basic_scope_id" \
            -H "Authorization: Bearer $admin_token" -s)
        local status_code
        status_code=$(echo "$response" | cut -d'|' -f1)
        
        if [[ "$status_code" == "204" ]]; then
            print_status $GREEN "✅ Basic scope added to $client_name"
        else
            print_status $YELLOW "⚠️ Basic scope may already exist for $client_name (Status: $status_code)"
        fi
    done
}

# Function to show keycloak help
keycloak_help() {
    cat << EOF
📋 Keycloak - Keycloak management and configuration

COMMANDS:
    realm-status                          Check realm accessibility
    oidc-config                          Check OpenID Connect configuration  
    clients                              List client configurations
    client-scopes [client_id]            Show client scopes (default: dev client)
    realm-roles                          List realm roles
    compare-clients <client1> <client2>  Compare client configurations
    fix-missing-sub                      Add missing sub mapper to clients
    add-basic-scope                      Add basic scope to clients

EXAMPLES:
    $0 keycloak realm-status
    $0 keycloak clients
    $0 keycloak compare-clients yotelohago-app-dev yotelohago-app-prod
    $0 keycloak fix-missing-sub

EOF
}

# Main keycloak function
keycloak_main() {
    local command=$1
    shift
    
    case "$command" in
        "realm-status")
            check_realm_status
            ;;
        "oidc-config")
            check_oidc_config
            ;;
        "clients")
            list_clients
            ;;
        "client-scopes")
            show_client_scopes "$1"
            ;;
        "realm-roles")
            list_realm_roles
            ;;
        "compare-clients")
            if [[ $# -lt 2 ]]; then
                print_status $RED "❌ Usage: compare-clients <client1> <client2>"
                exit 1
            fi
            compare_clients "$1" "$2"
            ;;
        "fix-missing-sub")
            fix_missing_sub
            ;;
        "add-basic-scope")
            add_basic_scope
            ;;
        *)
            print_status $RED "❌ Unknown keycloak command: $command"
            keycloak_help
            exit 1
            ;;
    esac
}
