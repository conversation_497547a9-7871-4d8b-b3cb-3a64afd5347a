import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { MapPin, ArrowRight, Phone, Mail } from 'lucide-react';

const CTAPanel: React.FC = () => {
  const [zipCode, setZipCode] = useState('');
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (zipCode.length >= 5) {
      setIsSubmitted(true);
      // Here you would typically send the zip code to your backend
      setTimeout(() => setIsSubmitted(false), 3000);
    }
  };

  return (
    <div className="w-full h-full bg-gradient-to-br from-coral-900 via-coral-800 to-coral-700 flex items-center justify-center relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 right-20 w-96 h-96 bg-coral-500/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 left-20 w-80 h-80 bg-coral-400/10 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-full h-full bg-gradient-to-r from-transparent via-white/5 to-transparent rotate-45"></div>
      </div>

      <div className="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 text-center h-full flex flex-col justify-center py-8 sm:py-12">
        {/* Main Heading */}
        <motion.h2
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-3xl sm:text-4xl md:text-5xl lg:text-7xl font-bold text-white mb-4 sm:mb-6"
        >
          Ready to Get Started?
        </motion.h2>

        <motion.p
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="text-base sm:text-lg md:text-xl lg:text-2xl text-coral-200 mb-8 sm:mb-12 max-w-2xl mx-auto leading-relaxed"
        >
          Enter your zip code to find trusted professionals in your area. 
          Available 24/7 for all your home service needs.
        </motion.p>

        {/* Form */}
        <motion.form
          initial={{ opacity: 0, scale: 0.9 }}
          whileInView={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          onSubmit={handleSubmit}
          className="max-w-md mx-auto mb-8 sm:mb-12"
        >
          <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
            <div className="relative flex-1">
              <MapPin className="absolute left-3 sm:left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4 sm:w-5 sm:h-5" />
              <input
                type="text"
                value={zipCode}
                onChange={(e) => setZipCode(e.target.value)}
                placeholder="Enter your zip code"
                className="w-full pl-10 sm:pl-12 pr-3 sm:pr-4 py-3 sm:py-4 rounded-xl bg-white/10 backdrop-blur-md border border-white/20 text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-coral-400 focus:border-transparent transition-all text-sm sm:text-base"
                maxLength={10}
              />
            </div>
            <motion.button
              type="submit"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              disabled={zipCode.length < 5}
              className="px-6 sm:px-8 py-3 sm:py-4 bg-gradient-to-r from-coral-500 to-coral-600 text-white rounded-xl font-semibold shadow-2xl hover:shadow-coral-500/25 transition-all duration-300 flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed text-sm sm:text-base"
            >
              {isSubmitted ? (
                <motion.span
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="flex items-center"
                >
                  ✓ Submitted!
                </motion.span>
              ) : (
                <>
                  Get Started
                  <ArrowRight className="ml-2 w-4 h-4 sm:w-5 sm:h-5" />
                </>
              )}
            </motion.button>
          </div>
        </motion.form>

        {/* Contact Options */}
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6 max-w-2xl mx-auto mb-8 sm:mb-12"
        >
          <motion.a
            href="tel:1-800-YOTELOHAGO"
            whileHover={{ scale: 1.05, y: -2 }}
            className="flex items-center justify-center space-x-3 bg-white/10 backdrop-blur-md rounded-xl p-4 sm:p-6 text-white hover:bg-coral-500/20 transition-all"
          >
            <Phone className="w-5 h-5 sm:w-6 sm:h-6 text-coral-400 flex-shrink-0" />
            <div className="text-left">
              <div className="font-semibold text-sm sm:text-base">Call Us Now</div>
              <div className="text-xs sm:text-sm text-coral-200">1-800-YOTELOHAGO</div>
            </div>
          </motion.a>
          
          <motion.a
            href="mailto:<EMAIL>"
            whileHover={{ scale: 1.05, y: -2 }}
            className="flex items-center justify-center space-x-3 bg-white/10 backdrop-blur-md rounded-xl p-4 sm:p-6 text-white hover:bg-coral-500/20 transition-all"
          >
            <Mail className="w-5 h-5 sm:w-6 sm:h-6 text-coral-400 flex-shrink-0" />
            <div className="text-left">
              <div className="font-semibold text-sm sm:text-base">Email Support</div>
              <div className="text-xs sm:text-sm text-coral-200"><EMAIL></div>
            </div>
          </motion.a>
        </motion.div>

        {/* Trust Indicators */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.8 }}
          className="flex flex-wrap justify-center items-center gap-4 sm:gap-6 lg:gap-8 text-coral-200 text-xs sm:text-sm"
        >
          <div className="flex items-center space-x-2">
            <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-coral-400 rounded-full flex-shrink-0"></div>
            <span>Licensed & Insured</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-coral-400 rounded-full flex-shrink-0"></div>
            <span>Background Checked</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-coral-400 rounded-full flex-shrink-0"></div>
            <span className="whitespace-nowrap">100% Satisfaction Guarantee</span>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default CTAPanel;