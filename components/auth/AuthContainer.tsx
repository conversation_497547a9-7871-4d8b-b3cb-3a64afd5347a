import React, { useState } from 'react';
import { View, StyleSheet } from 'react-native';
import CustomLoginScreen from './CustomLoginScreen';
import CustomRegistrationScreen from './CustomRegistrationScreen';

export type AuthScreen = 'login' | 'registration';

interface AuthContainerProps {
  onSuccess?: () => void;
  onCancel?: () => void;
  title?: string;
  subtitle?: string;
  initialScreen?: AuthScreen;
}

export default function AuthContainer({
  onSuccess,
  onCancel,
  title,
  subtitle,
  initialScreen = 'login',
}: AuthContainerProps) {
  const [currentScreen, setCurrentScreen] = useState<AuthScreen>(initialScreen);

  const handleNavigateToRegistration = () => {
    setCurrentScreen('registration');
  };

  const handleNavigateToLogin = () => {
    setCurrentScreen('login');
  };

  const handleRegistrationSuccess = () => {
    // After successful registration, show login screen
    setCurrentScreen('login');
  };

  const handleLoginSuccess = () => {
    onSuccess?.();
  };

  const handleCancel = () => {
    // Reset to initial screen when canceling
    setCurrentScreen(initialScreen);
    onCancel?.();
  };

  return (
    <View style={styles.container}>
      {currentScreen === 'login' ? (
        <CustomLoginScreen
          onSuccess={handleLoginSuccess}
          onCancel={handleCancel}
          onNavigateToRegistration={handleNavigateToRegistration}
          title={title}
          subtitle={subtitle}
        />
      ) : (
        <CustomRegistrationScreen
          onSuccess={handleRegistrationSuccess}
          onCancel={handleCancel}
          onNavigateToLogin={handleNavigateToLogin}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
