## Database config
quarkus.datasource.db-kind=postgresql
quarkus.datasource.username=yotelohago
quarkus.datasource.password=yotelohago
quarkus.hibernate-orm.sql-load-script=import.sql

## Cache Configuration
# Service list cache - 5 minutes TTL, max 100 entries
quarkus.cache.caffeine."service-list".expire-after-write=5M
quarkus.cache.caffeine."service-list".maximum-size=100

# Service category cache - 1 hour TTL, max 50 entries
quarkus.cache.caffeine."service-categories".expire-after-write=1H
quarkus.cache.caffeine."service-categories".maximum-size=50

# Individual service cache - 10 minutes TTL, max 500 entries
quarkus.cache.caffeine."service-details".expire-after-write=10M
quarkus.cache.caffeine."service-details".maximum-size=500

## MinIO Configuration
# MinIO endpoint and credentials
minio.endpoint=https://api.minio.yotelohago.co
minio.access-key=uFnyDKdv8dx1860CKp2D
minio.secret-key=s0nBFIlPfPLUr98stfIkrjL9nn0b2oKHW3srHNeC
minio.bucket=public
minio.region=us-east-1

# Media upload configuration
media.max-file-size=10485760
media.allowed-content-types=image/jpeg,image/png,image/gif,image/webp,application/pdf
media.presigned-url-expiry-minutes=15
media.url-resolver=https://minio.yotelohago.co/api/v1/buckets/public/objects/download?preview=true&prefix=

## OpenAPI + Swagger
#quarkus.swagger-ui.path=/ui
#quarkus.swagger-ui.always-include=true
#quarkus.smallrye-openapi.path=/api


# (optional but useful) session handling
#quarkus.oidc.auth-server-url=https://keycloak.yotelohago.co/realms/master
#quarkus.oidc.client-id=yotelohago-backend
#quarkus.oidc.credentials.secret=A2wA7yhvtn1Jb9VUCGPq180ogbMXEu1v
#quarkus.oidc.tls.verification=none
#quarkus.oidc.token.audience-check=yotelohago-backend
#quarkus.http.auth.permission.authenticated.paths=/*
#quarkus.http.auth.permission.authenticated.policy=authenticated


