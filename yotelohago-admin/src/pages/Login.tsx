import React, { useState } from 'react';
import { <PERSON><PERSON>, Card, Typography, message } from 'antd';
import { LoginOutlined } from '@ant-design/icons';
import { useAuth } from '../contexts/AuthContext';

const { Title, Text } = Typography;

const Login: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const { login } = useAuth();

  const handleLogin = async () => {
    setLoading(true);
    try {
      await login();
      message.success('Redirecting to Keycloak login...');
    } catch (error) {
      message.error('Login failed. Please try again.');
      setLoading(false);
    }
  };

  return (
    <div 
      className="min-h-screen flex items-center justify-center p-4"
      style={{
        background: 'linear-gradient(135deg, rgba(255, 90, 95, 0.1) 0%, rgba(255, 90, 95, 0.05) 100%)',
        backgroundColor: '#fef7f7'
      }}
    >
      <Card 
        className="w-full max-w-md shadow-2xl border-0"
        style={{
          borderRadius: '24px',
          boxShadow: '0 20px 60px rgba(255, 90, 95, 0.15), 0 8px 32px rgba(0, 0, 0, 0.1)'
        }}
      >
        <div className="text-center mb-8">
          {/* Company Logo */}
          <div className="flex items-center justify-center mb-6">
            <div 
              className="w-20 h-20 rounded-3xl overflow-hidden shadow-lg"
              style={{
                background: 'linear-gradient(135deg, #ff6b6b 0%, #ff5a5f 100%)',
                boxShadow: '0 8px 24px rgba(255, 90, 95, 0.4)'
              }}
            >
              <img 
                src="/Logo copy.png" 
                alt="YoteLoHago Admin Logo" 
                className="w-full h-full object-cover"
              />
            </div>
          </div>
          
          <Title level={2} className="m-0 text-gray-800 mb-2">
            YoteLoHago Admin
          </Title>
          <Text type="secondary" className="text-base">
            Sign in with your admin account
          </Text>
        </div>

        <div className="space-y-4">
          <Button
            type="primary"
            icon={<LoginOutlined />}
            onClick={handleLogin}
            loading={loading}
            className="w-full h-12 text-base font-medium rounded-xl border-2 transition-all duration-200"
            style={{
              backgroundColor: 'transparent',
              borderColor: '#ff5a5f',
              color: '#ff5a5f',
              boxShadow: 'none'
            }}
            onMouseEnter={(e) => {
              const target = e.target as HTMLElement;
              target.style.backgroundColor = '#ff5a5f';
              target.style.color = 'white';
              target.style.transform = 'translateY(-1px)';
              target.style.boxShadow = '0 8px 24px rgba(255, 90, 95, 0.3)';
            }}
            onMouseLeave={(e) => {
              const target = e.target as HTMLElement;
              target.style.backgroundColor = 'transparent';
              target.style.color = '#ff5a5f';
              target.style.transform = 'translateY(0)';
              target.style.boxShadow = 'none';
            }}
          >
            Sign In with Keycloak
          </Button>
        </div>

        <div className="text-center mt-6 pt-4 border-t border-gray-100">
          <Text type="secondary" className="text-sm">
            Admin access required. You will be redirected to Keycloak for authentication.
          </Text>
        </div>
      </Card>

      <style jsx global>{`
        .ant-btn-primary:not(:disabled):not(.ant-btn-disabled):hover {
          background-color: #ff5a5f !important;
          border-color: #ff5a5f !important;
          color: white !important;
        }
      `}</style>
    </div>
  );
};

export default Login;
