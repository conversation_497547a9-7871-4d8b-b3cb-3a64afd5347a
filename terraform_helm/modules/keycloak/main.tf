terraform {
  required_providers {
    kubernetes = {
      source = "hashicorp/kubernetes"
    }
    helm = {
      source = "hashicorp/helm"
    }
    postgresql = {
      source  = "jbg/postgresql"
      version = "1.19.0"
    }
  }
}

resource "kubernetes_namespace" "keycloak" {
  metadata {
    name = "keycloak"
  }
}

resource "postgresql_role" "keycloak_user" {
  name     = "keycloak_user"
  password = "yotelohago"
  login    = true

  depends_on = [kubernetes_namespace.keycloak]
}

resource "postgresql_database" "keycloak_db" {
  name              = "keycloak"
  owner             = postgresql_role.keycloak_user.name
  encoding          = "UTF8"
  lc_collate        = "en_US.UTF-8"
  lc_ctype          = "en_US.UTF-8"
  template          = "template0"
  allow_connections = true
  depends_on        = [postgresql_role.keycloak_user]
}

# ---------------------------
# Kubernetes Secret for DB Credentials
# ---------------------------
resource "kubernetes_secret" "keycloak_db" {
  metadata {
    name      = "keycloak-db-secret"
    namespace = kubernetes_namespace.keycloak.metadata[0].name
  }
  data = {
    KC_DB_USERNAME = base64encode("keycloak_user")
    KC_DB_PASSWORD = base64encode("yotelohago")
  }
  type = "Opaque"
}

# ---------------------------
# Helm Release - Keycloak
# ---------------------------
resource "helm_release" "keycloak" {
  name       = "keycloak"
  chart      = "keycloak"
  repository = "oci://registry-1.docker.io/bitnamicharts"
  version    = "24.7.1"
  namespace  = kubernetes_namespace.keycloak.metadata[0].name

  values = [file("${path.module}/values.yaml")]

  depends_on = [
    kubernetes_secret.keycloak_db,
    postgresql_database.keycloak_db
  ]
}
