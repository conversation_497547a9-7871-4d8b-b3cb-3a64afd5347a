services:
  postgres-db:
    image: postgres:14
    container_name: yotelohago-db
    environment:
      POSTGRES_USER: yotelohago
      POSTGRES_PASSWORD: yotelohago
      POSTGRES_DB: yotelohago
    ports:
      - "5432:5432"
    volumes:
      - postgres_db_data:/var/lib/postgresql/data
    networks:
      - yotelohago-network

#  keycloak-db:
#    image: postgres:14
#    container_name: keycloak-db
#    environment:
#      POSTGRES_DB: keycloak
#      POSTGRES_USER: keycloak
#      POSTGRES_PASSWORD: keycloak
#    volumes:
#      - keycloak_db_data:/var/lib/postgresql/data
#    networks:
#      - yotelohago-network

#  keycloak:
#    image: quay.io/keycloak/keycloak:24.0
#    container_name: keycloak
#    command: start-dev --import-realm
#    environment:
#      KEYCLOAK_ADMIN: admin
#      KEYCLOAK_ADMIN_PASSWORD: admin
#      KC_DB: postgres
#      KC_DB_URL: *******************************************
#      KC_DB_USERNAME: keycloak
#      KC_DB_PASSWORD: keycloak
#    ports:
#      - "8081:8080"
#    volumes:
#      - ./realms:/opt/keycloak/data/import
#    depends_on:
#      - keycloak-db
#    networks:
#      - yotelohago-network

volumes:
  postgres_db_data:
#  keycloak_db_data:

networks:
  yotelohago-network: