package com.yotelohago.messaging.api;

import com.yotelohago.common.ApiVersion;
import com.yotelohago.common.dto.PageRequest;
import com.yotelohago.common.dto.PageResponse;
import com.yotelohago.messaging.api.ConversationDTO;
import com.yotelohago.messaging.application.MessagingManagementService;
import com.yotelohago.messaging.application.ConversationInitiationService;
import jakarta.annotation.security.RolesAllowed;
import jakarta.inject.Inject;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.ForbiddenException;

import java.time.Instant;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Path(ApiVersion.BASE + "/messaging")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@RolesAllowed({"user", "professional", "admin"})
public class MessageResource {

    @Inject
    MessagingManagementService service;

    @Inject
    ConversationInitiationService conversationService;

    @Inject
    MessageMapper messageMapper;



    @GET
    @Path("/booking/{bookingId}")
    public Response getByBooking(@PathParam("bookingId") Long bookingId) {
        try {
            List<MessageDTO> messages = service.getMessagesForBooking(bookingId)
                    .stream()
                    .map(messageMapper::toDTO)
                    .collect(Collectors.toList());
            return Response.ok(messages).build();
        } catch (ForbiddenException e) {
            return Response.status(Response.Status.FORBIDDEN)
                    .entity("{\"error\": \"" + e.getMessage() + "\"}")
                    .build();
        } catch (IllegalArgumentException e) {
            return Response.status(Response.Status.NOT_FOUND)
                    .entity("{\"error\": \"" + e.getMessage() + "\"}")
                    .build();
        }
    }

    @POST
    public Response sendMessage(MessageDTO dto) {
        try {
            // Set timestamp and default read status
            dto.sentAt = Instant.now();
            dto.isRead = false;

            service.send(messageMapper.toEntity(dto));
            return Response.status(Response.Status.CREATED).build();
        } catch (ForbiddenException e) {
            return Response.status(Response.Status.FORBIDDEN)
                    .entity("{\"error\": \"" + e.getMessage() + "\"}")
                    .build();
        }
    }

    @PATCH
    @Path("/{id}/read")
    public Response markAsRead(@PathParam("id") Long id) {
        try {
            service.markAsRead(id);
            return Response.ok().build();
        } catch (ForbiddenException e) {
            return Response.status(Response.Status.FORBIDDEN)
                    .entity("{\"error\": \"" + e.getMessage() + "\"}")
                    .build();
        } catch (IllegalArgumentException e) {
            return Response.status(Response.Status.NOT_FOUND)
                    .entity("{\"error\": \"" + e.getMessage() + "\"}")
                    .build();
        }
    }

    @DELETE
    @Path("/{id}")
    public Response delete(@PathParam("id") Long id) {
        try {
            service.delete(id);
            return Response.noContent().build();
        } catch (ForbiddenException e) {
            return Response.status(Response.Status.FORBIDDEN)
                    .entity("{\"error\": \"" + e.getMessage() + "\"}")
                    .build();
        } catch (IllegalArgumentException e) {
            return Response.status(Response.Status.NOT_FOUND)
                    .entity("{\"error\": \"" + e.getMessage() + "\"}")
                    .build();
        }
    }

    // Conversation management endpoints

    /**
     * Initiate a conversation with a professional
     * Creates a draft booking if no existing conversation exists
     */
    @POST
    @Path("/conversations/initiate/{professionalUserId}")
    public Response initiateConversation(@PathParam("professionalUserId") String professionalUserIdStr) {
        try {
            UUID professionalUserId = UUID.fromString(professionalUserIdStr);
            Long bookingId = conversationService.findOrCreateConversation(professionalUserId);

            return Response.ok("{\"bookingId\": " + bookingId + ", \"message\": \"Conversation ready\"}").build();
        } catch (IllegalArgumentException e) {
            return Response.status(Response.Status.BAD_REQUEST)
                    .entity("{\"error\": \"" + e.getMessage() + "\"}")
                    .build();
        } catch (IllegalStateException e) {
            return Response.status(Response.Status.UNAUTHORIZED)
                    .entity("{\"error\": \"" + e.getMessage() + "\"}")
                    .build();
        } catch (Exception e) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("{\"error\": \"Failed to initiate conversation\"}")
                    .build();
        }
    }

    /**
     * Get conversation summaries with pagination
     * Admin gets all conversations, regular users get only their conversations
     */
    @GET
    @Path("/conversations")
    @RolesAllowed({"user", "professional", "admin"})
    public Response getConversations(@BeanParam PageRequest pageRequest) {
        try {
            PageResponse<ConversationDTO.ConversationSummary> conversations =
                service.getConversationsForUserPaginated(pageRequest);
            return Response.ok(conversations).build();
        } catch (ForbiddenException e) {
            return Response.status(Response.Status.FORBIDDEN)
                    .entity("{\"error\": \"" + e.getMessage() + "\"}")
                    .build();
        } catch (Exception e) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("{\"error\": \"Failed to fetch conversations\"}")
                    .build();
        }
    }

    /**
     * Get all messages for a specific conversation (booking)
     * Admin and participants can access
     */
    @GET
    @Path("/conversations/{bookingId}/messages")
    public Response getConversationMessages(@PathParam("bookingId") Long bookingId,
                                          @BeanParam PageRequest pageRequest) {
        try {
            PageResponse<MessageWithDetailsDTO> messages =
                service.getMessagesForConversationPaginated(bookingId, pageRequest);
            return Response.ok(messages).build();
        } catch (ForbiddenException e) {
            return Response.status(Response.Status.FORBIDDEN)
                    .entity("{\"error\": \"" + e.getMessage() + "\"}")
                    .build();
        } catch (Exception e) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("{\"error\": \"Failed to fetch conversation messages\"}")
                    .build();
        }
    }





    /**
     * Get conversation info for a booking (including participant details)
     */
    @GET
    @Path("/conversations/{bookingId}/info")
    public Response getConversationInfo(@PathParam("bookingId") Long bookingId) {
        try {
            ConversationDTO.ConversationInfo info = service.getConversationInfo(bookingId);
            return Response.ok(info).build();
        } catch (IllegalArgumentException e) {
            return Response.status(Response.Status.NOT_FOUND)
                    .entity("{\"error\": \"Resource not found\", \"status\": 404, \"message\": \"" + e.getMessage() + "\"}")
                    .build();
        } catch (ForbiddenException e) {
            return Response.status(Response.Status.FORBIDDEN)
                    .entity("{\"error\": \"" + e.getMessage() + "\"}")
                    .build();
        } catch (Exception e) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("{\"error\": \"Failed to get conversation info\"}")
                    .build();
        }
    }

    /**
     * Mark all messages in a conversation as read
     */
    @PUT
    @Path("/conversations/{bookingId}/read")
    public Response markConversationAsRead(@PathParam("bookingId") Long bookingId) {
        try {
            service.markConversationAsRead(bookingId);
            return Response.ok("{\"message\": \"Conversation marked as read\"}").build();
        } catch (ForbiddenException e) {
            return Response.status(Response.Status.FORBIDDEN)
                    .entity("{\"error\": \"" + e.getMessage() + "\"}")
                    .build();
        } catch (IllegalArgumentException e) {
            return Response.status(Response.Status.NOT_FOUND)
                    .entity("{\"error\": \"" + e.getMessage() + "\"}")
                    .build();
        } catch (Exception e) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("{\"error\": \"Failed to mark conversation as read\"}")
                    .build();
        }
    }

    /**
     * Get total unread message count for current user
     */
    @GET
    @Path("/unread-count")
    public Response getUnreadCount() {
        try {
            int unreadCount = service.getUnreadMessageCount();
            return Response.ok("{\"unreadCount\": " + unreadCount + "}").build();
        } catch (Exception e) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("{\"error\": \"Failed to fetch unread count\"}")
                    .build();
        }
    }

    /**
     * Get WebSocket connection information for a booking
     */
    @GET
    @Path("/websocket/info/{bookingId}")
    public Response getWebSocketInfo(@PathParam("bookingId") Long bookingId) {
        try {
            int activeSessions = MessagingWebSocket.getActiveSessionCount(bookingId);
            int totalSessions = MessagingWebSocket.getTotalActiveSessions();

            String response = String.format(
                "{\"bookingId\": %d, \"activeSessions\": %d, \"totalActiveSessions\": %d, \"websocketUrl\": \"ws://localhost:8080/v1/messaging/ws/%d\"}",
                bookingId, activeSessions, totalSessions, bookingId
            );

            return Response.ok(response).build();
        } catch (Exception e) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("{\"error\": \"Failed to get WebSocket info\"}")
                    .build();
        }
    }

}
