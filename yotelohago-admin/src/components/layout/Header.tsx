import React from 'react';
import { Layout, Breadcrumb, Space, Button, Avatar, Dropdown, Input } from 'antd';
import {
  SearchOutlined,
  UserOutlined,
  SettingOutlined,
  LogoutOutlined
} from '@ant-design/icons';
import { useAuth } from '../../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';

const { Header: AntHeader } = Layout;
const { Search } = Input;

interface HeaderProps {
  breadcrumbItems: Array<{
    title: string;
    path?: string;
  }>;
}

const Header: React.FC<HeaderProps> = ({ breadcrumbItems }) => {
  const { user, logout } = useAuth();
  const navigate = useNavigate();

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: 'Profile',
      onClick: () => navigate('/profile'),
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: 'Settings',
      onClick: () => navigate('/settings'),
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: 'Logout',
      onClick: handleLogout,
    },
  ];

  return (
    <AntHeader 
      className="bg-white border-b border-gray-200 px-6 h-16 flex items-center justify-between sticky top-0"
      style={{ 
        zIndex: 1000,
        position: 'sticky',
        top: 0,
        width: '100%'
      }}
    >
      <div className="flex items-center space-x-4 flex-1">
        <Breadcrumb
          items={breadcrumbItems.map(item => ({
            title: item.title,
            href: item.path,
          }))}
          className="hidden md:block"
        />
      </div>

      <div className="flex items-center justify-center flex-1">
        <Search
          placeholder="Search..."
          prefix={<SearchOutlined />}
          className="w-full max-w-md"
          onSearch={(value) => console.log('Search:', value)}
        />
      </div>

      <div className="flex items-center justify-end flex-1">
        <Dropdown
          menu={{ items: userMenuItems }}
          trigger={['click']}
          placement="bottomRight"
        >
          <Button type="text" className="flex items-center space-x-2 p-2">
            <Avatar
              size={32}
              src={user?.avatar}
              icon={<UserOutlined />}
            />
            <span className="hidden md:inline text-sm font-medium">
              {user?.name}
            </span>
          </Button>
        </Dropdown>
      </div>
    </AntHeader>
  );
};

export default Header;