{"name": "ecommerce-admin-panel", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --port 8083", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@refinedev/antd": "^5.37.4", "@refinedev/core": "^4.47.1", "@refinedev/react-router-v6": "^4.5.5", "@refinedev/simple-rest": "^5.0.1", "antd": "^5.12.8", "dayjs": "^1.11.10", "ionicons": "^8.0.9", "keycloak-js": "^24.0.5", "leaflet": "^1.9.4", "leaflet.markercluster": "^1.5.3", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-leaflet": "^4.2.1", "react-router-dom": "^6.20.1", "recharts": "^2.8.0"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/leaflet": "^1.9.8", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}