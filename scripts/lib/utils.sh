#!/bin/bash

# YoteLoHago Utilities Library
# Common utility functions used across all scripts

# Configuration
KEYCLOAK_URL="https://keycloak.yotelohago.co"
REALM_NAME="yotelohago"
BACKEND_URL="http://localhost:8080"
PRODUCTION_API_URL="https://api.yotelohago.co"

# Default credentials for testing
DEFAULT_USERNAME="<EMAIL>"
DEFAULT_PASSWORD="1234"

# Client configurations
DEV_CLIENT_ID="yotelohago-app-dev"
DEV_CLIENT_SECRET="7tH60G8BGCLWDTHXIlNcKjwWSWg87VYi"
BACKEND_CLIENT_ID="yotelohago-backend"
BACKEND_CLIENT_SECRET="yotelohago-backend-secret-change-in-production"
PROD_CLIENT_ID="yotelohago-app-prod"

# Admin credentials
ADMIN_USERNAME="admin"
ADMIN_PASSWORD="yotelohago"

# Temporary files directory
TEMP_DIR="/tmp/yotelohago-tools"

# Function to ensure temp directory exists
ensure_temp_dir() {
    mkdir -p "$TEMP_DIR"
}

# Function to cleanup temporary files
cleanup_temp_files() {
    if [[ -d "$TEMP_DIR" ]]; then
        rm -rf "$TEMP_DIR"/*
        print_status $GREEN "🧹 Temporary files cleaned up"
    fi
}

# Function to validate required tools
check_required_tools() {
    local tools=("curl" "jq" "base64")
    local missing_tools=()
    
    for tool in "${tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            missing_tools+=("$tool")
        fi
    done
    
    if [[ ${#missing_tools[@]} -gt 0 ]]; then
        print_status $RED "❌ Missing required tools: ${missing_tools[*]}"
        print_status $YELLOW "Please install the missing tools and try again"
        exit 1
    fi
}

# Function to make HTTP requests with error handling
http_request() {
    local method=$1
    local url=$2
    local headers=()
    local data=""
    local output_file=""
    local silent=false

    # Parse additional arguments
    shift 2
    while [[ $# -gt 0 ]]; do
        case $1 in
            -H|--header)
                headers+=("-H" "$2")
                shift 2
                ;;
            -d|--data)
                data="$2"
                shift 2
                ;;
            -o|--output)
                output_file="$2"
                shift 2
                ;;
            -s|--silent)
                silent=true
                shift
                ;;
            *)
                print_status $RED "❌ Unknown option: $1"
                return 1
                ;;
        esac
    done

    # Create temporary file for response
    local temp_response
    temp_response=$(mktemp)
    local temp_headers
    temp_headers=$(mktemp)

    # Build curl command with proper output handling
    local curl_args=("-s" "-w" "%{http_code}" "-D" "$temp_headers" "-o" "$temp_response")

    # Add method
    curl_args+=("-X" "$method")

    # Add headers
    for header in "${headers[@]}"; do
        curl_args+=("$header")
    done

    # Add data if provided
    if [[ -n "$data" ]]; then
        curl_args+=("-d" "$data")
    fi

    # Add URL
    curl_args+=("$url")

    # Execute request
    local status_code
    status_code=$(curl "${curl_args[@]}" 2>/dev/null)
    local exit_code=$?

    # Clean up and handle errors
    if [[ $exit_code -ne 0 ]]; then
        rm -f "$temp_response" "$temp_headers"
        if [[ "$silent" != true ]]; then
            print_status $RED "❌ HTTP request failed: $url"
        fi
        return 1
    fi

    # Read response body
    local body=""
    if [[ -f "$temp_response" ]]; then
        body=$(cat "$temp_response")
    fi

    # Clean up temp files
    rm -f "$temp_response" "$temp_headers"

    # Return status code and body
    echo "$status_code|$body"
    return 0
}

# Function to parse HTTP response
parse_http_response() {
    local response=$1
    local status_code
    local body
    
    status_code=$(echo "$response" | cut -d'|' -f1)
    body=$(echo "$response" | cut -d'|' -f2-)
    
    echo "STATUS:$status_code"
    echo "BODY:$body"
}

# Function to get admin token
get_admin_token() {
    local response
    response=$(http_request POST "$KEYCLOAK_URL/realms/master/protocol/openid-connect/token" \
        -H "Content-Type: application/x-www-form-urlencoded" \
        -d "username=$ADMIN_USERNAME&password=$ADMIN_PASSWORD&grant_type=password&client_id=admin-cli" \
        -s)
    
    local status_code
    status_code=$(echo "$response" | cut -d'|' -f1)
    
    if [[ "$status_code" == "200" ]]; then
        local body
        body=$(echo "$response" | cut -d'|' -f2-)
        echo "$body" | jq -r '.access_token'
    else
        echo "null"
    fi
}

# Function to get user token
get_user_token() {
    local username=${1:-$DEFAULT_USERNAME}
    local password=${2:-$DEFAULT_PASSWORD}
    local client_id=${3:-$DEV_CLIENT_ID}
    local client_secret=${4:-$DEV_CLIENT_SECRET}
    local scope=${5:-"openid profile email roles"}
    
    local response
    response=$(http_request POST "$KEYCLOAK_URL/realms/$REALM_NAME/protocol/openid-connect/token" \
        -H "Content-Type: application/x-www-form-urlencoded" \
        -d "grant_type=password&client_id=$client_id&client_secret=$client_secret&username=$username&password=$password&scope=$scope" \
        -s)
    
    local status_code
    status_code=$(echo "$response" | cut -d'|' -f1)
    
    if [[ "$status_code" == "200" ]]; then
        local body
        body=$(echo "$response" | cut -d'|' -f2-)
        echo "$body" | jq -r '.access_token'
    else
        echo "null"
    fi
}

# Function to decode JWT token
decode_jwt_token() {
    local token=$1
    
    if [[ "$token" == "null" ]] || [[ -z "$token" ]]; then
        echo "null"
        return 1
    fi
    
    # Extract payload (second part)
    local payload
    payload=$(echo "$token" | cut -d'.' -f2)
    
    # Add padding if needed
    local padding=$((4 - ${#payload} % 4))
    if [[ $padding -ne 4 ]]; then
        payload="${payload}$(printf '=%.0s' $(seq 1 $padding))"
    fi
    
    # Decode base64
    echo "$payload" | base64 -d 2>/dev/null
}

# Function to validate JWT token
validate_jwt_token() {
    local token=$1
    local username=${2:-""}
    
    if [[ "$token" == "null" ]] || [[ -z "$token" ]]; then
        print_status $RED "❌ Invalid or empty token"
        return 1
    fi
    
    # Decode token payload
    local payload
    payload=$(decode_jwt_token "$token")
    
    if [[ $? -ne 0 ]] || [[ "$payload" == "null" ]]; then
        print_status $RED "❌ Failed to decode token"
        return 1
    fi
    
    print_status $GREEN "✅ Token is valid and decodable"
    
    # Check expiration
    local exp
    exp=$(echo "$payload" | jq -r '.exp // 0')
    local current_time
    current_time=$(date +%s)
    
    if [[ "$exp" -gt "$current_time" ]]; then
        local remaining=$((exp - current_time))
        print_status $GREEN "✅ Token expires in $remaining seconds"
    else
        print_status $RED "❌ Token has expired"
        return 1
    fi
    
    # Check required claims
    local sub
    sub=$(echo "$payload" | jq -r '.sub // ""')
    local realm_roles
    realm_roles=$(echo "$payload" | jq -r '.realm_access.roles // []')
    
    if [[ -n "$sub" ]]; then
        print_status $GREEN "✅ Sub claim present: $sub"
    else
        print_status $RED "❌ Sub claim missing"
    fi
    
    if [[ "$realm_roles" != "[]" ]]; then
        print_status $GREEN "✅ Realm roles present: $(echo "$payload" | jq -r '.realm_access.roles[]' | tr '\n' ' ')"
    else
        print_status $YELLOW "⚠️ No realm roles found"
    fi
    
    return 0
}

# Function to test API endpoint
test_api_endpoint() {
    local endpoint=$1
    local token=${2:-""}
    local method=${3:-"GET"}
    local data=${4:-""}
    local base_url=${5:-$BACKEND_URL}

    print_status $CYAN "🔍 Testing $method $endpoint..."

    local headers=("-H" "Accept: application/json")

    if [[ -n "$token" ]]; then
        headers+=("-H" "Authorization: Bearer $token")
    fi

    if [[ -n "$data" ]]; then
        headers+=("-H" "Content-Type: application/json")
    fi

    local response
    response=$(http_request "$method" "$base_url$endpoint" "${headers[@]}" ${data:+-d "$data"})

    # Parse response more carefully
    local status_code
    local body

    if [[ "$response" == *"|"* ]]; then
        status_code=$(echo "$response" | cut -d'|' -f1)
        body=$(echo "$response" | cut -d'|' -f2-)
    else
        # Fallback if parsing fails
        status_code="$response"
        body=""
    fi

    # Clean up status code (remove any extra characters)
    status_code=$(echo "$status_code" | tr -d '\n\r' | grep -o '[0-9]*' | head -1)

    echo "  Status: $status_code"

    case "$status_code" in
        200|201)
            print_status $GREEN "  ✅ Success"
            if [[ -n "$body" ]] && [[ "$body" != "[]" ]] && [[ "$body" != "null" ]]; then
                local preview
                preview=$(echo "$body" | head -c 200 | tr -d '\n\r')
                echo "  Response: $preview"
                if [[ ${#body} -gt 200 ]]; then
                    echo "... (truncated)"
                fi
            fi
            ;;
        401)
            print_status $GREEN "  ✅ Correctly requires authentication (401)"
            ;;
        403)
            print_status $RED "  ❌ Forbidden - Check user permissions"
            ;;
        404)
            print_status $YELLOW "  ⚠️ Not Found - Check endpoint or resource"
            ;;
        "")
            print_status $RED "  ❌ No response received"
            ;;
        *)
            print_status $RED "  ❌ Error ($status_code)"
            if [[ -n "$body" ]]; then
                local preview
                preview=$(echo "$body" | head -c 100 | tr -d '\n\r')
                echo "  Response: $preview"
            fi
            ;;
    esac

    echo ""
    return 0
}

# Function to show utils help
utils_help() {
    cat << EOF
🔧 Utilities - Common utility functions

COMMANDS:
    decode-token <username> <password>     Decode and analyze JWT token
    full-check                            Run complete system health check
    cleanup                               Clean up temporary files
    validate-tools                        Check if required tools are installed

EXAMPLES:
    $0 utils decode-token <EMAIL> 1234
    $0 utils full-check
    $0 utils cleanup

EOF
}

# Main utils function
utils_main() {
    local command=$1
    shift
    
    case "$command" in
        "decode-token")
            local username=${1:-$DEFAULT_USERNAME}
            local password=${2:-$DEFAULT_PASSWORD}
            
            print_status $CYAN "🔍 Decoding JWT token for $username..."
            local token
            token=$(get_user_token "$username" "$password")
            
            if [[ "$token" != "null" ]] && [[ -n "$token" ]]; then
                echo "Token payload:"
                decode_jwt_token "$token" | jq .
            else
                print_status $RED "❌ Failed to get token"
            fi
            ;;
        "full-check")
            print_status $CYAN "🔍 Running full system check..."
            echo ""
            
            # Load other libraries and run checks
            keycloak_main "realm-status"
            echo ""
            keycloak_main "clients"
            echo ""
            testing_main "token" "$DEFAULT_USERNAME" "$DEFAULT_PASSWORD"
            echo ""
            testing_main "backend" "$DEFAULT_USERNAME" "$DEFAULT_PASSWORD"
            ;;
        "cleanup")
            cleanup_temp_files
            ;;
        "validate-tools")
            check_required_tools
            print_status $GREEN "✅ All required tools are available"
            ;;
        *)
            print_status $RED "❌ Unknown utils command: $command"
            utils_help
            exit 1
            ;;
    esac
}

# Initialize utilities
ensure_temp_dir
check_required_tools
