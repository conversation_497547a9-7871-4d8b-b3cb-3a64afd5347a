import React from 'react';
import { Typography } from 'antd';
import ProfessionalMap from '../components/map/ProfessionalMap';

const { Title, Text } = Typography;

const Map: React.FC = () => {
  return (
    <div className="p-6 relative">
      <div className="mb-6">
        <Title level={2} className="m-0 mb-2">Mapa de Profesionales</Title>
        <Text type="secondary">
          Visualización geográfica de la distribución de profesionales en Guadalajara, Jalisco, México
        </Text>
      </div>
      
      {/* Map container with proper z-index management */}
      <div className="relative" style={{ zIndex: 1 }}>
        <ProfessionalMap height={700} />
      </div>
    </div>
  );
};

export default Map;