import React from 'react';
import { Card, Statistic, Space } from 'antd';
import { ArrowUpOutlined, ArrowDownOutlined } from '@ant-design/icons';

interface StatsCardProps {
  title: string;
  value: number | string;
  prefix?: React.ReactNode;
  suffix?: React.ReactNode;
  growth?: number;
  loading?: boolean;
  precision?: number;
  icon?: React.ReactNode;
  color?: string;
}

const StatsCard: React.FC<StatsCardProps> = ({
  title,
  value,
  prefix,
  suffix,
  growth,
  loading,
  precision,
  icon,
  color = '#1890ff'
}) => {
  const isPositiveGrowth = growth && growth > 0;
  const isNegativeGrowth = growth && growth < 0;

  return (
    <Card
      className="shadow-sm hover:shadow-md transition-shadow duration-200"
      style={{ borderLeft: `4px solid ${color}` }}
    >
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <Statistic
            title={title}
            value={value}
            prefix={prefix}
            suffix={suffix}
            loading={loading}
            precision={precision}
            className="mb-0"
          />
          {growth !== undefined && (
            <div className="mt-2">
              <Space align="center">
                {isPositiveGrowth && (
                  <ArrowUpOutlined className="text-green-500" />
                )}
                {isNegativeGrowth && (
                  <ArrowDownOutlined className="text-red-500" />
                )}
                <span
                  className={`text-sm font-medium ${
                    isPositiveGrowth
                      ? 'text-green-500'
                      : isNegativeGrowth
                      ? 'text-red-500'
                      : 'text-gray-500'
                  }`}
                >
                  {growth > 0 ? '+' : ''}{growth}%
                </span>
                <span className="text-xs text-gray-500">vs last month</span>
              </Space>
            </div>
          )}
        </div>
        {icon && (
          <div
            className="flex items-center justify-center w-12 h-12 rounded-lg"
            style={{ backgroundColor: `${color}15`, color }}
          >
            {icon}
          </div>
        )}
      </div>
    </Card>
  );
};

export default StatsCard;