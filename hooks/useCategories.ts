import { useState, useEffect, useCallback } from 'react';
import { CategoryState, FrontendCategory } from '../types/api';
import { getFrontendCategories, clearCategoriesCache } from '../services';

/**
 * Custom hook for managing service categories
 * Provides efficient state management with caching
 */
export const useCategories = () => {
  // State management
  const [state, setState] = useState<CategoryState>({
    categories: [],
    selectedCategory: null,
    isLoading: false,
    error: null,
  });

  /**
   * Load categories from API
   */
  const loadCategories = useCallback(async (forceRefresh = false): Promise<void> => {
    try {
      setState(prev => ({
        ...prev,
        isLoading: true,
        error: null
      }));

      const categories = await getFrontendCategories(forceRefresh);

      setState(prev => ({
        ...prev,
        categories,
        isLoading: false,
        error: null
      }));

    } catch (error: any) {
      console.error('❌ Failed to load categories:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error.message || 'Failed to load categories'
      }));
    }
  }, []);

  /**
   * Initial load effect
   */
  useEffect(() => {
    loadCategories();
  }, [loadCategories]);

  /**
   * Select a category
   */
  const selectCategory = useCallback((categoryId: string | null): void => {
    setState(prev => ({
      ...prev,
      selectedCategory: categoryId
    }));
  }, []);

  /**
   * Refresh categories (force reload)
   */
  const refresh = useCallback(async (): Promise<void> => {
    clearCategoriesCache();
    await loadCategories(true);
  }, [loadCategories]);

  /**
   * Get category by ID
   */
  const getCategoryById = useCallback((id: string): FrontendCategory | undefined => {
    return state.categories.find(cat => cat.id === id) as FrontendCategory | undefined;
  }, [state.categories]);

  /**
   * Get selected category object
   */
  const selectedCategoryObject = state.selectedCategory 
    ? getCategoryById(state.selectedCategory) 
    : null;

  return {
    // State
    categories: state.categories,
    selectedCategory: state.selectedCategory,
    selectedCategoryObject,
    isLoading: state.isLoading,
    error: state.error,
    
    // Actions
    selectCategory,
    refresh,
    getCategoryById,
    
    // Computed values
    isEmpty: state.categories.length === 0 && !state.isLoading,
    hasCategories: state.categories.length > 0,
  };
};

export default useCategories;
