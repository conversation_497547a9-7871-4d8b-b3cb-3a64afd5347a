import React, { useState } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Layout, ConfigProvider } from 'antd';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import Sidebar from './components/layout/Sidebar';
import Header from './components/layout/Header';
import Dashboard from './pages/Dashboard';
import Users from './pages/Users';
import Professionals from './pages/Professionals';
import Services from './pages/Services';
import Categories from './pages/Categories';
import Bookings from './pages/Bookings';
import Reviews from './pages/Reviews';
import Messages from './pages/Messages';
import Settings from './pages/Settings';
import Map from './pages/Map';
import Login from './pages/Login';

const { Content } = Layout;

const AppContent: React.FC = () => {
  const { user, loading } = useAuth();
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-coral-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return <Login />;
  }

  const getBreadcrumbItems = (pathname: string) => {
    const pathMap: { [key: string]: string } = {
      '/': 'Dashboard',
      '/users': 'Users',
      '/professionals': 'Professionals',
      '/services': 'Services',
      '/categories': 'Categories',
      '/bookings': 'Bookings',
      '/reviews': 'Reviews',
      '/messages': 'Messages',
      '/map': 'Map',
      '/settings': 'Settings',
    };

    return [
      { title: 'Home', path: '/' },
      ...(pathname !== '/' ? [{ title: pathMap[pathname] || 'Page' }] : []),
    ];
  };

  return (
    <Layout className="min-h-screen">
      <Sidebar
        collapsed={sidebarCollapsed}
        onCollapse={setSidebarCollapsed}
      />
      <Layout>
        <Header
          breadcrumbItems={getBreadcrumbItems(window.location.pathname)}
        />
        <Content className="bg-gray-50 overflow-auto">
          <Routes>
            <Route path="/" element={<Dashboard />} />
            <Route path="/users" element={<Users />} />
            <Route path="/professionals" element={<Professionals />} />
            <Route path="/services" element={<Services />} />
            <Route path="/categories" element={<Categories />} />
            <Route path="/bookings" element={<Bookings />} />
            <Route path="/reviews" element={<Reviews />} />
            <Route path="/messages" element={<Messages />} />
            <Route path="/map" element={<Map />} />
            <Route path="/settings" element={<Settings />} />
            <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>
        </Content>
      </Layout>
    </Layout>
  );
};

const App: React.FC = () => {
  return (
    <ConfigProvider
      theme={{
        token: {
          colorPrimary: '#ff5a5f',
          borderRadius: 8,
        },
        components: {
          Button: {
            colorPrimary: '#ff5a5f',
            colorPrimaryHover: '#ff7875',
            colorPrimaryActive: '#d9363e',
          },
          Menu: {
            itemSelectedBg: '#ff5a5f',
            itemSelectedColor: '#ffffff',
            itemHoverBg: 'rgba(255, 90, 95, 0.1)',
            itemHoverColor: '#ff5a5f',
            itemActiveBg: 'rgba(255, 90, 95, 0.15)',
          },
        },
      }}
    >
      <AuthProvider>
        <Router>
          <AppContent />
        </Router>
      </AuthProvider>
    </ConfigProvider>
  );
};

export default App;