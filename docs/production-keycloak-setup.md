# Production Keycloak Setup Guide

## Overview
This guide will help you configure the production Keycloak server at `keycloak.yotelohago.co` with the yotelohago realm.

## Prerequisites
- Access to Keycloak admin console at `https://keycloak.yotelohago.co/admin/`
- Admin credentials: `admin` / `yotelohago`

## Step 1: Access Keycloak Admin Console

1. Open your browser and navigate to: `https://keycloak.yotelohago.co/admin/`
2. Login with:
   - Username: `admin`
   - Password: `yotelohago`

## Step 2: Create the Yotelohago Realm

### Option A: Import Realm (Recommended)
1. In the admin console, click on the realm dropdown (currently showing "Master")
2. Click "Create Realm"
3. Click "Browse" and select the file: `realms/yotelohago-realm-production.json`
4. Click "Create"

### Option B: Manual Configuration
If import fails, follow these manual steps:

1. **Create Realm:**
   - Click "Create Realm"
   - Realm name: `yotelohago`
   - Enabled: `ON`
   - Click "Create"

2. **Configure Realm Settings:**
   - Go to Realm Settings → General
   - SSL required: `External requests`
   - Login with email: `ON`
   - Registration allowed: `OFF`

3. **Create Roles:**
   - Go to Realm roles
   - Create these roles:
     - `user` (Regular user role)
     - `professional` (Professional service provider role)
     - `admin` (Administrator role)

4. **Create Clients:**

   **Production Client:**
   - Go to Clients → Create client
   - Client ID: `yotelohago-app-prod`
   - Name: `Yotelohago Production App`
   - Description: `Production client for YoteLoHago platform`
   - Client authentication: `OFF` (public client)
   - Authorization: `OFF`
   - Standard flow: `ON`
   - Direct access grants: `ON`
   - Valid redirect URIs:
     ```
     https://yotelohago.co/*
     https://app.yotelohago.co/*
     yotelohago://auth/callback
     yotelohago://*
     ```
   - Web origins:
     ```
     https://yotelohago.co
     https://app.yotelohago.co
     yotelohago://
     ```

   **Development Client:**
   - Go to Clients → Create client
   - Client ID: `yotelohago-app-dev`
   - Name: `Yotelohago Development App`
   - Description: `Development client for localhost testing`
   - Client authentication: `OFF` (public client)
   - Authorization: `OFF`
   - Standard flow: `ON`
   - Direct access grants: `ON`
   - Valid redirect URIs:
     ```
     http://localhost:8082/*
     http://localhost:19006/*
     yotelohago://auth/callback
     http://localhost:8082/auth/callback
     exp://localhost:19000/*
     yotelohago://*
     ```
   - Web origins:
     ```
     http://localhost:8082
     http://localhost:19006
     yotelohago://
     ```

5. **Configure Client Settings:**
   For both clients, go to Settings → Advanced:
   - Proof Key for Code Exchange Code Challenge Method: `S256`
   - OAuth 2.0 Device Authorization Grant Enabled: `OFF`

## Step 3: Create Test User

1. Go to Users → Add user
2. Username: `testuser`
3. Email: `<EMAIL>`
4. First name: `Test`
5. Last name: `User`
6. Email verified: `ON`
7. Enabled: `ON`
8. Click "Create"

9. Go to Credentials tab
10. Set password: `testpassword`
11. Temporary: `OFF`
12. Click "Set password"

13. Go to Role mapping tab
14. Assign roles → Filter by realm roles
15. Select `user` role and click "Assign"

## Step 4: Test Configuration

### Test Realm Access
Visit: `https://keycloak.yotelohago.co/realms/yotelohago`

You should see a JSON response with realm information.

### Test Authentication Endpoints
- Auth URL: `https://keycloak.yotelohago.co/realms/yotelohago/protocol/openid-connect/auth`
- Token URL: `https://keycloak.yotelohago.co/realms/yotelohago/protocol/openid-connect/token`
- UserInfo URL: `https://keycloak.yotelohago.co/realms/yotelohago/protocol/openid-connect/userinfo`

## Step 5: Update Frontend Configuration

The frontend environment configuration needs to be updated to use the correct client IDs:

- For localhost development: Use `yotelohago-app-dev`
- For production deployment: Use `yotelohago-app-prod`

## Verification Checklist

- [ ] Realm `yotelohago` created
- [ ] Client `yotelohago-app-prod` configured with production URLs
- [ ] Client `yotelohago-app-dev` configured with localhost URLs
- [ ] Roles `user`, `professional`, `admin` created
- [ ] Test user created and can authenticate
- [ ] Realm endpoints accessible
- [ ] Frontend configuration updated

## Security Notes

1. **Development Client Access**: The `yotelohago-app-dev` client allows localhost access to production Keycloak. This should be removed before final production release.

2. **HTTPS Only**: All production URLs use HTTPS. Localhost uses HTTP for development convenience.

3. **PKCE Enabled**: Both clients use PKCE (Proof Key for Code Exchange) for enhanced security.

4. **Public Clients**: Both clients are configured as public clients (no client secret) as required for mobile/SPA applications.

## Troubleshooting

If you encounter issues:

1. Check that all URLs are correctly configured
2. Verify that CORS settings allow your domains
3. Ensure PKCE is enabled for both clients
4. Check that direct access grants are enabled for password-based authentication

## Next Steps

After completing this setup:

1. Test authentication from your React Native app
2. Update frontend to use appropriate client ID based on environment
3. Create additional users as needed
4. Configure any additional realm settings (themes, email, etc.)
