export interface User {
  id: string;
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  phone: string;
  isProfessional: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Professional {
  userId: string;
  user?: User;
  available: boolean;
  avgRating: number;
  ratingCount: number;
  city: string;
  bio: string;
  profileImageUrl?: string;
  status: 'pending' | 'approved' | 'rejected' | 'suspended';
  verificationStatus: 'pending' | 'verified' | 'failed';
}

export interface ServiceCategory {
  id: string;
  name: string;
  description: string;
  iconUrl?: string;
  status: 'active' | 'inactive';
  serviceCount?: number;
}

export interface Service {
  id: string;
  title: string;
  description: string;
  price: number;
  status: 'active' | 'inactive' | 'draft';
  categoryId: string;
  category?: ServiceCategory;
  professionalUserId: string;
  professional?: Professional;
  createdAt: string;
  updatedAt: string;
}

export interface Booking {
  id: string;
  title: string;
  description: string;
  price: number;
  status: 'OPEN' | 'ACCEPTED' | 'COMPLETED' | 'CANCELLED';
  clientId: string;
  client?: User;
  professionalId: string;
  professional?: User;
  serviceId: string;
  service?: Service;
  requestedDate: string;
  scheduledAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Review {
  id: string;
  rating: number;
  comment: string;
  bookingId: string;
  booking?: Booking;
  clientId: string;
  client?: User;
  professionalId: string;
  professional?: User;
  createdAt: string;
}

export interface Message {
  id: string;
  content: string;
  senderId: string;
  sender?: User;
  receiverId: string;
  receiver?: User;
  bookingId: string;
  booking?: Booking;
  isRead: boolean;
  sentAt: string;
}

export interface Favorite {
  serviceId: string;
  service?: Service;
  userId: string;
  user?: User;
  createdAt: string;
}

export interface DashboardStats {
  totalUsers: number;
  totalProfessionals: number;
  totalBookings: number;
  totalRevenue: number;
  activeUsers: number;
  completionRate: number;
  userGrowth: number;
  professionalGrowth: number;
  bookingGrowth: number;
  revenueGrowth: number;
}

export interface Transaction {
  id: string;
  bookingId: string;
  booking?: Booking;
  amount: number;
  commission: number;
  status: 'pending' | 'completed' | 'refunded';
  type: 'payment' | 'payout' | 'refund';
  createdAt: string;
}

export interface Notification {
  id: string;
  type: 'success' | 'warning' | 'info' | 'error';
  title: string;
  description: string;
  userName?: string;
  isRead: boolean;
  createdAt: string;
}