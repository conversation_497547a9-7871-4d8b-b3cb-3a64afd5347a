import React from 'react';
import { motion } from 'framer-motion';
import { ArrowRight, Home, Star, Users } from 'lucide-react';

interface HeroPanelProps {
  onNext: () => void;
}

const HeroPanel: React.FC<HeroPanelProps> = ({ onNext }) => {
  return (
    <div className="w-full h-full bg-gradient-to-br from-slate-900 via-coral-900 to-coral-800 flex flex-col justify-center items-center relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-20 w-72 h-72 bg-coral-500/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-20 w-96 h-96 bg-coral-400/10 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-full h-full bg-gradient-to-r from-transparent via-coral-500/5 to-transparent"></div>
      </div>

      <div className="relative z-10 text-center px-6 max-w-4xl mx-auto">
        {/* Logo - iPhone App Style */}
        <motion.div
          initial={{ scale: 0, rotate: -180 }}
          animate={{ scale: 1, rotate: 0 }}
          transition={{ type: "spring", stiffness: 200, damping: 15, delay: 0.2 }}
          className="mb-8"
        >
          <div className="w-32 h-32 mx-auto mb-6 relative">
            {/* iPhone App Icon Container */}
            <div className="w-full h-full bg-gradient-to-br from-white/20 to-white/5 backdrop-blur-sm rounded-3xl shadow-2xl border border-white/10 overflow-hidden relative">
              {/* Inner glow effect */}
              <div className="absolute inset-0 bg-gradient-to-br from-coral-400/20 to-coral-600/20 rounded-3xl"></div>
              
              {/* Logo Image */}
              <img 
                src="/Logo.png" 
                alt="Yotelohago Logo" 
                className="w-full h-full object-cover rounded-3xl drop-shadow-xl relative z-10"
                style={{
                  filter: 'drop-shadow(0 4px 8px rgba(0,0,0,0.3))'
                }}
              />
              
              {/* Glossy overlay effect like iPhone apps */}
              <div className="absolute inset-0 bg-gradient-to-b from-white/30 via-transparent to-transparent rounded-3xl pointer-events-none"></div>
              
              {/* Subtle inner shadow */}
              <div className="absolute inset-0 rounded-3xl shadow-inner shadow-black/20 pointer-events-none"></div>
            </div>
            
            {/* Outer glow/shadow */}
            <div className="absolute inset-0 bg-coral-500/20 rounded-3xl blur-xl scale-110 -z-10"></div>
          </div>
        </motion.div>

        {/* Brand Name */}
        <motion.h1
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5, duration: 0.8 }}
          className="text-6xl md:text-8xl font-bold text-white mb-4"
        >
          Yotelohago
        </motion.h1>

        {/* Tagline */}
        <motion.p
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8, duration: 0.8 }}
          className="text-xl md:text-2xl text-coral-200 mb-12 font-light"
        >
          Fast, Trusted Home Services
        </motion.p>

        {/* Stats */}
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.1, duration: 0.8 }}
          className="grid grid-cols-3 gap-8 mb-16 max-w-2xl mx-auto"
        >
          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <Star className="w-6 h-6 text-coral-400 mr-2" />
              <span className="text-3xl font-bold text-white">4.9</span>
            </div>
            <p className="text-coral-200 text-sm">Rating</p>
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <Users className="w-6 h-6 text-coral-400 mr-2" />
              <span className="text-3xl font-bold text-white">50K+</span>
            </div>
            <p className="text-coral-200 text-sm">Happy Customers</p>
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <Home className="w-6 h-6 text-coral-400 mr-2" />
              <span className="text-3xl font-bold text-white">24/7</span>
            </div>
            <p className="text-coral-200 text-sm">Available</p>
          </div>
        </motion.div>

        {/* CTA Button */}
        <motion.button
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 1.4, duration: 0.6 }}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={onNext}
          className="group bg-gradient-to-r from-coral-500 to-coral-600 text-white px-8 py-4 rounded-full text-lg font-semibold shadow-2xl hover:shadow-coral-500/25 transition-all duration-300 flex items-center mx-auto"
        >
          Explore Services
          <ArrowRight className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" />
        </motion.button>
      </div>

      {/* Swipe Indicator */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 2, duration: 1 }}
        className="absolute bottom-8 right-8 flex items-center text-coral-200"
      >
        <span className="text-sm mr-2 hidden md:block">Swipe or scroll right</span>
        <motion.div
          animate={{ x: [0, 10, 0] }}
          transition={{ repeat: Infinity, duration: 2 }}
          className="flex items-center"
        >
          <ArrowRight className="w-5 h-5" />
        </motion.div>
      </motion.div>
    </div>
  );
};

export default HeroPanel;