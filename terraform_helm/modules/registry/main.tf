terraform {
  required_providers {
    kubernetes = {
      source  = "hashicorp/kubernetes"
    }
    helm = {
      source  = "hashicorp/helm"
    }
  }
}


resource "kubernetes_namespace" "registry" {
  metadata {
    name = "registry"
  }
}

resource "kubernetes_secret" "docker_registry" {
  metadata {
    name      = "registry-auth-secret"
    namespace = kubernetes_namespace.registry.metadata[0].name
  }

  type = "kubernetes.io/dockerconfigjson"

  data = {
    ".dockerconfigjson" = base64decode("************************************************************************************************************************************************************************************************************")
  }
}

resource "helm_release" "docker_registry" {
  name       = "docker-registry"
  repository = "https://helm.twun.io"
  chart      = "docker-registry"
  version    = "2.2.3"
  namespace  = kubernetes_namespace.registry.metadata[0].name

  values = [file("${path.module}/values.yaml")]

  depends_on = [kubernetes_namespace.registry]
}