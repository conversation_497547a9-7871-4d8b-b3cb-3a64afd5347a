#!/bin/bash

# Yo<PERSON>LoHago Testing Library
# Functions for testing authentication and API endpoints

# Function to test token generation and validity
test_token() {
    local username=${1:-$DEFAULT_USERNAME}
    local password=${2:-$DEFAULT_PASSWORD}
    
    print_status $CYAN "🔐 Testing token generation for $username..."
    
    local token
    token=$(get_user_token "$username" "$password")
    
    if [[ "$token" != "null" ]] && [[ -n "$token" ]]; then
        print_status $GREEN "✅ Token obtained successfully"
        validate_jwt_token "$token" "$username"
    else
        print_status $RED "❌ Failed to get token"
        return 1
    fi
}

# Function to test backend authentication
test_backend_auth() {
    local username=${1:-$DEFAULT_USERNAME}
    local password=${2:-$DEFAULT_PASSWORD}
    
    print_status $CYAN "🔗 Testing backend authentication for $username..."
    
    local token
    token=$(get_user_token "$username" "$password")
    
    if [[ "$token" == "null" ]] || [[ -z "$token" ]]; then
        print_status $RED "❌ Failed to get token"
        return 1
    fi
    
    # Test /v1/users endpoint (admin only)
    test_api_endpoint "/v1/users" "$token"
}

# Function to test all API endpoints
test_all_endpoints() {
    local username=${1:-$DEFAULT_USERNAME}
    local password=${2:-$DEFAULT_PASSWORD}
    
    print_status $CYAN "🧪 Testing all backend endpoints for $username..."
    
    local token
    token=$(get_user_token "$username" "$password")
    
    if [[ "$token" == "null" ]] || [[ -z "$token" ]]; then
        print_status $RED "❌ Failed to get token"
        return 1
    fi
    
    echo ""
    print_status $BLUE "👤 User Endpoints:"
    # Note: /v1/users/{id} requires specific user ID, testing with admin endpoint instead
    test_api_endpoint "/v1/users" "$token"
    
    echo ""
    print_status $BLUE "📅 Booking Endpoints:"
    test_api_endpoint "/v1/bookings" "$token"
    
    echo ""
    print_status $BLUE "💼 Professional Endpoints:"
    test_api_endpoint "/v1/professionals" "$token"
    
    echo ""
    print_status $BLUE "🛠️ Service Endpoints:"
    test_api_endpoint "/v1/services" "$token"
    
    echo ""
    print_status $BLUE "💬 Message Endpoints:"
    test_api_endpoint "/v1/messages/booking/1001" "$token"
    
    echo ""
    print_status $BLUE "⭐ Review Endpoints:"
    test_api_endpoint "/v1/reviews" "$token"
    
    echo ""
    print_status $BLUE "🔧 Admin Endpoints:"
    test_api_endpoint "/api/admin" "$token"
}

# Function to test user synchronization
test_user_sync() {
    local username=${1:-$DEFAULT_USERNAME}
    local password=${2:-$DEFAULT_PASSWORD}
    
    print_status $CYAN "🔄 Testing user synchronization for $username..."
    
    local token
    token=$(get_user_token "$username" "$password")
    
    if [[ "$token" == "null" ]] || [[ -z "$token" ]]; then
        print_status $RED "❌ Failed to get token"
        return 1
    fi
    
    if ! validate_jwt_token "$token"; then
        return 1
    fi
    
    # Test user endpoint multiple times to see if user gets created
    echo ""
    print_status $BLUE "First request (should trigger user creation):"
    test_api_endpoint "/v1/users/me" "$token"
    
    echo ""
    print_status $BLUE "Second request (should return existing user):"
    test_api_endpoint "/v1/users/me" "$token"
}

# Function to compare localhost vs production environments
compare_environments() {
    local username=${1:-$DEFAULT_USERNAME}
    local password=${2:-$DEFAULT_PASSWORD}
    
    print_status $CYAN "🔄 Comparing localhost vs production authentication for $username..."
    
    echo ""
    print_status $BLUE "🌐 Testing production Keycloak..."
    local prod_token
    prod_token=$(get_user_token "$username" "$password")
    
    if [[ "$prod_token" != "null" ]] && [[ -n "$prod_token" ]]; then
        print_status $GREEN "✅ Production token obtained"
        validate_jwt_token "$prod_token"
    else
        print_status $RED "❌ Failed to get production token"
    fi
    
    echo ""
    print_status $BLUE "🏠 Testing localhost Keycloak..."
    
    # Check if localhost Keycloak is available
    local localhost_check
    localhost_check=$(http_request GET "http://localhost:8081/realms/yotelohago" -s 2>/dev/null)
    local localhost_status
    localhost_status=$(echo "$localhost_check" | cut -d'|' -f1)
    
    if [[ "$localhost_status" == "200" ]]; then
        # Try to get token from localhost
        local local_token_response
        local_token_response=$(http_request POST "http://localhost:8081/realms/yotelohago/protocol/openid-connect/token" \
            -H "Content-Type: application/x-www-form-urlencoded" \
            -d "grant_type=password&client_id=yotelohago-app&client_secret=XGlACct2ZuDR5DHiyvERIvwDGRguLkv6&username=$username&password=$password&scope=openid profile email" \
            -s 2>/dev/null)
        local local_token_status
        local_token_status=$(echo "$local_token_response" | cut -d'|' -f1)
        
        if [[ "$local_token_status" == "200" ]]; then
            local local_token
            local_token=$(echo "$local_token_response" | cut -d'|' -f2- | jq -r '.access_token')
            
            if [[ "$local_token" != "null" ]] && [[ -n "$local_token" ]]; then
                print_status $GREEN "✅ Localhost token obtained"
                
                # Compare token contents
                echo ""
                print_status $BLUE "🔍 Token comparison:"
                
                local prod_sub
                prod_sub=$(decode_jwt_token "$prod_token" | jq -r '.sub // ""')
                local local_sub
                local_sub=$(decode_jwt_token "$local_token" | jq -r '.sub // ""')
                
                echo "  Production sub: $prod_sub"
                echo "  Localhost sub: $local_sub"
                
                if [[ "$prod_sub" == "$local_sub" ]]; then
                    print_status $GREEN "  ✅ Same user ID in both environments"
                else
                    print_status $YELLOW "  ⚠️ Different user IDs (expected for different Keycloak instances)"
                fi
            else
                print_status $RED "❌ Failed to get localhost token"
            fi
        else
            print_status $RED "❌ Failed to get localhost token (Status: $local_token_status)"
        fi
    else
        print_status $YELLOW "❌ Localhost Keycloak not accessible"
    fi
}

# Function to stress test authentication
stress_test_auth() {
    local username=${1:-$DEFAULT_USERNAME}
    local password=${2:-$DEFAULT_PASSWORD}
    local iterations=${3:-5}
    
    print_status $CYAN "🏋️ Stress testing authentication for $username ($iterations iterations)..."
    
    local success_count=0
    local start_time
    start_time=$(date +%s)
    
    for i in $(seq 1 "$iterations"); do
        echo "Iteration $i/$iterations..."
        
        local token
        token=$(get_user_token "$username" "$password")
        
        if [[ "$token" != "null" ]] && [[ -n "$token" ]]; then
            # Test backend endpoint
            local response
            response=$(http_request GET "$BACKEND_URL/v1/users/me" \
                -H "Authorization: Bearer $token" \
                -H "Accept: application/json" -s)
            local status_code
            status_code=$(echo "$response" | cut -d'|' -f1)
            
            if [[ "$status_code" == "200" ]]; then
                echo "  ✅ Success"
                ((success_count++))
            else
                echo "  ❌ Failed with status $status_code"
            fi
        else
            echo "  ❌ Failed to get token"
        fi
    done
    
    local end_time
    end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    echo ""
    print_status $BLUE "📊 Stress Test Results:"
    echo "  Successful requests: $success_count/$iterations"
    echo "  Total time: ${duration}s"
    echo "  Average time per request: $((duration / iterations))s"
    
    if [[ $success_count -eq $iterations ]]; then
        print_status $GREEN "🎉 All requests successful!"
    else
        print_status $YELLOW "⚠️ Some requests failed"
    fi
}

# Function to test specific endpoint with custom data
test_custom_endpoint() {
    local endpoint=$1
    local method=${2:-"GET"}
    local username=${3:-$DEFAULT_USERNAME}
    local password=${4:-$DEFAULT_PASSWORD}
    local data=${5:-""}
    
    print_status $CYAN "🧪 Testing custom endpoint: $method $endpoint"
    
    local token
    token=$(get_user_token "$username" "$password")
    
    if [[ "$token" == "null" ]] || [[ -z "$token" ]]; then
        print_status $RED "❌ Failed to get token"
        return 1
    fi
    
    test_api_endpoint "$endpoint" "$token" "$method" "$data"
}

# Function to test JWT token analysis
analyze_token() {
    local username=${1:-$DEFAULT_USERNAME}
    local password=${2:-$DEFAULT_PASSWORD}
    
    print_status $CYAN "🔍 Analyzing JWT token for $username..."
    
    local token
    token=$(get_user_token "$username" "$password")
    
    if [[ "$token" == "null" ]] || [[ -z "$token" ]]; then
        print_status $RED "❌ Failed to get token"
        return 1
    fi
    
    echo ""
    print_status $BLUE "🔑 Token Analysis:"
    
    local payload
    payload=$(decode_jwt_token "$token")
    
    if [[ $? -eq 0 ]] && [[ "$payload" != "null" ]]; then
        echo ""
        echo "📋 Basic Claims:"
        echo "  Issuer: $(echo "$payload" | jq -r '.iss // "MISSING"')"
        echo "  Subject: $(echo "$payload" | jq -r '.sub // "MISSING"')"
        echo "  Audience: $(echo "$payload" | jq -r '.aud // "MISSING"')"
        echo "  Expiration: $(echo "$payload" | jq -r '.exp // "MISSING"')"
        echo "  Issued At: $(echo "$payload" | jq -r '.iat // "MISSING"')"
        
        echo ""
        echo "👤 User Information:"
        echo "  Username: $(echo "$payload" | jq -r '.preferred_username // "MISSING"')"
        echo "  Email: $(echo "$payload" | jq -r '.email // "MISSING"')"
        echo "  Name: $(echo "$payload" | jq -r '.name // "MISSING"')"
        echo "  Email Verified: $(echo "$payload" | jq -r '.email_verified // "MISSING"')"
        
        echo ""
        echo "🏰 Realm Roles:"
        local realm_roles
        realm_roles=$(echo "$payload" | jq -r '.realm_access.roles[]?' 2>/dev/null)
        if [[ -n "$realm_roles" ]]; then
            echo "$realm_roles" | sed 's/^/  - /'
        else
            echo "  No realm roles found"
        fi
        
        echo ""
        echo "📱 Client Roles:"
        local resource_access
        resource_access=$(echo "$payload" | jq -r '.resource_access // {}')
        if [[ "$resource_access" != "{}" ]]; then
            echo "$resource_access" | jq -r 'to_entries[] | "  \(.key): \(.value.roles | join(", "))"'
        else
            echo "  No client roles found"
        fi
        
        echo ""
        echo "🎯 Scopes:"
        local scope
        scope=$(echo "$payload" | jq -r '.scope // "MISSING"')
        echo "  $scope"
        
        echo ""
        print_status $BLUE "⚠️ Critical Checks:"
        
        # Check for sub claim
        local sub
        sub=$(echo "$payload" | jq -r '.sub // ""')
        if [[ -n "$sub" ]]; then
            print_status $GREEN "  ✅ Sub claim present"
        else
            print_status $RED "  ❌ Sub claim missing - this will cause backend issues!"
        fi
        
        # Check for realm roles
        if [[ -n "$realm_roles" ]]; then
            print_status $GREEN "  ✅ Realm roles present"
        else
            print_status $YELLOW "  ⚠️ No realm roles - user may have limited access"
        fi
        
        # Check for email
        local email
        email=$(echo "$payload" | jq -r '.email // ""')
        if [[ -n "$email" ]]; then
            print_status $GREEN "  ✅ Email claim present"
        else
            print_status $YELLOW "  ⚠️ Email claim missing"
        fi
        
    else
        print_status $RED "❌ Failed to decode token payload"
    fi
}

# Function to show testing help
testing_help() {
    cat << EOF
🧪 Testing - Authentication and API testing

COMMANDS:
    token <username> <password>             Test token generation and validity
    backend <username> <password>           Test backend authentication
    endpoints <username> <password>         Test all API endpoints
    sync <username> <password>              Test user synchronization
    stress <username> <password> [count]    Stress test authentication
    compare-envs <username> <password>      Compare localhost vs production
    analyze <username> <password>           Analyze JWT token contents
    custom <endpoint> [method] [username] [password] [data]  Test custom endpoint

EXAMPLES:
    $0 <NAME_EMAIL> 1234
    $0 <NAME_EMAIL> 1234
    $0 <NAME_EMAIL> 1234 10
    $0 <NAME_EMAIL> 1234
    $0 testing custom /v1/<NAME_EMAIL> 1234

EOF
}

# Main testing function
testing_main() {
    local command=$1
    shift
    
    case "$command" in
        "token")
            test_token "$1" "$2"
            ;;
        "backend")
            test_backend_auth "$1" "$2"
            ;;
        "endpoints")
            test_all_endpoints "$1" "$2"
            ;;
        "sync")
            test_user_sync "$1" "$2"
            ;;
        "stress")
            stress_test_auth "$1" "$2" "$3"
            ;;
        "compare-envs")
            compare_environments "$1" "$2"
            ;;
        "analyze")
            analyze_token "$1" "$2"
            ;;
        "custom")
            test_custom_endpoint "$1" "$2" "$3" "$4" "$5"
            ;;
        *)
            print_status $RED "❌ Unknown testing command: $command"
            testing_help
            exit 1
            ;;
    esac
}
