import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Modal,
  Platform,
} from 'react-native';
import DateTimePicker from '@react-native-community/datetimepicker';
import { TimeRangeDTO } from '../../types/api';

interface TimeRangePickerProps {
  timeRange: TimeRangeDTO;
  onTimeRangeChange: (timeRange: TimeRangeDTO) => void;
  onRemove?: () => void;
  disabled?: boolean;
}

/**
 * Time Range Picker Component
 * Allows users to select start and end times for availability slots
 * Follows the black/white theme established in the calendar
 */
export const TimeRangePicker: React.FC<TimeRangePickerProps> = ({
  timeRange,
  onTimeRangeChange,
  onRemove,
  disabled = false,
}) => {
  const [showStartPicker, setShowStartPicker] = useState(false);
  const [showEndPicker, setShowEndPicker] = useState(false);

  // Convert time string (HH:mm) to Date object for picker
  const timeStringToDate = (timeString: string): Date => {
    const [hours, minutes] = timeString.split(':').map(Number);
    const date = new Date();
    date.setHours(hours, minutes, 0, 0);
    return date;
  };

  // Convert Date object to time string (HH:mm)
  const dateToTimeString = (date: Date): string => {
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    return `${hours}:${minutes}`;
  };

  // Round time to nearest 5-minute interval (matching backend constraint)
  const roundToNearestFiveMinutes = (date: Date): Date => {
    const minutes = date.getMinutes();
    const roundedMinutes = Math.round(minutes / 5) * 5;
    const newDate = new Date(date);
    newDate.setMinutes(roundedMinutes, 0, 0);
    return newDate;
  };

  const handleStartTimeChange = (event: any, selectedDate?: Date) => {
    setShowStartPicker(Platform.OS === 'ios');
    
    if (selectedDate) {
      const roundedDate = roundToNearestFiveMinutes(selectedDate);
      const newStartTime = dateToTimeString(roundedDate);
      
      // Ensure start time is before end time
      const startMinutes = roundedDate.getHours() * 60 + roundedDate.getMinutes();
      const endDate = timeStringToDate(timeRange.endTime);
      const endMinutes = endDate.getHours() * 60 + endDate.getMinutes();
      
      if (startMinutes >= endMinutes) {
        // Auto-adjust end time to be 1 hour after start time
        const newEndDate = new Date(roundedDate);
        newEndDate.setHours(newEndDate.getHours() + 1);
        const newEndTime = dateToTimeString(newEndDate);
        
        onTimeRangeChange({
          startTime: newStartTime,
          endTime: newEndTime,
        });
      } else {
        onTimeRangeChange({
          ...timeRange,
          startTime: newStartTime,
        });
      }
    }
  };

  const handleEndTimeChange = (event: any, selectedDate?: Date) => {
    setShowEndPicker(Platform.OS === 'ios');
    
    if (selectedDate) {
      const roundedDate = roundToNearestFiveMinutes(selectedDate);
      const newEndTime = dateToTimeString(roundedDate);
      
      // Ensure end time is after start time
      const startDate = timeStringToDate(timeRange.startTime);
      const startMinutes = startDate.getHours() * 60 + startDate.getMinutes();
      const endMinutes = roundedDate.getHours() * 60 + roundedDate.getMinutes();
      
      if (endMinutes <= startMinutes) {
        // Auto-adjust start time to be 1 hour before end time
        const newStartDate = new Date(roundedDate);
        newStartDate.setHours(newStartDate.getHours() - 1);
        const newStartTime = dateToTimeString(newStartDate);
        
        onTimeRangeChange({
          startTime: newStartTime,
          endTime: newEndTime,
        });
      } else {
        onTimeRangeChange({
          ...timeRange,
          endTime: newEndTime,
        });
      }
    }
  };

  const formatTimeForDisplay = (timeString: string): string => {
    const [hours, minutes] = timeString.split(':').map(Number);
    const period = hours >= 12 ? 'PM' : 'AM';
    const displayHours = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;
    return `${displayHours}:${minutes.toString().padStart(2, '0')} ${period}`;
  };

  return (
    <View style={styles.container}>
      <View style={styles.timeRangeContainer}>
        {/* Start Time Picker */}
        <TouchableOpacity
          style={[styles.timeButton, disabled && styles.disabledButton]}
          onPress={() => !disabled && setShowStartPicker(true)}
          disabled={disabled}
        >
          <Text style={[styles.timeLabel, disabled && styles.disabledText]}>
            From
          </Text>
          <Text style={[styles.timeValue, disabled && styles.disabledText]}>
            {formatTimeForDisplay(timeRange.startTime)}
          </Text>
        </TouchableOpacity>

        <Text style={styles.separator}>to</Text>

        {/* End Time Picker */}
        <TouchableOpacity
          style={[styles.timeButton, disabled && styles.disabledButton]}
          onPress={() => !disabled && setShowEndPicker(true)}
          disabled={disabled}
        >
          <Text style={[styles.timeLabel, disabled && styles.disabledText]}>
            Until
          </Text>
          <Text style={[styles.timeValue, disabled && styles.disabledText]}>
            {formatTimeForDisplay(timeRange.endTime)}
          </Text>
        </TouchableOpacity>

        {/* Remove Button */}
        {onRemove && (
          <TouchableOpacity
            style={[styles.removeButton, disabled && styles.disabledButton]}
            onPress={onRemove}
            disabled={disabled}
          >
            <Text style={[styles.removeButtonText, disabled && styles.disabledText]}>
              ×
            </Text>
          </TouchableOpacity>
        )}
      </View>

      {/* Start Time Picker Modal */}
      {showStartPicker && (
        <Modal
          transparent={true}
          animationType="slide"
          visible={showStartPicker}
          onRequestClose={() => setShowStartPicker(false)}
        >
          <View style={styles.modalContainer}>
            <View style={styles.pickerContainer}>
              <View style={styles.pickerHeader}>
                <TouchableOpacity onPress={() => setShowStartPicker(false)}>
                  <Text style={styles.pickerHeaderButton}>Cancel</Text>
                </TouchableOpacity>
                <Text style={styles.pickerTitle}>Start Time</Text>
                <TouchableOpacity onPress={() => setShowStartPicker(false)}>
                  <Text style={styles.pickerHeaderButton}>Done</Text>
                </TouchableOpacity>
              </View>
              <DateTimePicker
                value={timeStringToDate(timeRange.startTime)}
                mode="time"
                display="spinner"
                onChange={handleStartTimeChange}
                minuteInterval={5}
              />
            </View>
          </View>
        </Modal>
      )}

      {/* End Time Picker Modal */}
      {showEndPicker && (
        <Modal
          transparent={true}
          animationType="slide"
          visible={showEndPicker}
          onRequestClose={() => setShowEndPicker(false)}
        >
          <View style={styles.modalContainer}>
            <View style={styles.pickerContainer}>
              <View style={styles.pickerHeader}>
                <TouchableOpacity onPress={() => setShowEndPicker(false)}>
                  <Text style={styles.pickerHeaderButton}>Cancel</Text>
                </TouchableOpacity>
                <Text style={styles.pickerTitle}>End Time</Text>
                <TouchableOpacity onPress={() => setShowEndPicker(false)}>
                  <Text style={styles.pickerHeaderButton}>Done</Text>
                </TouchableOpacity>
              </View>
              <DateTimePicker
                value={timeStringToDate(timeRange.endTime)}
                mode="time"
                display="spinner"
                onChange={handleEndTimeChange}
                minuteInterval={5}
              />
            </View>
          </View>
        </Modal>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 8,
  },
  timeRangeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E5E5',
  },
  timeButton: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#000000',
    backgroundColor: '#FFFFFF',
  },
  disabledButton: {
    borderColor: '#CCCCCC',
    backgroundColor: '#F5F5F5',
  },
  timeLabel: {
    fontSize: 12,
    color: '#666666',
    marginBottom: 2,
  },
  timeValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
  },
  disabledText: {
    color: '#CCCCCC',
  },
  separator: {
    fontSize: 14,
    color: '#666666',
    marginHorizontal: 12,
  },
  removeButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#000000',
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 12,
  },
  removeButtonText: {
    color: '#FFFFFF',
    fontSize: 20,
    fontWeight: 'bold',
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  pickerContainer: {
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: 34, // Safe area padding
  },
  pickerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E5',
  },
  pickerHeaderButton: {
    fontSize: 16,
    color: '#000000',
    fontWeight: '600',
  },
  pickerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
  },
});

export default TimeRangePicker;
