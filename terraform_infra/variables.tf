variable "resource_base_name" {
  description = "Base value all resource to identify them on AWS console"
  default     = "jenkins"
}

variable "region" {
  description = "The AWS region to deploy the backend."
  type        = string
  default     = "us-east-1"
}

variable "instance_type" {
  description = "Value for Instance type"
  default     = "t3.medium"
}

variable "ec2_key_path" {
  description = "The path of your key locally"
  default     = "/Users/<USER>/.ssh/aws/key"
}

variable "ec2_user" {
  type        = string
  default     = "ec2_user"
}

variable "vpc_cidr" {
  description = "Value for cidr"
  default     = "10.0.0.0/16"
}

# Define a list of CIDR blocks for the subnets
variable "subnet_cidrs" {
  type    = list(string)
  default = ["********/24", "********/24"]
}

# Define a list of Availability Zones
variable "availability_zones" {
  type    = list(string)
  default = ["us-east-1a", "us-east-1b"]
}

variable "repository_name" {
  description = "Elastic container registry repository name."
  type        = string
}

variable "tailscale_dns_nameserver" {
  description = "DNS resolver for your Tailscale network (e.g. 100.x.x.x)"
  type        = string
}

### FolLowing vars are going to be injected as env var with the format TF_VAR

variable "cloudflare_api_token" {
  type        = string
  description = "API token for managing Cloudflare DNS"
  sensitive   = true
}

variable "tailscale_api_key" {
  description = "API key for Tailscale access"
  type        = string
  sensitive   = true
}




