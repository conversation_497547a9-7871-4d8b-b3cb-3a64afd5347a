{"expo": {"name": "YoteLoHago", "slug": "yo<PERSON>ohago", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "yo<PERSON>ohago", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "bundleIdentifier": "com.yotelohago.app", "infoPlist": {"CFBundleURLTypes": [{"CFBundleURLName": "yo<PERSON>ohago", "CFBundleURLSchemes": ["yo<PERSON>ohago"]}]}}, "android": {"package": "com.yotelohago.app", "intentFilters": [{"action": "VIEW", "autoVerify": true, "data": [{"scheme": "yo<PERSON>ohago"}], "category": ["BROWSABLE", "DEFAULT"]}]}, "web": {"bundler": "metro", "output": "single", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router"], "experiments": {"typedRoutes": true}}}