package com.yotelohago.user.domain;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface UserExternalIdentityRepository {
    void persist(UserExternalIdentity identity);
    boolean tryInsertIdentity(UUID identityId, UUID userId, AuthProvider provider, String externalId, java.time.Instant createdAt);
    Optional<UserExternalIdentity> findById(UUID id);
    Optional<UserExternalIdentity> findByProviderAndExternalId(AuthProvider provider, String externalId);
    Optional<UserExternalIdentity> findByUserId(UUID userId);
    List<UserExternalIdentity> findAllByUserId(UUID userId);
    List<UserExternalIdentity> findByProvider(AuthProvider provider);
    List<UserExternalIdentity> findAll();
    UserExternalIdentity update(UserExternalIdentity identity);
    void deleteById(UUID id);
    void deleteByUserIdAndProvider(UUID userId, AuthProvider provider);
    void flush();
}
