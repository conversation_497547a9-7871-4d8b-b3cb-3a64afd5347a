import React, { useState, useMemo, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Dimensions, Platform, RefreshControl, ActivityIndicator } from 'react-native';
import { useRouter } from 'expo-router';
import { LegendList } from '@legendapp/list';
import JobItem from '../components/JobItem';
import Colors from '../constants/Colors';
import { Service, Booking } from '@/types';
import { BookingStatus } from '../../../types/api';
import { AuthGuard } from '../../../components/auth';
import { useBookings } from '../../../hooks';

type FrontendBookingStatus = 'open' | 'accepted' | 'completed' | 'cancelled';

export default function JobsScreen() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState<FrontendBookingStatus>('open');
  const { width } = Dimensions.get('window');
  const isWeb = Platform.OS === 'web';

  // Use the useBookings hook for real API integration
  const {
    bookings: apiBookings,
    isLoading,
    error,
    isEmpty,
    refresh,
  } = useBookings({ autoLoad: true });

  // Debug logging for API bookings
  useEffect(() => {
    console.log('💼 JobsScreen - API bookings updated:', {
      bookingsCount: apiBookings?.length || 0,
      isLoading,
      error,
      isEmpty,
      bookings: apiBookings?.map(b => ({ id: b.id, status: b.status, title: b.title })) || []
    });
  }, [apiBookings, isLoading, error, isEmpty]);

  // Calculate grid configuration
  const gridConfig = useMemo(() => {
    if (isWeb) {
      const columns = width >= 1200 ? 3 : width >= 768 ? 2 : 1;
      const padding = 32;
      const gap = 16;
      const cardWidth = (width - padding - (gap * (columns - 1))) / columns;
      return { columns, cardWidth, gap };
    } else {
      return { columns: 1, cardWidth: width - 32, gap: 0 };
    }
  }, [width, isWeb]);

  // Filter bookings by status from API data
  const filteredBookings = useMemo(() => {
    if (!apiBookings || apiBookings.length === 0) {
      console.log('🔍 No API bookings available for filtering');
      return [];
    }

    const filtered = apiBookings.filter(booking => booking.status === activeTab);

    console.log('🔍 Filtering jobs:', {
      activeTab,
      totalBookings: apiBookings.length,
      filteredCount: filtered.length,
      allStatuses: apiBookings.map(b => ({ id: b.id, status: b.status, title: b.title })),
      filteredBookings: filtered.map(b => ({ id: b.id, status: b.status, title: b.title }))
    });

    return filtered;
  }, [apiBookings, activeTab]);

  // Group bookings into rows for grid layout
  const groupedBookings = useMemo(() => {
    const rows: Booking[][] = [];
    for (let i = 0; i < filteredBookings.length; i += gridConfig.columns) {
      rows.push(filteredBookings.slice(i, i + gridConfig.columns));
    }
    return rows;
  }, [filteredBookings, gridConfig.columns]);

  const handleJobPress = (booking: Booking) => {
    router.push({
      pathname: '/professional/service/[id]' as any,
      params: { id: booking.serviceId }
    });
  };

  const handleMessagePress = (clientId: string) => {
    // In a real app, navigate to message screen with this client
    console.log('Message client:', clientId);
  };

  const handleRefresh = async () => {
    try {
      await refresh();
    } catch (error) {
      console.error('Failed to refresh jobs:', error);
    }
  };

  // Render job row for grid layout
  const renderJobRow = ({ item: row, index }: { item: Booking[], index: number }) => {
    if (gridConfig.columns === 1) {
      // Single column layout
      const booking = row[0];

      // Create service and client objects from enriched booking data
      const service: Service = {
        id: booking.serviceId,
        providerId: booking.providerId,
        title: booking.serviceTitle || booking.title,
        description: booking.description || '',
        category: booking.serviceCategoryName || 'General',
        price: booking.price,
        location: 'Location not available', // TODO: Add location to enriched DTO
        images: [],
        status: booking.status,
        date: booking.date,
        time: booking.time,
        rating: 0, // TODO: Add rating to enriched DTO
        reviews: 0, // TODO: Add reviews to enriched DTO
        instantBook: false,
      };

      // For professional mode, we want to show CLIENT information instead of professional
      const client = {
        id: booking.clientId,
        name: booking.clientFullName || 'Client',
        email: '',
        avatar: (booking as any).clientProfileImageUrl || '',
        bio: '',
        role: 'user' as const,
        services: [],
        favorites: [],
      };

      return (
        <View style={styles.jobItemContainer}>
          <JobItem
            booking={booking}
            service={service}
            client={client}
            onPress={handleJobPress}
            onMessagePress={handleMessagePress}
          />
        </View>
      );
    }

    // Multi-column grid layout
    return (
      <View style={styles.jobRowContainer}>
        {row.map((booking, bookingIndex) => {
          // Create service and client objects from enriched booking data
          const service: Service = {
            id: booking.serviceId,
            providerId: booking.providerId,
            title: booking.serviceTitle || booking.title,
            description: booking.description || '',
            category: booking.serviceCategoryName || 'General',
            price: booking.price,
            location: 'Location not available',
            images: [],
            status: booking.status,
            date: booking.date,
            time: booking.time,
            rating: 0,
            reviews: 0,
            instantBook: false,
          };

          // For professional mode, we want to show CLIENT information
          const client = {
            id: booking.clientId,
            name: booking.clientFullName || 'Client',
            email: '',
            avatar: (booking as any).clientProfileImageUrl || '',
            bio: '',
            role: 'user' as const,
            services: [],
            favorites: [],
          };

          return (
            <View
              key={booking.id}
              style={[
                styles.jobColumnContainer,
                { width: gridConfig.cardWidth }
              ]}
            >
              <JobItem
                booking={booking}
                service={service}
                client={client}
                onPress={handleJobPress}
                onMessagePress={handleMessagePress}
              />
            </View>
          );
        })}
        {/* Fill empty spaces in the last row */}
        {row.length < gridConfig.columns &&
          Array.from({ length: gridConfig.columns - row.length }).map((_, emptyIndex) => (
            <View
              key={`empty-${emptyIndex}`}
              style={[
                styles.jobColumnContainer,
                { width: gridConfig.cardWidth }
              ]}
            />
          ))
        }
      </View>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Text style={styles.emptyTitle}>
        No {activeTab} jobs
      </Text>
      <Text style={styles.emptySubtitle}>
        Your {activeTab} service jobs will appear here.
      </Text>
    </View>
  );

  const renderLoadingState = () => (
    <View style={styles.loadingContainer}>
      <ActivityIndicator size="large" color={Colors.primary} />
      <Text style={styles.loadingText}>Loading jobs...</Text>
    </View>
  );

  const renderErrorState = () => (
    <View style={styles.errorContainer}>
      <Text style={styles.errorTitle}>Unable to load jobs</Text>
      <Text style={styles.errorText}>{error}</Text>
      <TouchableOpacity style={styles.retryButton} onPress={handleRefresh}>
        <Text style={styles.retryButtonText}>Try Again</Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <View style={styles.container}>
      {/* Centered Header */}
      <View style={styles.centeredHeader}>
        <Text style={styles.centeredTitle}>Jobs</Text>
      </View>

      <AuthGuard
        actionType="booking"
        promptTitle="Manage Your Jobs"
        promptMessage="Log in to view and manage your service jobs and appointments."
        tabIcon="briefcase-outline"
      >
        <View style={styles.tabContainer}>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'open' && styles.activeTab]}
            onPress={() => setActiveTab('open')}
          >
            <Text
              style={[
                styles.tabText,
                activeTab === 'open' && styles.activeTabText
              ]}
            >
              Open
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.tab, activeTab === 'accepted' && styles.activeTab]}
            onPress={() => setActiveTab('accepted')}
          >
            <Text
              style={[
                styles.tabText,
                activeTab === 'accepted' && styles.activeTabText
              ]}
            >
              Accepted
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.tab, activeTab === 'completed' && styles.activeTab]}
            onPress={() => setActiveTab('completed')}
          >
            <Text
              style={[
                styles.tabText,
                activeTab === 'completed' && styles.activeTabText
              ]}
            >
              Completed
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.tab, activeTab === 'cancelled' && styles.activeTab]}
            onPress={() => setActiveTab('cancelled')}
          >
            <Text
              style={[
                styles.tabText,
                activeTab === 'cancelled' && styles.activeTabText
              ]}
            >
              Cancelled
            </Text>
          </TouchableOpacity>
        </View>

        {/* Jobs List with LegendList */}
        <View style={styles.listContainer}>
          {isLoading ? (
            renderLoadingState()
          ) : error ? (
            renderErrorState()
          ) : filteredBookings.length === 0 ? (
            renderEmptyState()
          ) : (
            <LegendList
              data={groupedBookings}
              renderItem={renderJobRow}
              keyExtractor={(item: Booking[], index: number) =>
                gridConfig.columns === 1 ? item[0].id : `row-${index}-${item.map(b => b.id).join('-')}`
              }
              refreshControl={
                <RefreshControl
                  refreshing={isLoading}
                  onRefresh={handleRefresh}
                  colors={[Colors.primary]}
                  tintColor={Colors.primary}
                />
              }
              contentContainerStyle={styles.listContent}
              showsVerticalScrollIndicator={false}
              estimatedItemSize={gridConfig.columns > 1 ? 180 : 120}
              recycleItems
            />
          )}
        </View>
      </AuthGuard>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.primary,
  },
  centeredHeader: {
    backgroundColor: Colors.background.primary,
    paddingTop: 60,
    paddingBottom: 16,
    paddingHorizontal: 16,
    alignItems: 'center',
  },
  centeredTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: Colors.text.primary,
    textAlign: 'center',
  },
  header: {
    backgroundColor: Colors.background.primary,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 30,
    paddingBottom: 16,
    paddingHorizontal: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: Colors.text.primary,
    flex: 1,
    textAlign: 'center',
  },
  tabContainer: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    backgroundColor: Colors.background.primary,
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: Colors.primary,
  },
  tabText: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.text.secondary,
  },
  activeTabText: {
    color: Colors.primary,
  },
  listContainer: {
    flex: 1,
  },
  listContent: {
    padding: 10,
  },
  jobItemContainer: {
    marginBottom: 12,
  },
  jobRowContainer: {
    flexDirection: 'row',
    marginBottom: 12,
    gap: 10,
  },
  jobColumnContainer: {
    flex: 1,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
    paddingHorizontal: 20,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text.primary,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptySubtitle: {
    fontSize: 16,
    color: Colors.text.secondary,
    textAlign: 'center',
    lineHeight: 22,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    fontSize: 16,
    color: Colors.text.secondary,
    marginTop: 12,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
    paddingHorizontal: 20,
  },
  errorTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text.primary,
    marginBottom: 8,
    textAlign: 'center',
  },
  errorText: {
    fontSize: 14,
    color: Colors.text.secondary,
    textAlign: 'center',
    marginBottom: 20,
    lineHeight: 20,
  },
  retryButton: {
    backgroundColor: Colors.primary,
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  retryButtonText: {
    color: Colors.text.light,
    fontSize: 14,
    fontWeight: '600',
  },
});
