package com.yotelohago.review.application;

import com.yotelohago.auth.TokenIdentityProvider;
import com.yotelohago.booking.domain.Booking;
import com.yotelohago.booking.domain.BookingRepository;
import com.yotelohago.booking.domain.BookingStatus;
import com.yotelohago.review.domain.Review;
import com.yotelohago.review.domain.ReviewRepository;
import com.yotelohago.user.application.UserSyncService;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;
import jakarta.ws.rs.ForbiddenException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@ApplicationScoped
public class ReviewManagementService {

    private static final Logger logger = LoggerFactory.getLogger(ReviewManagementService.class);

    @Inject
    ReviewRepository reviewRepository;

    @Inject
    BookingRepository bookingRepository;

    @Inject
    TokenIdentityProvider tokenIdentityProvider;

    @Inject
    UserSyncService userSyncService;

    /**
     * Get all reviews - Public access
     * TODO: Add pagination for performance
     */
    public List<Review> findAll() {
        return reviewRepository.findAll();
    }

    /**
     * Get review by ID - Public access
     */
    public Optional<Review> findById(Long id) {
        return reviewRepository.findById(id);
    }

    /**
     * Get review by booking ID - Public access
     */
    public Optional<Review> findByBookingId(Long bookingId) {
        return reviewRepository.findByBookingId(bookingId);
    }

    /**
     * Get reviews for a professional - Public access
     * TODO: Add pagination for performance
     */
    public List<Review> findByProfessionalUserId(UUID professionalUserId) {
        return reviewRepository.findByProfessionalUserId(professionalUserId);
    }

    /**
     * Create a review - Only the client who made the booking can review after completion
     */
    @Transactional
    public Review create(Review review) {
        // Ensure current user exists in database
        userSyncService.ensureCurrentUserExists();

        // Validate rating
        if (!review.isValidRating()) {
            throw new IllegalArgumentException("Rating must be between 1 and 5 stars");
        }

        // Get the booking to validate
        Booking booking = bookingRepository.findById(review.getBooking().id)
                .orElseThrow(() -> new IllegalArgumentException("Booking not found"));

        // Check if booking is completed
        if (booking.status != BookingStatus.COMPLETED) {
            throw new IllegalArgumentException("Can only review completed bookings");
        }

        // Check if current user is the client of this booking
        UUID currentUserId = tokenIdentityProvider.getCurrentUserId();
        if (currentUserId == null) {
            throw new ForbiddenException("Authentication required");
        }

        // Check if current user can access this booking
        if (!tokenIdentityProvider.canAccessBooking(booking.getClient().id,
                booking.getProfessional() != null ? booking.getProfessional().id : null)) {
            throw new ForbiddenException("Only the client who made the booking can create a review");
        }

        // Additional check: ensure current user is specifically the client (not the professional)
        if (!currentUserId.equals(booking.getClient().id)) {
            throw new ForbiddenException("Only the client who made the booking can create a review");
        }

        // Check if review already exists for this booking
        if (reviewRepository.findByBookingId(review.getBooking().id).isPresent()) {
            throw new IllegalArgumentException("Review already exists for this booking");
        }

        // Set the client and professional entities from booking (security measure)
        review.setClient(booking.getClient());
        review.setProfessional(booking.getProfessional());
        review.setCreatedAt(Instant.now());

        reviewRepository.persist(review);
        reviewRepository.flush();

        logger.info("Review created for booking {} by client {}", review.getBooking().id, currentUserId);
        return review;
    }

    /**
     * Delete a review - Admin only
     */
    @Transactional
    public boolean delete(Long id) {
        // Ensure current user exists in database
        userSyncService.ensureCurrentUserExists();

        // Check if current user is admin
        if (!tokenIdentityProvider.hasRole("admin")) {
            throw new ForbiddenException("Admin access required to delete reviews");
        }

        // Check if review exists
        Optional<Review> reviewOpt = reviewRepository.findById(id);
        if (reviewOpt.isEmpty()) {
            return false;
        }

        reviewRepository.deleteById(id);
        logger.info("Review {} deleted by admin", id);
        return true;
    }

}