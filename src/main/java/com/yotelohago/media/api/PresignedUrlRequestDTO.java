package com.yotelohago.media.api;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Positive;

import java.util.UUID;

/**
 * Request DTO for generating presigned upload URLs
 */
public class PresignedUrlRequestDTO {

    /**
     * Original filename to be uploaded
     */
    @NotBlank(message = "Filename is required")
    public String filename;

    /**
     * Content type of the file (e.g., image/jpeg, image/png)
     */
    @NotBlank(message = "Content type is required")
    @Pattern(regexp = "^(image|application|text)/.*", message = "Invalid content type")
    public String contentType;

    /**
     * File size in bytes
     */
    @NotNull(message = "File size is required")
    @Positive(message = "File size must be positive")
    public Long fileSize;

    /**
     * Type of media being uploaded
     */
    @NotBlank(message = "Media type is required")
    @Pattern(regexp = "^(client_profile_picture|professional_profile_picture|service_photo|document)$", 
             message = "Invalid media type")
    public String mediaType;

    /**
     * Associated entity ID (required for service photos, optional for profile pictures)
     */
    public UUID associatedEntityId;

    /**
     * Default constructor
     */
    public PresignedUrlRequestDTO() {}

    /**
     * Constructor for creating request DTOs
     */
    public PresignedUrlRequestDTO(String filename, String contentType, Long fileSize, 
                                  String mediaType, UUID associatedEntityId) {
        this.filename = filename;
        this.contentType = contentType;
        this.fileSize = fileSize;
        this.mediaType = mediaType;
        this.associatedEntityId = associatedEntityId;
    }
}
