package com.yotelohago.review.api;

import com.yotelohago.review.domain.Review;
import com.yotelohago.booking.domain.Booking;
import com.yotelohago.user.domain.User;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

@ApplicationScoped
public class ReviewMapper {

    @PersistenceContext
    EntityManager entityManager; // CDI pattern like we use on ServiceMapper class

    public ReviewDTO toDTO(Review r) {
        ReviewDTO dto = new ReviewDTO();
        dto.id = r.getId();
        dto.bookingId = r.getBooking().id;
        dto.professionalId = r.getProfessional().id;
        dto.clientId = r.getClient().id;
        dto.rating = r.getRating();
        dto.comment = r.getComment();
        dto.createdAt = r.getCreatedAt();
        return dto;
    }

    public Review toEntity(ReviewDTO dto) {
        // Load the required entities from database
        Booking booking = Booking.findById(dto.bookingId);
        User professional = User.findById(dto.professionalId);
        User client = User.findById(dto.clientId);

        if (booking == null) {
            throw new IllegalArgumentException("Booking not found: " + dto.bookingId);
        }
        if (professional == null) {
            throw new IllegalArgumentException("Professional not found: " + dto.professionalId);
        }
        if (client == null) {
            throw new IllegalArgumentException("Client not found: " + dto.clientId);
        }

        // Create review with proper entity relationships
        Review review = new Review(booking, professional, client, dto.rating, dto.comment);
        review.id = dto.id;
        review.setCreatedAt(dto.createdAt);

        return review;
    }
}