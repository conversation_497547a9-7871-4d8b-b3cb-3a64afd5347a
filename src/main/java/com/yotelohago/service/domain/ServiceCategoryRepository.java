package com.yotelohago.service.domain;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository interface for ServiceCategory entity operations
 */
public interface ServiceCategoryRepository {
    
    /**
     * Persist a new service category
     * @param category The service category to persist
     */
    void persist(ServiceCategory category);
    
    /**
     * Find a service category by its ID
     * @param id The category ID
     * @return Optional containing the category if found
     */
    Optional<ServiceCategory> findById(UUID id);
    
    /**
     * Find a service category by its name
     * @param name The category name
     * @return Optional containing the category if found
     */
    Optional<ServiceCategory> findByName(String name);
    
    /**
     * Find all service categories
     * @return List of all service categories
     */
    List<ServiceCategory> findAll();
    
    /**
     * Update an existing service category
     * @param category The category to update
     * @return The updated category
     */
    ServiceCategory update(ServiceCategory category);
    
    /**
     * Delete a service category by ID
     * @param id The category ID to delete
     */
    void deleteById(UUID id);
    
    /**
     * Check if a category exists by name
     * @param name The category name
     * @return true if category exists, false otherwise
     */
    boolean existsByName(String name);
    
    /**
     * Count total number of service categories
     * @return Total count of categories
     */
    long count();
    
    /**
     * Flush pending changes to the database
     */
    void flush();
}
