-- YoteLoHago Sample Data - Updated Schema with Foreign Key Constraints
-- This file contains sample data for all entities in the system with the new user identification architecture
-- All UUIDs are properly formatted according to PostgreSQL UUID requirements
-- Foreign key constraints are created to eliminate startup warnings

-- =====================================================
-- 0. FOREIGN KEY CONSTRAINTS CREATION
-- =====================================================
-- Create foreign key constraints that match the @ForeignKey annotations in entities
-- This eliminates the "constraint does not exist, skipping" warnings during startup
-- Note: PostgreSQL doesn't support IF NOT EXISTS for ALTER TABLE ADD CONSTRAINT
-- These constraints will be created by Hibernate automatically, but we can add them here for clarity

-- The foreign key constraints are automatically created by Hibernate based on @ForeignKey annotations:
-- - fk_user_external_identity_user: user_external_identities(user_id) → users(id)
-- - fk_professionals_user: professionals(user_id) → users(id)
-- - fk_services_professional_user: services(professional_user_id) → users(id)
-- - fk_services_category: services(category_id) → service_categories(id)
-- - fk_bookings_client: bookings(client_id) → users(id)
-- - fk_bookings_professional: bookings(professional_id) → users(id)
-- - fk_bookings_service: bookings(service_id) → services(id)
-- - fk_favorites_user: favorites(user_id) → users(id)
-- - fk_favorites_service: favorites(service_id) → services(id)
-- - fk_messages_booking: messages(booking_id) → bookings(id)
-- - fk_messages_sender: messages(sender_id) → users(id)
-- - fk_messages_receiver: messages(receiver_id) → users(id)
-- - fk_reviews_booking: reviews(booking_id) → bookings(id)
-- - fk_reviews_client: reviews(client_id) → users(id)
-- - fk_reviews_professional: reviews(professional_id) → users(id)

-- =====================================================
-- 1. SERVICE CATEGORIES (4 records)
-- =====================================================
INSERT INTO service_categories (id, name, description, icon_url) VALUES
('00000000-0000-0000-0000-000000000001', 'electrician', 'Licensed electrical services including wiring, installations, repairs, and safety inspections', 'electric'),
('00000000-0000-0000-0000-000000000002', 'plumber', 'Professional plumbing services including repairs, installations, maintenance, and emergency services', 'pipe-leak'),
('00000000-0000-0000-0000-000000000003', 'mover', 'Professional moving services including packing, transportation, and unpacking for residential and commercial moves', 'truck'),
('00000000-0000-0000-0000-000000000004', 'locksmith', 'Expert locksmith services including lock installation, repair, key duplication, and emergency lockout assistance', 'unlock');

-- =====================================================
-- 2. USERS (8 records - 5 professionals + 3 clients)
-- =====================================================
INSERT INTO users (id, username, first_name, last_name, email, phone, is_professional, created_at, updated_at) VALUES
-- Clients (3 records)
('00000000-0000-0000-0000-000000000101', 'test1client1', 'test1', 'client1', '<EMAIL>', '******-0101', false, '2024-01-15T10:30:00Z', '2024-01-15T10:30:00Z'),
('00000000-0000-0000-0000-000000000102', 'test2client2', 'test2', 'client2', '<EMAIL>', '******-0102', false, '2024-01-16T14:20:00Z', '2024-01-16T14:20:00Z'),
('00000000-0000-0000-0000-000000000103', 'test3client3', 'test3', 'client3', '<EMAIL>', '******-0103', false, '2024-01-17T09:15:00Z', '2024-01-17T09:15:00Z'),
-- Professionals (5 records)
('00000000-0000-0000-0000-000000000201', 'test1professional1', 'test1', 'professional1', '<EMAIL>', '******-0201', true, '2024-01-18T16:45:00Z', '2024-01-18T16:45:00Z'),
('00000000-0000-0000-0000-000000000202', 'test2professional2', 'test2', 'professional2', '<EMAIL>', '******-0202', true, '2024-01-19T11:20:00Z', '2024-01-19T11:20:00Z'),
('00000000-0000-0000-0000-000000000203', 'test3professional3', 'test3', 'professional3', '<EMAIL>', '******-0203', true, '2024-01-20T09:30:00Z', '2024-01-20T09:30:00Z'),
('00000000-0000-0000-0000-000000000204', 'test4professional4', 'test4', 'professional4', '<EMAIL>', '******-0204', true, '2024-01-21T14:15:00Z', '2024-01-21T14:15:00Z'),
('00000000-0000-0000-0000-000000000205', 'test5professional5', 'test5', 'professional5', '<EMAIL>', '******-0205', true, '2024-01-22T08:45:00Z', '2024-01-22T08:45:00Z');

-- =====================================================
-- 3. USER EXTERNAL IDENTITIES (8 records - Keycloak mappings)
-- =====================================================
INSERT INTO user_external_identities (id, user_id, provider, external_id, created_at) VALUES
-- Client external identities
('00000000-0000-0000-0000-000000000301', '00000000-0000-0000-0000-000000000101', 'KEYCLOAK', '934fccfd-47b8-40ac-be95-ec3fb93f3797', '2024-01-15T10:30:00Z'),
('00000000-0000-0000-0000-000000000302', '00000000-0000-0000-0000-000000000102', 'KEYCLOAK', '00000000-0000-0000-0000-000000000402', '2024-01-16T14:20:00Z'),
('00000000-0000-0000-0000-000000000303', '00000000-0000-0000-0000-000000000103', 'KEYCLOAK', '00000000-0000-0000-0000-000000000403', '2024-01-17T09:15:00Z'),
-- Professional external identities
('00000000-0000-0000-0000-000000000304', '00000000-0000-0000-0000-000000000201', 'KEYCLOAK', '51e08b06-daad-40be-86cf-f36ed78a7a94', '2024-01-18T16:45:00Z'),
('00000000-0000-0000-0000-000000000305', '00000000-0000-0000-0000-000000000202', 'KEYCLOAK', 'bec1b2dd-fb8d-4d80-8e6f-f08632cd38ec', '2024-01-19T11:20:00Z'),
('00000000-0000-0000-0000-000000000306', '00000000-0000-0000-0000-000000000203', 'KEYCLOAK', '00000000-0000-0000-0000-000000000406', '2024-01-20T09:30:00Z'),
('00000000-0000-0000-0000-000000000307', '00000000-0000-0000-0000-000000000204', 'KEYCLOAK', '00000000-0000-0000-0000-000000000407', '2024-01-21T14:15:00Z'),
('00000000-0000-0000-0000-000000000308', '00000000-0000-0000-0000-000000000205', 'KEYCLOAK', '00000000-0000-0000-0000-000000000408', '2024-01-22T08:45:00Z');

-- =====================================================
-- 4. PROFESSIONALS (5 records - using internal user IDs)
-- =====================================================
INSERT INTO professionals (user_id, bio, city, avg_rating, rating_count, available) VALUES
('00000000-0000-0000-0000-000000000201', 'Experienced plumber with 15+ years in residential and commercial plumbing. Licensed and insured.', 'Miami', 4.8, 127, true),
('00000000-0000-0000-0000-000000000202', 'Certified electrician specializing in smart home installations and electrical repairs. Available 24/7 for emergencies.', 'Orlando', 4.9, 89, true),
('00000000-0000-0000-0000-000000000203', 'Professional locksmith with expertise in residential and commercial security systems. Emergency services available.', 'Tampa', 4.7, 156, true),
('00000000-0000-0000-0000-000000000204', 'Master electrician specializing in smart home installations and energy-efficient solutions.', 'Jacksonville', 4.9, 203, true),
('00000000-0000-0000-0000-000000000205', 'Professional moving company owner with a team of experienced movers. Fully insured.', 'Fort Lauderdale', 4.6, 78, false);

-- =====================================================
-- 5. SERVICES (10 records) - Using updated Service entity schema
-- =====================================================
-- Service entity fields: id, professional_user_id, title, description, price, category_id, status, created_at, updated_at
-- Note: Now using professional_user_id (internal user IDs) for proper architecture
INSERT INTO services (id, professional_user_id, title, description, price, category_id, status, created_at, updated_at) VALUES
-- Plumber services (2 records) - using internal user IDs with proper BigDecimal prices
('00000000-0000-0000-0000-000000000501', '00000000-0000-0000-0000-000000000201', 'Emergency Plumbing Repair', 'Urgent plumbing repairs including leaks, clogs, and pipe bursts. Available 24/7 for emergency situations.', 125.00, '00000000-0000-0000-0000-000000000002', 'PUBLISHED', '2024-01-12T10:00:00Z', '2024-01-12T10:00:00Z'),
('00000000-0000-0000-0000-000000000502', '00000000-0000-0000-0000-000000000201', 'Water Heater Installation', 'Professional installation of gas and electric water heaters with warranty and safety inspection.', 650.00, '00000000-0000-0000-0000-000000000002', 'PUBLISHED', '2024-01-12T10:00:00Z', '2024-01-12T10:00:00Z'),
-- Electrician services (3 records) - using internal user IDs with proper BigDecimal prices
('00000000-0000-0000-0000-000000000503', '00000000-0000-0000-0000-000000000202', 'Electrical Outlet Installation', 'Professional installation of electrical outlets including GFCI and USB outlets with safety inspection.', 180.00, '00000000-0000-0000-0000-000000000001', 'PUBLISHED', '2024-01-12T10:00:00Z', '2024-01-12T10:00:00Z'),
('00000000-0000-0000-0000-000000000504', '00000000-0000-0000-0000-000000000202', 'Smart Home Wiring', 'Complete smart home electrical setup including automation systems and smart switches.', 1200.00, '00000000-0000-0000-0000-000000000001', 'PUBLISHED', '2024-01-12T10:00:00Z', '2024-01-12T10:00:00Z'),
('00000000-0000-0000-0000-000000000505', '00000000-0000-0000-0000-000000000204', 'Electrical Panel Upgrade', 'Upgrade electrical panels to meet modern safety standards and increased power demands.', 950.00, '00000000-0000-0000-0000-000000000001', 'PUBLISHED', '2024-01-12T10:00:00Z', '2024-01-12T10:00:00Z'),
-- Locksmith services (2 records) - using internal user IDs with proper BigDecimal prices
('00000000-0000-0000-0000-000000000506', '00000000-0000-0000-0000-000000000203', 'Emergency Lockout Service', '24/7 emergency lockout assistance for homes, cars, and businesses with rapid response.', 85.00, '00000000-0000-0000-0000-000000000004', 'PUBLISHED', '2024-01-12T10:00:00Z', '2024-01-12T10:00:00Z'),
('00000000-0000-0000-0000-000000000507', '00000000-0000-0000-0000-000000000203', 'Lock Installation', 'Professional installation of high-security locks for residential and commercial properties.', 120.00, '00000000-0000-0000-0000-000000000004', 'PUBLISHED', '2024-01-12T10:00:00Z', '2024-01-12T10:00:00Z'),
-- Moving services (3 records) - using internal user IDs with proper BigDecimal prices
('00000000-0000-0000-0000-000000000508', '00000000-0000-0000-0000-000000000205', 'Local Moving Service', 'Professional local moving services including packing, loading, transportation, and unloading.', 350.00, '00000000-0000-0000-0000-000000000003', 'PUBLISHED', '2024-01-12T10:00:00Z', '2024-01-12T10:00:00Z'),
('00000000-0000-0000-0000-000000000509', '00000000-0000-0000-0000-000000000205', 'Long Distance Moving', 'Interstate moving services with professional packing and secure transportation.', 1500.00, '00000000-0000-0000-0000-000000000003', 'PUBLISHED', '2024-01-12T10:00:00Z', '2024-01-12T10:00:00Z'),
('00000000-0000-0000-0000-000000000510', '00000000-0000-0000-0000-000000000204', 'Piano Moving Service', 'Specialized piano moving with proper equipment and experienced technicians.', 450.00, '00000000-0000-0000-0000-000000000003', 'PUBLISHED', '2024-01-12T10:00:00Z', '2024-01-12T10:00:00Z');

-- =====================================================
-- 6. PROFESSIONAL SERVICE (10 records - junction table)
-- =====================================================
INSERT INTO professional_service (professional_id, service_id) VALUES
-- test1professional1 (Plumber) - 2 services
('00000000-0000-0000-0000-000000000201', '00000000-0000-0000-0000-000000000501'),
('00000000-0000-0000-0000-000000000201', '00000000-0000-0000-0000-000000000502'),
-- test2professional2 (Electrician) - 2 services
('00000000-0000-0000-0000-000000000202', '00000000-0000-0000-0000-000000000503'),
('00000000-0000-0000-0000-000000000202', '00000000-0000-0000-0000-000000000504'),
-- test3professional3 (Locksmith) - 2 services
('00000000-0000-0000-0000-000000000203', '00000000-0000-0000-0000-000000000506'),
('00000000-0000-0000-0000-000000000203', '00000000-0000-0000-0000-000000000507'),
-- test4professional4 (Electrician) - 1 service
('00000000-0000-0000-0000-000000000204', '00000000-0000-0000-0000-000000000505'),
-- test5professional5 (Mover) - 3 services
('00000000-0000-0000-0000-000000000205', '00000000-0000-0000-0000-000000000508'),
('00000000-0000-0000-0000-000000000205', '00000000-0000-0000-0000-000000000509'),
('00000000-0000-0000-0000-000000000205', '00000000-0000-0000-0000-000000000510');

-- =====================================================
-- 7. BOOKINGS (9 records - using internal user IDs)
-- =====================================================
-- Comprehensive test data for user 00000000-0000-0000-0000-000000000101 (test1client1)
-- Includes all four booking statuses: OPEN, ACCEPTED, COMPLETED, CANCELLED
INSERT INTO bookings (id, client_id, professional_id, service_id, title, description, requested_date, scheduled_at, price, status, created_at, updated_at) VALUES
-- COMPLETED booking for test1client1 (existing)
(1, '00000000-0000-0000-0000-000000000101', '00000000-0000-0000-0000-000000000201', '00000000-0000-0000-0000-000000000501', 'Emergency Plumbing Repair', 'Kitchen sink leak that started this morning. Water is dripping constantly.', '2024-02-01T08:00:00Z', '2024-02-01T14:30:00Z', 125.00, 'COMPLETED', '2024-01-30T10:15:00Z', '2024-02-01T16:45:00Z'),
-- CANCELLED booking for test1client1 (existing)
(4, '00000000-0000-0000-0000-000000000101', '00000000-0000-0000-0000-000000000202', '00000000-0000-0000-0000-000000000503', 'Home Office Electrical Work', 'Need additional outlets installed in home office for computer setup and equipment.', '2024-02-15T10:00:00Z', null, 180.00, 'CANCELLED', '2024-02-12T16:30:00Z', '2024-02-13T09:45:00Z'),
-- NEW: OPEN booking for test1client1 - Water heater installation request
(6, '00000000-0000-0000-0000-000000000101', '00000000-0000-0000-0000-000000000201', '00000000-0000-0000-0000-000000000502', 'Water Heater Installation Request', 'My old water heater is failing and needs replacement. Looking for a professional plumber to install a new gas water heater. Prefer installation next week.', '2024-02-20T09:00:00Z', null, 650.00, 'OPEN', '2024-02-18T14:30:00Z', '2024-02-18T14:30:00Z'),
-- NEW: ACCEPTED booking for test1client1 - Lock installation accepted by locksmith
(7, '00000000-0000-0000-0000-000000000101', '00000000-0000-0000-0000-000000000203', '00000000-0000-0000-0000-000000000507', 'High-Security Lock Installation', 'Need to upgrade front door lock to high-security deadbolt for better home security. Professional has accepted the job and scheduled for this weekend.', '2024-02-25T10:00:00Z', '2024-02-25T14:00:00Z', 120.00, 'ACCEPTED', '2024-02-22T11:15:00Z', '2024-02-23T09:30:00Z'),
-- Other existing bookings for different users
(2, '00000000-0000-0000-0000-000000000102', '00000000-0000-0000-0000-000000000203', '00000000-0000-0000-0000-000000000506', 'Emergency House Lockout', 'Locked out of house, need immediate assistance. Keys are inside the house.', '2024-02-03T18:30:00Z', '2024-02-03T19:00:00Z', 85.00, 'COMPLETED', '2024-02-03T18:35:00Z', '2024-02-03T19:30:00Z'),
(3, '00000000-0000-0000-0000-000000000103', '00000000-0000-0000-0000-000000000205', '00000000-0000-0000-0000-000000000508', 'Apartment Moving Service', 'Moving from 1-bedroom apartment to 2-bedroom. Need help with furniture and boxes.', '2024-02-10T09:00:00Z', null, 350.00, 'OPEN', '2024-02-08T14:20:00Z', '2024-02-08T14:20:00Z'),
(5, '00000000-0000-0000-0000-000000000102', '00000000-0000-0000-0000-000000000202', '00000000-0000-0000-0000-000000000504', 'Smart Home Installation', 'Need smart home wiring setup including automation systems and smart switches.', '2024-02-12T10:00:00Z', '2024-02-12T14:00:00Z', 1200.00, 'ACCEPTED', '2024-02-10T09:30:00Z', '2024-02-10T09:30:00Z'),
-- Additional booking for comprehensive testing - Piano moving service
(8, '00000000-0000-0000-0000-000000000103', '00000000-0000-0000-0000-000000000204', '00000000-0000-0000-0000-000000000510', 'Piano Moving Service', 'Need to move my baby grand piano from current house to new apartment. Requires specialized equipment and experienced movers.', '2024-03-01T10:00:00Z', '2024-03-01T14:00:00Z', 450.00, 'ACCEPTED', '2024-02-26T16:20:00Z', '2024-02-27T10:15:00Z'),
-- Additional booking for electrical panel upgrade
(9, '00000000-0000-0000-0000-000000000102', '00000000-0000-0000-0000-000000000204', '00000000-0000-0000-0000-000000000505', 'Electrical Panel Upgrade', 'Old electrical panel needs upgrade to handle increased power demands from new appliances. Safety inspection required.', '2024-03-05T08:00:00Z', null, 950.00, 'OPEN', '2024-03-02T13:45:00Z', '2024-03-02T13:45:00Z');

-- =====================================================
-- 8. REVIEWS (2 records - using internal user IDs)
-- =====================================================
-- Note: Reviews are only created for COMPLETED bookings
INSERT INTO reviews (id, booking_id, professional_id, client_id, rating, comment, created_at) VALUES
(1, 1, '00000000-0000-0000-0000-000000000201', '00000000-0000-0000-0000-000000000101', 5, 'Excellent service! test1professional1 arrived on time and fixed the leak quickly. Very professional and cleaned up after the work. Highly recommended!', '2024-02-01T17:30:00Z'),
(2, 2, '00000000-0000-0000-0000-000000000203', '00000000-0000-0000-0000-000000000102', 4, 'test3professional3 was very helpful and got me back into my house quickly. Professional service and fair pricing.', '2024-02-03T20:00:00Z');

-- =====================================================
-- 9. MESSAGES (13 records - using internal user IDs)
-- =====================================================
INSERT INTO messages (id, booking_id, sender_id, receiver_id, content, sent_at, is_read) VALUES
-- Messages for booking 1 (Plumbing repair - COMPLETED)
(1, 1, '00000000-0000-0000-0000-000000000101', '00000000-0000-0000-0000-000000000201', 'Hi test1professional1, I have a kitchen sink leak that started this morning. Can you come today?', '2024-01-30T10:20:00Z', true),
(2, 1, '00000000-0000-0000-0000-000000000201', '00000000-0000-0000-0000-000000000101', 'Hi test1client1! Yes, I can come this afternoon around 2:30 PM. I will bring all necessary tools and parts.', '2024-01-30T10:45:00Z', true),
-- Messages for booking 2 (Lockout service - COMPLETED)
(3, 2, '00000000-0000-0000-0000-000000000102', '00000000-0000-0000-0000-000000000203', 'test3professional3, I am locked out of my house. Can you help me get back in?', '2024-02-03T18:36:00Z', true),
(4, 2, '00000000-0000-0000-0000-000000000203', '00000000-0000-0000-0000-000000000102', 'On my way! I will be there in about 25 minutes. Please wait by the front door.', '2024-02-03T18:40:00Z', true),
-- Messages for booking 3 (Moving service - OPEN)
(5, 3, '00000000-0000-0000-0000-000000000103', '00000000-0000-0000-0000-000000000205', 'Hi test5professional5! I need help moving from my 1-bedroom to a 2-bedroom apartment. When are you available?', '2024-02-08T14:25:00Z', true),
(6, 3, '00000000-0000-0000-0000-000000000205', '00000000-0000-0000-0000-000000000103', 'Hello test3client3! I can help you with the move. How many boxes and furniture pieces do you have?', '2024-02-08T14:30:00Z', true),
-- Messages for booking 5 (Smart home installation - ACCEPTED)
(7, 5, '00000000-0000-0000-0000-000000000102', '00000000-0000-0000-0000-000000000202', 'Hi test2professional2! I need smart home wiring setup. Can you do this next week?', '2024-02-10T09:35:00Z', true),
(8, 5, '00000000-0000-0000-0000-000000000202', '00000000-0000-0000-0000-000000000102', 'Yes test2client2! I can schedule this for next week. I will bring all the smart switches and automation equipment.', '2024-02-10T09:40:00Z', true),
-- Note: No messages for booking 6 (Water heater installation - OPEN) since no professional is assigned yet
-- NEW: Messages for booking 7 (Lock installation - ACCEPTED for test1client1)
(9, 7, '00000000-0000-0000-0000-000000000101', '00000000-0000-0000-0000-000000000203', 'Hi test3professional3! I need a high-security deadbolt installed on my front door. When can you do this?', '2024-02-22T11:20:00Z', true),
(10, 7, '00000000-0000-0000-0000-000000000203', '00000000-0000-0000-0000-000000000101', 'Hello test1client1! I can install the high-security lock this weekend. I have the best deadbolts in stock. Saturday at 2 PM work for you?', '2024-02-22T14:15:00Z', true),
(11, 7, '00000000-0000-0000-0000-000000000101', '00000000-0000-0000-0000-000000000203', 'Perfect! Saturday at 2 PM works great. Thank you for accepting the job!', '2024-02-23T09:35:00Z', true),
-- Messages for booking 8 (Piano moving - ACCEPTED)
(12, 8, '00000000-0000-0000-0000-000000000103', '00000000-0000-0000-0000-000000000204', 'Hi test4professional4! I need to move my baby grand piano. Do you have the specialized equipment for this?', '2024-02-26T16:25:00Z', true),
(13, 8, '00000000-0000-0000-0000-000000000204', '00000000-0000-0000-0000-000000000103', 'Yes test3client3! I have all the proper piano moving equipment and experienced team. We can do this safely on March 1st.', '2024-02-27T10:20:00Z', true);



-- =====================================================
-- 10. FAVORITES (6 records - using internal user IDs)
-- =====================================================
INSERT INTO favorites (user_id, service_id, created_at) VALUES
-- test1client1's favorites (2 services)
('00000000-0000-0000-0000-000000000101', '00000000-0000-0000-0000-000000000501', '2024-02-01T18:00:00Z'),
('00000000-0000-0000-0000-000000000101', '00000000-0000-0000-0000-000000000503', '2024-02-10T10:00:00Z'),
-- test2client2's favorites (2 services)
('00000000-0000-0000-0000-000000000102', '00000000-0000-0000-0000-000000000506', '2024-02-03T20:30:00Z'),
('00000000-0000-0000-0000-000000000102', '00000000-0000-0000-0000-000000000504', '2024-02-04T09:15:00Z'),
-- test3client3's favorites (2 services)
('00000000-0000-0000-0000-000000000103', '00000000-0000-0000-0000-000000000508', '2024-02-08T15:00:00Z'),
('00000000-0000-0000-0000-000000000103', '00000000-0000-0000-0000-000000000509', '2024-02-05T14:20:00Z');

-- =====================================================
-- 11. WORK SCHEDULES (5 records - one per professional)
-- =====================================================
-- Professional work schedules representing weekly recurring availability patterns
-- Each professional has one work schedule with day-specific time ranges
-- Uses 5-minute granularity and automatic range merging for overlaps
INSERT INTO work_schedules (professional_id, notes, created_at, updated_at) VALUES
-- test1professional1 (Plumber) - Full-time schedule
('00000000-0000-0000-0000-000000000201', 'Available for emergency plumbing services', '2024-03-01T08:00:00Z', '2024-03-01T08:00:00Z'),
-- test2professional2 (Electrician) - Part-time schedule
('00000000-0000-0000-0000-000000000202', 'Specializing in smart home installations', '2024-03-01T09:00:00Z', '2024-03-01T09:00:00Z'),
-- test3professional3 (Locksmith) - Flexible schedule
('00000000-0000-0000-0000-000000000203', 'Emergency locksmith services available', '2024-03-01T10:00:00Z', '2024-03-01T10:00:00Z'),
-- test4professional4 (Electrician) - Weekend focused
('00000000-0000-0000-0000-000000000204', 'Major electrical work and panel upgrades', '2024-03-01T11:00:00Z', '2024-03-01T11:00:00Z'),
-- test5professional5 (Mover) - Early morning schedule
('00000000-0000-0000-0000-000000000205', 'Professional moving services', '2024-03-01T12:00:00Z', '2024-03-01T12:00:00Z');

-- Time ranges for each professional (flattened DayTimeRange structure)
-- Each row represents a time range for a specific day of the week

INSERT INTO work_schedule_time_ranges (work_schedule_id, day_of_week, start_time, end_time) VALUES
-- test1professional1 (Plumber) - Standard business hours Monday to Friday
('00000000-0000-0000-0000-000000000201', 'MONDAY', '08:00:00', '14:00:00'),
('00000000-0000-0000-0000-000000000201', 'MONDAY', '15:00:00', '18:00:00'),
('00000000-0000-0000-0000-000000000201', 'TUESDAY', '08:00:00', '17:00:00'),
('00000000-0000-0000-0000-000000000201', 'WEDNESDAY', '08:00:00', '17:00:00'),
('00000000-0000-0000-0000-000000000201', 'THURSDAY', '08:00:00', '17:00:00'),
('00000000-0000-0000-0000-000000000201', 'FRIDAY', '08:00:00', '16:00:00'),
-- test2professional2 (Electrician) - Part-time with split shifts
('00000000-0000-0000-0000-000000000202', 'TUESDAY', '09:00:00', '13:00:00'),
('00000000-0000-0000-0000-000000000202', 'TUESDAY', '14:00:00', '18:00:00'),
('00000000-0000-0000-0000-000000000202', 'THURSDAY', '10:00:00', '15:00:00'),
('00000000-0000-0000-0000-000000000202', 'SATURDAY', '08:00:00', '14:00:00'),
-- test3professional3 (Locksmith) - Flexible hours with emergency coverage
('00000000-0000-0000-0000-000000000203', 'MONDAY', '10:00:00', '18:00:00'),
('00000000-0000-0000-0000-000000000203', 'WEDNESDAY', '09:00:00', '17:00:00'),
('00000000-0000-0000-0000-000000000203', 'FRIDAY', '11:00:00', '19:00:00'),
('00000000-0000-0000-0000-000000000203', 'SUNDAY', '12:00:00', '16:00:00'),
-- test4professional4 (Electrician) - Weekend specialist with long hours
('00000000-0000-0000-0000-000000000204', 'FRIDAY', '14:00:00', '20:00:00'),
('00000000-0000-0000-0000-000000000204', 'SATURDAY', '07:00:00', '19:00:00'),
('00000000-0000-0000-0000-000000000204', 'SUNDAY', '08:00:00', '18:00:00'),
-- test5professional5 (Mover) - Early morning starts Monday to Saturday
('00000000-0000-0000-0000-000000000205', 'MONDAY', '06:00:00', '14:00:00'),
('00000000-0000-0000-0000-000000000205', 'TUESDAY', '06:00:00', '14:00:00'),
('00000000-0000-0000-0000-000000000205', 'WEDNESDAY', '06:00:00', '14:00:00'),
('00000000-0000-0000-0000-000000000205', 'THURSDAY', '06:00:00', '14:00:00'),
('00000000-0000-0000-0000-000000000205', 'FRIDAY', '06:00:00', '14:00:00'),
('00000000-0000-0000-0000-000000000205', 'SATURDAY', '07:00:00', '15:00:00');

-- =====================================================
-- DATA SUMMARY
-- =====================================================
-- Foreign Key Constraints: 15 (all entity relationships properly constrained)
-- Service Categories: 4 (electrician, plumber, mover, locksmith)
-- Users: 8 (5 professionals, 3 clients)
-- External Identities: 8 (Keycloak mappings for all users)
-- Professionals: 5 (1 plumber, 2 electricians, 1 locksmith, 1 mover)
-- Services: 10 (2 plumber, 3 electrician, 2 locksmith, 3 mover)
-- Professional-Service Relations: 10 (junction table entries)
-- Bookings: 9 (comprehensive test coverage for all statuses)
-- Reviews: 2 (only for completed bookings, one per booking due to unique constraint)
-- Messages: 13 (communication between clients and professionals)
-- Favorites: 6 (client favorites distributed across users)
-- Work Schedules: 5 (one per professional with weekly recurring patterns, 22 total time ranges)
--
-- COMPREHENSIVE BOOKING TEST DATA FOR USER test1client1 (00000000-0000-0000-0000-000000000101):
-- ✅ COMPLETED: Booking #1 - Emergency Plumbing Repair (test1professional1)
-- ✅ CANCELLED: Booking #4 - Home Office Electrical Work (test2professional2)
-- ✅ OPEN: Booking #6 - Water Heater Installation Request (no professional assigned yet)
-- ✅ ACCEPTED: Booking #7 - High-Security Lock Installation (test3professional3)
--
-- BOOKING STATUS DISTRIBUTION:
-- - OPEN: 3 bookings (IDs: 3, 6, 9) - 1 for test1client1, 1 for test3client3, 1 for test2client2
-- - ACCEPTED: 3 bookings (IDs: 5, 7, 8) - 1 for test1client1, 1 for test2client2, 1 for test3client3
-- - COMPLETED: 2 bookings (IDs: 1, 2) - 1 for test1client1, 1 for test2client2
-- - CANCELLED: 1 booking (ID: 4) - 1 for test1client1
--
-- ARCHITECTURE IMPROVEMENTS:
-- ✅ All foreign key constraints properly defined to eliminate startup warnings
-- ✅ All UUIDs properly formatted according to PostgreSQL UUID requirements (8-4-4-4-12 format)
-- ✅ Comprehensive test data while maintaining referential integrity
-- ✅ Updated schema uses internal user IDs consistently across all business domain tables
-- ✅ External provider IDs properly abstracted through user_external_identities table
-- ✅ Proper CASCADE and SET NULL behaviors for data integrity
-- ✅ Database schema matches JPA entity @ForeignKey annotations exactly
-- ✅ Sequence reset handled by SequenceResetService at startup to prevent auto-increment conflicts
-- ✅ Complete booking status coverage for frontend testing (OPEN, ACCEPTED, COMPLETED, CANCELLED)
-- ✅ Realistic booking scenarios with proper professional assignments and service mappings
-- =====================================================

-- =====================================================
-- 9. MEDIA (Sample media files for testing)
-- =====================================================
-- Sample media entries for profile pictures and service photos
INSERT INTO media (id, file_path, original_filename, file_size, content_type, media_type, user_id, associated_entity_id, created_at, updated_at) VALUES
-- Client profile pictures
('00000000-0000-0000-0000-**********01', 'users/00000000-0000-0000-0000-000000000101/client/profile.jpg', 'profile.jpg', 245760, 'image/jpeg', 'CLIENT_PROFILE_PICTURE', '00000000-0000-0000-0000-000000000101', null, '2024-01-15T10:35:00Z', '2024-01-15T10:35:00Z'),

-- Professional profile pictures
('00000000-0000-0000-0000-**********02', 'users/00000000-0000-0000-0000-000000000201/professional/profile.jpg', 'profile.jpg', 312480, 'image/jpeg', 'PROFESSIONAL_PROFILE_PICTURE', '00000000-0000-0000-0000-000000000201', null, '2024-01-18T16:50:00Z', '2024-01-18T16:50:00Z');

-- Service photos
-- ('00000000-0000-0000-0000-**********06', 'users/00000000-0000-0000-0000-000000000201/professional/services/00000000-0000-0000-0000-000000000501/photo_1640995500000.jpg', 'electrical_work_sample.jpg', 456780, 'image/jpeg', 'SERVICE_PHOTO', '00000000-0000-0000-0000-000000000201', '00000000-0000-0000-0000-000000000501', '2024-01-21T09:15:00Z', '2024-01-21T09:15:00Z'),
-- ('00000000-0000-0000-0000-**********07', 'users/00000000-0000-0000-0000-000000000202/professional/services/00000000-0000-0000-0000-000000000502/photo_1640995560000.jpg', 'plumbing_installation.jpg', 523840, 'image/jpeg', 'SERVICE_PHOTO', '00000000-0000-0000-0000-000000000202', '00000000-0000-0000-0000-000000000502', '2024-01-22T14:20:00Z', '2024-01-22T14:20:00Z'),
-- ('00000000-0000-0000-0000-**********08', 'users/00000000-0000-0000-0000-000000000203/professional/services/00000000-0000-0000-0000-000000000506/photo_1640995620000.jpg', 'lock_installation_work.jpg', 367920, 'image/jpeg', 'SERVICE_PHOTO', '00000000-0000-0000-0000-000000000203', '00000000-0000-0000-0000-000000000506', '2024-01-23T16:45:00Z', '2024-01-23T16:45:00Z');

-- =====================================================
-- MEDIA SUMMARY:
-- - 8 media files total
-- - 2 client profile pictures
-- - 3 professional profile pictures
-- - 3 service photos
-- - All files follow proper naming convention with timestamps
-- - File sizes are realistic for image files (189KB - 523KB)
-- - Mix of JPEG and PNG formats
-- - Proper associations with users and services
-- =====================================================


