package com.yotelohago.availability.api;

import java.time.DayOfWeek;
import java.util.List;

/**
 * DTO for DaySchedule embedded class.
 * 
 * Represents a professional's schedule for a specific day of the week
 * with a list of time ranges when they are available.
 */
public class DayScheduleDTO {
    
    public DayOfWeek dayOfWeek;
    public List<TimeRangeDTO> timeRanges;
    public Boolean isActive; // Computed field for UI convenience

    // Default constructor for JSON serialization
    public DayScheduleDTO() {}

    // Constructor for creating DTOs
    public DayScheduleDTO(DayOfWeek dayOfWeek, List<TimeRangeDTO> timeRanges) {
        this.dayOfWeek = dayOfWeek;
        this.timeRanges = timeRanges;
        this.isActive = timeRanges != null && !timeRanges.isEmpty();
    }

    // Constructor with isActive flag
    public DayScheduleDTO(DayOfWeek dayOfWeek, List<TimeRangeDTO> timeRanges, Boolean isActive) {
        this.dayOfWeek = dayOfWeek;
        this.timeRanges = timeRanges;
        this.isActive = isActive;
    }
}
