apiVersion: v1
kind: Service
metadata:
  name: {{ include "yotelohago-admin.fullname" . }}
  labels:
    {{- include "yotelohago-admin.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: {{ .Values.service.targetPort }}
      protocol: TCP
      name: http
  selector:
    {{- include "yotelohago-admin.selectorLabels" . | nindent 4 }}
