package com.yotelohago.auth;

import jakarta.annotation.security.RolesAllowed;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import jakarta.inject.Inject;
import io.quarkus.security.identity.SecurityIdentity;
import org.eclipse.microprofile.jwt.JsonWebToken;

import java.util.UUID;

@Path("/api/auth")
public class AuthResource {

    @Inject
    SecurityIdentity securityIdentity;

    @Inject
    JsonWebToken jwt;

    @Inject
    TokenIdentityProvider tokenIdentityProvider;

    @GET
    @Path("/user-info")
    @RolesAllowed({"user", "professional", "admin"})
    @Produces(MediaType.APPLICATION_JSON)
    public UserInfo getUserInfo() {
        UserInfo info = new UserInfo();
        info.name = securityIdentity.getPrincipal().getName();
        info.roles = securityIdentity.getRoles();
        info.userId = tokenIdentityProvider.getCurrentUserId(); // Now returns internal user ID
        return info;
    }

    public static class UserInfo {
        public String name;
        public java.util.Set<String> roles;
        public UUID userId;
    }


}