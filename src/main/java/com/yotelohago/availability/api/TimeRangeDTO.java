package com.yotelohago.availability.api;

import java.time.LocalTime;

/**
 * DTO for TimeRange value object.
 * 
 * Simple data transfer object for time ranges with start and end times.
 * Used in API requests and responses for work schedule management.
 */
public class TimeRangeDTO {
    
    public LocalTime startTime;
    public LocalTime endTime;

    // Default constructor for JSON serialization
    public TimeRangeDTO() {}

    // Constructor for creating DTOs
    public TimeRangeDTO(LocalTime startTime, LocalTime endTime) {
        this.startTime = startTime;
        this.endTime = endTime;
    }

    // Convenience constructor with string times
    public TimeRangeDTO(String startTime, String endTime) {
        this.startTime = LocalTime.parse(startTime);
        this.endTime = LocalTime.parse(endTime);
    }
}
