variable "region" {
  description = "The AWS region to deploy the backend."
  type        = string
  default     = "us-east-1"
}

variable "profile" {
  description = "The AWS user account to execute it as profile using aws-cli."
  type        = string
}

variable "bucket_name" {
  description = "The name of the S3 bucket to store Terraform state."
  type        = string
}

variable "bucket_key_terraform_infra" {
  description = "The name of the terraform state file for infra."
  type        = string
}

variable "bucket_key_terraform_helm" {
  description = "The name of the terraform state file for helm."
  type        = string
}

variable "dynamodb_table_name" {
  description = "The name of the DynamoDB table to use for state locking."
  type        = string
}
