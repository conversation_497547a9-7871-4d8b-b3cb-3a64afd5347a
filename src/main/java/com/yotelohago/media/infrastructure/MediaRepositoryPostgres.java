package com.yotelohago.media.infrastructure;

import com.yotelohago.media.api.MediaDTO;
import com.yotelohago.media.domain.Media;
import com.yotelohago.media.domain.MediaRepository;
import com.yotelohago.media.domain.MediaType;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;
import org.eclipse.microprofile.config.inject.ConfigProperty;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * PostgreSQL implementation of MediaRepository
 * Follows the same coding convention as Service and Booking domains:
 * - URLs are built exclusively in SQL queries using CONCAT()
 * - All public methods return DTOs with URLs pre-built
 * - No URL construction in service or mapper layers
 */
@ApplicationScoped
public class MediaRepositoryPostgres implements MediaRepository {

    @PersistenceContext
    EntityManager entityManager;

    @ConfigProperty(name = "media.url-resolver")
    String mediaBaseUrl;

    @Override
    public void persist(Media media) {
        entityManager.persist(media);
    }

    @Override
    public void flush() {
        entityManager.flush();
    }

    @Override
    public void delete(Media media) {
        if (entityManager.contains(media)) {
            entityManager.remove(media);
        } else {
            // If the entity is detached, merge it first
            Media managedMedia = entityManager.merge(media);
            entityManager.remove(managedMedia);
        }
    }

    @Override
    public boolean existsByFilePath(String filePath) {
        TypedQuery<Long> query = entityManager.createQuery(
            "SELECT COUNT(m) FROM Media m WHERE m.filePath = :filePath",
            Long.class
        );
        query.setParameter("filePath", filePath);
        return query.getSingleResult() > 0;
    }

    @Override
    public long countByUserId(UUID userId) {
        TypedQuery<Long> query = entityManager.createQuery(
            "SELECT COUNT(m) FROM Media m WHERE m.user.id = :userId",
            Long.class
        );
        query.setParameter("userId", userId);
        return query.getSingleResult();
    }

    @Override
    public long getTotalStorageSizeByUserId(UUID userId) {
        TypedQuery<Long> query = entityManager.createQuery(
            "SELECT COALESCE(SUM(m.fileSize), 0) FROM Media m WHERE m.user.id = :userId",
            Long.class
        );
        query.setParameter("userId", userId);
        return query.getSingleResult();
    }

    @Override
    public boolean deleteById(UUID id) {
        // Find the entity first to delete it
        Media media = entityManager.find(Media.class, id);
        if (media != null) {
            delete(media);
            return true;
        }
        return false;
    }

    // =====================================================
    // Methods that return DTOs with URLs built in SQL
    // Following the same convention as Service and Booking domains
    // =====================================================

    @Override
    public Optional<MediaDTO> findByIdWithUrl(UUID id) {
        String jpql = String.format("""
            SELECT new com.yotelohago.media.api.MediaDTO(
                m.id, m.filePath, m.originalFilename, m.fileSize, m.contentType,
                CAST(m.mediaType AS string), m.user.id, m.associatedEntityId,
                m.createdAt, m.updatedAt,
                CASE
                    WHEN m.filePath IS NOT NULL
                    THEN CONCAT('%s', m.filePath)
                    ELSE NULL
                END
            )
            FROM Media m
            WHERE m.id = :id
            """, mediaBaseUrl);

        TypedQuery<MediaDTO> query = entityManager.createQuery(jpql, MediaDTO.class);
        query.setParameter("id", id);

        List<MediaDTO> results = query.getResultList();
        return results.isEmpty() ? Optional.empty() : Optional.of(results.get(0));
    }

    @Override
    public List<MediaDTO> findByUserIdWithUrls(UUID userId) {
        String jpql = String.format("""
            SELECT new com.yotelohago.media.api.MediaDTO(
                m.id, m.filePath, m.originalFilename, m.fileSize, m.contentType,
                CAST(m.mediaType AS string), m.user.id, m.associatedEntityId,
                m.createdAt, m.updatedAt,
                CASE
                    WHEN m.filePath IS NOT NULL
                    THEN CONCAT('%s', m.filePath)
                    ELSE NULL
                END
            )
            FROM Media m
            WHERE m.user.id = :userId
            ORDER BY m.createdAt DESC
            """, mediaBaseUrl);

        TypedQuery<MediaDTO> query = entityManager.createQuery(jpql, MediaDTO.class);
        query.setParameter("userId", userId);

        return query.getResultList();
    }

    @Override
    public List<MediaDTO> findByUserIdAndTypeWithUrls(UUID userId, MediaType mediaType) {
        String jpql = String.format("""
            SELECT new com.yotelohago.media.api.MediaDTO(
                m.id, m.filePath, m.originalFilename, m.fileSize, m.contentType,
                CAST(m.mediaType AS string), m.user.id, m.associatedEntityId,
                m.createdAt, m.updatedAt,
                CASE
                    WHEN m.filePath IS NOT NULL
                    THEN CONCAT('%s', m.filePath)
                    ELSE NULL
                END
            )
            FROM Media m
            WHERE m.user.id = :userId AND m.mediaType = :mediaType
            ORDER BY m.createdAt DESC
            """, mediaBaseUrl);

        TypedQuery<MediaDTO> query = entityManager.createQuery(jpql, MediaDTO.class);
        query.setParameter("userId", userId);
        query.setParameter("mediaType", mediaType);

        return query.getResultList();
    }

    @Override
    public List<MediaDTO> findByAssociatedEntityIdWithUrls(UUID associatedEntityId) {
        String jpql = String.format("""
            SELECT new com.yotelohago.media.api.MediaDTO(
                m.id, m.filePath, m.originalFilename, m.fileSize, m.contentType,
                CAST(m.mediaType AS string), m.user.id, m.associatedEntityId,
                m.createdAt, m.updatedAt,
                CASE
                    WHEN m.filePath IS NOT NULL
                    THEN CONCAT('%s', m.filePath)
                    ELSE NULL
                END
            )
            FROM Media m
            WHERE m.associatedEntityId = :associatedEntityId
            ORDER BY m.createdAt DESC
            """, mediaBaseUrl);

        TypedQuery<MediaDTO> query = entityManager.createQuery(jpql, MediaDTO.class);
        query.setParameter("associatedEntityId", associatedEntityId);

        return query.getResultList();
    }

    @Override
    public List<MediaDTO> findByUserIdTypeAndAssociatedEntityWithUrls(UUID userId, MediaType mediaType, UUID associatedEntityId) {
        String jpql = String.format("""
            SELECT new com.yotelohago.media.api.MediaDTO(
                m.id, m.filePath, m.originalFilename, m.fileSize, m.contentType,
                CAST(m.mediaType AS string), m.user.id, m.associatedEntityId,
                m.createdAt, m.updatedAt,
                CASE
                    WHEN m.filePath IS NOT NULL
                    THEN CONCAT('%s', m.filePath)
                    ELSE NULL
                END
            )
            FROM Media m
            WHERE m.user.id = :userId AND m.mediaType = :mediaType AND m.associatedEntityId = :associatedEntityId
            ORDER BY m.createdAt DESC
            """, mediaBaseUrl);

        TypedQuery<MediaDTO> query = entityManager.createQuery(jpql, MediaDTO.class);
        query.setParameter("userId", userId);
        query.setParameter("mediaType", mediaType);
        query.setParameter("associatedEntityId", associatedEntityId);

        return query.getResultList();
    }

    @Override
    public Optional<MediaDTO> findByFilePathWithUrl(String filePath) {
        String jpql = String.format("""
            SELECT new com.yotelohago.media.api.MediaDTO(
                m.id, m.filePath, m.originalFilename, m.fileSize, m.contentType,
                CAST(m.mediaType AS string), m.user.id, m.associatedEntityId,
                m.createdAt, m.updatedAt,
                CASE
                    WHEN m.filePath IS NOT NULL
                    THEN CONCAT('%s', m.filePath)
                    ELSE NULL
                END
            )
            FROM Media m
            WHERE m.filePath = :filePath
            """, mediaBaseUrl);

        TypedQuery<MediaDTO> query = entityManager.createQuery(jpql, MediaDTO.class);
        query.setParameter("filePath", filePath);

        List<MediaDTO> results = query.getResultList();
        return results.isEmpty() ? Optional.empty() : Optional.of(results.get(0));
    }

    @Override
    public Optional<MediaDTO> findProfilePhotoByUserAndTypeWithUrl(UUID userId, MediaType mediaType) {
        String jpql = String.format("""
            SELECT new com.yotelohago.media.api.MediaDTO(
                m.id, m.filePath, m.originalFilename, m.fileSize, m.contentType,
                CAST(m.mediaType AS string), m.user.id, m.associatedEntityId,
                m.createdAt, m.updatedAt,
                CASE
                    WHEN m.filePath IS NOT NULL
                    THEN CONCAT('%s', m.filePath)
                    ELSE NULL
                END
            )
            FROM Media m
            WHERE m.user.id = :userId AND m.mediaType = :mediaType
            ORDER BY m.createdAt DESC
            """, mediaBaseUrl);

        TypedQuery<MediaDTO> query = entityManager.createQuery(jpql, MediaDTO.class);
        query.setParameter("userId", userId);
        query.setParameter("mediaType", mediaType);
        query.setMaxResults(1); // Only get the most recent one

        List<MediaDTO> results = query.getResultList();
        return results.isEmpty() ? Optional.empty() : Optional.of(results.get(0));
    }
}
