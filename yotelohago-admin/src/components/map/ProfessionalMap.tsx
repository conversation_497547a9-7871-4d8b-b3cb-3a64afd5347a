import React, { useState, useMemo, useRef, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, useMap } from 'react-leaflet';
import { Card, Typography, Avatar, Tag, Button, Select, Space } from 'antd';
import { UserOutlined, PhoneOutlined, MailOutlined, StarOutlined, CloseOutlined } from '@ant-design/icons';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import 'leaflet.markercluster/dist/MarkerCluster.css';
import 'leaflet.markercluster/dist/MarkerCluster.Default.css';

const { Title, Text } = Typography;
const { Option } = Select;

// Fix for default markers in React Leaflet
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-shadow.png',
});

// Guadalajara coordinates
const GUADALAJARA_CENTER: [number, number] = [20.6597, -103.3496];

// Zone definitions for Guadalajara
const GUADALAJARA_ZONES = {
  'Centro Histórico': { color: '#ff5a5f', bounds: { minLat: 20.665, maxLat: 20.685, minLng: -103.365, maxLng: -103.335 } },
  'Zona Minerva': { color: '#1890ff', bounds: { minLat: 20.665, maxLat: 20.695, minLng: -103.395, maxLng: -103.365 } },
  'Providencia': { color: '#52c41a', bounds: { minLat: 20.695, maxLat: 20.715, minLng: -103.385, maxLng: -103.355 } },
  'Chapultepec': { color: '#722ed1', bounds: { minLat: 20.645, maxLat: 20.675, minLng: -103.385, maxLng: -103.355 } },
  'Americana': { color: '#fa8c16', bounds: { minLat: 20.675, maxLat: 20.705, minLng: -103.415, maxLng: -103.385 } },
  'Santa Tere': { color: '#13c2c2', bounds: { minLat: 20.635, maxLat: 20.665, minLng: -103.355, maxLng: -103.325 } }
};

// Generate mock professional data for Guadalajara
const generateGuadalajaraProfessionals = () => {
  const professions = ['Electricista', 'Plomero', 'Cerrajero', 'Mudanzas'];
  const zones = Object.keys(GUADALAJARA_ZONES);
  
  const professionals = [];
  
  for (let i = 1; i <= 20; i++) {
    const profession = professions[(i - 1) % professions.length];
    const zone = zones[Math.floor(Math.random() * zones.length)];
    const zoneBounds = GUADALAJARA_ZONES[zone].bounds;
    
    // Generate random coordinates within zone bounds
    const latitude = zoneBounds.minLat + Math.random() * (zoneBounds.maxLat - zoneBounds.minLat);
    const longitude = zoneBounds.minLng + Math.random() * (zoneBounds.maxLng - zoneBounds.minLng);
    
    professionals.push({
      id: `prof-${i}`,
      name: `${getRandomName()} ${getRandomLastName()}`,
      profession,
      zone,
      coordinates: [latitude, longitude] as [number, number],
      phone: `+52 33 ${Math.floor(1000 + Math.random() * 9000)}-${Math.floor(1000 + Math.random() * 9000)}`,
      email: `profesional${i}@email.com`,
      rating: 3.5 + Math.random() * 1.5,
      reviewCount: Math.floor(5 + Math.random() * 50),
      bio: `Profesional ${profession.toLowerCase()} con experiencia en la zona ${zone}. Servicios de calidad garantizados.`,
      profileImage: `https://images.pexels.com/photos/${1000000 + i}/pexels-photo-${1000000 + i}.jpeg?auto=compress&cs=tinysrgb&w=400`,
      available: Math.random() > 0.3,
      yearsExperience: Math.floor(1 + Math.random() * 15)
    });
  }
  
  return professionals;
};

const getRandomName = () => {
  const names = ['Carlos', 'María', 'José', 'Ana', 'Luis', 'Carmen', 'Miguel', 'Rosa', 'Antonio', 'Isabel', 'Francisco', 'Pilar', 'Manuel', 'Dolores', 'David', 'Cristina', 'Alejandro', 'Marta', 'Rafael', 'Laura'];
  return names[Math.floor(Math.random() * names.length)];
};

const getRandomLastName = () => {
  const lastNames = ['García', 'Rodríguez', 'González', 'Fernández', 'López', 'Martínez', 'Sánchez', 'Pérez', 'Gómez', 'Martín', 'Jiménez', 'Ruiz', 'Hernández', 'Díaz', 'Moreno', 'Muñoz', 'Álvarez', 'Romero', 'Alonso', 'Gutiérrez'];
  return lastNames[Math.floor(Math.random() * lastNames.length)];
};

// Create custom icons for different professions
const createCustomIcon = (profession: string) => {
  const colors = {
    'Electricista': '#faad14',
    'Plomero': '#1890ff',
    'Cerrajero': '#722ed1',
    'Mudanzas': '#52c41a'
  };

  const color = colors[profession] || '#ff5a5f';
  
  return L.divIcon({
    html: `
      <div style="
        width: 32px;
        height: 32px;
        background-color: ${color};
        border: 2px solid white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 14px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.3);
      ">
        👤
      </div>
    `,
    className: 'custom-marker',
    iconSize: [32, 32],
    iconAnchor: [16, 16],
    popupAnchor: [0, -16]
  });
};

interface ProfessionalMapProps {
  height?: number;
}

const ProfessionalMap: React.FC<ProfessionalMapProps> = ({ height = 600 }) => {
  const [professionals] = useState(() => generateGuadalajaraProfessionals());
  const [selectedZone, setSelectedZone] = useState<string>('all');
  const [selectedProfession, setSelectedProfession] = useState<string>('all');

  // Filter professionals based on selected filters
  const filteredProfessionals = useMemo(() => {
    return professionals.filter(prof => {
      const zoneMatch = selectedZone === 'all' || prof.zone === selectedZone;
      const professionMatch = selectedProfession === 'all' || prof.profession === selectedProfession;
      return zoneMatch && professionMatch;
    });
  }, [professionals, selectedZone, selectedProfession]);

  const getProfessionColor = (profession: string) => {
    const colors = {
      'Electricista': '#faad14',
      'Plomero': '#1890ff',
      'Cerrajero': '#722ed1',
      'Mudanzas': '#52c41a'
    };
    return colors[profession] || '#ff5a5f';
  };

  const getZoneStats = () => {
    const stats = {};
    Object.keys(GUADALAJARA_ZONES).forEach(zone => {
      stats[zone] = filteredProfessionals.filter(prof => prof.zone === zone).length;
    });
    return stats;
  };

  return (
    <div className="space-y-4">
      {/* Controls */}
      <Card className="shadow-sm">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
          <div>
            <Title level={3} className="m-0 mb-2">Mapa de Profesionales - Guadalajara</Title>
            <Text type="secondary">
              Distribución geográfica de {filteredProfessionals.length} profesionales en Guadalajara, Jalisco
            </Text>
          </div>
          
          <Space wrap>
            <Select
              value={selectedZone}
              onChange={setSelectedZone}
              style={{ width: 150 }}
              placeholder="Filtrar por zona"
            >
              <Option value="all">Todas las zonas</Option>
              {Object.keys(GUADALAJARA_ZONES).map(zone => (
                <Option key={zone} value={zone}>{zone}</Option>
              ))}
            </Select>
            
            <Select
              value={selectedProfession}
              onChange={setSelectedProfession}
              style={{ width: 150 }}
              placeholder="Filtrar por profesión"
            >
              <Option value="all">Todas las profesiones</Option>
              <Option value="Electricista">Electricista</Option>
              <Option value="Plomero">Plomero</Option>
              <Option value="Cerrajero">Cerrajero</Option>
              <Option value="Mudanzas">Mudanzas</Option>
            </Select>
          </Space>
        </div>
      </Card>

      {/* Map Container with proper positioning */}
      <Card className="shadow-sm p-0 overflow-hidden">
        <div 
          style={{ 
            height,
            position: 'relative',
            zIndex: 1
          }}
        >
          <MapContainer
            center={GUADALAJARA_CENTER}
            zoom={12}
            style={{ 
              height: '100%', 
              width: '100%',
              position: 'relative',
              zIndex: 1
            }}
            zoomControl={true}
            scrollWheelZoom={true}
            doubleClickZoom={true}
            touchZoom={true}
            dragging={true}
          >
            <TileLayer
              attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
              url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
              zIndex={1}
            />
            
            {filteredProfessionals.map((professional) => (
              <Marker
                key={professional.id}
                position={professional.coordinates}
                icon={createCustomIcon(professional.profession)}
                zIndexOffset={1000}
              >
                <Popup 
                  maxWidth={350} 
                  className="professional-popup"
                  closeButton={true}
                  autoClose={false}
                  closeOnEscapeKey={true}
                >
                  <div className="p-2">
                    <div className="flex items-center space-x-3 mb-3">
                      <Avatar
                        size={48}
                        src={professional.profileImage}
                        icon={<UserOutlined />}
                      />
                      <div>
                        <Title level={5} className="m-0">{professional.name}</Title>
                        <Text type="secondary">{professional.profession}</Text>
                      </div>
                    </div>

                    <div className="space-y-2 mb-3">
                      <div className="flex items-center space-x-2">
                        <StarOutlined className="text-yellow-500" />
                        <span>{professional.rating.toFixed(1)}</span>
                        <Text type="secondary">({professional.reviewCount} reseñas)</Text>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <PhoneOutlined className="text-gray-400" />
                        <Text className="text-sm">{professional.phone}</Text>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <MailOutlined className="text-gray-400" />
                        <Text className="text-sm">{professional.email}</Text>
                      </div>
                    </div>

                    <div className="flex items-center justify-between mb-3">
                      <Tag color={GUADALAJARA_ZONES[professional.zone]?.color}>
                        {professional.zone}
                      </Tag>
                      <Tag color={professional.available ? 'green' : 'red'}>
                        {professional.available ? 'Disponible' : 'Ocupado'}
                      </Tag>
                    </div>

                    <Text className="text-sm text-gray-600 block mb-3">
                      {professional.bio}
                    </Text>

                    <div className="flex space-x-2">
                      <Button type="primary" size="small" className="flex-1">
                        Contactar
                      </Button>
                      <Button size="small" className="flex-1">
                        Ver Perfil
                      </Button>
                    </div>
                  </div>
                </Popup>
              </Marker>
            ))}
          </MapContainer>
        </div>
      </Card>

      {/* Legend and Statistics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        {/* Zone Legend */}
        <Card title="Leyenda de Zonas" className="shadow-sm">
          <div className="space-y-2">
            {Object.entries(GUADALAJARA_ZONES).map(([zone, config]) => {
              const count = getZoneStats()[zone] || 0;
              return (
                <div key={zone} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <div
                      className="w-4 h-4 rounded-full"
                      style={{ backgroundColor: config.color }}
                    />
                    <Text>{zone}</Text>
                  </div>
                  <Text strong>{count} profesionales</Text>
                </div>
              );
            })}
          </div>
        </Card>

        {/* Profession Legend */}
        <Card title="Leyenda de Profesiones" className="shadow-sm">
          <div className="space-y-2">
            {['Electricista', 'Plomero', 'Cerrajero', 'Mudanzas'].map(profession => {
              const count = filteredProfessionals.filter(prof => prof.profession === profession).length;
              return (
                <div key={profession} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <div
                      className="w-4 h-4 rounded-full"
                      style={{ backgroundColor: getProfessionColor(profession) }}
                    />
                    <Text>{profession}</Text>
                  </div>
                  <Text strong>{count} profesionales</Text>
                </div>
              );
            })}
          </div>
        </Card>
      </div>

      <style jsx global>{`
        .leaflet-container {
          z-index: 1 !important;
        }
        
        .leaflet-control-container {
          z-index: 1000 !important;
        }
        
        .leaflet-popup {
          z-index: 1001 !important;
        }
        
        .leaflet-popup-pane {
          z-index: 1001 !important;
        }
        
        .leaflet-popup-content-wrapper {
          border-radius: 8px;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          z-index: 1001 !important;
        }
        
        .leaflet-popup-content {
          margin: 0;
          padding: 0;
          z-index: 1001 !important;
        }
        
        .leaflet-popup-tip {
          z-index: 1001 !important;
        }
        
        .custom-marker {
          background: transparent !important;
          border: none !important;
          z-index: 1000 !important;
        }
        
        .leaflet-marker-icon {
          z-index: 1000 !important;
        }
        
        .leaflet-zoom-box {
          z-index: 1002 !important;
        }
        
        .leaflet-control-zoom {
          z-index: 1000 !important;
        }
        
        .leaflet-control-attribution {
          z-index: 1000 !important;
        }
        
        /* Ensure map tiles load properly */
        .leaflet-tile-pane {
          z-index: 1 !important;
        }
        
        .leaflet-overlay-pane {
          z-index: 1 !important;
        }
        
        .leaflet-shadow-pane {
          z-index: 1 !important;
        }
        
        .leaflet-marker-pane {
          z-index: 1000 !important;
        }
        
        /* Fix for map interaction */
        .leaflet-interactive {
          cursor: pointer;
        }
        
        .leaflet-grab {
          cursor: grab;
        }
        
        .leaflet-grabbing {
          cursor: grabbing;
        }
      `}</style>
    </div>
  );
};

export default ProfessionalMap;