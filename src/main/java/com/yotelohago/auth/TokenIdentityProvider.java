package com.yotelohago.auth;

import com.yotelohago.user.domain.AuthProvider;
import com.yotelohago.user.domain.UserExternalIdentity;
import com.yotelohago.user.domain.UserExternalIdentityRepository;
import io.quarkus.security.identity.SecurityIdentity;
import jakarta.enterprise.context.RequestScoped;
import jakarta.inject.Inject;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Objects;
import java.util.Optional;
import java.util.UUID;

/**
 * Provides authentication and authorization services for the current request context.
 *
 * <p>This service acts as the central point for:
 * <ul>
 *   <li>Mapping external provider IDs (Keycloak, Apple, Google) to internal user IDs</li>
 *   <li>Providing authorization checks for cross-domain operations</li>
 *   <li>Extracting user information from JWT tokens</li>
 * </ul>
 *
 * <p>The service is request-scoped to ensure thread safety and proper JWT token handling.
 * All authorization methods use internal user IDs for consistency across the application.
 *
 * <AUTHOR> Team
 * @since 1.0
 */
@RequestScoped
public class TokenIdentityProvider {

    private static final Logger logger = LoggerFactory.getLogger(TokenIdentityProvider.class);

    private final SecurityIdentity securityIdentity;
    private final JsonWebToken jwt;
    private final UserExternalIdentityRepository externalIdentityRepository;

    // Cache for the current user ID to avoid multiple database lookups per request
    private UUID cachedCurrentUserId;
    private boolean currentUserIdResolved = false;

    @Inject
    public TokenIdentityProvider(
            SecurityIdentity securityIdentity,
            JsonWebToken jwt,
            UserExternalIdentityRepository externalIdentityRepository) {
        this.securityIdentity = Objects.requireNonNull(securityIdentity, "SecurityIdentity cannot be null");
        this.jwt = jwt; // Can be null for anonymous requests
        this.externalIdentityRepository = Objects.requireNonNull(externalIdentityRepository, "ExternalIdentityRepository cannot be null");
    }

    /**
     * Retrieves the current user's internal UUID from the JWT token.
     *
     * <p>This method maps the external provider ID (from JWT subject) to our internal user ID
     * through the user_external_identities table. The result is cached per request to avoid
     * multiple database lookups.
     *
     * @return the internal user UUID, or {@code null} if:
     *         <ul>
     *           <li>No JWT token is present (anonymous request)</li>
     *           <li>JWT token has no subject</li>
     *           <li>No mapping exists for the external ID</li>
     *         </ul>
     */
    public UUID getCurrentUserId() {
        if (currentUserIdResolved) {
            return cachedCurrentUserId;
        }

        currentUserIdResolved = true;

        if (!isAuthenticated()) {
            logger.debug("No JWT token or subject found for current request");
            cachedCurrentUserId = null;
            return null;
        }

        try {
            String externalId = jwt.getSubject();
            AuthProvider provider = determineAuthProvider();

            Optional<UserExternalIdentity> identity = externalIdentityRepository
                    .findByProviderAndExternalId(provider, externalId);

            cachedCurrentUserId = identity.map(UserExternalIdentity::getUserId).orElse(null);

            if (cachedCurrentUserId == null) {
                logger.warn("No internal user mapping found for external ID: {} (provider: {})", externalId, provider);
            }

            return cachedCurrentUserId;

        } catch (Exception e) {
            logger.error("Failed to resolve current user ID from JWT", e);
            cachedCurrentUserId = null;
            return null;
        }
    }

    /**
     * Retrieves the current user's external ID (provider-specific ID) from the JWT token.
     *
     * <p>This is the ID assigned by the external authentication provider (Keycloak, Apple, Google).
     * Use {@link #getCurrentUserId()} to get the internal user ID for business logic.
     *
     * @return the external user ID from JWT subject, or {@code null} if not authenticated
     */
    public String getCurrentExternalUserId() {
        return isAuthenticated() ? jwt.getSubject() : null;
    }

    /**
     * Retrieves the current user's username from the JWT token.
     *
     * @return the username from JWT claims, or {@code null} if not available
     */
    public String getCurrentUserName() {
        if (!isAuthenticated()) {
            return null;
        }
        return jwt.getClaim("preferred_username");
    }

    /**
     * Checks if the current user has the specified role.
     *
     * @param role the role name to check (e.g., "admin", "user", "professional")
     * @return {@code true} if the user has the role, {@code false} otherwise
     * @throws IllegalArgumentException if role is null or empty
     */
    public boolean hasRole(String role) {
        if (role == null || role.trim().isEmpty()) {
            throw new IllegalArgumentException("Role cannot be null or empty");
        }
        return securityIdentity.hasRole(role);
    }

    /**
     * Checks if the current request is authenticated.
     *
     * @return {@code true} if there's a valid JWT token with a subject, {@code false} otherwise
     */
    public boolean isAuthenticated() {
        return jwt != null && jwt.getSubject() != null && !jwt.getSubject().trim().isEmpty();
    }

    // ========================================
    // Authorization Methods
    // ========================================

    /**
     * Checks if the current user can access a message between two users.
     *
     * <p>Access is granted if:
     * <ul>
     *   <li>User has admin role, OR</li>
     *   <li>User is either the sender or receiver of the message</li>
     * </ul>
     *
     * @param senderId the internal user ID of the message sender
     * @param receiverId the internal user ID of the message receiver
     * @return {@code true} if access is allowed, {@code false} otherwise
     * @throws IllegalArgumentException if either senderId or receiverId is null
     */
    public boolean canAccessMessage(UUID senderId, UUID receiverId) {
        validateUUID(senderId, "senderId");
        validateUUID(receiverId, "receiverId");

        if (hasRole("admin")) {
            return true;
        }

        UUID currentUserId = getCurrentUserId();
        return currentUserId != null &&
               (currentUserId.equals(senderId) || currentUserId.equals(receiverId));
    }

    /**
     * Checks if the current user can access a booking.
     *
     * <p>Access is granted if:
     * <ul>
     *   <li>User has admin role, OR</li>
     *   <li>User is the client who created the booking, OR</li>
     *   <li>User is the professional assigned to the booking</li>
     * </ul>
     *
     * @param clientId the internal user ID of the booking client
     * @param professionalId the internal user ID of the assigned professional (can be null)
     * @return {@code true} if access is allowed, {@code false} otherwise
     * @throws IllegalArgumentException if clientId is null
     */
    public boolean canAccessBooking(UUID clientId, UUID professionalId) {
        validateUUID(clientId, "clientId");
        // professionalId can be null for unassigned bookings

        if (hasRole("admin")) {
            return true;
        }

        UUID currentUserId = getCurrentUserId();
        if (currentUserId == null) {
            return false;
        }

        return currentUserId.equals(clientId) ||
               (professionalId != null && currentUserId.equals(professionalId));
    }

    /**
     * Checks if the current user can access professional data.
     *
     * <p>Access is granted if:
     * <ul>
     *   <li>User has admin role, OR</li>
     *   <li>User is the professional whose data is being accessed</li>
     * </ul>
     *
     * @param professionalUserId the internal user ID of the professional
     * @return {@code true} if access is allowed, {@code false} otherwise
     * @throws IllegalArgumentException if professionalUserId is null
     */
    public boolean canAccessProfessionalData(UUID professionalUserId) {
        return canAccessUserData(professionalUserId); // Same logic as user data access
    }

    /**
     * Checks if the current user can access user data.
     *
     * <p>Access is granted if:
     * <ul>
     *   <li>User has admin role, OR</li>
     *   <li>User is accessing their own data</li>
     * </ul>
     *
     * @param userId the internal user ID whose data is being accessed
     * @return {@code true} if access is allowed, {@code false} otherwise
     * @throws IllegalArgumentException if userId is null
     */
    public boolean canAccessUserData(UUID userId) {
        validateUUID(userId, "userId");

        if (hasRole("admin")) {
            return true;
        }

        UUID currentUserId = getCurrentUserId();
        return currentUserId != null && currentUserId.equals(userId);
    }

    /**
     * Checks if the current user can access service data.
     *
     * <p>Access is granted if:
     * <ul>
     *   <li>User has admin role, OR</li>
     *   <li>User is the professional who owns the service</li>
     * </ul>
     *
     * @param professionalUserId the internal user ID of the service owner
     * @return {@code true} if access is allowed, {@code false} otherwise
     * @throws IllegalArgumentException if professionalUserId is null
     */
    public boolean canAccessServiceData(UUID professionalUserId) {
        return canAccessProfessionalData(professionalUserId); // Same logic as professional data access
    }

    // ========================================
    // Private Helper Methods
    // ========================================

    /**
     * Determines the authentication provider from the JWT token.
     *
     * <p>Currently defaults to Keycloak, but can be extended to support multiple providers
     * by examining JWT claims or issuer information.
     *
     * @return the authentication provider
     */
    private AuthProvider determineAuthProvider() {
        // TODO: Implement multi-provider detection based on JWT issuer or custom claims
        // For now, default to Keycloak as it's our primary provider
        return AuthProvider.KEYCLOAK;
    }

    /**
     * Validates that a UUID parameter is not null.
     *
     * @param uuid the UUID to validate
     * @param parameterName the name of the parameter for error messages
     * @throws IllegalArgumentException if the UUID is null
     */
    private void validateUUID(UUID uuid, String parameterName) {
        if (uuid == null) {
            throw new IllegalArgumentException(parameterName + " cannot be null");
        }
    }
}