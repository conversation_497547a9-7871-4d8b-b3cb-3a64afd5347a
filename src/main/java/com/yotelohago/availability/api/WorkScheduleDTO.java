package com.yotelohago.availability.api;

import java.time.Instant;
import java.util.List;
import java.util.UUID;

/**
 * DTO for WorkSchedule entity.
 * 
 * Represents a professional's weekly work schedule with day-by-day availability.
 * Follows YoteLoHago DTO patterns with public fields for JSON serialization.
 */
public class WorkScheduleDTO {

    public UUID professionalId;
    public List<DayScheduleDTO> daySchedules;
    public String notes;
    public Instant createdAt;
    public Instant updatedAt;
    
    // Computed fields for UI convenience
    public Boolean hasAnyActiveDays;
    public Long totalWeeklyMinutes;
    public String summary;

    // Default constructor for JSON serialization
    public WorkScheduleDTO() {}

    // Constructor for creating DTOs
    public WorkScheduleDTO(UUID professionalId, List<DayScheduleDTO> daySchedules,
                          String notes, Instant createdAt, Instant updatedAt) {
        this.professionalId = professionalId;
        this.daySchedules = daySchedules;
        this.notes = notes;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
    }
}
