import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../../contexts/AuthContext';
import Colors from '../../app/client/constants/Colors';

interface AuthStatusProps {
  showDetails?: boolean;
  style?: any;
  variant?: 'compact' | 'detailed';
}

export default function AuthStatus({
  showDetails = false,
  style,
  variant = 'compact',
}: AuthStatusProps) {
  const { state: authState } = useAuth();

  if (authState.isLoading) {
    return (
      <View style={[styles.container, styles.loadingContainer, style]}>
        <Ionicons name="refresh" size={16} color={Colors.text.secondary} />
        <Text style={styles.loadingText}>Checking authentication...</Text>
      </View>
    );
  }

  if (variant === 'compact') {
    return (
      <View style={[styles.container, styles.compactContainer, style]}>
        <Ionicons 
          name={authState.isAuthenticated ? "checkmark-circle" : "person-outline"} 
          size={16} 
          color={authState.isAuthenticated ? Colors.success : Colors.text.secondary} 
        />
        <Text style={[
          styles.statusText,
          authState.isAuthenticated ? styles.authenticatedText : styles.unauthenticatedText
        ]}>
          {authState.isAuthenticated ? "Logged in" : "Not logged in"}
        </Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, styles.detailedContainer, style]}>
      <View style={styles.statusHeader}>
        <Ionicons 
          name={authState.isAuthenticated ? "checkmark-circle" : "person-outline"} 
          size={20} 
          color={authState.isAuthenticated ? Colors.success : Colors.text.secondary} 
        />
        <Text style={[
          styles.statusTitle,
          authState.isAuthenticated ? styles.authenticatedText : styles.unauthenticatedText
        ]}>
          {authState.isAuthenticated ? "Authenticated" : "Not Authenticated"}
        </Text>
      </View>
      
      {showDetails && authState.isAuthenticated && authState.user && (
        <View style={styles.userDetails}>
          <Text style={styles.userDetailText}>
            User: {authState.user.username || authState.user.email}
          </Text>
          {authState.user.email && authState.user.username && (
            <Text style={styles.userDetailText}>
              Email: {authState.user.email}
            </Text>
          )}
        </View>
      )}
      
      {authState.error && (
        <View style={styles.errorContainer}>
          <Ionicons name="warning" size={16} color={Colors.error} />
          <Text style={styles.errorText}>{authState.error}</Text>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  compactContainer: {
    gap: 6,
  },
  detailedContainer: {
    flexDirection: 'column',
    alignItems: 'flex-start',
    gap: 8,
  },
  loadingContainer: {
    gap: 8,
  },
  statusHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  statusText: {
    fontSize: 14,
    fontWeight: '500',
  },
  statusTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  authenticatedText: {
    color: Colors.success,
  },
  unauthenticatedText: {
    color: Colors.text.secondary,
  },
  loadingText: {
    fontSize: 14,
    color: Colors.text.secondary,
    fontStyle: 'italic',
  },
  userDetails: {
    paddingLeft: 28,
    gap: 4,
  },
  userDetailText: {
    fontSize: 14,
    color: Colors.text.secondary,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    paddingLeft: 28,
  },
  errorText: {
    fontSize: 12,
    color: Colors.error,
    flex: 1,
  },
});
