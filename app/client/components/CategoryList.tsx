import React from 'react';
import { FlatList, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { Category } from '@/types';
import { Ionicons } from '@expo/vector-icons';
import Colors from '../constants/Colors';

interface CategoryListProps {
  categories: Category[];
  onSelectCategory: (categoryId: string) => void;
  selectedCategory?: string;
}

const CategoryList: React.FC<CategoryListProps> = ({ 
  categories, 
  onSelectCategory,
  selectedCategory 
}) => {
  
  const renderIcon = (iconName: string, selected: boolean) => {
    const color = selected ? Colors.text.light : Colors.text.secondary;
    const size = 24;

    switch (iconName) {
      // New trade-specific icons
      case 'electric':
        return <Ionicons name="flash-outline" size={size} color={color} />;
      case 'pipe-leak':
        return <Ionicons name="water-outline" size={size} color={color} />;
      case 'truck':
        return <Ionicons name="cube-outline" size={size} color={color} />;
      case 'unlock':
        return <Ionicons name="key-outline" size={size} color={color} />;
      // Legacy icon mappings (for backward compatibility)
      case 'home':
        return <Ionicons name="home-outline" size={size} color={color} />;
      case 'tool':
        return <Ionicons name="construct-outline" size={size} color={color} />;
      case 'book-open':
        return <Ionicons name="book-outline" size={size} color={color} />;
      case 'camera':
        return <Ionicons name="camera-outline" size={size} color={color} />;
      case 'code':
        return <Ionicons name="code-outline" size={size} color={color} />;
      case 'scissors':
        return <Ionicons name="cut-outline" size={size} color={color} />;
      case 'activity':
        return <Ionicons name="fitness-outline" size={size} color={color} />;
      case 'chef-hat':
        return <Ionicons name="restaurant-outline" size={size} color={color} />;
      default:
        return <Ionicons name="home-outline" size={size} color={color} />;
    }
  };

  const renderCategory = ({ item }: { item: Category }) => {
    const isSelected = selectedCategory === item.id;
    
    return (
      <TouchableOpacity
        style={[
          styles.categoryItem,
          isSelected && styles.selectedCategoryItem
        ]}
        onPress={() => onSelectCategory(item.id)}
      >
        <View style={styles.iconContainer}>
          {renderIcon(item.icon, isSelected)}
        </View>
        <Text 
          style={[
            styles.categoryText,
            isSelected && styles.selectedCategoryText
          ]}
          numberOfLines={1}
        >
          {item.name}
        </Text>
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      <FlatList
        data={categories}
        renderItem={renderCategory}
        keyExtractor={(item) => item.id}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={[
          styles.listContent,
          categories.length <= 4 && styles.listContentCentered
        ]}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 16,
  },
  listContent: {
    paddingHorizontal: 16,
    gap: 16,
  },
  listContentCentered: {
    flexGrow: 1,
    justifyContent: 'center',
  },
  categoryItem: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 32,
    backgroundColor: Colors.background.secondary,
    flexDirection: 'row',
    gap: 8,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  selectedCategoryItem: {
    backgroundColor: Colors.primary,
    borderColor: Colors.primary,
  },
  iconContainer: {
    width: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  categoryText: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.text.primary,
  },
  selectedCategoryText: {
    color: Colors.text.light,
  },
});

export default CategoryList;