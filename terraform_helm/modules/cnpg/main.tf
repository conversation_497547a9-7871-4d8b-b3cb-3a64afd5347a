terraform {
  required_providers {
    kubernetes = {
      source  = "hashicorp/kubernetes"
    }
  }
}


# Create the DB password secret
resource "kubernetes_secret" "yotelohago_db_secret" {
  metadata {
    name      = "yotelohago-db-secret"
    namespace = "cnpg-system"
  }

  data = {
    username = "yotelohago"
    password = "yotelohago"
  }
  type = "Opaque"
}

resource "kubernetes_manifest" "cnpg_postgres_cluster" {
  depends_on = [kubernetes_secret.yotelohago_db_secret]
  manifest = yamldecode(file("${path.module}/cnpg-postgres-cluster.yaml"))
}