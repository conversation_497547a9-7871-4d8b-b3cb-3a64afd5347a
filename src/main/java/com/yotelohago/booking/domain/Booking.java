package com.yotelohago.booking.domain;

import io.quarkus.hibernate.orm.panache.PanacheEntityBase;
import jakarta.persistence.*;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import com.yotelohago.user.domain.User;
import com.yotelohago.service.domain.Service;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.UUID;

@Entity
@Table(name = "bookings",
       indexes = {
           @Index(name = "idx_bookings_client_id", columnList = "client_id"),
           @Index(name = "idx_bookings_professional_id", columnList = "professional_id"),
           @Index(name = "idx_bookings_service_id", columnList = "service_id"),
           @Index(name = "idx_bookings_status", columnList = "status"),
           @Index(name = "idx_bookings_created_at", columnList = "created_at"),
           @Index(name = "idx_bookings_professional_status", columnList = "professional_id, status")
       })
public class Booking extends PanacheEntityBase {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "bookings_seq")
    @SequenceGenerator(name = "bookings_seq", sequenceName = "bookings_id_seq", allocationSize = 1)
    public Long id;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "client_id", nullable = false,
                foreignKey = @ForeignKey(name = "fk_bookings_client"))
    public User client;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "professional_id",
                foreignKey = @ForeignKey(name = "fk_bookings_professional"))
    public User professional;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "service_id", nullable = false,
                foreignKey = @ForeignKey(name = "fk_bookings_service"))
    public Service service;

    @Column(nullable = false)
    public String title;

    @Column(nullable = false, columnDefinition = "TEXT")
    public String description;

    @Column(name = "requested_date")
    public Instant requestedDate;

    @Column(name = "scheduled_at")
    public Instant scheduledAt;

    @Column(nullable = false, precision = 10, scale = 2)
    public BigDecimal price;

    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    public BookingStatus status;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    public Instant createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at")
    public Instant updatedAt;

    public Booking() {
        // JPA
    }

    public Booking(User client, Service service, String title, String description,
                   Instant requestedDate, BigDecimal price, BookingStatus status) {
        this.client = client;
        this.service = service;
        this.title = title;
        this.description = description;
        this.requestedDate = requestedDate;
        this.price = price;
        this.status = status;
        // createdAt and updatedAt will be set automatically by annotations
        // professional will be assigned later when booking is accepted
    }

    // Getters
    public Long getId() { return id; }
    public User getClient() { return client; }
    public User getProfessional() { return professional; }
    public Service getService() { return service; }
    public String getTitle() { return title; }
    public String getDescription() { return description; }
    public Instant getRequestedDate() { return requestedDate; }
    public Instant getScheduledAt() { return scheduledAt; }
    public BigDecimal getPrice() { return price; }
    public BookingStatus getStatus() { return status; }
    public Instant getCreatedAt() { return createdAt; }
    public Instant getUpdatedAt() { return updatedAt; }



    // Setters
    public void setId(Long id) { this.id = id; }
    public void setClient(User client) { this.client = client; }
    public void setProfessional(User professional) { this.professional = professional; }
    public void setService(Service service) { this.service = service; }

    // Convenience setters for backward compatibility - load entities by ID
    public void setClientId(UUID clientId) {
        this.client = clientId != null ? User.findById(clientId) : null;
    }

    public void setProfessionalId(UUID professionalId) {
        this.professional = professionalId != null ? User.findById(professionalId) : null;
    }

    public void setServiceId(UUID serviceId) {
        this.service = serviceId != null ? Service.findById(serviceId) : null;
    }

    public void setTitle(String title) { this.title = title; }
    public void setDescription(String description) { this.description = description; }
    public void setRequestedDate(Instant requestedDate) { this.requestedDate = requestedDate; }
    public void setScheduledAt(Instant scheduledAt) { this.scheduledAt = scheduledAt; }
    public void setPrice(BigDecimal price) { this.price = price; }
    public void setStatus(BookingStatus status) { this.status = status; }
    public void setCreatedAt(Instant createdAt) { this.createdAt = createdAt; }
    public void setUpdatedAt(Instant updatedAt) { this.updatedAt = updatedAt; }
}