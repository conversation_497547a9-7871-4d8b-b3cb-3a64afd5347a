import React from 'react';
import { Typography, Card, Form, Input, Select, Switch, Button, Divider, Space, Upload, Avatar } from 'antd';
import { UploadOutlined, UserOutlined } from '@ant-design/icons';

const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;

const Settings: React.FC = () => {
  const [form] = Form.useForm();

  const handleSaveSettings = (values: any) => {
    console.log('Settings saved:', values);
  };

  return (
    <div className="p-6 space-y-6">
      <Title level={2} className="m-0">Settings</Title>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 space-y-6">
          <Card title="Platform Settings" className="shadow-sm">
            <Form
              form={form}
              layout="vertical"
              onFinish={handleSaveSettings}
              initialValues={{
                platformName: 'ServiceAdmin',
                platformDescription: 'Professional service marketplace connecting clients with skilled professionals',
                currency: 'USD',
                timezone: 'America/New_York',
                language: 'en',
                commissionRate: 10,
              }}
            >
              <Form.Item
                name="platformName"
                label="Platform Name"
                rules={[{ required: true, message: 'Please enter platform name' }]}
              >
                <Input placeholder="Enter platform name" />
              </Form.Item>

              <Form.Item
                name="platformDescription"
                label="Platform Description"
              >
                <TextArea
                  rows={3}
                  placeholder="Enter platform description"
                />
              </Form.Item>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Form.Item
                  name="currency"
                  label="Currency"
                >
                  <Select>
                    <Option value="USD">USD - US Dollar</Option>
                    <Option value="EUR">EUR - Euro</Option>
                    <Option value="GBP">GBP - British Pound</Option>
                    <Option value="CAD">CAD - Canadian Dollar</Option>
                  </Select>
                </Form.Item>

                <Form.Item
                  name="timezone"
                  label="Timezone"
                >
                  <Select>
                    <Option value="America/New_York">Eastern Time (ET)</Option>
                    <Option value="America/Chicago">Central Time (CT)</Option>
                    <Option value="America/Denver">Mountain Time (MT)</Option>
                    <Option value="America/Los_Angeles">Pacific Time (PT)</Option>
                  </Select>
                </Form.Item>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Form.Item
                  name="language"
                  label="Default Language"
                >
                  <Select>
                    <Option value="en">English</Option>
                    <Option value="es">Spanish</Option>
                    <Option value="fr">French</Option>
                  </Select>
                </Form.Item>

                <Form.Item
                  name="commissionRate"
                  label="Commission Rate (%)"
                >
                  <Input type="number" min="0" max="100" placeholder="10" />
                </Form.Item>
              </div>
            </Form>
          </Card>

          <Card title="Notification Settings" className="shadow-sm">
            <Form layout="vertical">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Text strong>Email Notifications</Text>
                    <div className="text-sm text-gray-500">
                      Receive email notifications for important events
                    </div>
                  </div>
                  <Switch defaultChecked />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Text strong>Booking Notifications</Text>
                    <div className="text-sm text-gray-500">
                      Get notified when new bookings are created
                    </div>
                  </div>
                  <Switch defaultChecked />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Text strong>Professional Applications</Text>
                    <div className="text-sm text-gray-500">
                      Alert when new professionals apply
                    </div>
                  </div>
                  <Switch defaultChecked />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Text strong>User Registration</Text>
                    <div className="text-sm text-gray-500">
                      Notify about new user registrations
                    </div>
                  </div>
                  <Switch />
                </div>
              </div>
            </Form>
          </Card>

          <Card title="Security Settings" className="shadow-sm">
            <Form layout="vertical">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Text strong>Two-Factor Authentication</Text>
                    <div className="text-sm text-gray-500">
                      Add an extra layer of security to your account
                    </div>
                  </div>
                  <Switch />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Text strong>Login Notifications</Text>
                    <div className="text-sm text-gray-500">
                      Get notified of login attempts
                    </div>
                  </div>
                  <Switch defaultChecked />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Text strong>Auto Logout</Text>
                    <div className="text-sm text-gray-500">
                      Automatically logout after period of inactivity
                    </div>
                  </div>
                  <Switch defaultChecked />
                </div>
              </div>
            </Form>
          </Card>
        </div>

        <div className="space-y-6">
          <Card title="Profile Settings" className="shadow-sm">
            <div className="text-center mb-6">
              <Avatar
                size={80}
                icon={<UserOutlined />}
                className="mb-4"
              />
              <Upload
                showUploadList={false}
                beforeUpload={() => false}
              >
                <Button icon={<UploadOutlined />}>
                  Change Avatar
                </Button>
              </Upload>
            </div>

            <Form layout="vertical">
              <Form.Item
                label="Full Name"
                initialValue="Admin User"
              >
                <Input />
              </Form.Item>

              <Form.Item
                label="Email"
                initialValue="<EMAIL>"
              >
                <Input />
              </Form.Item>

              <Form.Item
                label="Phone"
                initialValue="******-0123"
              >
                <Input />
              </Form.Item>

              <Divider />

              <Form.Item
                label="Current Password"
              >
                <Input.Password placeholder="Enter current password" />
              </Form.Item>

              <Form.Item
                label="New Password"
              >
                <Input.Password placeholder="Enter new password" />
              </Form.Item>

              <Form.Item
                label="Confirm Password"
              >
                <Input.Password placeholder="Confirm new password" />
              </Form.Item>
            </Form>
          </Card>

          <Card title="Quick Actions" className="shadow-sm">
            <Space direction="vertical" className="w-full">
              <Button type="primary" block>
                Export Data
              </Button>
              <Button block>
                Import Data
              </Button>
              <Button block>
                Clear Cache
              </Button>
              <Button danger block>
                Reset Settings
              </Button>
            </Space>
          </Card>
        </div>
      </div>

      <div className="flex justify-end">
        <Space>
          <Button>Cancel</Button>
          <Button type="primary" onClick={() => form.submit()}>
            Save Settings
          </Button>
        </Space>
      </div>
    </div>
  );
};

export default Settings;