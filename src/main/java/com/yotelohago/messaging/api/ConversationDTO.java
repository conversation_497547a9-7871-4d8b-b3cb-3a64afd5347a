package com.yotelohago.messaging.api;

import java.time.Instant;
import java.util.UUID;

/**
 * Parent class for conversation-related DTOs
 */
public class ConversationDTO {

    /**
     * DTO for conversation summaries
     * Uses client/professional fields instead of generic otherParticipant fields
     * for better semantic clarity and mode-aware frontend support
     */
    public static class ConversationSummary {
        public Long bookingId;
        public String bookingTitle;
        public String bookingDescription;
        public String lastMessageContent;
        public Instant lastMessageTime;
        public boolean lastMessageFromCurrentUser;
        public int unreadCount;

        // Participant details - always populated for both client and professional
        public UUID clientId;
        public String clientName;
        public String clientEmail;
        public UUID professionalId;
        public String professionalName;
        public String professionalEmail;
    }

    /**
     * DTO for conversation info (participant details without messages)
     * Uses client/professional fields for consistency with ConversationSummary
     */
    public static class ConversationInfo {
        public Long bookingId;
        // Participant details - always populated for both client and professional
        public UUID clientId;
        public String clientName;
        public String clientEmail;
        public UUID professionalId;
        public String professionalName;
        public String professionalEmail;
    }
}
