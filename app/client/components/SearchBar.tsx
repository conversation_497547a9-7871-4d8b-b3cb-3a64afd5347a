import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  Pressable,
  Animated,
  Platform,
  Dimensions,
  Modal,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import Colors from '../constants/Colors';

interface SearchBarProps {
  placeholder?: string;
  onSearch?: (text: string) => void;
  onLocationChange?: (location: string) => void;
  onDateChange?: (startDate: string, endDate?: string) => void;
  onFiltersPress?: () => void;
  debounceMs?: number;
  selectedLocation?: string;
  selectedDate?: string;
  disabled?: boolean;
  autoFocus?: boolean;
}

const LOCATIONS = ['New York', 'Los Angeles', 'Chicago', 'Miami'];
const TIME_OPTIONS = ['Today', 'Tomorrow', 'This week', 'Next week'];

const SearchBar: React.FC<SearchBarProps> = ({
  placeholder = 'Type a service',
  onSearch,
  onLocationChange,
  onDateChange,
  onFiltersPress,
  debounceMs = 500,
  selectedLocation = '',
  selectedDate = '',
  disabled = false,
  autoFocus = false,
}) => {
  // State management
  const [searchText, setSearchText] = useState('');
  const [showWhereModal, setShowWhereModal] = useState(false);
  const [showWhenModal, setShowWhenModal] = useState(false);
  const [showMobileModal, setShowMobileModal] = useState(false);
  const [hoveredButton, setHoveredButton] = useState<string | null>(null);
  const [whereHovered, setWhereHovered] = useState(false);
  const [whenHovered, setWhenHovered] = useState(false);

  // Refs
  const debounceTimer = useRef<ReturnType<typeof setTimeout> | null>(null);
  const shadowAnim = useRef(new Animated.Value(0)).current;
  const searchButtonScaleAnim = useRef(new Animated.Value(1)).current;

  // Responsive design
  const { width } = Dimensions.get('window');
  const isDesktop = useMemo(() => width >= 768, [width]);

  // Debounced search handler
  const handleTextChange = useCallback((text: string) => {
    setSearchText(text);

    if (debounceTimer.current) {
      clearTimeout(debounceTimer.current);
    }

    debounceTimer.current = setTimeout(() => {
      onSearch?.(text);
    }, debounceMs);
  }, [onSearch, debounceMs]);

  // Search button handler
  const handleSearchPress = useCallback(() => {
    onSearch?.(searchText);
  }, [onSearch, searchText]);

  // Animation handlers
  const handleMouseEnter = useCallback(() => {
    if (Platform.OS === 'web' && !disabled) {
      Animated.timing(shadowAnim, {
        toValue: 1,
        duration: 200,
        useNativeDriver: false,
      }).start();
    }
  }, [shadowAnim, disabled]);

  const handleMouseLeave = useCallback(() => {
    if (Platform.OS === 'web') {
      Animated.timing(shadowAnim, {
        toValue: 0,
        duration: 200,
        useNativeDriver: false,
      }).start();
    }
  }, [shadowAnim]);

  // Button animation handlers
  const handleButtonPressIn = useCallback(() => {
    if (!disabled) {
      Animated.timing(searchButtonScaleAnim, {
        toValue: 1.1,
        duration: 100,
        useNativeDriver: true,
      }).start();

      if (Platform.OS !== 'web') {
        handleMouseEnter();
      }
    }
  }, [searchButtonScaleAnim, disabled, handleMouseEnter]);

  const handleButtonPressOut = useCallback(() => {
    Animated.timing(searchButtonScaleAnim, {
      toValue: 1,
      duration: 150,
      useNativeDriver: true,
    }).start();

    if (Platform.OS !== 'web') {
      setTimeout(handleMouseLeave, 100);
    }
  }, [searchButtonScaleAnim, handleMouseLeave]);

  // Modal handlers
  const handleLocationSelect = useCallback((location: string) => {
    onLocationChange?.(location);
    setShowWhereModal(false);
    setHoveredButton(null);
  }, [onLocationChange]);

  const handleDateSelect = useCallback((date: string) => {
    onDateChange?.(date);
    setShowWhenModal(false);
    setHoveredButton(null);
  }, [onDateChange]);

  // Cleanup effect
  useEffect(() => {
    return () => {
      if (debounceTimer.current) {
        clearTimeout(debounceTimer.current);
      }
    };
  }, []);

  // Animated styles
  const animatedShadowOpacity = shadowAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0.1, 0.3],
  });

  const animatedShadowRadius = shadowAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [4, 20],
  });

  const animatedElevation = shadowAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [4, 16],
  });

  return (
    <View style={styles.wrapper}>
      <View style={[
        styles.container,
        isDesktop && styles.containerDesktop
      ]}>
        <View style={styles.searchWrapper}>
          <Animated.View
            style={[
              styles.searchContainer,
              disabled && styles.searchContainerDisabled,
              {
                shadowOpacity: animatedShadowOpacity,
                shadowRadius: animatedShadowRadius,
                elevation: animatedElevation,
              },
            ]}
            {...(Platform.OS === 'web' && !disabled && {
              onMouseEnter: handleMouseEnter,
              onMouseLeave: handleMouseLeave,
            })}
          >
            <View style={styles.gradientContainer}>
              {/* Search Input Section */}
              <View style={styles.whatSection}>
                <Text style={styles.sectionLabel}>Services</Text>
                <TextInput
                  style={[styles.input, disabled && styles.inputDisabled]}
                  placeholder={placeholder}
                  placeholderTextColor={disabled ? Colors.text.disabled : Colors.text.secondary}
                  value={searchText}
                  onChangeText={handleTextChange}
                  editable={!disabled}
                  autoFocus={autoFocus}
                  returnKeyType="search"
                  onSubmitEditing={handleSearchPress}
                />
              </View>
              
              {/* Desktop Filters */}
              {isDesktop && (
                <>
                  <View style={styles.separator} />
                  
                  {/* Where Section */}
                  <Pressable
                    style={[
                      styles.filterSection,
                      whereHovered && styles.filterSectionHovered,
                      disabled && styles.filterSectionDisabled,
                    ]}
                    onPress={() => !disabled && setShowWhereModal(true)}
                    disabled={disabled}
                    {...(Platform.OS === 'web' && !disabled && {
                      onMouseEnter: () => setWhereHovered(true),
                      onMouseLeave: () => setWhereHovered(false),
                    })}
                  >
                    <Text style={styles.sectionLabel}>Where</Text>
                    <Text style={[
                      styles.filterValue,
                      !selectedLocation && styles.filterPlaceholder,
                      disabled && styles.filterValueDisabled,
                    ]}>
                      {selectedLocation || 'Add location'}
                    </Text>
                  </Pressable>

                  <View style={styles.separator} />
                  
                  {/* When Section */}
                  <Pressable
                    style={[
                      styles.filterSection,
                      whenHovered && styles.filterSectionHovered,
                      disabled && styles.filterSectionDisabled,
                    ]}
                    onPress={() => !disabled && setShowWhenModal(true)}
                    disabled={disabled}
                    {...(Platform.OS === 'web' && !disabled && {
                      onMouseEnter: () => setWhenHovered(true),
                      onMouseLeave: () => setWhenHovered(false),
                    })}
                  >
                    <Text style={styles.sectionLabel}>When</Text>
                    <Text style={[
                      styles.filterValue,
                      !selectedDate && styles.filterPlaceholder,
                      disabled && styles.filterValueDisabled,
                    ]}>
                      {selectedDate || 'Add date'}
                    </Text>
                  </Pressable>
                </>
              )}
              
              {/* Search Button */}
              <Animated.View style={{ transform: [{ scale: searchButtonScaleAnim }] }}>
                <Pressable
                  style={[styles.searchButton, disabled && styles.searchButtonDisabled]}
                  onPress={handleSearchPress}
                  disabled={disabled}
                  {...(Platform.OS === 'web' && !disabled && {
                    onMouseEnter: () => {
                      Animated.timing(searchButtonScaleAnim, {
                        toValue: 1.1,
                        duration: 150,
                        useNativeDriver: true,
                      }).start();
                    },
                    onMouseLeave: () => {
                      Animated.timing(searchButtonScaleAnim, {
                        toValue: 1,
                        duration: 150,
                        useNativeDriver: true,
                      }).start();
                    },
                  })}
                  onPressIn={handleButtonPressIn}
                  onPressOut={handleButtonPressOut}
                >
                  <LinearGradient
                    colors={disabled ? [Colors.inactive, Colors.inactive] : ['#FF5A5F', '#FF7B7F']}
                    style={styles.searchButtonGradient}
                  >
                    <Ionicons 
                      name="search" 
                      size={20} 
                      color={disabled ? Colors.text.disabled : Colors.text.light}
                    />
                  </LinearGradient>
                </Pressable>
              </Animated.View>
            </View>
          </Animated.View>

          {/* Where Modal */}
          <Modal
            visible={showWhereModal && isDesktop}
            transparent
            animationType="fade"
            onRequestClose={() => setShowWhereModal(false)}
          >
            <Pressable
              style={styles.miniModalOverlay}
              onPress={() => setShowWhereModal(false)}
            >
              <View style={[styles.miniModal, styles.whereModal]}>
                <Text style={styles.modalQuestion}>Where do you need the service?</Text>
                <View style={styles.modalButtons}>
                  {LOCATIONS.map((location) => (
                    <Pressable
                      key={location}
                      style={[
                        styles.modalButton,
                        hoveredButton === location && styles.modalButtonHovered
                      ]}
                      onPress={() => handleLocationSelect(location)}
                      onPressIn={() => setHoveredButton(location)}
                      onPressOut={() => setHoveredButton(null)}
                      {...(Platform.OS === 'web' && {
                        onMouseEnter: () => setHoveredButton(location),
                        onMouseLeave: () => setHoveredButton(null),
                      })}
                    >
                      <Text style={[
                        styles.modalButtonText,
                        hoveredButton === location && styles.modalButtonTextHovered
                      ]}>
                        {location}
                      </Text>
                    </Pressable>
                  ))}
                </View>
              </View>
            </Pressable>
          </Modal>

          {/* When Modal */}
          <Modal
            visible={showWhenModal && isDesktop}
            transparent
            animationType="fade"
            onRequestClose={() => setShowWhenModal(false)}
          >
            <Pressable
              style={styles.miniModalOverlay}
              onPress={() => setShowWhenModal(false)}
            >
              <View style={[styles.miniModal, styles.whenModal]}>
                <Text style={styles.modalQuestion}>When do you need the service?</Text>
                <View style={styles.modalButtons}>
                  {TIME_OPTIONS.map((time) => (
                    <Pressable
                      key={time}
                      style={[
                        styles.modalButton,
                        hoveredButton === time && styles.modalButtonHovered
                      ]}
                      onPress={() => handleDateSelect(time)}
                      onPressIn={() => setHoveredButton(time)}
                      onPressOut={() => setHoveredButton(null)}
                      {...(Platform.OS === 'web' && {
                        onMouseEnter: () => setHoveredButton(time),
                        onMouseLeave: () => setHoveredButton(null),
                      })}
                    >
                      <Text style={[
                        styles.modalButtonText,
                        hoveredButton === time && styles.modalButtonTextHovered
                      ]}>
                        {time}
                      </Text>
                    </Pressable>
                  ))}
                </View>
              </View>
            </Pressable>
          </Modal>
        </View>

        {/* Mobile Filters Button */}
        {!isDesktop && (
          <Pressable
            style={[styles.filtersButton, disabled && styles.filtersButtonDisabled]}
            onPress={() => !disabled && setShowMobileModal(true)}
            disabled={disabled}
          >
            <View style={styles.filtersGradient}>
              <Ionicons
                name="options"
                size={20}
                color={disabled ? Colors.text.disabled : Colors.text.primary}
              />
            </View>
          </Pressable>
        )}
      </View>

      {/* Mobile Modal */}
      <Modal
        visible={showMobileModal && !isDesktop}
        transparent
        animationType="slide"
        onRequestClose={() => setShowMobileModal(false)}
      >
        <View style={styles.mobileModalOverlay}>
          <View style={styles.mobileModal}>
            <View style={styles.mobileModalHeader}>
              <Text style={styles.mobileModalTitle}>Filters</Text>
              <Pressable onPress={() => setShowMobileModal(false)}>
                <Ionicons name="close" size={24} color={Colors.text.primary} />
              </Pressable>
            </View>

            <View style={styles.mobileModalContent}>
              {/* Where Section */}
              <View style={styles.mobileModalSection}>
                <Text style={styles.mobileModalQuestion}>Where do you need the service?</Text>
                <View style={styles.mobileModalButtons}>
                  {LOCATIONS.map((location) => (
                    <Pressable
                      key={location}
                      style={[
                        styles.mobileModalOptionButton,
                        selectedLocation === location && styles.mobileModalOptionButtonSelected
                      ]}
                      onPress={() => onLocationChange?.(location)}
                    >
                      <Text style={[
                        styles.mobileModalOptionButtonText,
                        selectedLocation === location && styles.mobileModalOptionButtonTextSelected
                      ]}>
                        {location}
                      </Text>
                    </Pressable>
                  ))}
                </View>
              </View>

              {/* When Section */}
              <View style={styles.mobileModalSection}>
                <Text style={styles.mobileModalQuestion}>When do you need the service?</Text>
                <View style={styles.mobileModalButtons}>
                  {TIME_OPTIONS.map((time) => (
                    <Pressable
                      key={time}
                      style={[
                        styles.mobileModalOptionButton,
                        selectedDate === time && styles.mobileModalOptionButtonSelected
                      ]}
                      onPress={() => onDateChange?.(time)}
                    >
                      <Text style={[
                        styles.mobileModalOptionButtonText,
                        selectedDate === time && styles.mobileModalOptionButtonTextSelected
                      ]}>
                        {time}
                      </Text>
                    </Pressable>
                  ))}
                </View>
              </View>
            </View>

            <Pressable
              style={styles.mobileModalButton}
              onPress={() => setShowMobileModal(false)}
            >
              <Text style={styles.mobileModalButtonText}>Apply</Text>
            </Pressable>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    position: 'relative',
    width: '100%',
  },
  searchWrapper: {
    position: 'relative',
    flex: 1,
    minWidth: 0,
  },
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    //paddingHorizontal: 50,
    marginVertical: 9,
    marginHorizontal: 'auto',
    gap: 12,
    width: '90%',
    maxWidth: 1000,
    minHeight: 56,
  },
  searchContainer: {
    flex: 1,
    borderRadius: 50,
    borderWidth: 0,
    borderColor: Colors.border,
    backgroundColor: Colors.background.primary,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 6,
    minWidth: 0,
    ...(Platform.OS === 'web' && {
      outlineStyle: 'none' as any,
      outlineWidth: 0,
    }),
  },
  searchContainerDisabled: {
    backgroundColor: Colors.background.disabled,
    borderColor: Colors.inactive,
  },
  gradientContainer: {
    borderRadius: 50,
    overflow: 'hidden',
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingLeft: 20,
    paddingRight: 8,
    minHeight: 56,
    flex: 1,
  },
  whatSection: {
    flex: 1,
    justifyContent: 'center',
    minHeight: 40,
    minWidth: 0,
  },
  filterSection: {
    flex: 1,
    paddingHorizontal: 16,
    paddingVertical: 8,
    minWidth: 120,
    borderRadius: 20,
  },
  filterSectionHovered: {
    backgroundColor: 'rgba(0,0,0,0.05)',
  },
  filterSectionDisabled: {
    opacity: 0.5,
  },
  sectionLabel: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.text.primary,
    marginBottom: 4,
  },
  input: {
    fontSize: 16,
    color: Colors.text.primary,
    borderWidth: 0,
    padding: 0,
    minHeight: 24,
    flex: 1,
    minWidth: 0,
    textAlignVertical: 'center',
    ...(Platform.OS === 'web' && {
      outlineStyle: 'none' as any,
      outlineWidth: 0,
    }),
  },
  inputDisabled: {
    color: Colors.text.disabled,
  },
  filterValue: {
    fontSize: 14,
    color: Colors.text.primary,
    fontWeight: '400',
  },
  filterPlaceholder: {
    color: Colors.text.secondary,
  },
  filterValueDisabled: {
    color: Colors.text.disabled,
  },
  separator: {
    width: 1,
    height: 32,
    backgroundColor: Colors.border,
  },
  searchButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    overflow: 'hidden',
    marginLeft: 12,
    alignSelf: 'center',
    flexShrink: 0,
  },
  searchButtonDisabled: {
    opacity: 0.5,
  },
  searchButtonGradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  filtersButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: Colors.background.secondary,
    borderWidth: 1,
    borderColor: Colors.border,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
    alignSelf: 'center',
  },
  filtersButtonDisabled: {
    backgroundColor: Colors.background.disabled,
    borderColor: Colors.inactive,
  },
  filtersGradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  // Modal styles
  miniModalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.1)',
    justifyContent: 'flex-start',
    alignItems: 'center',
    paddingTop: 120,
    paddingHorizontal: 16,
  },
  miniModal: {
    backgroundColor: Colors.background.primary,
    borderRadius: 16,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
    width: '100%',
    maxWidth: 400,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  whereModal: {},
  whenModal: {},
  modalQuestion: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text.primary,
    marginBottom: 12,
  },
  modalButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginTop: 4,
  },
  modalButton: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    backgroundColor: Colors.background.primary,
    borderWidth: 1,
    borderColor: Colors.border,
    alignSelf: 'flex-start',
    minWidth: 80,
  },
  modalButtonHovered: {
    backgroundColor: '#FF5A5F',
    borderColor: '#FF5A5F',
  },
  modalButtonText: {
    fontSize: 14,
    color: Colors.text.primary,
    textAlign: 'center',
    fontWeight: '500',
  },
  modalButtonTextHovered: {
    color: Colors.text.light,
  },
  // Mobile modal styles
  mobileModalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'flex-end',
  },
  mobileModal: {
    backgroundColor: Colors.background.primary,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    padding: 24,
    maxHeight: '80%',
  },
  mobileModalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  mobileModalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text.primary,
  },
  mobileModalContent: {
    gap: 20,
    marginBottom: 24,
  },
  mobileModalSection: {
    gap: 12,
  },
  mobileModalQuestion: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text.primary,
    marginBottom: 8,
  },
  mobileModalButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  mobileModalOptionButton: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    backgroundColor: Colors.background.primary,
    borderWidth: 1,
    borderColor: Colors.border,
    alignSelf: 'flex-start',
    minWidth: 80,
  },
  mobileModalOptionButtonSelected: {
    backgroundColor: '#FF5A5F',
    borderColor: '#FF5A5F',
  },
  mobileModalOptionButtonText: {
    fontSize: 14,
    color: Colors.text.primary,
    textAlign: 'center',
    fontWeight: '500',
  },
  mobileModalOptionButtonTextSelected: {
    color: Colors.text.light,
  },
  mobileModalButton: {
    backgroundColor: Colors.primary,
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
  },
  mobileModalButtonText: {
    color: Colors.text.light,
    fontSize: 16,
    fontWeight: '600',
  },
});

export default SearchBar;
