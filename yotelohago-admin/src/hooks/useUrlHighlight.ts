import { useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';

export const useUrlHighlight = () => {
  const location = useLocation();
  const [highlightId, setHighlightId] = useState<string | null>(null);

  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const highlight = params.get('highlight');
    setHighlightId(highlight);

    // Clear highlight after 3 seconds
    if (highlight) {
      const timer = setTimeout(() => {
        setHighlightId(null);
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [location.search]);

  return highlightId;
};