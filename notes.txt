# Create lab and got to AWS login page. IAM -> USERE -> ADD SECRET KEY. Copya and paste key and secret

# Configure aws cli profile

aws configure --profile kodekloud
AWS Access Key ID [****************C33T]: ********************
AWS Secret Access Key [****************CDw3]: DaH5l/yOnCXSlcEz9zg6ZvkzvRkuymJET/Itxg5V
Default region name [us-east-1]: <Hit ENTER>
Default output format [None]: <Hit ENTER>

# Set AWS env var to ensure we are using the profile we created on this terminal session.
export AWS_PROFILE=kcloud

# install
brew install yq  # For MacOS

# Install Minikube
brew install minikube

# Install kubectl (Kubernetes CLI)
brew install kubectl


# Start Minikube with Docker driver
minikube start --driver=docker

kubectl get nodes

kubectl create namespace jenkins

kubectl apply -n jenkins -f - <<EOF
apiVersion: apps/v1
kind: Deployment
metadata:
  name: jenkins
spec:
  replicas: 1
  selector:
    matchLabels:
      app: jenkins
  template:
    metadata:
      labels:
        app: jenkins
    spec:
      containers:
      - name: jenkins
        image: jenkins/jenkins:lts
        ports:
        - containerPort: 8080
        - containerPort: 50000
        resources:
          limits:
            memory: "512Mi"
            cpu: "500m"
          requests:
            memory: "256Mi"
            cpu: "250m"
EOF


kubectl expose deployment jenkins --type=NodePort --port=8080 -n jenkins

# Get URL
minikube service jenkins -n jenkins --url

# Get password
kubectl logs -n jenkins $(kubectl get pod -n jenkins -l app=jenkins -o jsonpath="{.items[0].metadata.name}")

# Jenkins Test pipeline
pipeline {
    agent any
    stages {
        stage('Build') {
            steps {
                echo 'Building...'
            }
        }
        stage('Test') {
            steps {
                echo 'Testing...'
            }
        }
        stage('Deploy') {
            steps {
                echo 'Deploying...'
            }
        }
    }
}

Step 1: Install Kubernetes Plugin on Jenkins
	1.	Go to your Jenkins dashboard: http://127.0.0.1:<NodePort>
	2.	Log in with your Admin credentials.
	3.	Go to: Manage Jenkins > Manage Plugins > Available
	4.	Search for: Kubernetes
	5.	Install the plugin Kubernetes (No restart needed).

  kubectl apply -f jenkins_persisten_volume.yaml

	kubectl delete pod jenkins -n jenkins

	minikube mount /path/on/your/mac:/data/jenkins


 sudo yum update -y
 sudo yum install -y python3 python3-pip git
 cd /home/<USER>
 sudo pip3 install flask
 git clone https://<EMAIL>/hectorfloresflores/master-computer-science",
 cd /home/<USER>/master-computer-science/cs-498-cloud-computing-applications/mp2/flask-app",
 sudo python3 app.py &",

 https://381491916497.signin.aws.amazon.com/console?region=us-east-1
 kk_labs_user_649032
 w!ObB^A3WDVh
 ********************
 cVVLg50xb+AMwm9Biy5MWDaIHtglBNUntZ8IfAMf

 rm -rf ./config/*.generated $(BACKEND_CONFIG_FILE_DIR)/.terraform $(BACKEND_CONFIG_FILE_DIR)/.terraform.lock.hcl $(BACKEND_CONFIG_FILE_DIR)/terraform.tfstate* .terraform/.terraform ./terraform/.terraform.lock.hcl ./terraform/terraform.tfstate*
 echo "ec2-host ansible_host=$$(terraform -chdir=terraform output -raw ec2_ip) ansible_user=$$(terraform -chdir=terraform output -raw ec2_user) ansible_ssh_private_key_file=$$(terraform -chdir=terraform output -raw ec2_key_path) ansible_python_interpreter=/usr/bin/python3.8" >> $(ANSIBLE_HOSTS_FILE)


minikube delete

minikube start \
  --driver=docker \
  --apiserver-ips=************ \
  --extra-config=apiserver.authorization-mode=Node,RBAC

cat /home/<USER>/.minikube/ca.crt
cat /home/<USER>/.minikube/profiles/minikube/client.crt
cat /home/<USER>/.minikube/profiles/minikube/client.key

# Install k3s
sudo systemctl status k3s
sudo systemctl disable --now nm-cloud-setup.service
sudo systemctl is-enabled nm-cloud-setup.service # should say "disabled"
sudo systemctl restart k3s
sudo journalctl -u k3s -f  # check logs
mkdir -p ~/.kube
sudo cp /etc/rancher/k3s/k3s.yaml ~/.kube/config

Makefile
ansible
config
gitops
helm
terraform_helm
terraform_infra


 aws configure --profile kodekloud
 export AWS_PROFILE=kodekloud
 make tf_infra_init
 make tf_infra_apply
 ssh -i ~/.ssh/aws/key  ec2-user@*************
 make deploy_k3s
 export KUBECONFIG=/.config/ansible_kubeconfig-k3s.generated && kubectl get all -A
 export KUBECONFIG=./config/ansible_kubeconfig-k3s.generated && kubectl get all -A
 kubectl get namespace -A
 make tf_helm_init
 make tf_helm_apply
 kubectl get all -A
 make jenkins_pass

