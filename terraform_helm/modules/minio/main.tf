terraform {
  required_providers {
    helm = {
      source  = "hashicorp/helm"
    }
  }
}

resource "kubernetes_namespace" "minio" {
  metadata {
    name = "minio"
  }
}

resource "helm_release" "minio" {
  name       = "minio"
  repository = "oci://registry-1.docker.io/bitnamicharts"
  chart      = "minio"
  version    = "16.0.10"
  namespace = kubernetes_namespace.minio.metadata[0].name

  create_namespace = false
  values = [
    file("${path.module}/values.yaml")
  ]

  depends_on = [kubernetes_namespace.minio]
}