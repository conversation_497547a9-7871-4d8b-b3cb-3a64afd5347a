package com.yotelohago.messaging.application;

import com.yotelohago.auth.TokenIdentityProvider;
import com.yotelohago.common.dto.PageRequest;
import com.yotelohago.common.dto.PageResponse;
import com.yotelohago.messaging.api.ConversationDTO;
import com.yotelohago.messaging.api.MessageWithDetailsDTO;
import com.yotelohago.messaging.api.MessagingWebSocket;
import com.yotelohago.messaging.domain.Message;
import com.yotelohago.messaging.domain.MessageRepository;
import com.yotelohago.user.domain.User;
import com.yotelohago.user.domain.UserRepository;
import com.yotelohago.booking.domain.Booking;
import com.yotelohago.booking.domain.BookingRepository;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;
import jakarta.ws.rs.ForbiddenException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.stream.Collectors;

@ApplicationScoped
public class MessagingManagementService {

    private static final Logger logger = LoggerFactory.getLogger(MessagingManagementService.class);

    private final MessageRepository repository;
    private final UserRepository userRepository;
    private final BookingRepository bookingRepository;

    @Inject
    TokenIdentityProvider tokenIdentityProvider;

    public MessagingManagementService(MessageRepository repository, UserRepository userRepository, BookingRepository bookingRepository) {
        this.repository = repository;
        this.userRepository = userRepository;
        this.bookingRepository = bookingRepository;
    }

    public List<Message> getMessagesForBooking(Long bookingId) {
        // Get any message from this booking to check authorization once
        List<Message> messages = repository.findByBookingId(bookingId);

        if (messages.isEmpty()) {
            return messages; // No messages, return empty list
        }

        // Check authorization once using the first message
        Message firstMessage = messages.get(0);
        if (!tokenIdentityProvider.canAccessMessage(firstMessage.getSender().id, firstMessage.getReceiver().id)) {
            throw new ForbiddenException("Not authorized to access messages for this booking");
        }

        return messages; // User is authorized, return all messages
    }

    @Transactional
    public void send(Message message) {
        // Ensure user can send as this sender
        if (!tokenIdentityProvider.canAccessMessage(message.getSender().id, message.getReceiver().id)) {
            throw new ForbiddenException("Not authorized to send this message");
        }
        repository.persist(message);
    }

    /**
     * Send a message without authorization check (for WebSocket usage)
     * Authorization should be handled at the WebSocket connection level
     */
    @Transactional
    public void sendWithoutAuthCheck(Message message) {
        repository.persist(message);
        logger.info("Message sent from {} to {} for booking {} (WebSocket)",
                   message.getSender().id, message.getReceiver().id, message.getBooking().id);
    }

    @Transactional
    public void markAsRead(Long messageId) {
        // First get the message to check authorization
        Message message = repository.findById(messageId)
                .orElseThrow(() -> new IllegalArgumentException("Message not found"));

        if (!tokenIdentityProvider.canAccessMessage(message.getSender().id, message.getReceiver().id)) {
            throw new ForbiddenException("Not authorized to modify this message");
        }

        repository.markAsRead(messageId);
    }

    @Transactional
    public void delete(Long id) {
        // First get the message to check authorization
        Message message = repository.findById(id)
                .orElseThrow(() -> new IllegalArgumentException("Message not found"));

        if (!tokenIdentityProvider.canAccessMessage(message.getSender().id, message.getReceiver().id)) {
            throw new ForbiddenException("Not authorized to delete this message");
        }

        repository.deleteById(id);
    }

    /**
     * Get conversation summaries with pagination
     * Admin gets all conversations, regular users get only their conversations
     */
    public PageResponse<ConversationDTO.ConversationSummary> getConversationsForUserPaginated(PageRequest pageRequest) {
        if (tokenIdentityProvider.hasRole("admin")) {
            // Admin gets all conversations
            logger.info("🔍 Admin user getting all conversations with pagination: {}", pageRequest);
            return repository.findAllConversationsPaginated(pageRequest);
        } else {
            // Regular users get only their conversations
            UUID currentUserId = tokenIdentityProvider.getCurrentUserId();
            logger.info("🔍 Getting conversations for user: {} with pagination: {}", currentUserId, pageRequest);
            return repository.findConversationsByParticipantPaginated(currentUserId, pageRequest);
        }
    }

    /**
     * Get all messages for a specific conversation (booking) with pagination
     * Admin and conversation participants can access
     */
    public PageResponse<MessageWithDetailsDTO> getMessagesForConversationPaginated(Long bookingId, PageRequest pageRequest) {
        UUID currentUserId = tokenIdentityProvider.getCurrentUserId();
        boolean isAdmin = tokenIdentityProvider.hasRole("admin");

        logger.info("🔍 Getting messages for booking: {} by user: {} (admin: {})", bookingId, currentUserId, isAdmin);

        // For now, allow admin and any authenticated user
        // TODO: Add proper authorization to check if user is participant in the conversation
        return repository.findMessagesByBookingPaginated(bookingId, pageRequest);
    }

    /**
     * Mark all messages in a conversation as read for the current user
     */
    @Transactional
    public void markConversationAsRead(Long bookingId) {
        UUID currentUserId = tokenIdentityProvider.getCurrentUserId();

        List<Message> messages = repository.findByBookingId(bookingId);

        // If no messages exist, check if the booking exists and user has access to it
        if (messages.isEmpty()) {
            // Check if booking exists and user has access to it
            if (!canAccessBooking(bookingId, currentUserId)) {
                throw new IllegalArgumentException("No conversation found for booking " + bookingId);
            }
            // Booking exists and user has access, but no messages yet - this is valid for new conversations
            logger.info("📝 No messages to mark as read for booking {} (new conversation)", bookingId);
            return;
        }

        // Check authorization using first message
        Message firstMessage = messages.get(0);
        if (!tokenIdentityProvider.canAccessMessage(firstMessage.getSender().id, firstMessage.getReceiver().id)) {
            throw new ForbiddenException("Not authorized to access this conversation");
        }

        // Mark all unread messages for current user as read
        int markedCount = 0;
        for (Message message : messages) {
            if (message.getReceiver().id.equals(currentUserId) && !message.isRead) {
                repository.markAsRead(message.id);
                markedCount++;
            }
        }

        logger.info("📝 Marked {} messages as read for booking {} by user {}", markedCount, bookingId, currentUserId);
    }

    /**
     * Get total unread message count for current user
     */
    public int getUnreadMessageCount() {
        UUID currentUserId = tokenIdentityProvider.getCurrentUserId();
        return repository.countUnreadForUser(currentUserId);
    }

    /**
     * Get conversation info for a booking (including participant details)
     */
    public ConversationDTO.ConversationInfo getConversationInfo(Long bookingId) {
        UUID currentUserId = tokenIdentityProvider.getCurrentUserId();

        // Check if booking exists and user has access to it
        if (!canAccessBooking(bookingId, currentUserId)) {
            throw new IllegalArgumentException("No conversation found for booking " + bookingId);
        }

        Optional<Booking> bookingOpt = bookingRepository.findById(bookingId);
        Booking booking = bookingOpt.get(); // We know it exists from canAccessBooking check

        ConversationDTO.ConversationInfo info = new ConversationDTO.ConversationInfo();
        info.bookingId = bookingId;

        // Always populate both client and professional details
        info.clientId = booking.getClient().id;
        info.clientName = booking.getClient().firstName + " " + booking.getClient().lastName;
        info.clientEmail = booking.getClient().email;

        info.professionalId = booking.getProfessional().id;
        info.professionalName = booking.getProfessional().firstName + " " + booking.getProfessional().lastName;
        info.professionalEmail = booking.getProfessional().email;

        logger.info("📋 Retrieved conversation info for booking {} - client: {}, professional: {}",
                   bookingId, info.clientName, info.professionalName);

        return info;
    }

    /**
     * Check if the current user has access to a booking
     */
    private boolean canAccessBooking(Long bookingId, UUID currentUserId) {
        Optional<Booking> bookingOpt = bookingRepository.findById(bookingId);
        if (bookingOpt.isEmpty()) {
            return false;
        }

        Booking booking = bookingOpt.get();
        // User can access booking if they are the client or professional
        return booking.getClient().id.equals(currentUserId) ||
               booking.getProfessional().id.equals(currentUserId);
    }

}
