package com.yotelohago.messaging.application;

import com.yotelohago.auth.TokenIdentityProvider;
import com.yotelohago.booking.domain.Booking;
import com.yotelohago.booking.domain.BookingRepository;
import com.yotelohago.booking.domain.BookingStatus;
import com.yotelohago.user.domain.User;
import com.yotelohago.user.domain.UserRepository;
import com.yotelohago.user.application.UserSyncService;
import com.yotelohago.service.domain.Service;
import com.yotelohago.service.domain.ServiceRepository;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Service for initiating conversations between clients and professionals
 * Creates draft bookings to enable messaging when no existing booking exists
 */
@ApplicationScoped
public class ConversationInitiationService {

    private static final Logger logger = LoggerFactory.getLogger(ConversationInitiationService.class);

    @Inject
    TokenIdentityProvider tokenIdentityProvider;

    @Inject
    UserRepository userRepository;

    @Inject
    BookingRepository bookingRepository;

    @Inject
    ServiceRepository serviceRepository;

    @Inject
    UserSyncService userSyncService;

    /**
     * Find or create a conversation between the current user and a professional
     * This creates a draft booking if no existing conversation exists
     * 
     * @param professionalUserId The internal user ID of the professional
     * @return The booking ID that can be used for messaging
     */
    @Transactional
    public Long findOrCreateConversation(UUID professionalUserId) {
        // Ensure current user exists in database
        userSyncService.ensureCurrentUserExists();
        
        UUID currentUserId = tokenIdentityProvider.getCurrentUserId();
        if (currentUserId == null) {
            throw new IllegalStateException("User not authenticated");
        }

        logger.info("🔍 Finding or creating conversation between {} and {}", currentUserId, professionalUserId);

        // Check if users exist
        Optional<User> currentUser = userRepository.findEntityById(currentUserId);
        Optional<User> professionalUser = userRepository.findEntityById(professionalUserId);

        if (currentUser.isEmpty()) {
            throw new IllegalArgumentException("Current user not found: " + currentUserId);
        }
        if (professionalUser.isEmpty()) {
            throw new IllegalArgumentException("Professional user not found: " + professionalUserId);
        }

        // Prevent self-messaging
        if (currentUserId.equals(professionalUserId)) {
            throw new IllegalArgumentException("Cannot create conversation with yourself");
        }

        // Look for existing bookings between these users
        List<Booking> existingBookings = bookingRepository.findByClientAndProfessional(currentUserId, professionalUserId);
        
        if (!existingBookings.isEmpty()) {
            // Use the most recent booking
            Booking mostRecentBooking = existingBookings.stream()
                .max((b1, b2) -> b1.getCreatedAt().compareTo(b2.getCreatedAt()))
                .orElse(existingBookings.get(0));
            
            logger.info("✅ Found existing booking {} for conversation between {} and {}", 
                       mostRecentBooking.getId(), currentUserId, professionalUserId);
            return mostRecentBooking.getId();
        }

        // Create a new draft booking for messaging
        Booking draftBooking = createDraftBooking(currentUser.get(), professionalUser.get());
        
        logger.info("✅ Created draft booking {} for conversation between {} and {}", 
                   draftBooking.getId(), currentUserId, professionalUserId);
        return draftBooking.getId();
    }

    /**
     * Create a draft booking specifically for messaging purposes
     */
    private Booking createDraftBooking(User client, User professional) {
        // Find a service from the professional to use for the booking
        // We need a service because the Booking entity requires it
        List<Service> professionalServices = serviceRepository.findAllByProfessional(professional.id);

        if (professionalServices.isEmpty()) {
            throw new IllegalStateException("Professional " + professional.id + " has no services available for booking");
        }

        // Use the first available service (could be improved to use a "consultation" service if available)
        Service service = professionalServices.get(0);

        Booking booking = new Booking();

        // Set participants using User entities (internal IDs)
        booking.setClient(client);
        booking.setProfessional(professional);
        booking.setService(service);

        // Set as open status (for messaging/inquiry)
        booking.setStatus(BookingStatus.OPEN);

        // Set basic information
        booking.setTitle("Message Inquiry - " + service.getTitle());
        booking.setDescription("Initial conversation about: " + service.getDescription());
        booking.setPrice(BigDecimal.ZERO); // No price for messaging-only booking
        
        // Set timestamps
        Instant now = Instant.now();
        booking.setCreatedAt(now);
        booking.setUpdatedAt(now);
        
        // Persist the booking
        bookingRepository.persist(booking);
        bookingRepository.flush();
        
        return booking;
    }

    /**
     * Check if a conversation exists between two users
     */
    public boolean conversationExists(UUID userId1, UUID userId2) {
        List<Booking> bookings = bookingRepository.findByClientAndProfessional(userId1, userId2);
        if (bookings.isEmpty()) {
            // Check reverse direction (in case userId1 is professional and userId2 is client)
            bookings = bookingRepository.findByClientAndProfessional(userId2, userId1);
        }
        return !bookings.isEmpty();
    }

    /**
     * Get the booking ID for an existing conversation
     */
    public Optional<Long> getConversationBookingId(UUID userId1, UUID userId2) {
        List<Booking> bookings = bookingRepository.findByClientAndProfessional(userId1, userId2);
        if (bookings.isEmpty()) {
            // Check reverse direction
            bookings = bookingRepository.findByClientAndProfessional(userId2, userId1);
        }
        
        if (bookings.isEmpty()) {
            return Optional.empty();
        }
        
        // Return the most recent booking
        Booking mostRecentBooking = bookings.stream()
            .max((b1, b2) -> b1.getCreatedAt().compareTo(b2.getCreatedAt()))
            .orElse(bookings.get(0));
            
        return Optional.of(mostRecentBooking.getId());
    }
}
