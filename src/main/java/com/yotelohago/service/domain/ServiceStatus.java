package com.yotelohago.service.domain;

/**
 * Enum representing the status of a service
 * DRAFT - Service is being created/edited and not visible to public
 * PUBLISHED - Service is live and visible to public
 */
public enum ServiceStatus {
    DRAFT("draft"),
    PUBLISHED("published");

    private final String value;

    ServiceStatus(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    /**
     * Get ServiceStatus from string value
     * @param value String representation of status
     * @return ServiceStatus enum
     * @throws IllegalArgumentException if value is not valid
     */
    public static ServiceStatus fromValue(String value) {
        for (ServiceStatus status : ServiceStatus.values()) {
            if (status.value.equalsIgnoreCase(value)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Invalid service status: " + value);
    }

    /**
     * Check if status allows public visibility
     * @return true if service should be visible to public
     */
    public boolean isPubliclyVisible() {
        return this == PUBLISHED;
    }

    @Override
    public String toString() {
        return value;
    }
}
