import React from 'react';
import { View, Text, StyleSheet, ScrollView, Image, Pressable, Dimensions, Platform, TouchableOpacity, ActivityIndicator, Alert } from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import Colors from '../constants/Colors';
import { AuthActionButton } from '../../../components/auth';
import { useProfessional } from '../../../hooks/useProfessional';
import { useAuth } from '../../../contexts/AuthContext';
import { ENV } from '../../../config/environment';

const { width } = Dimensions.get('window');
const isWeb = Platform.OS === 'web';

// Default professional image
const defaultProfessionalImage = require('../../../assets/images/user.png');

export default function ProfessionalProfileScreen() {
  const params = useLocalSearchParams();
  const router = useRouter();
  const { state: authState } = useAuth();

  // Ensure id is a string and exists
  const id = typeof params.id === 'string' ? params.id : null;

  console.log('🏗️ ProfessionalProfileScreen rendered with id:', id);

  // Fetch professional data from API
  const { professional: professionalData, isLoading, error } = useProfessional(id);

  if (!id) {
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>Invalid professional ID</Text>
      </View>
    );
  }

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={Colors.primary} />
        <Text style={styles.loadingText}>Loading professional profile...</Text>
      </View>
    );
  }

  if (error || !professionalData) {
    return (
      <View style={styles.container}>
        <Pressable
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Ionicons name="arrow-back" size={24} color={Colors.text.primary} />
        </Pressable>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error || 'Professional not found'}</Text>
          <Pressable
            style={styles.retryButton}
            onPress={() => window.location.reload()}
          >
            <Text style={styles.retryButtonText}>Try Again</Text>
          </Pressable>
        </View>
      </View>
    );
  }

  const { professional, user, services: professionalServices } = professionalData;

  const handleBack = () => {
    router.back();
  };

  const handleMessageProfessional = async () => {
    console.log('🚀 handleMessageProfessional called!');

    try {
      console.log('🔍 Checking authentication state:', {
        isAuthenticated: authState.isAuthenticated,
        hasTokens: !!authState.tokens?.accessToken,
        professionalDataAvailable: !!professionalData,
        userInternalId: professionalData?.user?.id
      });

      // Debug: Log complete professional data structure
      console.log('🔍 Complete professional data structure:', {
        professionalData: professionalData,
        userObject: professionalData?.user,
        userKeys: professionalData?.user ? Object.keys(professionalData.user) : 'no user object',
        userId: professionalData?.user?.id,
        userInternalId: professionalData?.user?.internalId,
        userName: professionalData?.user ? `${professionalData.user.firstName} ${professionalData.user.lastName}` : 'no name'
      });

      // Check if user is authenticated
      if (!authState.isAuthenticated || !authState.tokens?.accessToken) {
        console.log('❌ Authentication check failed');
        Alert.alert('Authentication Required', 'Please log in to message professionals.');
        return;
      }

      // Check if professional data is available
      if (!professionalData?.user?.id) {
        console.log('❌ Professional data check failed:', professionalData);
        Alert.alert('Error', 'Professional information not available. Please try again.');
        return;
      }

      console.log('🔄 Initiating conversation with professional:', {
        professionalId: id,
        professionalInternalId: professionalData.user.id,
        professionalName: `${professionalData.user.firstName} ${professionalData.user.lastName}`
      });

      // Initiate conversation with the professional using their internal user ID
      const response = await fetch(`${ENV.API_BASE_URL}/messaging/conversations/initiate/${professionalData.user.id}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authState.tokens?.accessToken}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Conversation initiation failed:', response.status, errorText);
        throw new Error(`Failed to initiate conversation: ${response.status}`);
      }

      const data = await response.json();
      const bookingId = data.bookingId;

      console.log('✅ Conversation initiated successfully:', { bookingId });

      // Navigate to chat with the booking ID
      router.push(`/client/chat/${bookingId}`);
    } catch (error) {
      console.error('❌ Error initiating conversation:', error);
      Alert.alert('Error', 'Failed to start conversation. Please try again.');
    }
  };

  // Debug: Log function definition
  console.log('🔧 handleMessageProfessional function defined:', typeof handleMessageProfessional);

  const renderRatingBar = (label: string, value: number) => (
    <View style={styles.ratingBarContainer}>
      <Text style={styles.ratingLabel}>{label}</Text>
      <View style={styles.ratingBarBackground}>
        <View style={[styles.ratingBarFill, { width: `${value * 20}%` }]} />
      </View>
      <Text style={styles.ratingValue}>{value.toFixed(1)}</Text>
    </View>
  );

  const renderServices = () => {
    if (!professionalServices.length) return null;

    return (
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Services Offered</Text>
        <View style={styles.servicesContainer}>
          {professionalServices.map((service) => (
            <View key={service.id} style={styles.serviceItem}>
              <Text style={styles.serviceTitle}>{service.title}</Text>
              <Text style={styles.serviceDescription} numberOfLines={2}>
                {service.description}
              </Text>
              <Text style={styles.servicePrice}>${service.price}</Text>
            </View>
          ))}
        </View>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      {/* Back Button */}
      <Pressable 
        style={styles.backButton}
        onPress={handleBack}
      >
        <Ionicons name="arrow-back" size={24} color={Colors.text.primary} />
      </Pressable>

      <ScrollView 
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Header Section */}
        <View style={styles.header}>
          <Image
            source={{
              uri: professional.profileImageUrl || 'https://via.placeholder.com/120x120/cccccc/666666?text=No+Image'
            }}
            style={styles.avatar}
          />
          <Text style={styles.name}>{user.firstName} {user.lastName}</Text>
          <Text style={styles.services}>
            {professionalServices.map(s => s.categoryName).filter(Boolean).join(' • ') || 'Professional Services'}
          </Text>
          {professional.city && (
            <Text style={styles.location}>
              <Ionicons name="location-outline" size={16} color={Colors.text.secondary} />
              {' '}{professional.city}
            </Text>
          )}
        </View>

        {/* About Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>About Me</Text>
          <Text style={styles.aboutText}>
            {professional.bio || 'No bio available'}
          </Text>
        </View>

        {/* Services Section */}
        {renderServices()}

        {/* Q&A Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Q&A</Text>
          <View style={styles.qaContainer}>
            <View style={styles.qaItem}>
              <Text style={styles.question}>What services do you offer?</Text>
              <Text style={styles.answer}>
                I specialize in {professionalServices.map(s => s.categoryName).filter(Boolean).join(', ') || 'various professional services'}.
              </Text>
            </View>
            <View style={styles.qaItem}>
              <Text style={styles.question}>How long have you been in business?</Text>
              <Text style={styles.answer}>
                I have been providing these services for over 5 years.
              </Text>
            </View>
          </View>
        </View>

        {/* Ratings Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Ratings</Text>
          {professional.avgRating ? (
            <>
              <View style={styles.overallRating}>
                <Text style={styles.ratingScore}>{professional.avgRating.toFixed(1)}</Text>
                <View style={styles.ratingStars}>
                  {[1, 2, 3, 4, 5].map((star) => (
                    <Ionicons
                      key={star}
                      name={star <= Math.round(professional.avgRating!) ? "star" : "star-outline"}
                      size={20}
                      color={Colors.warning}
                    />
                  ))}
                </View>
                <Text style={styles.ratingCount}>
                  ({professional.ratingCount || 0} reviews)
                </Text>
              </View>
              {renderRatingBar('Overall Rating', professional.avgRating)}
            </>
          ) : (
            <Text style={styles.noRatingsText}>No ratings yet</Text>
          )}
        </View>

        {/* Cancellation Policy Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Cancellation Policy</Text>
          <Text style={styles.policyText}>
            Free cancellation up to 24 hours before the scheduled service. 
            Cancellations within 24 hours may be subject to a fee.
          </Text>
        </View>

        {/* Contact Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Contact Professional</Text>
          <AuthActionButton
            actionType="messaging"
            actionName="Message Professional"
            professionalId={id}
            professionalName={`${user.firstName} ${user.lastName}`}
            title="Message Professional"
            icon="chatbubble-outline"
            onPress={handleMessageProfessional}
            style={styles.contactButton}
            textStyle={styles.contactButtonText}
            variant="secondary"
            size="medium"
          />
        </View>

        {/* Bottom Spacing */}
        <View style={styles.bottomSpacing} />
      </ScrollView>

      {/* Fixed Availability Button */}
      <View style={styles.availabilityContainer}>
        <AuthActionButton
          actionType="booking"
          actionName="Check Availability"
          professionalId={id}
          professionalName={`${user.firstName} ${user.lastName}`}
          title="Check Availability"
          onPress={() => router.push(`/client/availability/${id}`)}
          style={styles.availabilityButton}
          textStyle={styles.availabilityButtonText}
          variant="primary"
          size="large"
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.primary,
  },
  backButton: {
    position: 'absolute',
    top: 60,
    left: 16,
    zIndex: 1,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.background.secondary,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.border,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 80, // Space for fixed button
  },
  header: {
    alignItems: 'center',
    paddingTop: 60,
    paddingBottom: 24,
    backgroundColor: Colors.background.primary,
  },
  avatar: {
    width: 120,
    height: 120,
    borderRadius: 60,
    marginBottom: 16,
  },
  name: {
    fontSize: 24,
    fontWeight: '700',
    color: Colors.text.primary,
    marginBottom: 8,
  },
  services: {
    fontSize: 16,
    color: Colors.text.secondary,
    marginBottom: 4,
  },
  location: {
    fontSize: 14,
    color: Colors.text.secondary,
    flexDirection: 'row',
    alignItems: 'center',
  },
  section: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text.primary,
    marginBottom: 16,
  },
  aboutText: {
    fontSize: 16,
    color: Colors.text.secondary,
    lineHeight: 24,
  },
  galleryContainer: {
    paddingRight: 16,
  },
  galleryImage: {
    width: 200,
    height: 200,
    borderRadius: 12,
    marginRight: 12,
  },
  qaContainer: {
    gap: 16,
  },
  qaItem: {
    gap: 8,
  },
  question: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text.primary,
  },
  answer: {
    fontSize: 16,
    color: Colors.text.secondary,
    lineHeight: 24,
  },
  ratingBarContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  ratingLabel: {
    width: 120,
    fontSize: 14,
    color: Colors.text.secondary,
  },
  ratingBarBackground: {
    flex: 1,
    height: 8,
    backgroundColor: Colors.background.secondary,
    borderRadius: 4,
    marginHorizontal: 12,
  },
  ratingBarFill: {
    height: '100%',
    backgroundColor: Colors.primary,
    borderRadius: 4,
  },
  ratingValue: {
    width: 40,
    fontSize: 14,
    fontWeight: '600',
    color: Colors.text.primary,
    textAlign: 'right',
  },
  policyText: {
    fontSize: 16,
    color: Colors.text.secondary,
    lineHeight: 24,
  },
  contactButton: {
    backgroundColor: Colors.background.secondary,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: 'center',
  },
  contactButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.primary,
  },
  bottomSpacing: {
    height: 16,
  },
  availabilityContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 16,
    backgroundColor: Colors.background.primary,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  availabilityButton: {
    backgroundColor: Colors.primary,
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  availabilityButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text.light,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.background.primary,
  },
  loadingText: {
    fontSize: 16,
    color: Colors.text.secondary,
    marginTop: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  errorText: {
    fontSize: 16,
    color: Colors.text.secondary,
    textAlign: 'center',
    marginBottom: 24,
  },
  retryButton: {
    backgroundColor: Colors.primary,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  retryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text.light,
  },
  servicesContainer: {
    gap: 12,
  },
  serviceItem: {
    backgroundColor: Colors.background.secondary,
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  serviceTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text.primary,
    marginBottom: 4,
  },
  serviceDescription: {
    fontSize: 14,
    color: Colors.text.secondary,
    lineHeight: 20,
    marginBottom: 8,
  },
  servicePrice: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.primary,
  },
  overallRating: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    gap: 12,
  },
  ratingScore: {
    fontSize: 24,
    fontWeight: '700',
    color: Colors.text.primary,
  },
  ratingStars: {
    flexDirection: 'row',
    gap: 2,
  },
  ratingCount: {
    fontSize: 14,
    color: Colors.text.secondary,
  },
  noRatingsText: {
    fontSize: 16,
    color: Colors.text.secondary,
    fontStyle: 'italic',
  },
}); 