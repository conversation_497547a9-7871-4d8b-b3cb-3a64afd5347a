package com.yotelohago.service.domain;

import com.yotelohago.common.dto.PageRequest;
import com.yotelohago.common.dto.PageResponse;
import com.yotelohago.service.api.ServiceWithProfessionalDTO;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface ServiceRepository {
    void persist(Service service);

    Optional<Service> findById(UUID id);

    /**
     * Find all published services with pagination (public endpoint - most used)
     * Returns only PUBLISHED services for public consumption
     * @param pageRequest Pagination parameters
     * @return Paginated response with published services only
     */
    PageResponse<Service> findAllPublishedPaginated(PageRequest pageRequest);

    /**
     * Find published services by category with pagination (public endpoint - most used for filtering)
     * Returns only PUBLISHED services filtered by category (electrician, plumber, etc.)
     * @param categoryId The category ID to filter by
     * @param pageRequest Pagination parameters
     * @return Paginated response with published services in the category
     */
    PageResponse<Service> findByCategoryPublishedPaginated(UUID categoryId, PageRequest pageRequest);

    /**
     * Find all services by professional (professional-only endpoint - not paginated)
     * Returns ALL services (DRAFT + PUBLISHED) for the professional
     * Used by professionals to manage their own services
     * @param professionalUserId The professional's internal user ID
     * @return List of all services belonging to the professional
     */
    List<Service> findAllByProfessional(UUID professionalUserId);



    /**
     * Search published services with filters and pagination (public endpoint - most used for search)
     * Returns only PUBLISHED services matching the search criteria
     * @param categoryId Optional category filter
     * @param minPrice Optional minimum price filter
     * @param maxPrice Optional maximum price filter
     * @param searchTerm Optional search term for title/description
     * @param pageRequest Pagination parameters
     * @return Paginated response with published services matching criteria
     */
    PageResponse<Service> searchPublishedPaginated(UUID categoryId, Double minPrice, Double maxPrice,
                                                 String searchTerm, PageRequest pageRequest);

    // =====================================================
    // NEW: Methods with Professional Data Joins (Option 2)
    // =====================================================

    /**
     * Find all published services with professional data using database joins
     * This eliminates N+1 queries by fetching service + professional data in a single query
     * @param pageRequest Pagination parameters
     * @return Paginated response with services including professional information
     */
    PageResponse<ServiceWithProfessionalDTO> findAllPublishedWithProfessionalPaginated(PageRequest pageRequest);

    /**
     * Find published services by category with professional data using database joins
     * @param categoryId The category ID to filter by
     * @param pageRequest Pagination parameters
     * @return Paginated response with services including professional information
     */
    PageResponse<ServiceWithProfessionalDTO> findByCategoryPublishedWithProfessionalPaginated(UUID categoryId, PageRequest pageRequest);

    /**
     * Search published services with professional data using database joins
     * @param categoryId Optional category filter
     * @param minPrice Optional minimum price filter
     * @param maxPrice Optional maximum price filter
     * @param searchTerm Optional search term for title/description
     * @param pageRequest Pagination parameters
     * @return Paginated response with services including professional information
     */
    PageResponse<ServiceWithProfessionalDTO> searchPublishedWithProfessionalPaginated(UUID categoryId, Double minPrice, Double maxPrice,
                                                                                     String searchTerm, PageRequest pageRequest);

    void deleteById(UUID id);

    Service update(Service service);

    void flush();
}