import React, { useState } from 'react';
import { Layout, Menu, Button, Avatar, Typography, Space, Divider, Badge, Dropdown, Card, Empty } from 'antd';
import {
  DashboardOutlined,
  UserOutlined,
  TeamOutlined,
  AppstoreOutlined,
  CalendarOutlined,
  StarOutlined,
  MessageOutlined,
  SettingOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  LogoutOutlined,
  BellOutlined,
  ToolOutlined,
  EnvironmentOutlined,
  CheckOutlined,
  CloseOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined,
  WarningOutlined
} from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { mockNotifications } from '../../data/mockData';
import { Notification } from '../../types';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';

dayjs.extend(relativeTime);

const { Sider } = Layout;
const { Text } = Typography;

interface SidebarProps {
  collapsed: boolean;
  onCollapse: (collapsed: boolean) => void;
}

const Sidebar: React.FC<SidebarProps> = ({ collapsed, onCollapse }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user, logout } = useAuth();
  const [notifications, setNotifications] = useState<Notification[]>(mockNotifications);

  const unreadCount = notifications.filter(n => !n.isRead).length;

  const menuItems = [
    {
      key: '/',
      icon: <DashboardOutlined />,
      label: 'Dashboard',
    },
    {
      key: '/users',
      icon: <UserOutlined />,
      label: 'Users',
    },
    {
      key: '/professionals',
      icon: <TeamOutlined />,
      label: 'Professionals',
    },
    {
      key: '/services',
      icon: <ToolOutlined />,
      label: 'Services',
    },
    {
      key: '/categories',
      icon: <AppstoreOutlined />,
      label: 'Categories',
    },
    {
      key: '/bookings',
      icon: <CalendarOutlined />,
      label: 'Bookings',
    },
    {
      key: '/reviews',
      icon: <StarOutlined />,
      label: 'Reviews',
    },
    {
      key: '/messages',
      icon: <MessageOutlined />,
      label: 'Messages',
    },
    {
      key: '/map',
      icon: <EnvironmentOutlined />,
      label: 'Map',
    },
    {
      key: '/settings',
      icon: <SettingOutlined />,
      label: 'Settings',
    },
  ];

  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key);
  };

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  const handleMarkAllAsRead = () => {
    setNotifications(notifications.map(n => ({ ...n, isRead: true })));
  };

  const handleMarkAsRead = (notificationId: string) => {
    setNotifications(notifications.map(n => 
      n.id === notificationId ? { ...n, isRead: true } : n
    ));
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'success': return <CheckOutlined className="text-green-500" />;
      case 'warning': return <WarningOutlined className="text-yellow-500" />;
      case 'error': return <CloseOutlined className="text-red-500" />;
      case 'info': return <InfoCircleOutlined className="text-blue-500" />;
      default: return <BellOutlined className="text-gray-500" />;
    }
  };

  const getNotificationColor = (type: string) => {
    switch (type) {
      case 'success': return 'border-l-green-500 bg-green-50';
      case 'warning': return 'border-l-yellow-500 bg-yellow-50';
      case 'error': return 'border-l-red-500 bg-red-50';
      case 'info': return 'border-l-blue-500 bg-blue-50';
      default: return 'border-l-gray-500 bg-gray-50';
    }
  };

  const notificationDropdown = (
    <Card 
      className="w-80 max-h-96 overflow-hidden shadow-lg border-0"
      style={{ borderRadius: '12px' }}
      bodyStyle={{ padding: 0 }}
    >
      <div className="p-4 border-b border-gray-100 bg-white">
        <div className="flex items-center justify-between">
          <Text strong className="text-lg">Notifications</Text>
          {unreadCount > 0 && (
            <Button 
              type="link" 
              size="small" 
              onClick={handleMarkAllAsRead}
              className="text-coral-500 hover:text-coral-600 p-0"
            >
              Mark all as read
            </Button>
          )}
        </div>
        {unreadCount > 0 && (
          <Text type="secondary" className="text-sm">
            {unreadCount} unread notification{unreadCount > 1 ? 's' : ''}
          </Text>
        )}
      </div>
      
      <div className="max-h-80 overflow-y-auto">
        {notifications.length === 0 ? (
          <div className="p-6">
            <Empty 
              description="No notifications"
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            />
          </div>
        ) : (
          <div className="space-y-0">
            {notifications.map((notification) => (
              <div
                key={notification.id}
                className={`p-4 border-l-4 cursor-pointer transition-colors hover:bg-gray-50 ${
                  getNotificationColor(notification.type)
                } ${!notification.isRead ? 'bg-opacity-100' : 'bg-opacity-30'}`}
                onClick={() => handleMarkAsRead(notification.id)}
              >
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 mt-1">
                    {getNotificationIcon(notification.type)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <Text strong className={`text-sm ${!notification.isRead ? 'text-gray-900' : 'text-gray-600'}`}>
                        {notification.title}
                      </Text>
                      {!notification.isRead && (
                        <div className="w-2 h-2 bg-coral-500 rounded-full flex-shrink-0 ml-2" />
                      )}
                    </div>
                    <Text className={`text-sm block mt-1 ${!notification.isRead ? 'text-gray-700' : 'text-gray-500'}`}>
                      {notification.description}
                    </Text>
                    {notification.userName && (
                      <Text className="text-xs text-coral-500 font-medium block mt-1">
                        {notification.userName}
                      </Text>
                    )}
                    <Text type="secondary" className="text-xs block mt-2">
                      {dayjs(notification.createdAt).fromNow()}
                    </Text>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
      
      <div className="p-3 border-t border-gray-100 bg-gray-50">
        <Button 
          type="link" 
          className="w-full text-center text-coral-500 hover:text-coral-600 p-0"
          onClick={() => console.log('View all notifications')}
        >
          View all notifications
        </Button>
      </div>
    </Card>
  );

  return (
    <Sider
      trigger={null}
      collapsible
      collapsed={collapsed}
      width={240}
      className="h-screen sticky top-0 shadow-lg"
      style={{
        background: '#fff',
        borderRight: '1px solid #f0f0f0',
      }}
    >
      <div className="h-full flex flex-col">
        {/* Logo and Toggle */}
        <div className="p-4 border-b border-gray-100">
          <div className="flex items-center justify-between">
            {!collapsed && (
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 rounded-2xl overflow-hidden shadow-lg" style={{
                  background: 'linear-gradient(135deg, #ff6b6b 0%, #ff5a5f 100%)',
                  boxShadow: '0 4px 12px rgba(255, 90, 95, 0.3)'
                }}>
                  <img 
                    src="/Logo copy.png" 
                    alt="ServiceAdmin Logo" 
                    className="w-full h-full object-cover"
                  />
                </div>
                <Text strong className="text-lg text-gray-800">
                  ServiceAdmin
                </Text>
              </div>
            )}
            {collapsed && (
              <div className="flex justify-center w-full">
                <div className="w-10 h-10 rounded-2xl overflow-hidden shadow-lg" style={{
                  background: 'linear-gradient(135deg, #ff6b6b 0%, #ff5a5f 100%)',
                  boxShadow: '0 4px 12px rgba(255, 90, 95, 0.3)'
                }}>
                  <img 
                    src="/Logo copy.png" 
                    alt="ServiceAdmin Logo" 
                    className="w-full h-full object-cover"
                  />
                </div>
              </div>
            )}
            <Button
              type="text"
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={() => onCollapse(!collapsed)}
              className="flex items-center justify-center"
            />
          </div>
        </div>

        {/* User Profile */}
        {!collapsed && user && (
          <div className="p-4 border-b border-gray-100">
            <div className="flex items-center space-x-3">
              <Avatar
                size={40}
                src={user.avatar}
                icon={<UserOutlined />}
                className="flex-shrink-0"
              />
              <div className="flex-1 min-w-0">
                <Text strong className="block truncate">
                  {user.name}
                </Text>
                <Text type="secondary" className="text-xs block truncate">
                  Platform Admin
                </Text>
              </div>
            </div>
          </div>
        )}

        {/* Navigation Menu */}
        <div className="flex-1 overflow-y-auto">
          <Menu
            mode="inline"
            selectedKeys={[location.pathname]}
            items={menuItems}
            onClick={handleMenuClick}
            className="border-none"
            style={{ 
              background: 'transparent',
            }}
            theme="light"
          />
        </div>

        {/* Bottom Actions */}
        <div className="border-t border-gray-100 p-4">
          {!collapsed && (
            <Space direction="vertical" className="w-full">
              <Dropdown
                overlay={notificationDropdown}
                trigger={['click']}
                placement="topRight"
                overlayStyle={{ zIndex: 1050 }}
              >
                <Button
                  type="text"
                  className="w-full text-left justify-start flex items-center"
                >
                  <Badge count={unreadCount} size="small" offset={[10, 0]}>
                    <BellOutlined />
                  </Badge>
                  <span className="ml-3">Notifications</span>
                </Button>
              </Dropdown>
              <Button
                type="text"
                icon={<LogoutOutlined />}
                onClick={handleLogout}
                className="w-full text-left justify-start text-red-500 hover:text-red-600 hover:bg-red-50"
              >
                Logout
              </Button>
            </Space>
          )}
          {collapsed && (
            <Space direction="vertical" className="w-full" size="small">
              <Dropdown
                overlay={notificationDropdown}
                trigger={['click']}
                placement="topRight"
                overlayStyle={{ zIndex: 1050 }}
              >
                <Button
                  type="text"
                  className="w-full flex items-center justify-center"
                >
                  <Badge count={unreadCount} size="small">
                    <BellOutlined />
                  </Badge>
                </Button>
              </Dropdown>
              <Button
                type="text"
                icon={<LogoutOutlined />}
                onClick={handleLogout}
                className="w-full text-red-500 hover:text-red-600 hover:bg-red-50"
              />
            </Space>
          )}
        </div>
      </div>

      <style jsx global>{`
        .ant-menu-item-selected {
          background-color: #ff5a5f !important;
          color: white !important;
        }
        
        .ant-menu-item-selected .anticon {
          color: white !important;
        }
        
        .ant-menu-item:hover {
          background-color: rgba(255, 90, 95, 0.1) !important;
          color: #ff5a5f !important;
        }
        
        .ant-menu-item:hover .anticon {
          color: #ff5a5f !important;
        }
        
        .ant-menu-item-active {
          background-color: rgba(255, 90, 95, 0.15) !important;
        }
      `}</style>
    </Sider>
  );
};

export default Sidebar;