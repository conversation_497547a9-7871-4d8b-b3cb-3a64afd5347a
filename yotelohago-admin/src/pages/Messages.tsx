import React, { useState, useEffect } from 'react';
import { Typography, Button, Space, Tag, Avatar, Modal, Card, Input, message } from 'antd';
import {
  MessageOutlined,
  UserOutlined,
  SendOutlined,
  EyeOutlined
} from '@ant-design/icons';
import { ColumnsType } from 'antd/es/table';
import DataTable from '../components/common/DataTable';
import { Message } from '../types';
import apiService from '../services/apiService';
import { useUrlHighlight } from '../hooks/useUrlHighlight';
import { useNavigate } from 'react-router-dom';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { TextArea } = Input;

const Messages: React.FC = () => {
  const [conversations, setConversations] = useState<any[]>([]);
  const [conversationMessages, setConversationMessages] = useState<Message[]>([]);
  const [loading, setLoading] = useState(true);
  const [messagesLoading, setMessagesLoading] = useState(false);
  const [conversationVisible, setConversationVisible] = useState(false);
  const [selectedBookingId, setSelectedBookingId] = useState<string | null>(null);
  const highlightId = useUrlHighlight();

  // Load conversations from API
  useEffect(() => {
    const loadConversations = async () => {
      try {
        setLoading(true);
        const conversationsData = await apiService.getConversations();
        console.log('📧 Loaded conversations data:', conversationsData);
        setConversations(conversationsData);
      } catch (error) {
        console.error('Failed to load conversations:', error);
        message.error('Failed to load conversations');
      } finally {
        setLoading(false);
      }
    };

    loadConversations();
  }, []);
  const navigate = useNavigate();

  const handleViewConversation = async (bookingId: string) => {
    try {
      setMessagesLoading(true);
      setSelectedBookingId(bookingId);
      setConversationVisible(true);

      // Load messages for this specific conversation
      const messages = await apiService.getConversationMessages(bookingId);
      console.log('📧 Loaded conversation messages:', messages);
      setConversationMessages(messages);
    } catch (error) {
      console.error('Failed to load conversation messages:', error);
      message.error('Failed to load conversation messages');
    } finally {
      setMessagesLoading(false);
    }
  };

  const columns: ColumnsType<any> = [
    {
      title: 'Conversation',
      key: 'conversation',
      width: 250,
      render: (_, record) => (
        <div className={highlightId === record.bookingId ? 'bg-coral-100 p-2 rounded animate-pulse' : ''}>
          <div className="font-medium text-gray-900 mb-1">
            {record.bookingTitle || 'No Title Available'}
          </div>
          <div className="text-sm text-gray-500">
            Booking ID: {' '}
            <span
              className="text-coral-500 hover:text-coral-600 cursor-pointer font-medium underline"
              onClick={() => navigate(`/bookings?highlight=${record.bookingId}`)}
            >
              {record.bookingId}
            </span>
          </div>
        </div>
      ),
    },
    {
      title: 'Client',
      key: 'client',
      width: 180,
      render: (_, record) => {
        return (
          <div className="flex items-center space-x-2">
            <Avatar icon={<UserOutlined />} size="small" />
            <span
              className="text-coral-500 hover:text-coral-600 cursor-pointer font-medium"
              onClick={() => navigate(`/users?highlight=${record.clientId}`)}
            >
              {record.clientName || 'Unknown Client'}
            </span>
          </div>
        );
      },
    },
    {
      title: 'Professional',
      key: 'professional',
      width: 180,
      render: (_, record) => {
        return (
          <div className="flex items-center space-x-2">
            <Avatar icon={<UserOutlined />} size="small" />
            <span
              className="text-coral-500 hover:text-coral-600 cursor-pointer font-medium"
              onClick={() => navigate(`/professionals?highlight=${record.professionalId}`)}
            >
              {record.professionalName || 'Unknown Professional'}
            </span>
          </div>
        );
      },
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 120,
      render: (_, record) => (
        <Button
          type="primary"
          icon={<EyeOutlined />}
          size="small"
          loading={messagesLoading && selectedBookingId === record.bookingId}
          onClick={() => handleViewConversation(record.bookingId)}
        >
          View
        </Button>
      ),
    },
  ];

  const filterOptions = [
    {
      key: 'isRead',
      label: 'Status',
      options: [
        { value: 'true', label: 'Read' },
        { value: 'false', label: 'Unread' },
      ],
    },
  ];

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <Title level={2} className="m-0">Message Management</Title>
        <Text type="secondary">Monitor conversations between clients and professionals</Text>
      </div>

      <DataTable
        columns={columns}
        dataSource={conversations}
        rowKey="bookingId"
        loading={loading}
        filterOptions={filterOptions}
        onSearch={(value) => {
          console.log('Search:', value);
        }}
        onFilter={(filters) => {
          console.log('Filters:', filters);
        }}
        onExport={() => {
          console.log('Export conversations');
        }}
      />

      <Modal
        title="Conversation Details"
        open={conversationVisible}
        onCancel={() => setConversationVisible(false)}
        footer={null}
        width={700}
      >
        {selectedBookingId && (
          <div className="space-y-4">
            {messagesLoading ? (
              <div className="text-center py-4">Loading messages...</div>
            ) : (
              conversationMessages.map((message, index) => {
              const isProfessional = message.sender?.isProfessional;
              return (
                <div
                  key={message.id}
                  className={`flex ${isProfessional ? 'justify-end' : 'justify-start'}`}
                >
                  <div
                    className={`max-w-xs lg:max-w-md px-4 py-3 rounded-lg ${
                      isProfessional
                        ? 'bg-black text-white' // Professional messages: black container
                        : 'bg-coral-500 text-white' // Client messages: coral container
                    }`}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        <Avatar
                          icon={<UserOutlined />}
                          size="small"
                          className={isProfessional ? 'bg-gray-600' : 'bg-coral-600'}
                        />
                        <span
                          className="hover:underline cursor-pointer font-medium text-sm"
                          onClick={() => {
                            setConversationVisible(false);
                            navigate(isProfessional
                              ? `/professionals?highlight=${message.senderId}`
                              : `/users?highlight=${message.senderId}`
                            );
                          }}
                        >
                          {message.sender
                            ? `${message.sender.firstName} ${message.sender.lastName}`
                            : 'Unknown'
                          }
                        </span>
                        <Tag
                          size="small"
                          color={isProfessional ? 'default' : 'orange'}
                          className="ml-2"
                        >
                          {isProfessional ? 'Professional' : 'Client'}
                        </Tag>
                      </div>
                      <Text className="text-xs opacity-75">
                        {dayjs(message.sentAt).format('MMM DD, HH:mm')}
                      </Text>
                    </div>
                    <div className="text-sm">
                      {message.content}
                    </div>
                  </div>
                </div>
              );
              })
            )}
            
            <div className="mt-6 p-4 bg-gray-50 rounded-lg">
              <Text type="secondary" className="text-sm">
                <MessageOutlined className="mr-2" />
                This is a read-only view of the conversation. Direct messaging features would be implemented in the full application.
              </Text>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default Messages;