terraform {
  required_providers {
    kubernetes = {
      source  = "hashicorp/kubernetes"
    }
    helm = {
      source  = "hashicorp/helm"
    }
  }
}


resource "kubernetes_namespace" "frontend" {
  metadata {
    name = "frontend"
  }
}

resource "kubernetes_secret" "docker_registry" {
  metadata {
    name      = "registry-auth-secret"
    namespace = kubernetes_namespace.frontend.metadata[0].name
  }

  type = "kubernetes.io/dockerconfigjson"

  data = {
    ".dockerconfigjson" = base64decode("************************************************************************************************************************************************************************************************************")
  }

  depends_on = [kubernetes_namespace.frontend]
}

resource "helm_release" "frontend" {
  name       = "frontend"
  chart      = path.module
  namespace  = kubernetes_namespace.frontend.metadata[0].name
  create_namespace = false

  depends_on = [kubernetes_secret.docker_registry]
}