### Database config ###
quarkus.datasource.jdbc.url=*******************************************
quarkus.hibernate-orm.database.generation=drop-and-create

### CORS Configuration for React Native and Admin Panel ###
quarkus.http.cors=true
quarkus.http.cors.origins=http://localhost:8082,http://localhost:8083,http://localhost:19006,exp://localhost:19000
quarkus.http.cors.headers=accept,authorization,content-type,x-requested-with
quarkus.http.cors.methods=GET,POST,PUT,DELETE,OPTIONS
quarkus.http.cors.exposed-headers=Content-Disposition
quarkus.http.cors.access-control-max-age=24H
quarkus.http.cors.access-control-allow-credentials=true

# OLD LOCALHOST KEYCLOAK CONFIG (COMMENTED OUT)
#quarkus.oidc.auth-server-url=http://localhost:8081/realms/yotelohago
#quarkus.oidc.client-id=yotelohago-app
#quarkus.oidc.credentials.secret=XGlACct2ZuDR5DHiyvERIvwDGRguLkv6

### OIDC ####
quarkus.oidc.auth-server-url=https://keycloak.yotelohago.co/realms/yotelohago
quarkus.oidc.client-id=yotelohago-app-dev
# We dont need client secret now since it is a public client.
#quarkus.oidc.credentials.secret=yotelohago-backend-secret-change-in-production
quarkus.oidc.application-type=service
# Remove TLS verification=none for production
# quarkus.oidc.tls.verification=none

quarkus.swagger-ui.always-include=true
# Correct security scheme for JWT tokens
quarkus.smallrye-openapi.security-scheme=jwt


### Keycloak Admin API Configuration ###
keycloak.admin.server-url=https://keycloak.yotelohago.co
keycloak.admin.client-id=yotelohago-backend
keycloak.admin.client-secret=yotelohago-backend-secret-change-in-production
keycloak.realm=yotelohago



# Optionally allow Swagger/OpenAPI access without login
quarkus.http.auth.permission.public.paths=/,/index.html,/q/swagger-ui,/q/openapi,/favicon.ico,/login
quarkus.http.auth.permission.public.policy=permit


# Enable Dev Services (default: true)
quarkus.devservices.enabled=true




