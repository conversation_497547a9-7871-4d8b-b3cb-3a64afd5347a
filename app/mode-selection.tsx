import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Dimensions, Platform } from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import Colors from '@/constants/Colors';

const { width } = Dimensions.get('window');
const isWeb = Platform.OS === 'web';

export default function ModeSelectionScreen() {
  const router = useRouter();

  const handleClientMode = () => {
    router.push('/client/(tabs)');
  };

  const handleProfessionalMode = () => {
    router.push('/professional/(tabs)');
  };

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>Welcome to YoTeLoHago</Text>
          <Text style={styles.subtitle}>Choose how you want to use the app</Text>
        </View>

        {/* Mode Selection Cards */}
        <View style={styles.modesContainer}>
          {/* Client Mode */}
          <TouchableOpacity 
            style={styles.modeCard}
            onPress={handleClientMode}
            activeOpacity={0.8}
          >
            <View style={styles.modeIcon}>
              <Ionicons name="search" size={48} color={Colors.primary} />
            </View>
            <Text style={styles.modeTitle}>I&apos;m looking for services</Text>
            <Text style={styles.modeDescription}>
              Find and book professional services in your area
            </Text>
            <View style={styles.modeFeatures}>
              <View style={styles.featureItem}>
                <Ionicons name="checkmark-circle" size={16} color={Colors.success} />
                <Text style={styles.featureText}>Browse services</Text>
              </View>
              <View style={styles.featureItem}>
                <Ionicons name="checkmark-circle" size={16} color={Colors.success} />
                <Text style={styles.featureText}>Book appointments</Text>
              </View>
              <View style={styles.featureItem}>
                <Ionicons name="checkmark-circle" size={16} color={Colors.success} />
                <Text style={styles.featureText}>Message providers</Text>
              </View>
            </View>
          </TouchableOpacity>

          {/* Professional Mode */}
          <TouchableOpacity 
            style={styles.modeCard}
            onPress={handleProfessionalMode}
            activeOpacity={0.8}
          >
            <View style={styles.modeIcon}>
              <Ionicons name="briefcase" size={48} color={Colors.primary} />
            </View>
            <Text style={styles.modeTitle}>I&apos;m a service provider</Text>
            <Text style={styles.modeDescription}>
              Manage your business and connect with clients
            </Text>
            <View style={styles.modeFeatures}>
              <View style={styles.featureItem}>
                <Ionicons name="checkmark-circle" size={16} color={Colors.success} />
                <Text style={styles.featureText}>Manage bookings</Text>
              </View>
              <View style={styles.featureItem}>
                <Ionicons name="checkmark-circle" size={16} color={Colors.success} />
                <Text style={styles.featureText}>Set availability</Text>
              </View>
              <View style={styles.featureItem}>
                <Ionicons name="checkmark-circle" size={16} color={Colors.success} />
                <Text style={styles.featureText}>Chat with clients</Text>
              </View>
            </View>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.primary,
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
    paddingTop: 80,
    paddingBottom: 40,
    justifyContent: 'center',
  },
  header: {
    alignItems: 'center',
    marginBottom: 48,
  },
  title: {
    fontSize: 28,
    fontWeight: '700',
    color: Colors.text.primary,
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: Colors.text.secondary,
    textAlign: 'center',
  },
  modesContainer: {
    gap: 24,
    maxWidth: isWeb ? 800 : '100%',
    alignSelf: 'center',
    width: '100%',
  },
  modeCard: {
    backgroundColor: Colors.background.secondary,
    borderRadius: 16,
    padding: 24,
    borderWidth: 1,
    borderColor: Colors.border,
    alignItems: 'center',
  },
  modeIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: Colors.background.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
    borderWidth: 2,
    borderColor: Colors.primary,
  },
  modeTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: Colors.text.primary,
    marginBottom: 8,
    textAlign: 'center',
  },
  modeDescription: {
    fontSize: 14,
    color: Colors.text.secondary,
    textAlign: 'center',
    marginBottom: 20,
    lineHeight: 20,
  },
  modeFeatures: {
    gap: 8,
    alignSelf: 'stretch',
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  featureText: {
    fontSize: 14,
    color: Colors.text.secondary,
  },
});
