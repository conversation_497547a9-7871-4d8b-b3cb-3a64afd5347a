terraform {
  required_providers {
    kubernetes = {
      source  = "hashicorp/kubernetes"
    }
    helm = {
      source  = "hashicorp/helm"
    }
  }
}

resource "kubernetes_namespace" "admin" {
  metadata {
    name = "admin"
    labels = {
      "app.kubernetes.io/name" = "yotelohago-admin"
      "app.kubernetes.io/component" = "admin-panel"
    }
  }
}

resource "kubernetes_secret" "docker_registry" {
  metadata {
    name      = "registry-auth-secret"
    namespace = kubernetes_namespace.admin.metadata[0].name
  }

  type = "kubernetes.io/dockerconfigjson"

  data = {
    ".dockerconfigjson" = base64decode("************************************************************************************************************************************************************************************************************")
  }

  depends_on = [kubernetes_namespace.admin]
}

resource "helm_release" "admin" {
  name       = "yotelohago-admin"
  chart      = path.module
  namespace  = kubernetes_namespace.admin.metadata[0].name
  create_namespace = false

  values = [
    file("${path.module}/values.yaml")
  ]

  depends_on = [
    kubernetes_namespace.admin,
    kubernetes_secret.docker_registry
  ]
}
