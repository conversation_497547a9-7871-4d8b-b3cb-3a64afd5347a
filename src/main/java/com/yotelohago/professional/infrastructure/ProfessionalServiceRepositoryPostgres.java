package com.yotelohago.professional.infrastructure;

import com.yotelohago.professional.domain.*;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@ApplicationScoped
public class ProfessionalServiceRepositoryPostgres implements ProfessionalServiceRepository {

    @PersistenceContext
    EntityManager entityManager;

    @Override
    public void assign(UUID professionalId, UUID serviceId) {
        entityManager.persist(new ProfessionalService(professionalId, serviceId));
    }

    @Override
    public void remove(UUID professionalId, UUID serviceId) {
        ProfessionalService.Id id = new ProfessionalService.Id(professionalId, serviceId);
        ProfessionalService ps = entityManager.find(ProfessionalService.class, id);
        if (ps != null) entityManager.remove(ps);
    }

    @Override
    public void deleteAllForProfessional(UUID professionalId) {
        entityManager.createQuery("DELETE FROM ProfessionalService WHERE id.professionalId = :pid")
                .setParameter("pid", professionalId)
                .executeUpdate();
    }

    @Override
    public List<UUID> findServiceIdsByProfessional(UUID professionalId) {
        return entityManager.createQuery("SELECT ps.id.serviceId FROM ProfessionalService ps WHERE ps.id.professionalId = :pid", UUID.class)
                .setParameter("pid", professionalId)
                .getResultList();
    }
}