// Export all types from here for easy importing
export * from './api';
export * from './auth';

// Re-export commonly used types for convenience
export type {
  UserWithDetailsDTO,
  ServiceDTO,
  BookingDTO,
  ProfessionalDTO,
  ServiceCategoryDTO,
  ReviewDTO,
  MediaDTO,
  PresignedUrlRequestDTO,
  PresignedUrlResponseDTO,
  ProfilePhotoUploadState,
  ImagePickerResult
} from './api';

export { MediaType } from './api';

export type {
  AuthUser,
  AuthTokens,
  AuthState,
  UserRole
} from './auth';

// Legacy types for mock data compatibility (will be removed later)
export interface User {
  id: string;
  name: string;
  email: string;
  avatar: string;
  bio?: string;
  role: 'user' | 'provider';
  services?: string[];
  favorites?: string[];
}

export interface Service {
  id: string;
  providerId: string;
  title: string;
  description: string;
  category: string;
  price: number;
  location: string;
  images: string[];
  status: 'upcoming' | 'completed' | 'cancelled';
  date: string;
  time: string;
  rating: number;
  reviews: number;
  instantBook?: boolean;
  featured?: boolean;
}

export interface Category {
  id: string;
  name: string;
  icon: string;
  description?: string;
}

export interface Message {
  id: string;
  senderId: string;
  receiverId: string;
  content: string;
  timestamp: string;
  read: boolean;
}

export interface Conversation {
  id: string;
  participants: string[];
  lastMessage: Message;
  unreadCount: number;
}

export interface Booking {
  id: string;
  serviceId: string;
  providerId: string;
  userId: string;
  date: string;
  time: string;
  status: 'open' | 'accepted' | 'completed' | 'cancelled';
  price: number;
  notes?: string;
  duration: number;
}

export interface Review {
  id: string;
  serviceId: string;
  userId: string;
  rating: number;
  comment: string;
  timestamp: string;
  images?: string[];
}