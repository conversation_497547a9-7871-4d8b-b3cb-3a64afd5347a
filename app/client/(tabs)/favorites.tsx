import React, { useMemo, useCallback, useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, Dimensions, Platform, StatusBar, RefreshControl, ActivityIndicator } from 'react-native';
import { useRouter } from 'expo-router';
import ServiceCard from '../components/ServiceCard';
import Colors from '../constants/Colors';
import { AuthGuard } from '../../../components/auth';
import { getFavorites, removeFromFavorites } from '../../../services/api/favoriteService';
import { ServiceDTO } from '../../../types/api';
import { useAuth } from '../../../contexts/AuthContext';

export default function FavoritesScreen() {
  const router = useRouter();
  const { width } = Dimensions.get('window');
  const isWeb = Platform.OS === 'web';
  const { state: authState } = useAuth();

  // State management
  const [favoriteServices, setFavoriteServices] = useState<ServiceDTO[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Responsive grid configuration (same as main index tab)
  const gridConfig = useMemo(() => {
    const horizontalPadding = 32; // 16px on each side
    const itemSpacing = 8; // 4px on each side of items

    if (width >= 1200) {
      // 3 columns for large screens
      const availableWidth = width - horizontalPadding;
      const cardWidth = (availableWidth - (itemSpacing * 2)) / 3;
      return { columns: 3, cardWidth };
    } else if (width >= 768) {
      // 2 columns for medium screens
      const availableWidth = width - horizontalPadding;
      const cardWidth = (availableWidth - itemSpacing) / 2;
      return { columns: 2, cardWidth };
    } else {
      // 1 column for small screens
      return { columns: 1, cardWidth: width - horizontalPadding };
    }
  }, [width]);

  // Load favorites from API
  const loadFavorites = useCallback(async () => {
    if (!authState.isAuthenticated) {
      setFavoriteServices([]);
      setLoading(false);
      return;
    }

    try {
      setError(null);
      const favorites = await getFavorites();
      setFavoriteServices(favorites);
    } catch (err) {
      console.error('Failed to load favorites:', err);
      setError(err instanceof Error ? err.message : 'Failed to load favorites');
      setFavoriteServices([]);
    } finally {
      setLoading(false);
    }
  }, [authState.isAuthenticated]);

  // Load favorites on mount and when auth state changes
  useEffect(() => {
    loadFavorites();
  }, [loadFavorites]);

  // Handle refresh
  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    await loadFavorites();
    setRefreshing(false);
  }, [loadFavorites]);

  // Group services into rows for grid layout (same as main index tab)
  const groupedServices = useMemo(() => {
    if (gridConfig.columns === 1) {
      return favoriteServices.map(service => [service]);
    }

    const rows = [];
    for (let i = 0; i < favoriteServices.length; i += gridConfig.columns) {
      rows.push(favoriteServices.slice(i, i + gridConfig.columns));
    }
    return rows;
  }, [favoriteServices, gridConfig.columns]);

  const handleServicePress = useCallback((service: any) => {
    if (service.professionalUserId) {
      router.push({
        pathname: '/client/professional/[id]',
        params: { id: service.professionalUserId }
      });
    }
  }, [router]);

  const handleFavoritePress = useCallback(async (service: ServiceDTO) => {
    try {
      console.log('Removing from favorites:', service.id);
      await removeFromFavorites(service.id);

      // Remove from local state for immediate UI update
      setFavoriteServices(prev => prev.filter(s => s.id !== service.id));
    } catch (err) {
      console.error('Failed to remove from favorites:', err);
      // Could show a toast notification here
    }
  }, []);

  // Render service row (same logic as main index tab)
  const renderServiceRow = useCallback((row: any[], rowIndex: number) => {
    if (gridConfig.columns === 1) {
      // Single column layout
      const service = row[0];
      return (
        <View key={service.id} style={styles.serviceItemContainer}>
          <ServiceCard
            service={service}
            onPress={handleServicePress}
            onFavoritePress={handleFavoritePress}
            isFavorite={true}
            cardWidth={gridConfig.cardWidth}
          />
        </View>
      );
    }

    // Multi-column grid layout
    return (
      <View key={`row-${rowIndex}`} style={styles.serviceRowContainer}>
        {row.map((service, serviceIndex) => (
          <View
            key={service.id}
            style={[
              styles.serviceColumnContainer,
              { width: gridConfig.cardWidth }
            ]}
          >
            <ServiceCard
              service={service}
              onPress={handleServicePress}
              onFavoritePress={handleFavoritePress}
              isFavorite={true}
              cardWidth={gridConfig.cardWidth}
            />
          </View>
        ))}
        {/* Fill empty spaces in the last row */}
        {row.length < gridConfig.columns &&
          Array.from({ length: gridConfig.columns - row.length }).map((_, emptyIndex) => (
            <View
              key={`empty-${emptyIndex}`}
              style={[
                styles.serviceColumnContainer,
                { width: gridConfig.cardWidth }
              ]}
            />
          ))
        }
      </View>
    );
  }, [gridConfig, handleServicePress, handleFavoritePress]);

  const renderServices = useCallback(() => {
    if (loading) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors.primary} />
          <Text style={styles.loadingText}>Loading favorites...</Text>
        </View>
      );
    }

    if (error) {
      return (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyTitle}>Error loading favorites</Text>
          <Text style={styles.emptyText}>{error}</Text>
        </View>
      );
    }

    if (favoriteServices.length === 0) {
      return (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyTitle}>No favorites yet</Text>
          <Text style={styles.emptyText}>
            Start exploring services and tap the heart icon to save your favorites here.
          </Text>
        </View>
      );
    }

    return groupedServices.map((row, index) => renderServiceRow(row, index));
  }, [loading, error, favoriteServices.length, groupedServices, renderServiceRow]);

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={Colors.background.primary} />
      <View style={styles.header}>
        <Text style={styles.title}>Favorites</Text>
      </View>

      <AuthGuard
        actionType="favorites"
        promptTitle="Save Your Favorites"
        promptMessage="Log in to save and manage your favorite services and professionals."
      >
        <ScrollView
          style={styles.content}
          contentContainerStyle={styles.contentContainer}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={[Colors.primary]}
              tintColor={Colors.primary}
            />
          }
        >
          {renderServices()}
        </ScrollView>
      </AuthGuard>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.primary,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 30,
    paddingBottom: 16,
    paddingHorizontal: 16,
    backgroundColor: Colors.background.primary,
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: Colors.text.primary,
    flex: 1,
    textAlign: 'center',
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    paddingBottom: 24,
    paddingTop: 10
  },
  // Service item container (single column) - same as main index tab
  serviceItemContainer: {
    paddingHorizontal: 16,
    paddingVertical: 4,
  },
  // Service row container (multi-column grid) - same as main index tab
  serviceRowContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 4,
    justifyContent: 'space-between',
  },
  // Service column container (individual items in grid) - same as main index tab
  serviceColumnContainer: {
    paddingHorizontal: 4,
  },
  // Empty state styles
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 64,
    paddingHorizontal: 32,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: Colors.text.primary,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptyText: {
    fontSize: 16,
    color: Colors.text.secondary,
    textAlign: 'center',
    lineHeight: 24,
  },
  // Loading state styles
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 64,
  },
  loadingText: {
    fontSize: 16,
    color: Colors.text.secondary,
    marginTop: 16,
    textAlign: 'center',
  },
});