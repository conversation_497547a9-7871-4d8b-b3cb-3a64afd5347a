import Keycloak from 'keycloak-js';

export interface KeycloakUser {
  id: string;
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  roles: string[];
  isAdmin: boolean;
}

class KeycloakService {
  private keycloak: Keycloak;
  private initialized = false;

  constructor() {
    this.keycloak = new Keycloak({
      url: 'https://keycloak.yotelohago.co',
      realm: 'yotelohago',
      clientId: 'yotelohago-admin'
    });
  }

  async init(): Promise<boolean> {
    if (this.initialized) {
      return this.keycloak.authenticated || false;
    }

    try {
      const authenticated = await this.keycloak.init({
        onLoad: 'check-sso',
        silentCheckSsoRedirectUri: window.location.origin + '/silent-check-sso.html',
        checkLoginIframe: false,
      });

      this.initialized = true;

      // Set up token refresh
      if (authenticated) {
        this.setupTokenRefresh();
      }

      return authenticated;
    } catch (error) {
      console.error('Keycloak initialization failed:', error);
      return false;
    }
  }

  private setupTokenRefresh() {
    // Refresh token when it's about to expire
    setInterval(() => {
      this.keycloak.updateToken(70).then((refreshed) => {
        if (refreshed) {
          console.log('Token refreshed');
        }
      }).catch(() => {
        console.log('Failed to refresh token');
        this.logout();
      });
    }, 60000); // Check every minute
  }

  async login(): Promise<void> {
    if (!this.initialized) {
      await this.init();
    }
    return this.keycloak.login();
  }

  logout(): void {
    this.keycloak.logout();
  }

  isAuthenticated(): boolean {
    return this.keycloak.authenticated || false;
  }

  getToken(): string | undefined {
    return this.keycloak.token;
  }

  getUser(): KeycloakUser | null {
    if (!this.isAuthenticated() || !this.keycloak.tokenParsed) {
      return null;
    }

    const token = this.keycloak.tokenParsed as any;
    const realmRoles = this.keycloak.realmAccess?.roles || [];
    const clientRoles = this.keycloak.resourceAccess?.['yotelohago-admin']?.roles || [];
    const allRoles = [...realmRoles, ...clientRoles];

    return {
      id: token.sub,
      username: token.preferred_username,
      email: token.email,
      firstName: token.given_name,
      lastName: token.family_name,
      roles: allRoles,
      isAdmin: allRoles.includes('admin')
    };
  }

  hasRole(role: string): boolean {
    const user = this.getUser();
    return user?.roles.includes(role) || false;
  }

  isAdmin(): boolean {
    return this.hasRole('admin');
  }

  getApiBaseUrl(): string {
    return process.env.NODE_ENV === 'development'
      ? 'http://localhost:8080/v1'
      : 'https://api.yotelohago.co/v1';
  }

  getAuthHeaders(): Record<string, string> {
    const token = this.getToken();
    return token ? { Authorization: `Bearer ${token}` } : {};
  }
}

// Export singleton instance
const keycloakService = new KeycloakService();
export default keycloakService;
