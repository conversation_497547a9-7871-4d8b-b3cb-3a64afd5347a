import React, { useState, useCallback, useEffect, useMemo } from 'react';
import { View, Text, StyleSheet, Dimensions, TouchableOpacity, Platform, StatusBar, RefreshControl, ActivityIndicator } from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { LegendList } from '@legendapp/list';
import SearchBar from '../components/SearchBar';
import CategoryList from '../components/CategoryList';
import ServiceCard from '../components/ServiceCard';
import { useServices, useCategories } from '../../../hooks';
// No longer need useProfessionalBatch - professional data is included in service response
import { ServiceDTO } from '../../../types/api';
import Colors from '../constants/Colors';
import { getFavoriteServiceIds, toggleFavorite } from '../../../services/api/favoriteService';
import { useAuth } from '../../../contexts/AuthContext';


export default function SearchScreen() {
  const router = useRouter();
  const { state: authState } = useAuth();
  const [favorites, setFavorites] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [selectedLocation, setSelectedLocation] = useState<string>('');
  const [selectedDate, setSelectedDate] = useState<string>('');

  const { width } = Dimensions.get('window');
  const isWeb = Platform.OS === 'web';

  // Responsive grid configuration
  const gridConfig = useMemo(() => {
    const horizontalPadding = 32; // 16px on each side
    const itemSpacing = 8; // 4px on each side of items

    if (width >= 1200) {
      // 3 columns for large screens
      const availableWidth = width - horizontalPadding;
      const cardWidth = (availableWidth - (itemSpacing * 2)) / 3;
      return { columns: 3, cardWidth };
    } else if (width >= 768) {
      // 2 columns for medium screens
      const availableWidth = width - horizontalPadding;
      const cardWidth = (availableWidth - itemSpacing) / 2;
      return { columns: 2, cardWidth };
    } else {
      // 1 column for small screens
      return { columns: 1, cardWidth: width - horizontalPadding };
    }
  }, [width]);

  // Use our custom hooks for API integration
  const {
    categories,
    selectedCategory,
    selectCategory,
    isLoading: categoriesLoading,
    error: categoriesError,
    refresh: refreshCategories
  } = useCategories();

  const {
    services,
    totalElements,
    isLoading: servicesLoading,
    isLoadingMore,
    error: servicesError,
    hasMore,
    isEmpty,
    isFirstLoad,
    refresh: refreshServices,
    loadMore,
    search,
    filterByCategory,
    clearFilters
  } = useServices({
    size: 20, // Load 20 services per page
    category: selectedCategory || undefined
  });

  // Professional data is now included in service response - no need for separate fetching

  // Load favorites from API
  const loadFavorites = useCallback(async () => {
    if (!authState.isAuthenticated) {
      setFavorites([]);
      return;
    }

    try {
      const favoriteIds = await getFavoriteServiceIds();
      setFavorites(favoriteIds);
    } catch (err) {
      console.error('Failed to load favorites:', err);
      setFavorites([]);
    }
  }, [authState.isAuthenticated]);

  // Load favorites on mount and when auth state changes
  useEffect(() => {
    loadFavorites();
  }, [loadFavorites]);

  // Handle category selection
  const handleSelectCategory = useCallback((categoryId: string) => {
    const newCategoryId = selectedCategory === categoryId ? null : categoryId;
    selectCategory(newCategoryId);
    filterByCategory(newCategoryId);
  }, [selectedCategory, selectCategory, filterByCategory]);

  // Handle search
  const handleSearch = useCallback((query: string) => {
    setSearchQuery(query);
    search(query);
  }, [search]);

  // Handle location change
  const handleLocationChange = useCallback((location: string) => {
    setSelectedLocation(location);
    // TODO: Integrate with backend location filtering
    console.log('Location changed:', location);
  }, []);

  // Handle date change
  const handleDateChange = useCallback((startDate: string, endDate?: string) => {
    setSelectedDate(startDate);
    // TODO: Integrate with backend date filtering
    console.log('Date changed:', startDate, endDate);
  }, []);

  // Handle service press
  const handleServicePress = useCallback((service: ServiceDTO) => {
    if (service.professionalUserId) {
      router.push({
        pathname: '/client/professional/[id]',
        params: { id: service.professionalUserId }
      });
    }
  }, [router]);

  // Handle favorite press - integrated with backend favorites API
  const handleFavoritePress = useCallback(async (service: ServiceDTO) => {
    if (!authState.isAuthenticated) {
      // Could show login modal here
      console.log('User not authenticated, cannot favorite');
      return;
    }

    try {
      // Optimistic update
      const wasFavorited = favorites.includes(service.id);
      setFavorites(prev => {
        if (wasFavorited) {
          return prev.filter(id => id !== service.id);
        } else {
          return [...prev, service.id];
        }
      });

      // Call API
      await toggleFavorite(service.id);
    } catch (err) {
      console.error('Failed to toggle favorite:', err);
      // Revert optimistic update on error
      setFavorites(prev => {
        if (prev.includes(service.id)) {
          return prev.filter(id => id !== service.id);
        } else {
          return [...prev, service.id];
        }
      });
    }
  }, [authState.isAuthenticated, favorites]);

  // Handle refresh
  const handleRefresh = useCallback(async () => {
    await Promise.all([
      refreshCategories(),
      refreshServices(),
      loadFavorites()
    ]);
  }, [refreshCategories, refreshServices, loadFavorites]);
  
  // Group services into rows for grid layout
  const groupedServices = useMemo(() => {
    if (gridConfig.columns === 1) {
      return services.map(service => [service]);
    }

    const rows = [];
    for (let i = 0; i < services.length; i += gridConfig.columns) {
      rows.push(services.slice(i, i + gridConfig.columns));
    }
    return rows;
  }, [services, gridConfig.columns]);

  // Render service row for @legendapp/list
  const renderServiceRow = useCallback(({ item: row, index }: { item: ServiceDTO[], index: number }) => {
    if (gridConfig.columns === 1) {
      // Single column layout
      const service = row[0];
      return (
        <View style={styles.serviceItemContainer}>
          <ServiceCard
            service={service}
            onPress={handleServicePress}
            onFavoritePress={handleFavoritePress}
            isFavorite={favorites.includes(service.id)}
            cardWidth={gridConfig.cardWidth}
          />
        </View>
      );
    }

    // Multi-column grid layout
    return (
      <View style={styles.serviceRowContainer}>
        {row.map((service, serviceIndex) => (
          <View
            key={service.id}
            style={[
              styles.serviceColumnContainer,
              { width: gridConfig.cardWidth }
            ]}
          >
            <ServiceCard
              service={service}
              onPress={handleServicePress}
              onFavoritePress={handleFavoritePress}
              isFavorite={favorites.includes(service.id)}
              cardWidth={gridConfig.cardWidth}
            />
          </View>
        ))}
        {/* Fill empty spaces in the last row */}
        {row.length < gridConfig.columns &&
          Array.from({ length: gridConfig.columns - row.length }).map((_, emptyIndex) => (
            <View
              key={`empty-${emptyIndex}`}
              style={[
                styles.serviceColumnContainer,
                { width: gridConfig.cardWidth }
              ]}
            />
          ))
        }
      </View>
    );
  }, [handleServicePress, handleFavoritePress, favorites, gridConfig]);

  // Render loading footer for infinite scroll
  const renderLoadingFooter = useCallback(() => {
    if (!isLoadingMore) return null;

    return (
      <View style={styles.loadingFooter}>
        <ActivityIndicator size="small" color={Colors.primary} />
        <Text style={styles.loadingText}>Loading more services...</Text>
      </View>
    );
  }, [isLoadingMore]);

  // Render empty state
  const renderEmptyState = useCallback(() => {
    if (isFirstLoad) return null;

    return (
      <View style={styles.emptyContainer}>
        <Ionicons
          name={searchQuery ? "search" : "briefcase-outline"}
          size={64}
          color={Colors.text.secondary}
        />
        <Text style={styles.emptyTitle}>
          {searchQuery ? 'No services found' : 'No services available'}
        </Text>
        <Text style={styles.emptyText}>
          {searchQuery
            ? `No services match "${searchQuery}". Try adjusting your search.`
            : selectedCategory
              ? 'No services found in this category.'
              : 'No services are currently available.'
          }
        </Text>
        {(searchQuery || selectedCategory) && (
          <TouchableOpacity
            style={styles.clearFiltersButton}
            onPress={() => {
              setSearchQuery('');
              selectCategory(null);
              clearFilters();
            }}
          >
            <Text style={styles.clearFiltersText}>Clear filters</Text>
          </TouchableOpacity>
        )}
      </View>
    );
  }, [isFirstLoad, searchQuery, selectedCategory, selectCategory, clearFilters]);

  // Render error state
  const renderErrorState = useCallback(() => {
    if (!servicesError) return null;

    return (
      <View style={styles.errorContainer}>
        <Ionicons name="alert-circle-outline" size={64} color={Colors.error} />
        <Text style={styles.errorTitle}>Something went wrong</Text>
        <Text style={styles.errorText}>{servicesError}</Text>
        <TouchableOpacity
          style={styles.retryButton}
          onPress={refreshServices}
        >
          <Text style={styles.retryButtonText}>Try again</Text>
        </TouchableOpacity>
      </View>
    );
  }, [servicesError, refreshServices]);
  
  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={Colors.background.primary} />

      {/* Premium Search Section with Background */}
      <View style={styles.searchSection}>
        {/* Title Section */}
        {isWeb && (
          <View style={styles.header}>
            <View style={styles.titleContainer}>
              <Text style={styles.title}>
                Find the perfect{' '}
                <Text style={styles.titleHighlight}>professional</Text>
              </Text>
            </View>
          </View>
        )}



        {/* Search Bar */}
        <SearchBar
          onSearch={handleSearch}
          placeholder="Type a service"
          onLocationChange={handleLocationChange}
          onDateChange={handleDateChange}
          selectedLocation={selectedLocation}
          selectedDate={selectedDate}
          onFiltersPress={() => {
            // TODO: Implement advanced filters modal
            console.log('Advanced filters pressed');
          }}
        />

        {/* Categories */}
        {categoriesLoading ? (
          <View style={styles.categoriesLoading}>
            <ActivityIndicator size="small" color={Colors.primary} />
          </View>
        ) : categoriesError ? (
          <View style={styles.categoriesError}>
            <Text style={styles.errorText}>Failed to load categories</Text>
          </View>
        ) : (
          <CategoryList
            categories={categories as any}
            onSelectCategory={handleSelectCategory}
            selectedCategory={selectedCategory || undefined}
          />
        )}
      </View>

      {/* Services List with @legendapp/list */}
      <View style={styles.listContainer}>
        {/* Section Header */}
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>
            {selectedCategory
              ? `${categories.find(c => c.id === selectedCategory)?.name || 'Category'} Services`
              : searchQuery
                ? `Search results for "${searchQuery}"`
                : 'All Services'
            }
          </Text>
          {totalElements > 0 && (
            <Text style={styles.resultsCount}>
              {totalElements} service{totalElements !== 1 ? 's' : ''}
            </Text>
          )}
        </View>

        {/* Error State */}
        {servicesError && renderErrorState()}

        {/* Empty State */}
        {isEmpty && !servicesError && renderEmptyState()}

        {/* Loading State for first load */}
        {isFirstLoad && (
          <View style={styles.firstLoadContainer}>
            <ActivityIndicator size="large" color={Colors.primary} />
            <Text style={styles.loadingText}>Loading services...</Text>
          </View>
        )}

        {/* Services List */}
        {!isEmpty && !servicesError && (
          <LegendList
            data={groupedServices}
            renderItem={renderServiceRow}
            keyExtractor={(item: ServiceDTO[], index: number) =>
              gridConfig.columns === 1 ? item[0].id : `row-${index}-${item.map(s => s.id).join('-')}`
            }
            onEndReached={loadMore}
            onEndReachedThreshold={0.5}
            refreshControl={
              <RefreshControl
                refreshing={servicesLoading && !isFirstLoad}
                onRefresh={handleRefresh}
                colors={[Colors.primary]}
                tintColor={Colors.primary}
              />
            }
            ListFooterComponent={renderLoadingFooter}
            contentContainerStyle={styles.listContent}
            showsVerticalScrollIndicator={false}
            estimatedItemSize={gridConfig.columns > 1 ? 180 : 120} // Adjust for grid layout
            recycleItems // Enable recycling for better performance
          />
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.primary,
  },
  searchSection: {
    backgroundColor: '#F5F5F5', // Light pink/gray background to match the photo
    paddingTop: 30,
    paddingBottom: 10,
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 8,
    zIndex: 1,
    width: '100%',
    flexShrink: 0,
  },
  header: {
    paddingHorizontal: 16,
    paddingBottom: 10,
    alignItems: 'center',
  },
  titleContainer: {
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: '700',
    color: Colors.text.primary,
    textAlign: 'center',
    lineHeight: 36,
  },
  titleHighlight: {
    color: Colors.primary, // Coral color for "professional"
  },
  notificationButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.background.secondary,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.border,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },

  // Categories loading/error states
  categoriesLoading: {
    paddingVertical: 20,
    alignItems: 'center',
  },
  categoriesError: {
    paddingVertical: 20,
    paddingHorizontal: 16,
    alignItems: 'center',
  },

  // List container and content
  listContainer: {
    flex: 1,
    backgroundColor: Colors.background.primary,
    marginTop: -12, // Slight overlap with search section for smooth transition
  },
  listContent: {
    paddingBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 24,
    paddingBottom: 16,
    backgroundColor: Colors.background.primary,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text.primary,
    flex: 1,
  },
  resultsCount: {
    fontSize: 14,
    color: Colors.text.secondary,
    fontWeight: '500',
  },

  // Service item container (single column)
  serviceItemContainer: {
    paddingHorizontal: 16,
    paddingVertical: 4,
  },
  // Service row container (multi-column grid)
  serviceRowContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 4,
    justifyContent: 'space-between',
  },
  // Service column container (individual items in grid)
  serviceColumnContainer: {
    paddingHorizontal: 4,
  },

  // Loading states
  firstLoadContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 64,
  },
  loadingFooter: {
    paddingVertical: 20,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 8,
  },
  loadingText: {
    fontSize: 14,
    color: Colors.text.secondary,
  },

  // Empty state
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 64,
    paddingHorizontal: 32,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: Colors.text.primary,
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptyText: {
    fontSize: 16,
    color: Colors.text.secondary,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 24,
  },
  clearFiltersButton: {
    backgroundColor: Colors.primary,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  clearFiltersText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },

  // Error state
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 64,
    paddingHorizontal: 32,
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: Colors.text.primary,
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  errorText: {
    fontSize: 16,
    color: Colors.text.secondary,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 24,
  },
  retryButton: {
    backgroundColor: Colors.primary,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },
});