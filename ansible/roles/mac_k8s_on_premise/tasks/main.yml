---
#
#- name: Debug environment
#  debug:
#    var: ansible_env.HOME

- name: Install lima
  import_tasks: lima.yml

#- name: Install and configure CoreDNS for internal DNS.
#  import_tasks: coredns.yml

- name: Bootstrap RHEL VM using lima
  vars:
    lima_image: "~/lima/rhel-9.5-aarch64-kvm.qcow2"
    lima_cpus: 9
    lima_memory: "15GiB"
    lima_disk: "120GiB"
    lima_mount_location: "~"
    lima_ssh_port: 52591
    lima_provision_script: |
      sudo dnf install -y vim curl git
  import_tasks: bootstrap_rhel_lima.yml

#- name: Setup Tailscale and connect to Tailnet
#  import_tasks: tailscale.yml
