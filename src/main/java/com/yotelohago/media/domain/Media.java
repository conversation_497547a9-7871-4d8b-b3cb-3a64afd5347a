package com.yotelohago.media.domain;

import com.yotelohago.user.domain.User;
import io.quarkus.hibernate.orm.panache.PanacheEntityBase;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;

import java.time.Instant;
import java.util.UUID;

/**
 * Media entity representing uploaded files and their metadata
 * Follows DDD patterns used in other domains
 */
@Entity
@Table(name = "media",
       indexes = {
           @Index(name = "idx_media_user_id", columnList = "user_id"),
           @Index(name = "idx_media_type", columnList = "media_type"),
           @Index(name = "idx_media_associated_entity_id", columnList = "associated_entity_id"),
           @Index(name = "idx_media_created_at", columnList = "created_at"),
           @Index(name = "idx_media_user_type", columnList = "user_id, media_type")
       })
public class Media extends PanacheEntityBase {

    @Id
    @GeneratedValue
    public UUID id;

    /**
     * Path/key of the file in MinIOk storage
     * Example: users/{userId}/professional/profile.jpg
     */
    @Column(name = "file_path", nullable = false, length = 500)
    @NotBlank
    public String filePath;

    /**
     * Original filename as uploaded by the user
     */
    @Column(name = "original_filename", nullable = false, length = 255)
    @NotBlank
    public String originalFilename;

    /**
     * File size in bytes
     */
    @Column(name = "file_size", nullable = false)
    @Positive
    public Long fileSize;

    /**
     * MIME content type (e.g., image/jpeg, image/png)
     */
    @Column(name = "content_type", nullable = false, length = 100)
    @NotBlank
    public String contentType;

    /**
     * Type of media (profile picture, service photo, etc.)
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "media_type", nullable = false, length = 50)
    @NotNull
    public MediaType mediaType;

    /**
     * User who owns this media
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    @NotNull
    public User user;

    /**
     * Associated entity ID (e.g., serviceId for service photos)
     * Can be null for profile pictures
     */
    @Column(name = "associated_entity_id")
    public UUID associatedEntityId;

    /**
     * When the media was uploaded
     */
    @Column(name = "created_at", nullable = false)
    @NotNull
    public Instant createdAt;

    /**
     * When the media metadata was last updated
     */
    @Column(name = "updated_at", nullable = false)
    @NotNull
    public Instant updatedAt;

    /**
     * Default constructor for JPA
     */
    public Media() {}

    /**
     * Constructor for creating new media entries
     */
    public Media(String filePath, String originalFilename, Long fileSize, String contentType, 
                 MediaType mediaType, User user, UUID associatedEntityId) {
        this.filePath = filePath;
        this.originalFilename = originalFilename;
        this.fileSize = fileSize;
        this.contentType = contentType;
        this.mediaType = mediaType;
        this.user = user;
        this.associatedEntityId = associatedEntityId;
        this.createdAt = Instant.now();
        this.updatedAt = Instant.now();
    }

    /**
     * Get the ID of this media entity
     */
    public UUID getId() {
        return id;
    }

    /**
     * Update the updated_at timestamp
     */
    @PreUpdate
    public void preUpdate() {
        this.updatedAt = Instant.now();
    }

    /**
     * Set created_at and updated_at before persisting
     */
    @PrePersist
    public void prePersist() {
        Instant now = Instant.now();
        if (this.createdAt == null) {
            this.createdAt = now;
        }
        this.updatedAt = now;
    }
}
