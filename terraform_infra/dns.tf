data "cloudflare_zones" "yotelohago" {
  filter {
    name   = "yotelohago.co"
    status = "active"
    paused = false
  }
}

resource "cloudflare_record" "root_domain" {
  depends_on = [null_resource.wait_for_ssh]

  zone_id = data.cloudflare_zones.yotelohago.zones[0].id
  name    = "yotelohago.co"
  content   = aws_instance.servers.public_ip
  type    = "A"
  proxied = true
}

resource "cloudflare_record" "wildcard" {
  depends_on = [null_resource.wait_for_ssh]

  zone_id = data.cloudflare_zones.yotelohago.zones[0].id
  name    = "*.yotelohago.co"
  content   = aws_instance.servers.public_ip
  type    = "A"
  proxied = true
}

# Enable MagicDNS globally (required for Split DNS to work)
resource "tailscale_dns_preferences" "prefs" {
  magic_dns = true
}

resource "tailscale_dns_split_nameservers" "private_split_nameservers" {
  domain = "jenkins.yotelohago.co"

  nameservers = [var.tailscale_dns_nameserver]
}

