locals {
  k8s_context_path = "${path.module}/../${var.k8s_kubeconfig_path}"

  image_tag         = var.image_tag
  image_repo_name   = var.repository_name
  aws_account_id    = data.aws_caller_identity.current.account_id
  image_repository  = "${local.aws_account_id}.dkr.ecr.${var.region}.amazonaws.com/${local.image_repo_name}"
  full_image        = "${local.image_repository}:${local.image_tag}"
}

# xport program ignore for yotelohago.co
data "aws_caller_identity" "current" {}

### Resources are in order of priority, that means some resources depend on others but visually keep them in order ###

module "cert-manager" {
  source          = "./modules/cert-manager"

  providers = {
    kubernetes = kubernetes
  }
}

module "registry" {
  depends_on = [module.cert-manager]
  source          = "./modules/registry"

  providers = {
    kubernetes = kubernetes
    helm = helm
  }
}

module "portainer" {
  depends_on = [module.cert-manager]
  source          = "./modules/portainer"

  providers = {
    kubernetes = kubernetes
    helm = helm
  }
}

module "hello-api" {
  depends_on = [module.cert-manager]
  source          = "./modules/hello-api"

  providers = {
    helm = helm
  }
}

module "jenkins" {
  depends_on = [module.cert-manager]
  source          = "./modules/jenkins"

  providers = {
    helm = helm
  }
}

module "cnpg" {
  depends_on = [module.cert-manager]
  source          = "./modules/cnpg"

  providers = {
    kubernetes = kubernetes
  }
}

module "pgadmin" {
  depends_on = [module.cert-manager, module.cnpg]
  source          = "./modules/pgadmin"

  providers = {
    kubernetes = kubernetes
    helm = helm
  }
}

### IMPORTANT: YOU MUST HAVE PORT FORWARDING ENABLED SO POSTGRES PROVIDER CAN ACCESS LOCALHOST 5432 PORT
### Command running:  kubectl port-forward svc/yotelohago-db-rw -n cnpg-system 5432:5432
module "keycloak" {
  depends_on = [module.cert-manager, module.cnpg]
  source          = "./modules/keycloak"

  providers = {
    kubernetes = kubernetes
    helm = helm
    postgresql = postgresql
  }
}

module "minio" {
  source          = "./modules/minio"
  providers = {
    helm = helm
  }
}

### important Requires registry.yotelohago.co/api image
module "api" {
  depends_on = [module.cert-manager, module.cnpg, module.registry]
  source          = "./modules/api"

  providers = {
    kubernetes = kubernetes
    helm = helm
  }
}

### important Requires registry.yotelohago.co/frontend/prod/react-native-web:latest image
module "frontend" {
  depends_on = [module.cert-manager, module.cnpg, module.registry, module.api]
  source          = "./modules/frontend"

  providers = {
    kubernetes = kubernetes
    helm = helm
  }
}

# Helm argocd deployment
# module "argocd" {
#   source              = "./modules/argocd"
#   kubeconfig_path     = local.k8s_context_path
#   helm_argocd_version = var.helm_argocd_version
# }



### important Requires registry.yotelohago.co/admin/main:latest image
module "admin" {
  depends_on = [module.cert-manager, module.cnpg, module.registry, module.api]
  source          = "./modules/admin"

  providers = {
    kubernetes = kubernetes
    helm = helm
  }
}
