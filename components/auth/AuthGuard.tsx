import React, { ReactNode, useEffect, useRef } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import AuthPrompt from './AuthPrompt';

interface AuthGuardProps {
  children: ReactNode;
  fallback?: ReactNode;
  requireAuth?: boolean;
  promptTitle?: string;
  promptMessage?: string;
  actionType?: 'booking' | 'messaging' | 'favorites' | 'general';
  onAuthSuccess?: () => void;
}

export default function AuthGuard({
  children,
  fallback,
  requireAuth = true,
  promptTitle,
  promptMessage,
  actionType = 'general',
  tabIcon,
  onAuthSuccess,
}: AuthGuardProps) {
  const { state: authState, refreshAuthState } = useAuth();
  const hasCheckedRef = useRef(false);

  // Only check auth state once when component mounts and user appears unauthenticated
  useEffect(() => {
    // Only run if:
    // 1. Auth is required
    // 2. User appears unauthenticated
    // 3. Not currently loading
    // 4. Haven't checked yet (prevent multiple calls)
    if (requireAuth && !authState.isAuthenticated && !authState.isLoading && !hasCheckedRef.current) {
      hasCheckedRef.current = true;
      console.log(`🔄 AuthGuard (${actionType}): Checking auth state on mount...`);

      // Small delay to avoid race conditions
      const timer = setTimeout(() => {
        refreshAuthState();
      }, 100);

      return () => clearTimeout(timer);
    }
  }, []); // Empty dependency array - only run on mount

  // If authentication is not required, always render children
  if (!requireAuth) {
    return <>{children}</>;
  }

  // If user is authenticated, render children
  if (authState.isAuthenticated) {
    return <>{children}</>;
  }

  // If user is not authenticated, show fallback or auth prompt
  if (fallback) {
    return <>{fallback}</>;
  }

  // Default auth prompt based on action type
  const getDefaultPromptContent = () => {
    switch (actionType) {
      case 'booking':
        return {
          title: promptTitle || 'Login Required',
          message: promptMessage || 'Please log in to book services and manage your appointments.',
        };
      case 'messaging':
        return {
          title: promptTitle || 'Login Required',
          message: promptMessage || 'Please log in to message professionals and view your conversations.',
        };
      case 'favorites':
        return {
          title: promptTitle || 'Login Required',
          message: promptMessage || 'Please log in to save your favorite services and professionals.',
        };
      default:
        return {
          title: promptTitle || 'Login Required',
          message: promptMessage || 'Please log in to access this feature.',
        };
    }
  };

  const { title, message } = getDefaultPromptContent();

  return (
    <AuthPrompt
      title={title}
      message={message}
      actionType={actionType}
      tabIcon={tabIcon}
      onAuthSuccess={onAuthSuccess}
    />
  );
}
