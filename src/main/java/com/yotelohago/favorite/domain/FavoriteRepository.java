package com.yotelohago.favorite.domain;

import com.yotelohago.service.api.ServiceWithProfessionalDTO;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface FavoriteRepository {
    
    /**
     * Add a service to user's favorites
     * @param favorite The favorite to persist
     */
    void persist(Favorite favorite);

    /**
     * Find a specific favorite by user and service
     * @param userId The user's internal ID
     * @param serviceId The service ID
     * @return Optional favorite if exists
     */
    Optional<Favorite> findByUserAndService(UUID userId, UUID serviceId);

    /**
     * Get all favorite service IDs for a user
     * @param userId The user's internal ID
     * @return List of service IDs that are favorited by the user
     */
    List<UUID> findServiceIdsByUser(UUID userId);

    /**
     * Get all favorite services with professional data for a user
     * This method joins with services and professional data for efficient retrieval
     * @param userId The user's internal ID
     * @return List of services with professional information that are favorited by the user
     */
    List<ServiceWithProfessionalDTO> findFavoriteServicesWithProfessionalByUser(UUID userId);

    /**
     * Check if a service is favorited by a user
     * @param userId The user's internal ID
     * @param serviceId The service ID
     * @return true if the service is favorited by the user
     */
    boolean isFavorited(UUID userId, UUID serviceId);

    /**
     * Remove a service from user's favorites
     * @param userId The user's internal ID
     * @param serviceId The service ID
     * @return true if the favorite was removed, false if it didn't exist
     */
    boolean removeByUserAndService(UUID userId, UUID serviceId);

    /**
     * Get count of favorites for a user
     * @param userId The user's internal ID
     * @return Number of favorites for the user
     */
    long countByUser(UUID userId);

    /**
     * Remove all favorites for a user (useful for user deletion)
     * @param userId The user's internal ID
     * @return Number of favorites removed
     */
    int removeAllByUser(UUID userId);

    /**
     * Remove all favorites for a service (useful for service deletion)
     * @param serviceId The service ID
     * @return Number of favorites removed
     */
    int removeAllByService(UUID serviceId);

    void flush();
}
