# Default values for portainer.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1

# If enterpriseEdition is enabled, then use the values below _instead_ of those in .image
enterpriseEdition: 
  enabled: false
  image:
    repository: portainer/portainer-ee
    tag: 2.27.6
    pullPolicy: Always

image:
  repository: portainer/portainer-ce
  tag: 2.27.6
  pullPolicy: Always

imagePullSecrets: []

nodeSelector: {}
tolerations: []

serviceAccount:
  annotations: {}
  name: portainer-sa-clusteradmin

# This flag provides the ability to enable or disable RBAC-related resources during the deployment of the Portainer application
# If you are using Portainer to manage the K8s cluster it is deployed to, this flag must be set to true 
localMgmt: true

service:
  # Set the httpNodePort and edgeNodePort only if the type is NodePort
  # For Ingress, set the type to be ClusterIP and set ingress.enabled to true
  # For Cloud Providers, set the type to be LoadBalancer
  type: NodePort
  httpPort: 9000
  httpsPort: 9443
  httpNodePort: 30777
  httpsNodePort: 30779
  edgePort: 8000
  edgeNodePort: 30776
  annotations: {}

tls:
  # If set, <PERSON><PERSON><PERSON> will be configured to use TLS only
  force: false
  # If set, will mount the existing secret into the pod
  existingSecret: ""

mtls:
  # If set, Portainer will be configured to use mTLS only
  enable: false
  # If set, will mount the existing secret into the pod
  existingSecret: ""

feature:
  flags: []


ingress:
  enabled: true
  ingressClassName: "traefik"
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-prod
    traefik.ingress.kubernetes.io/router.tls: "true"
  hosts:
    - host: portainer.yotelohago.co
      paths:
        - path: /
          pathType: Prefix
  tls:
    - hosts:
        - portainer.yotelohago.co
      secretName: portainer-yotelohago-co-tls

resources: {}

persistence:
  enabled: true
  size: "10Gi"
  annotations: {}
  storageClass:
  existingClaim:
