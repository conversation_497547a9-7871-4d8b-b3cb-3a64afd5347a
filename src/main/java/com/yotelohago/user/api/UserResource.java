package com.yotelohago.user.api;

import com.yotelohago.common.ApiVersion;
import com.yotelohago.common.dto.PageRequest;
import com.yotelohago.common.dto.PageResponse;
import com.yotelohago.user.application.UserManagementService;
import com.yotelohago.user.application.UserRegistrationService;
import com.yotelohago.user.domain.User;
import jakarta.annotation.security.RolesAllowed;
import jakarta.inject.Inject;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.ForbiddenException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.UUID;

@Path(ApiVersion.BASE + "/users")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@RolesAllowed({"user", "professional", "admin"})
public class UserResource {

    private static final Logger logger = LoggerFactory.getLogger(UserResource.class);

    @Inject
    UserManagementService userService;

    @Inject
    UserRegistrationService userRegistrationService;

    @Inject
    UserMapper userMapper;

    /**
     * Register a new user - Public endpoint
     */
    @POST
    @Path("/register")
    public Response registerUser(UserRegistrationDTO registrationDTO) {
        try {
            if (registrationDTO == null) {
                return Response.status(Response.Status.BAD_REQUEST)
                        .entity("{\"error\": \"Registration data is required\"}")
                        .build();
            }

            UserRegistrationResponseDTO response = userRegistrationService.registerUser(registrationDTO);

            return Response.status(Response.Status.CREATED)
                    .entity(response)
                    .build();

        } catch (IllegalArgumentException e) {
            return Response.status(Response.Status.BAD_REQUEST)
                    .entity("{\"error\": \"" + e.getMessage() + "\"}")
                    .build();
        } catch (RuntimeException e) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("{\"error\": \"" + e.getMessage() + "\"}")
                    .build();
        } catch (Exception e) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("{\"error\": \"Registration failed. Please try again later.\"}")
                    .build();
        }
    }

    /**
     * Get all users with pagination and optional filtering - Admin only
     * Includes professional and media details to avoid N+1 queries
     */
    @GET
    @RolesAllowed("admin")
    public Response getAllUsers(@BeanParam PageRequest pageRequest,
                               @QueryParam("isProfessional") Boolean isProfessional) {
        logger.info("GET /users called with pageRequest: page={}, size={}, isProfessional={}",
                   pageRequest.getPage(), pageRequest.getSize(), isProfessional);

        try {
            PageResponse<UserWithDetailsDTO> pageResponse;

            if (isProfessional != null) {
                logger.info("Filtering users by professional status: {}", isProfessional);
                pageResponse = userService.findByProfessionalStatusWithDetailsPaginated(isProfessional, pageRequest);
            } else {
                logger.info("Getting all users without filtering");
                pageResponse = userService.findAllWithDetailsPaginated(pageRequest);
            }

            logger.info("Successfully retrieved {} users out of {} total",
                       pageResponse.getContent().size(), pageResponse.getTotalElements());

            return Response.ok(pageResponse).build();
        } catch (ForbiddenException e) {
            logger.error("Access forbidden for user listing: {}", e.getMessage());
            return Response.status(Response.Status.FORBIDDEN)
                    .entity("{\"error\": \"" + e.getMessage() + "\"}")
                    .build();
        } catch (Exception e) {
            logger.error("Failed to retrieve users", e);
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("{\"error\": \"Failed to retrieve users: " + e.getMessage() + "\"}")
                    .build();
        }
    }



    /**
     * Get user by internal ID - User can access their own data, admin can access any
     * Returns complete user details including professional data and media URLs
     */
    @GET
    @Path("/internal/{id}")
    public Response getUserById(@PathParam("id") UUID id) {
        try {
            return userService.findById(id)
                    .map(dto -> Response.ok(dto).build())
                    .orElse(Response.status(Response.Status.NOT_FOUND)
                            .entity("{\"error\": \"User not found\"}")
                            .build());
        } catch (ForbiddenException e) {
            return Response.status(Response.Status.FORBIDDEN)
                    .entity("{\"error\": \"" + e.getMessage() + "\"}")
                    .build();
        } catch (IllegalArgumentException e) {
            return Response.status(Response.Status.BAD_REQUEST)
                    .entity("{\"error\": \"Invalid user ID format\"}")
                    .build();
        } catch (Exception e) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("{\"error\": \"Failed to retrieve user\"}")
                    .build();
        }
    }

    /**
     * Get current user profile with enhanced data - Convenience endpoint
     * Returns complete profile information including professional data and media URLs
     */
    @GET
    @Path("/me")
    public Response getCurrentUser() {
        try {
            UserWithDetailsDTO enhancedProfile = userService.getCurrentUserProfile();
            return Response.ok(enhancedProfile).build();
        } catch (Exception e) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("{\"error\": \"Failed to retrieve current user profile\"}")
                    .build();
        }
    }

    /**
     * Update user profile - User can update their own data, admin can update any
     */
    @PUT
    public Response updateUser(UserWithDetailsDTO userDto) {
        try {
            if (userDto == null) {
                return Response.status(Response.Status.BAD_REQUEST)
                        .entity("{\"error\": \"User data is required\"}")
                        .build();
            }

            User updatedUser = userService.updateUser(userMapper.toEntity(userDto));
            return Response.ok(userMapper.toDTO(updatedUser)).build();
        } catch (ForbiddenException e) {
            return Response.status(Response.Status.FORBIDDEN)
                    .entity("{\"error\": \"" + e.getMessage() + "\"}")
                    .build();
        } catch (IllegalArgumentException e) {
            return Response.status(Response.Status.NOT_FOUND)
                    .entity("{\"error\": \"" + e.getMessage() + "\"}")
                    .build();
        } catch (RuntimeException e) {
            if (e.getMessage() != null && e.getMessage().contains("not found")) {
                return Response.status(Response.Status.NOT_FOUND)
                        .entity("{\"error\": \"" + e.getMessage() + "\"}")
                        .build();
            }
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("{\"error\": \"Failed to update user\"}")
                    .build();
        } catch (Exception e) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("{\"error\": \"Failed to update user\"}")
                    .build();
        }
    }

    /**
     * Update current user profile - Convenience endpoint
     */
    @PUT
    @Path("/me")
    public Response updateCurrentUser(UserWithDetailsDTO userDto) {
        try {
            if (userDto == null) {
                return Response.status(Response.Status.BAD_REQUEST)
                        .entity("{\"error\": \"User data is required\"}")
                        .build();
            }

            // Get current user to ensure we're updating the right user
            User currentUser = userService.getCurrentUser()
                    .orElse(null);

            if (currentUser == null) {
                return Response.status(Response.Status.NOT_FOUND)
                        .entity("{\"error\": \"Current user not found\"}")
                        .build();
            }

            // Set the ID to current user's ID to prevent updating wrong user
            userDto.id = currentUser.getId();

            return updateUser(userDto);
        } catch (Exception e) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("{\"error\": \"Failed to update current user\"}")
                    .build();
        }
    }
}