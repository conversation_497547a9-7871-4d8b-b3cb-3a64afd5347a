package com.yotelohago.user.infrastructure;

import jakarta.enterprise.context.ApplicationScoped;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.util.UUID;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

@ApplicationScoped
public class KeycloakAdminService {

    private static final Logger logger = LoggerFactory.getLogger(KeycloakAdminService.class);

    @ConfigProperty(name = "keycloak.admin.server-url")
    String keycloakServerUrl;

    @ConfigProperty(name = "keycloak.admin.client-id")
    String adminClientId;

    @ConfigProperty(name = "keycloak.admin.client-secret")
    String adminClientSecret;

    @ConfigProperty(name = "keycloak.realm")
    String realm;

    private final HttpClient httpClient = HttpClient.newHttpClient();
    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * Create a new user in Keycloak
     *
     * @param email User's email address
     * @param password User's password
     * @param firstName User's first name
     * @param lastName User's last name
     * @return The UUID of the created user in Keycloak
     */
    public UUID createUser(String email, String password, String firstName, String lastName) {
        try {
            String accessToken = getAdminAccessToken();

            // Create user payload
            String userPayload = createUserPayload(email, password, firstName, lastName);

            // Create user in Keycloak
            UUID keycloakUserId = createUserInKeycloak(userPayload, accessToken);

            // Assign default "user" role
            addRoleToUser(keycloakUserId, "user", accessToken);

            logger.info("Successfully created user in Keycloak: email={}, keycloakId={}", email, keycloakUserId);
            return keycloakUserId;

        } catch (Exception e) {
            logger.error("Failed to create user in Keycloak: email={}", email, e);
            throw new RuntimeException("Failed to create user in Keycloak: " + e.getMessage(), e);
        }
    }

    public void addProfessionalRole(UUID keycloakUserId) {
        try {
            String accessToken = getAdminAccessToken();
            addRoleToUser(keycloakUserId, "professional", accessToken);
            logger.info("Successfully added professional role to user: {}", keycloakUserId);
        } catch (Exception e) {
            logger.error("Failed to add professional role to user: {}", keycloakUserId, e);
            throw new RuntimeException("Failed to update user roles in Keycloak", e);
        }
    }

    public void removeProfessionalRole(UUID keycloakUserId) {
        try {
            String accessToken = getAdminAccessToken();
            removeRoleFromUser(keycloakUserId, "professional", accessToken);
            logger.info("Successfully removed professional role from user: {}", keycloakUserId);
        } catch (Exception e) {
            logger.error("Failed to remove professional role from user: {}", keycloakUserId, e);
            throw new RuntimeException("Failed to update user roles in Keycloak", e);
        }
    }

    private String getAdminAccessToken() throws Exception {
        String tokenUrl = keycloakServerUrl + "/realms/" + realm + "/protocol/openid-connect/token";
        
        String requestBody = "grant_type=client_credentials" +
                "&client_id=" + adminClientId +
                "&client_secret=" + adminClientSecret;

        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(tokenUrl))
                .header("Content-Type", "application/x-www-form-urlencoded")
                .POST(HttpRequest.BodyPublishers.ofString(requestBody))
                .build();

        HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
        
        if (response.statusCode() != 200) {
            throw new RuntimeException("Failed to get admin access token: " + response.body());
        }

        // Parse JSON response to extract access_token
        // For simplicity, using basic string parsing. In production, use a JSON library
        String responseBody = response.body();
        String tokenPrefix = "\"access_token\":\"";
        int startIndex = responseBody.indexOf(tokenPrefix) + tokenPrefix.length();
        int endIndex = responseBody.indexOf("\"", startIndex);
        
        return responseBody.substring(startIndex, endIndex);
    }

    private void addRoleToUser(UUID keycloakUserId, String roleName, String accessToken) throws Exception {
        // First, get the role ID
        String roleId = getRoleId(roleName, accessToken);
        
        // Then assign the role to the user
        String assignRoleUrl = keycloakServerUrl + "/admin/realms/" + realm + 
                "/users/" + keycloakUserId + "/role-mappings/realm";

        String requestBody = "[{\"id\":\"" + roleId + "\",\"name\":\"" + roleName + "\"}]";

        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(assignRoleUrl))
                .header("Authorization", "Bearer " + accessToken)
                .header("Content-Type", "application/json")
                .POST(HttpRequest.BodyPublishers.ofString(requestBody))
                .build();

        HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
        
        if (response.statusCode() != 204) {
            throw new RuntimeException("Failed to assign role to user: " + response.body());
        }
    }

    private void removeRoleFromUser(UUID keycloakUserId, String roleName, String accessToken) throws Exception {
        // First, get the role ID
        String roleId = getRoleId(roleName, accessToken);
        
        // Then remove the role from the user
        String removeRoleUrl = keycloakServerUrl + "/admin/realms/" + realm + 
                "/users/" + keycloakUserId + "/role-mappings/realm";

        String requestBody = "[{\"id\":\"" + roleId + "\",\"name\":\"" + roleName + "\"}]";

        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(removeRoleUrl))
                .header("Authorization", "Bearer " + accessToken)
                .header("Content-Type", "application/json")
                .method("DELETE", HttpRequest.BodyPublishers.ofString(requestBody))
                .build();

        HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
        
        if (response.statusCode() != 204) {
            throw new RuntimeException("Failed to remove role from user: " + response.body());
        }
    }

    private String getRoleId(String roleName, String accessToken) throws Exception {
        String getRoleUrl = keycloakServerUrl + "/admin/realms/" + realm + "/roles/" + roleName;

        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(getRoleUrl))
                .header("Authorization", "Bearer " + accessToken)
                .GET()
                .build();

        HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
        
        if (response.statusCode() != 200) {
            throw new RuntimeException("Failed to get role ID: " + response.body());
        }

        // Parse JSON response to extract role ID
        String responseBody = response.body();
        String idPrefix = "\"id\":\"";
        int startIndex = responseBody.indexOf(idPrefix) + idPrefix.length();
        int endIndex = responseBody.indexOf("\"", startIndex);
        
        return responseBody.substring(startIndex, endIndex);
    }

    private String createUserPayload(String email, String password, String firstName, String lastName) {
        // Create JSON payload for user creation
        // TODO: Add email verification settings when implementing email verification
        return "{"
                + "\"username\":\"" + email + "\","
                + "\"email\":\"" + email + "\","
                + "\"firstName\":\"" + firstName + "\","
                + "\"lastName\":\"" + lastName + "\","
                + "\"enabled\":true,"
                + "\"emailVerified\":false,"
                + "\"credentials\":[{"
                + "\"type\":\"password\","
                + "\"value\":\"" + password + "\","
                + "\"temporary\":false"
                + "}]"
                + "}";
    }

    private UUID createUserInKeycloak(String userPayload, String accessToken) throws Exception {
        String createUserUrl = keycloakServerUrl + "/admin/realms/" + realm + "/users";

        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(createUserUrl))
                .header("Authorization", "Bearer " + accessToken)
                .header("Content-Type", "application/json")
                .POST(HttpRequest.BodyPublishers.ofString(userPayload))
                .build();

        HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());

        if (response.statusCode() == 201) {
            // User created successfully, extract user ID from Location header
            String locationHeader = response.headers().firstValue("Location").orElse("");
            if (locationHeader.isEmpty()) {
                throw new RuntimeException("User created but no Location header found");
            }

            // Extract user ID from location header (format: .../users/{userId})
            String userId = locationHeader.substring(locationHeader.lastIndexOf('/') + 1);
            return UUID.fromString(userId);

        } else if (response.statusCode() == 409) {
            // User already exists
            throw new RuntimeException("User with this email already exists");
        } else {
            throw new RuntimeException("Failed to create user: HTTP " + response.statusCode() + " - " + response.body());
        }
    }
}
