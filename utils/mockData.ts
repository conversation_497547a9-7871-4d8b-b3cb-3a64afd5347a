import { Service, Category, User, Conversation, Message, Booking, Review } from '../types';

// Mock Categories
export const categories: Category[] = [
  {
    id: '1',
    name: 'Cleaning',
    icon: 'home',
    description: 'Home cleaning services'
  },
  {
    id: '2',
    name: 'Plumbing',
    icon: 'tool',
    description: 'Plumbing repair and installation'
  },
  {
    id: '3', 
    name: 'Tutoring',
    icon: 'book-open',
    description: 'Academic tutoring and education'
  },
  {
    id: '4',
    name: 'Photography',
    icon: 'camera',
    description: 'Professional photography services'
  },
  {
    id: '5',
    name: 'Web Design',
    icon: 'code',
    description: 'Website design and development'
  },
  {
    id: '6',
    name: 'Gardening',
    icon: 'scissors',
    description: 'Garden maintenance and design'
  },
  {
    id: '7',
    name: 'Personal Training',
    icon: 'activity',
    description: 'Fitness and personal training'
  },
  {
    id: '8',
    name: 'Cooking',
    icon: 'chef-hat',
    description: 'Private chef and cooking lessons'
  },
  {
    id: '9',
    name: 'Beauty',
    icon: 'scissors',
    description: 'Hair styling, makeup, and beauty services'
  },
  {
    id: '10',
    name: '<PERSON><PERSON>',
    icon: 'tool',
    description: 'General repairs and maintenance'
  }
];

// Mock Users
export const users: User[] = [
  {
    id: '1',
    name: '<PERSON> Doe',
    email: '<EMAIL>',
    avatar: 'https://randomuser.me/api/portraits/men/1.jpg',
    bio: 'Professional web developer with 5 years of experience in creating modern and responsive websites.',
    role: 'provider',
    services: ['1', '2'],
    favorites: [],
  },
  {
    id: '2',
    name: 'Jane Smith',
    email: '<EMAIL>',
    avatar: 'https://randomuser.me/api/portraits/women/1.jpg',
    bio: 'Experienced house cleaner with a focus on detail and customer satisfaction.',
    role: 'provider',
    services: ['3'],
    favorites: [],
  },
  {
    id: '3',
    name: 'Mike Johnson',
    email: '<EMAIL>',
    avatar: 'https://randomuser.me/api/portraits/men/2.jpg',
    bio: 'Professional photographer specializing in portrait and event photography.',
    role: 'provider',
    services: ['4'],
    favorites: [],
  },
  {
    id: '4',
    name: 'Sarah Wilson',
    email: '<EMAIL>',
    avatar: 'https://randomuser.me/api/portraits/women/2.jpg',
    bio: 'Certified personal trainer with expertise in strength training and nutrition.',
    role: 'provider',
    services: ['5'],
    favorites: [],
  },
  {
    id: '5',
    name: 'Alex Brown',
    email: '<EMAIL>',
    avatar: 'https://randomuser.me/api/portraits/men/3.jpg',
    bio: 'Professional graphic designer with a passion for creating beautiful and functional designs.',
    role: 'provider',
    services: ['6'],
    favorites: [],
  },
  // Backend-compatible professionals for real booking data
  {
    id: '00000000-0000-0000-0000-000000000201',
    name: 'Maria Rodriguez',
    email: '<EMAIL>',
    avatar: 'https://randomuser.me/api/portraits/women/5.jpg',
    bio: 'Licensed plumber with 8 years of experience in emergency repairs and installations.',
    role: 'provider',
    services: ['00000000-0000-0000-0000-000000000501'],
    favorites: [],
  },
  {
    id: '00000000-0000-0000-0000-000000000202',
    name: 'Carlos Martinez',
    email: '<EMAIL>',
    avatar: 'https://randomuser.me/api/portraits/men/5.jpg',
    bio: 'Certified electrician specializing in home office setups and electrical installations.',
    role: 'provider',
    services: ['00000000-0000-0000-0000-000000000503'],
    favorites: [],
  },
];

// Mock Services
export const services: Service[] = [
  {
    id: '1',
    providerId: '1',
    title: 'House Cleaning',
    description: 'Professional house cleaning service. We clean everything from top to bottom, including dusting, vacuuming, mopping, and sanitizing bathrooms and kitchens.',
    category: 'Cleaning',
    price: 80,
    location: 'San Francisco, CA',
    images: [
      'https://images.unsplash.com/photo-1581578731548-c64695cc6952?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80',
      'https://images.unsplash.com/photo-1584820927498-cfe5211fd8bf?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80',
    ],
    status: 'upcoming',
    date: '2025-06-15',
    time: '10:00 AM',
    rating: 4.8,
    reviews: 128,
    instantBook: true,
    featured: true,
  },
  {
    id: '2',
    providerId: '2',
    title: 'Garden Maintenance',
    description: 'Complete garden maintenance service including lawn mowing, hedge trimming, weeding, and plant care.',
    category: 'Gardening',
    price: 120,
    location: 'Oakland, CA',
    images: [
      'https://images.unsplash.com/photo-**********-efa843a96f01?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80',
      'https://images.unsplash.com/photo-1585320806297-9794b3e4eeae?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80',
    ],
    status: 'completed',
    date: '2025-06-01',
    time: '2:00 PM',
    rating: 4.9,
    reviews: 95,
    instantBook: true,
  },
  {
    id: '3',
    providerId: '3',
    title: 'Plumbing Repair',
    description: 'Expert plumbing services for leaks, clogs, and installations. Available 24/7 for emergency repairs.',
    category: 'Plumbing',
    price: 150,
    location: 'San Jose, CA',
    images: [
      'https://images.unsplash.com/photo-1607472586893-edb57bdc0e39?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80',
      'https://images.unsplash.com/photo-1581093458791-9f3c3900df7b?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80',
    ],
    status: 'cancelled',
    date: '2025-06-10',
    time: '11:30 AM',
    rating: 4.7,
    reviews: 156,
    instantBook: false,
  },
  // Backend-compatible services for real booking data
  {
    id: '00000000-0000-0000-0000-000000000501',
    providerId: '00000000-0000-0000-0000-000000000201',
    title: 'Emergency Plumbing Repair',
    description: 'Kitchen sink leak that started this morning. Water is dripping constantly.',
    category: 'Plumbing',
    price: 125,
    location: 'San Francisco, CA',
    images: [
      'https://images.unsplash.com/photo-1607472586893-edb57bdc0e39?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80',
    ],
    status: 'completed',
    date: '2024-02-01',
    time: '2:30 PM',
    rating: 4.8,
    reviews: 45,
    instantBook: true,
  },
  {
    id: '00000000-0000-0000-0000-000000000503',
    providerId: '00000000-0000-0000-0000-000000000202',
    title: 'Home Office Electrical Work',
    description: 'Need additional outlets installed in home office for computer setup and equipment.',
    category: 'Electrical',
    price: 180,
    location: 'San Francisco, CA',
    images: [
      'https://images.unsplash.com/photo-1621905251189-08b45d6a269e?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80',
    ],
    status: 'cancelled',
    date: '2024-02-15',
    time: '10:00 AM',
    rating: 4.6,
    reviews: 32,
    instantBook: false,
  },
];

// Mock Conversations and Messages
export const messages: Message[] = [
  {
    id: '1',
    senderId: '5', // Sarah (client)
    receiverId: '1', // Jane (provider)
    content: 'Hi Jane, I\'m interested in booking a family portrait session next month. Do you have availability on weekends?',
    timestamp: '2023-05-10T14:22:00Z',
    read: true
  },
  {
    id: '2',
    senderId: '1', // Jane (provider)
    receiverId: '5', // Sarah (client)
    content: 'Hello Sarah! Yes, I have some weekend slots available. Would you prefer morning or afternoon lighting?',
    timestamp: '2023-05-10T15:45:00Z',
    read: true
  },
  {
    id: '3',
    senderId: '5', // Sarah (client)
    receiverId: '1', // Jane (provider)
    content: 'Morning would be best for us. Do you have any availability on the 15th of next month?',
    timestamp: '2023-05-11T09:10:00Z',
    read: false
  },
  {
    id: '4',
    senderId: '5', // Sarah (client)
    receiverId: '2', // Michael (provider)
    content: 'Hi Michael, I have a leaking faucet in my kitchen. When would you be able to take a look at it?',
    timestamp: '2023-05-12T10:30:00Z',
    read: true
  },
  {
    id: '5',
    senderId: '2', // Michael (provider)
    receiverId: '5', // Sarah (client)
    content: 'I can stop by tomorrow between 2-4pm if that works for you. Would you be able to send a photo of the faucet?',
    timestamp: '2023-05-12T11:15:00Z',
    read: false
  }
];

export const conversations: Conversation[] = [
  {
    id: '1',
    participants: ['5', '1'], // Sarah and Jane
    lastMessage: messages[2],
    unreadCount: 1
  },
  {
    id: '2',
    participants: ['5', '2'], // Sarah and Michael
    lastMessage: messages[4],
    unreadCount: 1
  }
];

// Mock Bookings - 6 bookings for each status (Open, Accepted, Completed, Cancelled)
export const bookings: Booking[] = [
  // Open bookings (6)
  {
    id: '1',
    serviceId: '1',
    providerId: '1',
    userId: '1',
    date: '2025-07-15',
    time: '10:00 AM',
    status: 'open',
    price: 80,
    notes: 'Please bring cleaning supplies',
    duration: 120,
  },
  {
    id: '2',
    serviceId: '2',
    providerId: '2',
    userId: '1',
    date: '2025-07-20',
    time: '2:00 PM',
    status: 'open',
    price: 120,
    duration: 180,
  },
  {
    id: '3',
    serviceId: '3',
    providerId: '3',
    userId: '1',
    date: '2025-07-25',
    time: '11:30 AM',
    status: 'open',
    price: 150,
    notes: 'Kitchen faucet repair needed',
    duration: 90,
  },
  {
    id: '4',
    serviceId: '1',
    providerId: '1',
    userId: '1',
    date: '2025-08-01',
    time: '9:00 AM',
    status: 'open',
    price: 80,
    duration: 120,
  },
  {
    id: '5',
    serviceId: '2',
    providerId: '2',
    userId: '1',
    date: '2025-08-05',
    time: '3:00 PM',
    status: 'open',
    price: 120,
    duration: 180,
  },
  {
    id: '6',
    serviceId: '3',
    providerId: '3',
    userId: '1',
    date: '2025-08-10',
    time: '1:00 PM',
    status: 'open',
    price: 150,
    duration: 90,
  },
  // Accepted bookings (6)
  {
    id: '7',
    serviceId: '1',
    providerId: '1',
    userId: '1',
    date: '2025-06-30',
    time: '10:00 AM',
    status: 'accepted',
    price: 80,
    notes: 'Confirmed for deep cleaning',
    duration: 120,
  },
  {
    id: '8',
    serviceId: '2',
    providerId: '2',
    userId: '1',
    date: '2025-07-02',
    time: '2:00 PM',
    status: 'accepted',
    price: 120,
    duration: 180,
  },
  {
    id: '9',
    serviceId: '3',
    providerId: '3',
    userId: '1',
    date: '2025-07-05',
    time: '11:30 AM',
    status: 'accepted',
    price: 150,
    notes: 'Bathroom plumbing repair',
    duration: 90,
  },
  {
    id: '10',
    serviceId: '1',
    providerId: '1',
    userId: '1',
    date: '2025-07-08',
    time: '9:00 AM',
    status: 'accepted',
    price: 80,
    duration: 120,
  },
  {
    id: '11',
    serviceId: '2',
    providerId: '2',
    userId: '1',
    date: '2025-07-12',
    time: '3:00 PM',
    status: 'accepted',
    price: 120,
    duration: 180,
  },
  {
    id: '12',
    serviceId: '3',
    providerId: '3',
    userId: '1',
    date: '2025-07-18',
    time: '1:00 PM',
    status: 'accepted',
    price: 150,
    duration: 90,
  },
  // Completed bookings (6)
  {
    id: '13',
    serviceId: '1',
    providerId: '1',
    userId: '1',
    date: '2025-06-01',
    time: '10:00 AM',
    status: 'completed',
    price: 80,
    notes: 'House cleaning completed successfully',
    duration: 120,
  },
  {
    id: '14',
    serviceId: '2',
    providerId: '2',
    userId: '1',
    date: '2025-06-05',
    time: '2:00 PM',
    status: 'completed',
    price: 120,
    notes: 'Garden maintenance finished',
    duration: 180,
  },
  {
    id: '15',
    serviceId: '3',
    providerId: '3',
    userId: '1',
    date: '2025-06-10',
    time: '11:30 AM',
    status: 'completed',
    price: 150,
    notes: 'Fixed leaky faucet in kitchen',
    duration: 90,
  },
  {
    id: '16',
    serviceId: '1',
    providerId: '1',
    userId: '1',
    date: '2025-06-15',
    time: '9:00 AM',
    status: 'completed',
    price: 80,
    notes: 'Deep cleaning service completed',
    duration: 120,
  },
  {
    id: '17',
    serviceId: '2',
    providerId: '2',
    userId: '1',
    date: '2025-06-18',
    time: '3:00 PM',
    status: 'completed',
    price: 120,
    duration: 180,
  },
  {
    id: '18',
    serviceId: '3',
    providerId: '3',
    userId: '1',
    date: '2025-06-22',
    time: '1:00 PM',
    status: 'completed',
    price: 150,
    notes: 'Bathroom plumbing repair completed',
    duration: 90,
  },
  // Cancelled bookings (6)
  {
    id: '19',
    serviceId: '1',
    providerId: '1',
    userId: '1',
    date: '2025-05-15',
    time: '10:00 AM',
    status: 'cancelled',
    price: 80,
    notes: 'Cancelled due to scheduling conflict',
    duration: 120,
  },
  {
    id: '20',
    serviceId: '2',
    providerId: '2',
    userId: '1',
    date: '2025-05-20',
    time: '2:00 PM',
    status: 'cancelled',
    price: 120,
    notes: 'Weather conditions not suitable',
    duration: 180,
  },
  {
    id: '21',
    serviceId: '3',
    providerId: '3',
    userId: '1',
    date: '2025-05-25',
    time: '11:30 AM',
    status: 'cancelled',
    price: 150,
    notes: 'Customer cancelled last minute',
    duration: 90,
  },
  {
    id: '22',
    serviceId: '1',
    providerId: '1',
    userId: '1',
    date: '2025-05-28',
    time: '9:00 AM',
    status: 'cancelled',
    price: 80,
    duration: 120,
  },
  {
    id: '23',
    serviceId: '2',
    providerId: '2',
    userId: '1',
    date: '2025-06-02',
    time: '3:00 PM',
    status: 'cancelled',
    price: 120,
    notes: 'Provider unavailable',
    duration: 180,
  },
  {
    id: '24',
    serviceId: '3',
    providerId: '3',
    userId: '1',
    date: '2025-06-08',
    time: '1:00 PM',
    status: 'cancelled',
    price: 150,
    duration: 90,
  },
];

// Mock Reviews
export const reviews: Review[] = [
  {
    id: '1',
    serviceId: '1',
    userId: '5', // Sarah
    rating: 5,
    comment: 'Jane was incredible! She made our family feel so comfortable during the shoot and the photos turned out amazing. Highly recommend!',
    timestamp: '2023-03-20T14:30:00Z'
  },
  {
    id: '2',
    serviceId: '2',
    userId: '6', // Robert
    rating: 4,
    comment: 'Michael arrived on time and fixed our plumbing issue quickly. Very professional and knowledgeable.',
    timestamp: '2023-04-05T16:45:00Z'
  },
  {
    id: '3',
    serviceId: '3',
    userId: '5', // Sarah
    rating: 5,
    comment: 'Emily is an excellent tutor! She helped my son improve his calculus grade from a C to an A within just two months.',
    timestamp: '2023-02-12T11:20:00Z'
  }
];

// Mock favorite services for the current user (Sarah)
export const favoriteServices = [
  services[0], // Professional Portrait Photography
  services[3], // Modern Website Development
  services[6]  // Garden Design and Maintenance
];

// Combine all mock data
export const mockData = {
  categories,
  users,
  services,
  messages,
  conversations,
  bookings,
  reviews,
  favoriteServices
};

export default mockData;