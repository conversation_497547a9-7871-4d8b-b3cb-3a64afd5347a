import React from 'react';
import { motion } from 'framer-motion';

const MovingTruck: React.FC = () => {
  return (
    <div className="relative w-full h-full flex items-center justify-center">
      {/* Main Truck SVG - Responsive sizing */}
      <motion.svg
        width="100%"
        height="100%"
        viewBox="0 0 96 96"
        className="relative z-10 max-w-20 max-h-20 sm:max-w-24 sm:max-h-24"
        animate={{
          x: [0, -2, 0, 1, 0],
          y: [0, -0.5, 0, 0.5, 0],
        }}
        transition={{
          duration: 0.8,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      >
        <defs>
          {/* Truck Body Gradient */}
          <linearGradient id="truck-body" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#3B82F6" />
            <stop offset="30%" stopColor="#2563EB" />
            <stop offset="70%" stopColor="#1D4ED8" />
            <stop offset="100%" stopColor="#1E40AF" />
          </linearGradient>
          
          {/* Truck Shadow */}
          <linearGradient id="truck-shadow" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#1E40AF" />
            <stop offset="100%" stopColor="#1E3A8A" />
          </linearGradient>
          
          {/* Cargo Bed - Open Truck Bed */}
          <linearGradient id="cargo-bed" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#6B7280" />
            <stop offset="50%" stopColor="#4B5563" />
            <stop offset="100%" stopColor="#374151" />
          </linearGradient>
          
          {/* Cardboard Box Gradient */}
          <linearGradient id="cardboard-box" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#FEF3C7" />
            <stop offset="30%" stopColor="#FDE68A" />
            <stop offset="70%" stopColor="#F59E0B" />
            <stop offset="100%" stopColor="#D97706" />
          </linearGradient>
          
          {/* Box Shadow */}
          <linearGradient id="box-shadow" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#D97706" />
            <stop offset="100%" stopColor="#92400E" />
          </linearGradient>
          
          {/* Tire Gradient */}
          <radialGradient id="tire-gradient" cx="50%" cy="50%" r="50%">
            <stop offset="0%" stopColor="#374151" />
            <stop offset="60%" stopColor="#1F2937" />
            <stop offset="100%" stopColor="#111827" />
          </radialGradient>
          
          {/* Rim Gradient */}
          <radialGradient id="rim-gradient" cx="50%" cy="50%" r="50%">
            <stop offset="0%" stopColor="#F9FAFB" />
            <stop offset="40%" stopColor="#E5E7EB" />
            <stop offset="80%" stopColor="#9CA3AF" />
            <stop offset="100%" stopColor="#6B7280" />
          </radialGradient>
          
          {/* Window Gradient */}
          <linearGradient id="window-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#DBEAFE" />
            <stop offset="50%" stopColor="#93C5FD" />
            <stop offset="100%" stopColor="#60A5FA" />
          </linearGradient>
        </defs>
        
        {/* Open Cargo Bed - Truck Bed Floor */}
        <rect
          x="12"
          y="52"
          width="48"
          height="8"
          fill="url(#cargo-bed)"
          rx="1"
        />
        
        {/* Cargo Bed Side Walls */}
        <rect
          x="12"
          y="28"
          width="2"
          height="24"
          fill="url(#cargo-bed)"
        />
        
        <rect
          x="58"
          y="28"
          width="2"
          height="24"
          fill="url(#cargo-bed)"
        />
        
        {/* Cargo Bed Back Wall */}
        <rect
          x="12"
          y="28"
          width="48"
          height="2"
          fill="url(#cargo-bed)"
        />
        
        {/* CARDBOARD BOXES - Multiple boxes with realistic stacking */}
        
        {/* Large Box 1 - Back Left */}
        <motion.g
          animate={{
            y: [0, -0.5, 0, 0.3, 0],
            rotate: [0, 0.5, 0, -0.3, 0]
          }}
          transition={{
            duration: 0.8,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        >
          <rect
            x="16"
            y="36"
            width="12"
            height="16"
            fill="url(#cardboard-box)"
            rx="1"
          />
          
          {/* Box Shadow */}
          <rect
            x="14"
            y="36"
            width="2"
            height="16"
            fill="url(#box-shadow)"
            rx="0.5"
          />
          
          {/* Box Top */}
          <rect
            x="16"
            y="34"
            width="12"
            height="2"
            fill="#FEF3C7"
            rx="1"
          />
          
          {/* Box Tape Lines */}
          <rect
            x="18"
            y="34"
            width="8"
            height="0.5"
            fill="#92400E"
            opacity="0.6"
          />
          
          <rect
            x="21"
            y="36"
            width="0.5"
            height="16"
            fill="#92400E"
            opacity="0.6"
          />
          
          {/* Fragile Symbol */}
          <circle
            cx="22"
            cy="44"
            r="2"
            fill="none"
            stroke="#DC2626"
            strokeWidth="0.5"
          />
          
          <text
            x="22"
            y="46"
            textAnchor="middle"
            fontSize="2"
            fill="#DC2626"
            fontWeight="bold"
          >
            !
          </text>
        </motion.g>
        
        {/* Medium Box 2 - Back Right */}
        <motion.g
          animate={{
            y: [0, -0.3, 0, 0.4, 0],
            rotate: [0, -0.4, 0, 0.2, 0]
          }}
          transition={{
            duration: 0.9,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 0.1
          }}
        >
          <rect
            x="32"
            y="40"
            width="10"
            height="12"
            fill="url(#cardboard-box)"
            rx="1"
          />
          
          {/* Box Shadow */}
          <rect
            x="30"
            y="40"
            width="2"
            height="12"
            fill="url(#box-shadow)"
            rx="0.5"
          />
          
          {/* Box Top */}
          <rect
            x="32"
            y="38"
            width="10"
            height="2"
            fill="#FEF3C7"
            rx="1"
          />
          
          {/* Box Tape */}
          <rect
            x="34"
            y="38"
            width="6"
            height="0.5"
            fill="#92400E"
            opacity="0.6"
          />
          
          <rect
            x="36"
            y="40"
            width="0.5"
            height="12"
            fill="#92400E"
            opacity="0.6"
          />
          
          {/* Box Label */}
          <rect
            x="34"
            y="44"
            width="4"
            height="2"
            fill="#FFFFFF"
            opacity="0.8"
          />
        </motion.g>
        
        {/* Small Box 3 - Front Left */}
        <motion.g
          animate={{
            y: [0, -0.4, 0, 0.2, 0],
            rotate: [0, 0.3, 0, -0.5, 0]
          }}
          transition={{
            duration: 0.7,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 0.2
          }}
        >
          <rect
            x="44"
            y="44"
            width="8"
            height="8"
            fill="url(#cardboard-box)"
            rx="1"
          />
          
          {/* Box Shadow */}
          <rect
            x="42"
            y="44"
            width="2"
            height="8"
            fill="url(#box-shadow)"
            rx="0.5"
          />
          
          {/* Box Top */}
          <rect
            x="44"
            y="42"
            width="8"
            height="2"
            fill="#FEF3C7"
            rx="1"
          />
          
          {/* Box Tape Cross */}
          <rect
            x="46"
            y="42"
            width="4"
            height="0.5"
            fill="#92400E"
            opacity="0.6"
          />
          
          <rect
            x="47.5"
            y="44"
            width="0.5"
            height="8"
            fill="#92400E"
            opacity="0.6"
          />
        </motion.g>
        
        {/* Stacked Small Box 4 - On top of Box 3 */}
        <motion.g
          animate={{
            y: [0, -0.6, 0, 0.1, 0],
            rotate: [0, 0.6, 0, -0.2, 0]
          }}
          transition={{
            duration: 0.8,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 0.3
          }}
        >
          <rect
            x="46"
            y="36"
            width="6"
            height="6"
            fill="url(#cardboard-box)"
            rx="1"
          />
          
          {/* Box Top */}
          <rect
            x="46"
            y="34"
            width="6"
            height="2"
            fill="#FEF3C7"
            rx="1"
          />
          
          {/* Box Tape */}
          <rect
            x="47"
            y="34"
            width="4"
            height="0.5"
            fill="#92400E"
            opacity="0.6"
          />
        </motion.g>
        
        {/* Loose Items - Simulating movement */}
        
        {/* Rope/Strap */}
        <motion.path
          d="M16 30 Q24 32 32 30 Q40 28 48 30"
          stroke="#8B4513"
          strokeWidth="1"
          fill="none"
          animate={{
            d: [
              "M16 30 Q24 32 32 30 Q40 28 48 30",
              "M16 30.5 Q24 31.5 32 30.5 Q40 28.5 48 30.5",
              "M16 30 Q24 32 32 30 Q40 28 48 30"
            ]
          }}
          transition={{
            duration: 0.8,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        
        {/* Packing Peanuts/Foam - Floating */}
        <motion.circle
          cx="20"
          cy="32"
          r="0.5"
          fill="#F3F4F6"
          animate={{
            y: [0, -2, 0, 1, 0],
            x: [0, 1, 0, -0.5, 0],
            opacity: [0.6, 1, 0.6, 0.8, 0.6]
          }}
          transition={{
            duration: 1.2,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        
        <motion.circle
          cx="38"
          cy="34"
          r="0.3"
          fill="#F3F4F6"
          animate={{
            y: [0, -1.5, 0, 0.8, 0],
            x: [0, -0.8, 0, 0.3, 0],
            opacity: [0.5, 0.9, 0.5, 0.7, 0.5]
          }}
          transition={{
            duration: 1.1,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 0.3
          }}
        />
        
        <motion.circle
          cx="50"
          cy="33"
          r="0.4"
          fill="#F3F4F6"
          animate={{
            y: [0, -1.8, 0, 1.2, 0],
            x: [0, 0.6, 0, -0.4, 0],
            opacity: [0.4, 0.8, 0.4, 0.6, 0.4]
          }}
          transition={{
            duration: 1.3,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 0.6
          }}
        />
        
        {/* Truck Cab - Now at the Right */}
        <rect
          x="60"
          y="35"
          width="28"
          height="20"
          fill="url(#truck-body)"
          rx="2"
        />
        
        {/* Truck Cab Shadow/Depth */}
        <rect
          x="60"
          y="35"
          width="4"
          height="20"
          fill="url(#truck-shadow)"
          rx="1"
        />
        
        {/* Windshield */}
        <rect
          x="78"
          y="37"
          width="8"
          height="12"
          fill="url(#window-gradient)"
          rx="1"
        />
        
        {/* Side Window */}
        <rect
          x="68"
          y="37"
          width="8"
          height="8"
          fill="url(#window-gradient)"
          rx="1"
        />
        
        {/* Truck Grille */}
        <rect
          x="88"
          y="40"
          width="2"
          height="10"
          fill="#374151"
          rx="0.5"
        />
        
        {/* Headlight */}
        <circle
          cx="89"
          cy="42"
          r="2"
          fill="#FEF3C7"
          opacity="0.9"
        />
        
        {/* Front Bumper */}
        <rect
          x="88"
          y="48"
          width="4"
          height="4"
          fill="#6B7280"
          rx="1"
        />
        
        {/* WHEELS INTEGRATED INTO SVG - Properly positioned */}
        
        {/* Front Tire - Animated Rotation (Right Side) */}
        <motion.g
          animate={{
            rotate: [0, -360],
          }}
          transition={{
            duration: 0.3,
            repeat: Infinity,
            ease: "linear"
          }}
          style={{ transformOrigin: '74px 58px' }}
        >
          <circle cx="74" cy="58" r="6" fill="url(#tire-gradient)" />
          <circle cx="74" cy="58" r="3" fill="url(#rim-gradient)" />
          <circle cx="74" cy="58" r="1.5" fill="#374151" />
          {/* Tire Treads */}
          <rect x="73.5" y="53" width="1" height="2" fill="#111827" rx="0.5" />
          <rect x="73.5" y="61" width="1" height="2" fill="#111827" rx="0.5" />
          <rect x="69" y="57.5" width="2" height="1" fill="#111827" rx="0.5" />
          <rect x="77" y="57.5" width="2" height="1" fill="#111827" rx="0.5" />
        </motion.g>

        {/* Rear Tire - Animated Rotation (Left Side) */}
        <motion.g
          animate={{
            rotate: [0, -360],
          }}
          transition={{
            duration: 0.3,
            repeat: Infinity,
            ease: "linear"
          }}
          style={{ transformOrigin: '32px 58px' }}
        >
          <circle cx="32" cy="58" r="6" fill="url(#tire-gradient)" />
          <circle cx="32" cy="58" r="3" fill="url(#rim-gradient)" />
          <circle cx="32" cy="58" r="1.5" fill="#374151" />
          {/* Tire Treads */}
          <rect x="31.5" y="53" width="1" height="2" fill="#111827" rx="0.5" />
          <rect x="31.5" y="61" width="1" height="2" fill="#111827" rx="0.5" />
          <rect x="27" y="57.5" width="2" height="1" fill="#111827" rx="0.5" />
          <rect x="35" y="57.5" width="2" height="1" fill="#111827" rx="0.5" />
        </motion.g>
      </motion.svg>

      {/* Speed Lines - Motion Effect (Reversed Direction) */}
      <motion.div
        className="absolute inset-0 overflow-hidden pointer-events-none"
        animate={{
          opacity: [0.3, 0.7, 0.3]
        }}
        transition={{
          duration: 0.8,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      >
        {/* Horizontal Speed Lines - Moving Right to Left */}
        <motion.div
          className="absolute top-8 right-0 w-8 h-0.5 bg-gradient-to-l from-transparent to-blue-400 opacity-60"
          animate={{
            x: [0, 20, 0],
            scaleX: [0.5, 1.5, 0.5]
          }}
          transition={{
            duration: 0.6,
            repeat: Infinity,
            ease: "easeOut"
          }}
        />
        
        <motion.div
          className="absolute top-12 right-2 w-6 h-0.5 bg-gradient-to-l from-transparent to-blue-300 opacity-50"
          animate={{
            x: [0, 15, 0],
            scaleX: [0.3, 1.2, 0.3]
          }}
          transition={{
            duration: 0.7,
            repeat: Infinity,
            ease: "easeOut",
            delay: 0.1
          }}
        />
        
        <motion.div
          className="absolute top-16 right-1 w-10 h-0.5 bg-gradient-to-l from-transparent to-blue-500 opacity-40"
          animate={{
            x: [0, 25, 0],
            scaleX: [0.4, 1.8, 0.4]
          }}
          transition={{
            duration: 0.5,
            repeat: Infinity,
            ease: "easeOut",
            delay: 0.2
          }}
        />
        
        <motion.div
          className="absolute top-20 right-3 w-7 h-0.5 bg-gradient-to-l from-transparent to-blue-400 opacity-55"
          animate={{
            x: [0, 18, 0],
            scaleX: [0.6, 1.4, 0.6]
          }}
          transition={{
            duration: 0.65,
            repeat: Infinity,
            ease: "easeOut",
            delay: 0.3
          }}
        />
      </motion.div>

      {/* Dust Cloud Effect (Reversed) */}
      <motion.div
        className="absolute bottom-0 right-4 w-16 h-4 opacity-30"
        animate={{
          scale: [0.8, 1.2, 0.8],
          opacity: [0.2, 0.4, 0.2]
        }}
        transition={{
          duration: 1.2,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      >
        <div className="w-full h-full bg-gradient-to-l from-gray-400 via-gray-300 to-transparent rounded-full blur-sm" />
      </motion.div>

      {/* Tire Smoke/Dust (Reversed) */}
      <motion.div
        className="absolute bottom-1 right-5"
        animate={{
          scale: [0, 1.5, 0],
          opacity: [0, 0.3, 0],
          x: [0, 8, 15],
          y: [0, -2, -4]
        }}
        transition={{
          duration: 1,
          repeat: Infinity,
          ease: "easeOut",
          delay: 0.1
        }}
      >
        <div className="w-3 h-3 bg-gray-300 rounded-full blur-sm opacity-40" />
      </motion.div>

      <motion.div
        className="absolute bottom-1 left-8"
        animate={{
          scale: [0, 1.3, 0],
          opacity: [0, 0.25, 0],
          x: [0, 6, 12],
          y: [0, -1, -3]
        }}
        transition={{
          duration: 0.9,
          repeat: Infinity,
          ease: "easeOut",
          delay: 0.2
        }}
      >
        <div className="w-2.5 h-2.5 bg-gray-400 rounded-full blur-sm opacity-35" />
      </motion.div>

      {/* Wind Effect Lines (Reversed) */}
      <motion.div
        className="absolute top-6 right-0 w-12 h-0.5 bg-gradient-to-l from-transparent via-gray-300 to-transparent opacity-40"
        animate={{
          x: [0, 30, 0],
          scaleX: [0.5, 2, 0.5],
          opacity: [0, 0.4, 0]
        }}
        transition={{
          duration: 1.5,
          repeat: Infinity,
          ease: "easeOut"
        }}
      />

      <motion.div
        className="absolute top-18 right-2 w-8 h-0.5 bg-gradient-to-l from-transparent via-gray-400 to-transparent opacity-30"
        animate={{
          x: [0, 20, 0],
          scaleX: [0.3, 1.5, 0.3],
          opacity: [0, 0.3, 0]
        }}
        transition={{
          duration: 1.3,
          repeat: Infinity,
          ease: "easeOut",
          delay: 0.3
        }}
      />

      {/* Exhaust Smoke (Reversed) */}
      <motion.div
        className="absolute top-10 left-0"
        animate={{
          scale: [0, 1, 1.5],
          opacity: [0, 0.4, 0],
          x: [0, 5, 12],
          y: [0, -2, -6]
        }}
        transition={{
          duration: 2,
          repeat: Infinity,
          ease: "easeOut"
        }}
      >
        <div className="w-2 h-2 bg-gray-500 rounded-full blur-sm opacity-30" />
      </motion.div>

      <motion.div
        className="absolute top-8 left-1"
        animate={{
          scale: [0, 0.8, 1.2],
          opacity: [0, 0.3, 0],
          x: [0, 3, 8],
          y: [0, -1, -4]
        }}
        transition={{
          duration: 1.8,
          repeat: Infinity,
          ease: "easeOut",
          delay: 0.4
        }}
      >
        <div className="w-1.5 h-1.5 bg-gray-400 rounded-full blur-sm opacity-25" />
      </motion.div>

      {/* Road Vibration Effect */}
      <motion.div
        className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-gray-200 to-transparent opacity-20"
        animate={{
          scaleY: [0.5, 1.5, 0.5],
          opacity: [0.1, 0.3, 0.1]
        }}
        transition={{
          duration: 0.4,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />

      {/* Headlight Beam (Reversed) */}
      <motion.div
        className="absolute top-10 right-0 w-8 h-2 bg-gradient-to-l from-yellow-200 via-yellow-100 to-transparent opacity-40 rounded-full blur-sm"
        animate={{
          opacity: [0.3, 0.6, 0.3],
          scaleX: [0.8, 1.2, 0.8]
        }}
        transition={{
          duration: 2,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />

      {/* Motion Blur on Truck Body */}
      <motion.div
        className="absolute inset-0 pointer-events-none"
        animate={{
          opacity: [0, 0.1, 0]
        }}
        transition={{
          duration: 0.8,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      >
        <div className="w-full h-full bg-gradient-to-l from-transparent via-blue-200 to-transparent blur-sm opacity-20" />
      </motion.div>
    </div>
  );
};

export default MovingTruck;