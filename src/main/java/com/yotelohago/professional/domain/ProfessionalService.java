package com.yotelohago.professional.domain;

import io.quarkus.hibernate.orm.panache.PanacheEntityBase;
import jakarta.persistence.*;

import java.io.Serializable;
import java.util.UUID;

@Entity
@Table(name = "professional_service")
public class ProfessionalService {

    @EmbeddedId
    public Id id;

    public ProfessionalService() {}

    public ProfessionalService(UUID professionalId, UUID serviceId) {
        this.id = new Id(professionalId, serviceId);
    }

    @Embeddable
    public static class Id implements Serializable {
        @Column(name = "professional_id")
        public UUID professionalId;

        @Column(name = "service_id")
        public UUID serviceId;

        public Id() {}

        public Id(UUID professionalId, UUID serviceId) {
            this.professionalId = professionalId;
            this.serviceId = serviceId;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            Id that = (Id) o;
            return professionalId.equals(that.professionalId) && serviceId.equals(that.serviceId);
        }

        @Override
        public int hashCode() {
            return professionalId.hashCode() + serviceId.hashCode();
        }
    }
}