import { 
  User, 
  Professional, 
  ServiceCategory, 
  Service, 
  Booking, 
  Review, 
  Message, 
  Favorite, 
  DashboardStats,
  Transaction,
  Notification
} from '../types';

// 25 Users: 20 Professionals + 5 Clients
export const mockUsers: User[] = [
  // 5 Clients
  {
    id: 'client-1',
    username: 'sarah_johnson',
    email: '<EMAIL>',
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    phone: '******-0101',
    isProfessional: false,
    createdAt: '2024-01-15T08:00:00Z',
    updatedAt: '2024-01-20T10:30:00Z'
  },
  {
    id: 'client-2',
    username: 'michael_chen',
    email: '<EMAIL>',
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    phone: '******-0102',
    isProfessional: false,
    createdAt: '2024-01-10T09:00:00Z',
    updatedAt: '2024-01-18T14:15:00Z'
  },
  {
    id: 'client-3',
    username: 'emily_davis',
    email: '<EMAIL>',
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    phone: '******-0103',
    isProfessional: false,
    createdAt: '2024-01-05T11:00:00Z',
    updatedAt: '2024-01-15T16:45:00Z'
  },
  {
    id: 'client-4',
    username: 'david_wilson',
    email: '<EMAIL>',
    firstName: 'David',
    lastName: 'Wilson',
    phone: '******-0104',
    isProfessional: false,
    createdAt: '2024-01-12T13:20:00Z',
    updatedAt: '2024-01-22T09:10:00Z'
  },
  {
    id: 'client-5',
    username: 'jessica_brown',
    email: '<EMAIL>',
    firstName: 'Jessica',
    lastName: 'Brown',
    phone: '******-0105',
    isProfessional: false,
    createdAt: '2024-01-08T15:30:00Z',
    updatedAt: '2024-01-25T11:20:00Z'
  },

  // 5 Electricians
  {
    id: 'elec-1',
    username: 'mike_electrician',
    email: '<EMAIL>',
    firstName: 'Mike',
    lastName: 'Rodriguez',
    phone: '******-0201',
    isProfessional: true,
    createdAt: '2024-01-01T08:00:00Z',
    updatedAt: '2024-01-20T10:30:00Z'
  },
  {
    id: 'elec-2',
    username: 'james_voltage',
    email: '<EMAIL>',
    firstName: 'James',
    lastName: 'Thompson',
    phone: '******-0202',
    isProfessional: true,
    createdAt: '2024-01-02T09:15:00Z',
    updatedAt: '2024-01-21T14:20:00Z'
  },
  {
    id: 'elec-3',
    username: 'carlos_power',
    email: '<EMAIL>',
    firstName: 'Carlos',
    lastName: 'Martinez',
    phone: '******-0203',
    isProfessional: true,
    createdAt: '2024-01-03T10:30:00Z',
    updatedAt: '2024-01-22T16:45:00Z'
  },
  {
    id: 'elec-4',
    username: 'robert_circuits',
    email: '<EMAIL>',
    firstName: 'Robert',
    lastName: 'Lee',
    phone: '******-0204',
    isProfessional: true,
    createdAt: '2024-01-04T11:45:00Z',
    updatedAt: '2024-01-23T12:30:00Z'
  },
  {
    id: 'elec-5',
    username: 'anthony_electric',
    email: '<EMAIL>',
    firstName: 'Anthony',
    lastName: 'Garcia',
    phone: '******-0205',
    isProfessional: true,
    createdAt: '2024-01-05T13:00:00Z',
    updatedAt: '2024-01-24T15:15:00Z'
  },

  // 5 Plumbers
  {
    id: 'plumb-1',
    username: 'sarah_plumber',
    email: '<EMAIL>',
    firstName: 'Sarah',
    lastName: 'Wilson',
    phone: '******-0301',
    isProfessional: true,
    createdAt: '2024-01-06T08:30:00Z',
    updatedAt: '2024-01-25T10:45:00Z'
  },
  {
    id: 'plumb-2',
    username: 'daniel_pipes',
    email: '<EMAIL>',
    firstName: 'Daniel',
    lastName: 'Anderson',
    phone: '******-0302',
    isProfessional: true,
    createdAt: '2024-01-07T09:45:00Z',
    updatedAt: '2024-01-26T13:20:00Z'
  },
  {
    id: 'plumb-3',
    username: 'maria_water',
    email: '<EMAIL>',
    firstName: 'Maria',
    lastName: 'Gonzalez',
    phone: '******-0303',
    isProfessional: true,
    createdAt: '2024-01-08T11:00:00Z',
    updatedAt: '2024-01-27T14:35:00Z'
  },
  {
    id: 'plumb-4',
    username: 'kevin_drain',
    email: '<EMAIL>',
    firstName: 'Kevin',
    lastName: 'Taylor',
    phone: '******-0304',
    isProfessional: true,
    createdAt: '2024-01-09T12:15:00Z',
    updatedAt: '2024-01-28T16:50:00Z'
  },
  {
    id: 'plumb-5',
    username: 'lisa_hydro',
    email: '<EMAIL>',
    firstName: 'Lisa',
    lastName: 'White',
    phone: '******-0305',
    isProfessional: true,
    createdAt: '2024-01-10T13:30:00Z',
    updatedAt: '2024-01-29T18:05:00Z'
  },

  // 5 Movers
  {
    id: 'move-1',
    username: 'john_mover',
    email: '<EMAIL>',
    firstName: 'John',
    lastName: 'Jackson',
    phone: '******-0401',
    isProfessional: true,
    createdAt: '2024-01-11T08:45:00Z',
    updatedAt: '2024-01-30T09:20:00Z'
  },
  {
    id: 'move-2',
    username: 'alex_transport',
    email: '<EMAIL>',
    firstName: 'Alex',
    lastName: 'Harris',
    phone: '******-0402',
    isProfessional: true,
    createdAt: '2024-01-12T10:00:00Z',
    updatedAt: '2024-01-31T11:35:00Z'
  },
  {
    id: 'move-3',
    username: 'stephanie_relocate',
    email: '<EMAIL>',
    firstName: 'Stephanie',
    lastName: 'Clark',
    phone: '******-0403',
    isProfessional: true,
    createdAt: '2024-01-13T11:15:00Z',
    updatedAt: '2024-02-01T12:50:00Z'
  },
  {
    id: 'move-4',
    username: 'marcus_haul',
    email: '<EMAIL>',
    firstName: 'Marcus',
    lastName: 'Lewis',
    phone: '******-0404',
    isProfessional: true,
    createdAt: '2024-01-14T12:30:00Z',
    updatedAt: '2024-02-02T14:05:00Z'
  },
  {
    id: 'move-5',
    username: 'rachel_pack',
    email: '<EMAIL>',
    firstName: 'Rachel',
    lastName: 'Walker',
    phone: '******-0405',
    isProfessional: true,
    createdAt: '2024-01-15T13:45:00Z',
    updatedAt: '2024-02-03T15:20:00Z'
  },

  // 5 Locksmiths
  {
    id: 'lock-1',
    username: 'thomas_locksmith',
    email: '<EMAIL>',
    firstName: 'Thomas',
    lastName: 'Hall',
    phone: '******-0501',
    isProfessional: true,
    createdAt: '2024-01-16T09:00:00Z',
    updatedAt: '2024-02-04T10:15:00Z'
  },
  {
    id: 'lock-2',
    username: 'jennifer_keys',
    email: '<EMAIL>',
    firstName: 'Jennifer',
    lastName: 'Allen',
    phone: '******-0502',
    isProfessional: true,
    createdAt: '2024-01-17T10:15:00Z',
    updatedAt: '2024-02-05T11:30:00Z'
  },
  {
    id: 'lock-3',
    username: 'brian_security',
    email: '<EMAIL>',
    firstName: 'Brian',
    lastName: 'Young',
    phone: '******-0503',
    isProfessional: true,
    createdAt: '2024-01-18T11:30:00Z',
    updatedAt: '2024-02-06T12:45:00Z'
  },
  {
    id: 'lock-4',
    username: 'amanda_access',
    email: '<EMAIL>',
    firstName: 'Amanda',
    lastName: 'King',
    phone: '******-0504',
    isProfessional: true,
    createdAt: '2024-01-19T12:45:00Z',
    updatedAt: '2024-02-07T14:00:00Z'
  },
  {
    id: 'lock-5',
    username: 'christopher_vault',
    email: '<EMAIL>',
    firstName: 'Christopher',
    lastName: 'Wright',
    phone: '******-0505',
    isProfessional: true,
    createdAt: '2024-01-20T14:00:00Z',
    updatedAt: '2024-02-08T15:15:00Z'
  }
];

export const mockProfessionals: Professional[] = [
  // Electricians
  {
    userId: 'elec-1',
    available: true,
    avgRating: 4.8,
    ratingCount: 24,
    city: 'New York',
    bio: 'Licensed electrician with 10+ years of experience. Specializing in residential and commercial electrical work, panel upgrades, and smart home installations.',
    profileImageUrl: 'https://images.pexels.com/photos/1681010/pexels-photo-1681010.jpeg?auto=compress&cs=tinysrgb&w=400',
    status: 'approved',
    verificationStatus: 'verified'
  },
  {
    userId: 'elec-2',
    available: true,
    avgRating: 4.7,
    ratingCount: 18,
    city: 'Los Angeles',
    bio: 'Master electrician specializing in industrial electrical systems, motor controls, and electrical troubleshooting. Available for emergency calls.',
    profileImageUrl: 'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=400',
    status: 'approved',
    verificationStatus: 'verified'
  },
  {
    userId: 'elec-3',
    available: false,
    avgRating: 4.9,
    ratingCount: 32,
    city: 'Chicago',
    bio: 'Certified electrical contractor with expertise in renewable energy systems, solar installations, and energy-efficient lighting solutions.',
    profileImageUrl: 'https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg?auto=compress&cs=tinysrgb&w=400',
    status: 'approved',
    verificationStatus: 'verified'
  },
  {
    userId: 'elec-4',
    available: true,
    avgRating: 4.6,
    ratingCount: 15,
    city: 'Houston',
    bio: 'Residential electrical specialist focusing on home rewiring, outlet installations, and electrical safety inspections.',
    profileImageUrl: 'https://images.pexels.com/photos/1516680/pexels-photo-1516680.jpeg?auto=compress&cs=tinysrgb&w=400',
    status: 'approved',
    verificationStatus: 'verified'
  },
  {
    userId: 'elec-5',
    available: true,
    avgRating: 4.5,
    ratingCount: 21,
    city: 'Phoenix',
    bio: 'Commercial electrician with experience in office buildings, retail spaces, and warehouse electrical systems.',
    profileImageUrl: 'https://images.pexels.com/photos/1043471/pexels-photo-1043471.jpeg?auto=compress&cs=tinysrgb&w=400',
    status: 'approved',
    verificationStatus: 'verified'
  },

  // Plumbers
  {
    userId: 'plumb-1',
    available: true,
    avgRating: 4.6,
    ratingCount: 18,
    city: 'Los Angeles',
    bio: 'Professional plumber serving LA area. Emergency services available 24/7. Specializing in drain cleaning, pipe repairs, and bathroom renovations.',
    profileImageUrl: 'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=400',
    status: 'approved',
    verificationStatus: 'verified'
  },
  {
    userId: 'plumb-2',
    available: true,
    avgRating: 4.8,
    ratingCount: 26,
    city: 'Miami',
    bio: 'Licensed master plumber with 15+ years experience. Expert in water heater installations, sewer line repairs, and kitchen plumbing.',
    profileImageUrl: 'https://images.pexels.com/photos/2379005/pexels-photo-2379005.jpeg?auto=compress&cs=tinysrgb&w=400',
    status: 'approved',
    verificationStatus: 'verified'
  },
  {
    userId: 'plumb-3',
    available: true,
    avgRating: 4.7,
    ratingCount: 22,
    city: 'Denver',
    bio: 'Residential and commercial plumbing specialist. Expertise in leak detection, pipe replacement, and fixture installations.',
    profileImageUrl: 'https://images.pexels.com/photos/1516680/pexels-photo-1516680.jpeg?auto=compress&cs=tinysrgb&w=400',
    status: 'approved',
    verificationStatus: 'verified'
  },
  {
    userId: 'plumb-4',
    available: false,
    avgRating: 4.9,
    ratingCount: 31,
    city: 'Seattle',
    bio: 'Emergency plumber available 24/7. Specializing in drain cleaning, water damage restoration, and bathroom remodeling.',
    profileImageUrl: 'https://images.pexels.com/photos/1043471/pexels-photo-1043471.jpeg?auto=compress&cs=tinysrgb&w=400',
    status: 'approved',
    verificationStatus: 'verified'
  },
  {
    userId: 'plumb-5',
    available: true,
    avgRating: 4.4,
    ratingCount: 19,
    city: 'Boston',
    bio: 'Certified plumber with expertise in hydro-jetting, pipe relining, and water filtration system installations.',
    profileImageUrl: 'https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg?auto=compress&cs=tinysrgb&w=400',
    status: 'approved',
    verificationStatus: 'verified'
  },

  // Movers
  {
    userId: 'move-1',
    available: true,
    avgRating: 4.7,
    ratingCount: 28,
    city: 'Dallas',
    bio: 'Professional moving service with 8+ years experience. Specializing in residential moves, packing services, and furniture assembly.',
    profileImageUrl: 'https://images.pexels.com/photos/1681010/pexels-photo-1681010.jpeg?auto=compress&cs=tinysrgb&w=400',
    status: 'approved',
    verificationStatus: 'verified'
  },
  {
    userId: 'move-2',
    available: true,
    avgRating: 4.6,
    ratingCount: 24,
    city: 'Atlanta',
    bio: 'Quick and reliable moving services. Expert in long-distance moves, office relocations, and specialty item transport.',
    profileImageUrl: 'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=400',
    status: 'approved',
    verificationStatus: 'verified'
  },
  {
    userId: 'move-3',
    available: true,
    avgRating: 4.8,
    ratingCount: 33,
    city: 'Portland',
    bio: 'Professional relocation specialist with expertise in residential and commercial moves. Full-service packing and unpacking available.',
    profileImageUrl: 'https://images.pexels.com/photos/2379005/pexels-photo-2379005.jpeg?auto=compress&cs=tinysrgb&w=400',
    status: 'approved',
    verificationStatus: 'verified'
  },
  {
    userId: 'move-4',
    available: false,
    avgRating: 4.5,
    ratingCount: 20,
    city: 'San Diego',
    bio: 'Heavy lifting and hauling specialist. Expert in furniture moving, appliance installation, and junk removal services.',
    profileImageUrl: 'https://images.pexels.com/photos/1516680/pexels-photo-1516680.jpeg?auto=compress&cs=tinysrgb&w=400',
    status: 'approved',
    verificationStatus: 'verified'
  },
  {
    userId: 'move-5',
    available: true,
    avgRating: 4.9,
    ratingCount: 35,
    city: 'Nashville',
    bio: 'Full-service moving company specializing in careful packing, secure transport, and efficient unpacking services.',
    profileImageUrl: 'https://images.pexels.com/photos/1043471/pexels-photo-1043471.jpeg?auto=compress&cs=tinysrgb&w=400',
    status: 'approved',
    verificationStatus: 'verified'
  },

  // Locksmiths
  {
    userId: 'lock-1',
    available: true,
    avgRating: 4.8,
    ratingCount: 27,
    city: 'Las Vegas',
    bio: 'Certified locksmith with 12+ years experience. Specializing in residential lockouts, lock installations, and security system upgrades.',
    profileImageUrl: 'https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg?auto=compress&cs=tinysrgb&w=400',
    status: 'approved',
    verificationStatus: 'verified'
  },
  {
    userId: 'lock-2',
    available: true,
    avgRating: 4.7,
    ratingCount: 23,
    city: 'Orlando',
    bio: 'Master locksmith specializing in high-security locks, key duplication, and automotive locksmith services. Available 24/7.',
    profileImageUrl: 'https://images.pexels.com/photos/1681010/pexels-photo-1681010.jpeg?auto=compress&cs=tinysrgb&w=400',
    status: 'approved',
    verificationStatus: 'verified'
  },
  {
    userId: 'lock-3',
    available: true,
    avgRating: 4.6,
    ratingCount: 19,
    city: 'Minneapolis',
    bio: 'Security specialist with expertise in access control systems, smart locks, and commercial security solutions.',
    profileImageUrl: 'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=400',
    status: 'approved',
    verificationStatus: 'verified'
  },
  {
    userId: 'lock-4',
    available: false,
    avgRating: 4.9,
    ratingCount: 30,
    city: 'Salt Lake City',
    bio: 'Access control expert specializing in keyless entry systems, biometric locks, and comprehensive security assessments.',
    profileImageUrl: 'https://images.pexels.com/photos/2379005/pexels-photo-2379005.jpeg?auto=compress&cs=tinysrgb&w=400',
    status: 'approved',
    verificationStatus: 'verified'
  },
  {
    userId: 'lock-5',
    available: true,
    avgRating: 4.5,
    ratingCount: 16,
    city: 'Kansas City',
    bio: 'Vault and safe specialist with expertise in high-security installations, safe combinations, and vault maintenance.',
    profileImageUrl: 'https://images.pexels.com/photos/1516680/pexels-photo-1516680.jpeg?auto=compress&cs=tinysrgb&w=400',
    status: 'approved',
    verificationStatus: 'verified'
  }
];

export const mockServiceCategories: ServiceCategory[] = [
  {
    id: '1',
    name: 'electricians',
    description: 'Professional electrical services for homes and businesses',
    iconUrl: 'https://images.pexels.com/photos/257736/pexels-photo-257736.jpeg?auto=compress&cs=tinysrgb&w=400',
    status: 'active',
    serviceCount: 15
  },
  {
    id: '2',
    name: 'plumbers',
    description: 'Expert plumbing services and emergency repairs',
    iconUrl: 'https://images.pexels.com/photos/8486944/pexels-photo-8486944.jpeg?auto=compress&cs=tinysrgb&w=400',
    status: 'active',
    serviceCount: 12
  },
  {
    id: '3',
    name: 'movers',
    description: 'Professional moving and relocation services',
    iconUrl: 'https://images.pexels.com/photos/7464230/pexels-photo-7464230.jpeg?auto=compress&cs=tinysrgb&w=400',
    status: 'active',
    serviceCount: 8
  },
  {
    id: '4',
    name: 'locksmiths',
    description: 'Lock installation, repair, and emergency lockout services',
    iconUrl: 'https://images.pexels.com/photos/279810/pexels-photo-279810.jpeg?auto=compress&cs=tinysrgb&w=400',
    status: 'active',
    serviceCount: 6
  }
];

export const mockServices: Service[] = [
  // Electrician Services
  {
    id: 'serv-1',
    title: 'Electrical Panel Upgrade',
    description: 'Complete electrical panel replacement and upgrade service for modern electrical demands',
    price: 1200.00,
    status: 'active',
    categoryId: '1',
    professionalUserId: 'elec-1',
    createdAt: '2024-01-15T08:00:00Z',
    updatedAt: '2024-01-20T10:30:00Z'
  },
  {
    id: 'serv-2',
    title: 'Smart Home Wiring',
    description: 'Installation of smart home electrical systems and automation wiring',
    price: 800.00,
    status: 'active',
    categoryId: '1',
    professionalUserId: 'elec-2',
    createdAt: '2024-01-16T09:00:00Z',
    updatedAt: '2024-01-21T11:30:00Z'
  },
  {
    id: 'serv-3',
    title: 'Solar Panel Installation',
    description: 'Complete solar panel system installation with electrical connections',
    price: 2500.00,
    status: 'active',
    categoryId: '1',
    professionalUserId: 'elec-3',
    createdAt: '2024-01-17T10:00:00Z',
    updatedAt: '2024-01-22T12:30:00Z'
  },

  // Plumber Services
  {
    id: 'serv-4',
    title: 'Emergency Plumbing Repair',
    description: '24/7 emergency plumbing services for urgent repairs and leaks',
    price: 150.00,
    status: 'active',
    categoryId: '2',
    professionalUserId: 'plumb-1',
    createdAt: '2024-01-10T09:00:00Z',
    updatedAt: '2024-01-18T14:15:00Z'
  },
  {
    id: 'serv-5',
    title: 'Water Heater Installation',
    description: 'Professional water heater installation and replacement service',
    price: 650.00,
    status: 'active',
    categoryId: '2',
    professionalUserId: 'plumb-2',
    createdAt: '2024-01-11T10:00:00Z',
    updatedAt: '2024-01-19T15:20:00Z'
  },
  {
    id: 'serv-6',
    title: 'Bathroom Renovation Plumbing',
    description: 'Complete plumbing services for bathroom renovations and remodeling',
    price: 950.00,
    status: 'active',
    categoryId: '2',
    professionalUserId: 'plumb-3',
    createdAt: '2024-01-12T11:00:00Z',
    updatedAt: '2024-01-20T16:25:00Z'
  },

  // Moving Services
  {
    id: 'serv-7',
    title: 'Residential Moving Service',
    description: 'Full-service residential moving with packing and unpacking',
    price: 450.00,
    status: 'active',
    categoryId: '3',
    professionalUserId: 'move-1',
    createdAt: '2024-01-13T12:00:00Z',
    updatedAt: '2024-01-21T17:30:00Z'
  },
  {
    id: 'serv-8',
    title: 'Office Relocation',
    description: 'Professional office moving and equipment relocation services',
    price: 750.00,
    status: 'active',
    categoryId: '3',
    professionalUserId: 'move-2',
    createdAt: '2024-01-14T13:00:00Z',
    updatedAt: '2024-01-22T18:35:00Z'
  },

  // Locksmith Services
  {
    id: 'serv-9',
    title: 'Emergency Lockout Service',
    description: '24/7 emergency lockout service for homes and vehicles',
    price: 85.00,
    status: 'active',
    categoryId: '4',
    professionalUserId: 'lock-1',
    createdAt: '2024-01-15T14:00:00Z',
    updatedAt: '2024-01-23T19:40:00Z'
  },
  {
    id: 'serv-10',
    title: 'Smart Lock Installation',
    description: 'Installation and setup of smart lock systems for enhanced security',
    price: 200.00,
    status: 'active',
    categoryId: '4',
    professionalUserId: 'lock-2',
    createdAt: '2024-01-16T15:00:00Z',
    updatedAt: '2024-01-24T20:45:00Z'
  }
];

export const mockBookings: Booking[] = [
  {
    id: 'book-1',
    title: 'Kitchen Electrical Work',
    description: 'Install new outlets and lighting in kitchen renovation',
    price: 450.00,
    status: 'ACCEPTED',
    clientId: 'client-1',
    professionalId: 'elec-1',
    serviceId: 'serv-1',
    requestedDate: '2024-02-15T10:00:00Z',
    scheduledAt: '2024-02-15T10:00:00Z',
    createdAt: '2024-01-20T10:00:00Z',
    updatedAt: '2024-01-20T14:30:00Z'
  },
  {
    id: 'book-2',
    title: 'Bathroom Pipe Repair',
    description: 'Fix leaking pipes under bathroom sink',
    price: 180.00,
    status: 'COMPLETED',
    clientId: 'client-2',
    professionalId: 'plumb-1',
    serviceId: 'serv-4',
    requestedDate: '2024-01-25T14:00:00Z',
    scheduledAt: '2024-01-25T14:00:00Z',
    createdAt: '2024-01-18T15:00:00Z',
    updatedAt: '2024-01-25T16:00:00Z'
  },
  {
    id: 'book-3',
    title: 'Office Move Downtown',
    description: 'Relocate office equipment and furniture to new downtown location',
    price: 750.00,
    status: 'OPEN',
    clientId: 'client-3',
    professionalId: 'move-1',
    serviceId: 'serv-7',
    requestedDate: '2024-02-20T09:00:00Z',
    scheduledAt: '2024-02-20T09:00:00Z',
    createdAt: '2024-01-22T11:00:00Z',
    updatedAt: '2024-01-22T11:00:00Z'
  },
  {
    id: 'book-4',
    title: 'Smart Lock Installation',
    description: 'Install smart locks on front and back doors',
    price: 200.00,
    status: 'ACCEPTED',
    clientId: 'client-4',
    professionalId: 'lock-1',
    serviceId: 'serv-9',
    requestedDate: '2024-02-18T13:00:00Z',
    scheduledAt: '2024-02-18T13:00:00Z',
    createdAt: '2024-01-24T16:00:00Z',
    updatedAt: '2024-01-24T16:30:00Z'
  },
  {
    id: 'book-5',
    title: 'Water Heater Replacement',
    description: 'Replace old water heater with new energy-efficient model',
    price: 650.00,
    status: 'COMPLETED',
    clientId: 'client-5',
    professionalId: 'plumb-2',
    serviceId: 'serv-5',
    requestedDate: '2024-01-30T11:00:00Z',
    scheduledAt: '2024-01-30T11:00:00Z',
    createdAt: '2024-01-26T14:00:00Z',
    updatedAt: '2024-01-30T15:00:00Z'
  }
];

export const mockReviews: Review[] = [
  {
    id: 'rev-1',
    rating: 5,
    comment: 'Excellent work! Mike was professional and completed the electrical panel upgrade quickly and efficiently. Highly recommend!',
    bookingId: 'book-1',
    clientId: 'client-1',
    professionalId: 'elec-1',
    createdAt: '2024-02-15T18:00:00Z'
  },
  {
    id: 'rev-2',
    rating: 4,
    comment: 'Sarah did a great job fixing the bathroom pipes. Quick response and fair pricing.',
    bookingId: 'book-2',
    clientId: 'client-2',
    professionalId: 'plumb-1',
    createdAt: '2024-01-25T18:00:00Z'
  },
  {
    id: 'rev-3',
    rating: 5,
    comment: 'Daniel was fantastic! The water heater installation was done perfectly and he explained everything clearly.',
    bookingId: 'book-5',
    clientId: 'client-5',
    professionalId: 'plumb-2',
    createdAt: '2024-01-30T17:00:00Z'
  }
];

export const mockMessages: Message[] = [
  {
    id: 'msg-1',
    content: 'Hi Sarah, I can start the electrical work tomorrow morning. Is 10 AM good for you?',
    senderId: 'elec-1',
    receiverId: 'client-1',
    bookingId: 'book-1',
    isRead: true,
    sentAt: '2024-01-20T16:00:00Z'
  },
  {
    id: 'msg-2',
    content: 'Perfect! 10 AM works great. I\'ll make sure someone is here to let you in.',
    senderId: 'client-1',
    receiverId: 'elec-1',
    bookingId: 'book-1',
    isRead: true,
    sentAt: '2024-01-20T16:15:00Z'
  },
  {
    id: 'msg-3',
    content: 'I\'m running about 15 minutes late due to traffic. Should be there by 2:15 PM.',
    senderId: 'plumb-1',
    receiverId: 'client-2',
    bookingId: 'book-2',
    isRead: true,
    sentAt: '2024-01-25T13:45:00Z'
  },
  {
    id: 'msg-4',
    content: 'No problem at all. Thanks for letting me know!',
    senderId: 'client-2',
    receiverId: 'plumb-1',
    bookingId: 'book-2',
    isRead: true,
    sentAt: '2024-01-25T13:50:00Z'
  }
];

export const mockFavorites: Favorite[] = [
  {
    serviceId: 'serv-1',
    userId: 'client-1',
    createdAt: '2024-01-20T12:00:00Z'
  },
  {
    serviceId: 'serv-4',
    userId: 'client-2',
    createdAt: '2024-01-18T14:00:00Z'
  }
];

export const mockDashboardStats: DashboardStats = {
  totalUsers: 25,
  totalProfessionals: 20,
  totalBookings: 156,
  totalRevenue: 67890.50,
  activeUsers: 234,
  completionRate: 92.5,
  userGrowth: 15.7,
  professionalGrowth: 8.3,
  bookingGrowth: 22.1,
  revenueGrowth: 18.9
};

export const mockRevenueData = [
  { month: 'Jan', revenue: 12000, bookings: 45, professionals: 12 },
  { month: 'Feb', revenue: 15000, bookings: 52, professionals: 15 },
  { month: 'Mar', revenue: 18000, bookings: 61, professionals: 18 },
  { month: 'Apr', revenue: 22000, bookings: 73, professionals: 22 },
  { month: 'May', revenue: 19000, bookings: 58, professionals: 19 },
  { month: 'Jun', revenue: 25000, bookings: 84, professionals: 25 }
];

export const mockNotifications: Notification[] = [
  {
    id: 'notif-1',
    type: 'success',
    title: 'New User Registration',
    description: 'A new user has registered on the platform',
    userName: 'Emily Davis',
    isRead: false,
    createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString() // 2 hours ago
  },
  {
    id: 'notif-2',
    type: 'info',
    title: 'Professional Application',
    description: 'A new professional has applied to join the platform',
    userName: 'Carlos Martinez',
    isRead: false,
    createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString() // 4 hours ago
  },
  {
    id: 'notif-3',
    type: 'warning',
    title: 'System Maintenance',
    description: 'Scheduled maintenance will occur tonight from 2:00 AM to 4:00 AM',
    isRead: true,
    createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString() // 6 hours ago
  },
  {
    id: 'notif-4',
    type: 'success',
    title: 'Professional Verification',
    description: 'Professional verification completed successfully',
    userName: 'Jennifer Allen',
    isRead: false,
    createdAt: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString() // 8 hours ago
  },
  {
    id: 'notif-5',
    type: 'info',
    title: 'Platform Update',
    description: 'New features have been deployed to the platform',
    isRead: true,
    createdAt: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString() // 12 hours ago
  },
  {
    id: 'notif-6',
    type: 'error',
    title: 'Payment Processing Issue',
    description: 'Payment gateway experiencing temporary issues',
    isRead: true,
    createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString() // 1 day ago
  },
  {
    id: 'notif-7',
    type: 'success',
    title: 'New User Registration',
    description: 'A new user has registered on the platform',
    userName: 'Michael Chen',
    isRead: true,
    createdAt: new Date(Date.now() - 36 * 60 * 60 * 1000).toISOString() // 1.5 days ago
  }
];