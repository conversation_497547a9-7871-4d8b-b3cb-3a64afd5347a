package com.yotelohago.media.api;

import com.yotelohago.common.ApiVersion;
import com.yotelohago.media.application.MediaManagementService;
import com.yotelohago.media.domain.MediaType;
import jakarta.annotation.security.RolesAllowed;
import jakarta.inject.Inject;
import jakarta.validation.Valid;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.Response;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * REST Resource for Media management
 * Provides endpoints for media upload, retrieval, and deletion
 */
@Path(ApiVersion.BASE + "/media")
@Produces(jakarta.ws.rs.core.MediaType.APPLICATION_JSON)
@Consumes(jakarta.ws.rs.core.MediaType.APPLICATION_JSON)
@RolesAllowed({"user", "professional", "admin"})
public class MediaResource {

    private static final Logger logger = LoggerFactory.getLogger(MediaResource.class);

    @Inject
    MediaManagementService mediaService;

    /**
     * Generate presigned URL for uploading media
     * POST /v1/media/presigned-url
     */
    @POST
    @Path("/presigned-url")
    @RolesAllowed({"user", "professional"})
    public Response generatePresignedUrl(@Valid PresignedUrlRequestDTO request) {
        try {
            logger.info("📤 Generating presigned URL for file: {} ({})", request.filename, request.mediaType);

            MediaType mediaType = MediaType.fromValue(request.mediaType);
            
            MediaManagementService.PresignedUrlResult result = mediaService.generatePresignedUrl(
                request.filename,
                request.contentType,
                request.fileSize,
                mediaType,
                request.associatedEntityId
            );

            PresignedUrlResponseDTO response = new PresignedUrlResponseDTO(
                result.uploadUrl,
                result.filePath,
                result.mediaId,
                result.expiresAt,
                result.maxFileSize,
                "Use PUT method to upload the file to the provided URL"
            );

            return Response.ok(response).build();

        } catch (IllegalArgumentException e) {
            logger.warn("⚠️ Invalid request for presigned URL: {}", e.getMessage());
            return Response.status(Response.Status.BAD_REQUEST)
                    .entity("{\"error\": \"" + e.getMessage() + "\"}")
                    .build();
        } catch (ForbiddenException e) {
            logger.warn("🚫 Forbidden request for presigned URL: {}", e.getMessage());
            return Response.status(Response.Status.FORBIDDEN)
                    .entity("{\"error\": \"" + e.getMessage() + "\"}")
                    .build();
        } catch (Exception e) {
            logger.error("❌ Failed to generate presigned URL", e);
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("{\"error\": \"Failed to generate presigned URL\"}")
                    .build();
        }
    }

    /**
     * Get media by ID
     * GET /v1/media/{mediaId}
     */
    @GET
    @Path("/{mediaId}")
    public Response getMediaById(@PathParam("mediaId") UUID mediaId) {
        try {
            Optional<MediaDTO> mediaDTOOpt = mediaService.findByIdWithUrl(mediaId);
            if (mediaDTOOpt.isEmpty()) {
                return Response.status(Response.Status.NOT_FOUND)
                        .entity("{\"error\": \"Media not found\"}")
                        .build();
            }

            return Response.ok(mediaDTOOpt.get()).build();

        } catch (ForbiddenException e) {
            return Response.status(Response.Status.FORBIDDEN)
                    .entity("{\"error\": \"" + e.getMessage() + "\"}")
                    .build();
        } catch (Exception e) {
            logger.error("❌ Failed to get media by ID: {}", mediaId, e);
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("{\"error\": \"Failed to retrieve media\"}")
                    .build();
        }
    }



    /**
     * Get media for a specific user (admin only or own media)
     * GET /v1/media/user/{userId}
     * Optional query parameter: mediaType (CLIENT_PROFILE_PICTURE, PROFESSIONAL_PROFILE_PICTURE, SERVICE_PHOTO, DOCUMENT)
     */
    @GET
    @Path("/user/{userId}")
    public Response getMediaByUserId(@PathParam("userId") UUID userId,
                                   @QueryParam("mediaType") String mediaTypeParam) {
        try {
            MediaType mediaType = null;
            if (mediaTypeParam != null && !mediaTypeParam.trim().isEmpty()) {
                try {
                    mediaType = MediaType.fromValue(mediaTypeParam);
                } catch (IllegalArgumentException e) {
                    return Response.status(Response.Status.BAD_REQUEST)
                            .entity("{\"error\": \"Invalid mediaType parameter. Valid values: CLIENT_PROFILE_PICTURE, PROFESSIONAL_PROFILE_PICTURE, SERVICE_PHOTO, DOCUMENT\"}")
                            .build();
                }
            }

            List<MediaDTO> mediaDTOs = mediaService.findByUserIdWithUrls(userId, mediaType);
            return Response.ok(mediaDTOs).build();

        } catch (ForbiddenException e) {
            return Response.status(Response.Status.FORBIDDEN)
                    .entity("{\"error\": \"" + e.getMessage() + "\"}")
                    .build();
        } catch (Exception e) {
            logger.error("❌ Failed to get media for user: {} with mediaType: {}", userId, mediaTypeParam, e);
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("{\"error\": \"Failed to retrieve media\"}")
                    .build();
        }
    }

    /**
     * Delete media by ID
     * DELETE /v1/media/{mediaId}
     */
    @DELETE
    @Path("/{mediaId}")
    public Response deleteMedia(@PathParam("mediaId") UUID mediaId) {
        try {
            boolean deleted = mediaService.deleteById(mediaId);
            if (!deleted) {
                return Response.status(Response.Status.NOT_FOUND)
                        .entity("{\"error\": \"Media not found\"}")
                        .build();
            }

            return Response.noContent().build();

        } catch (ForbiddenException e) {
            return Response.status(Response.Status.FORBIDDEN)
                    .entity("{\"error\": \"" + e.getMessage() + "\"}")
                    .build();
        } catch (Exception e) {
            logger.error("❌ Failed to delete media: {}", mediaId, e);
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("{\"error\": \"Failed to delete media\"}")
                    .build();
        }
    }
}
