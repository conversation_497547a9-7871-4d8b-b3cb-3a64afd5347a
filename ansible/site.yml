# site.yml - Modular playbook with tagged roles. Never tag ensure the role will not be executed if we don't specify the tag

#- name: Generate JCasC templates locally
#  hosts: local
#  gather_facts: false
#  tags:
#    - jcasc
#  roles:
#    - role: jcasc
#      tags: [ 'jcasc', 'never' ]
#
- name: Bootstra<PERSON> MacBook for hosting k3s
  hosts: remote
  gather_facts: true
  tags:
    - macos_k8s
  roles:
    - role: mac_k8s_on_premise
      tags: ['macos_k8s']


- name: Install k3s on Darwin MacOS
  hosts: darwin_arm64
  become: true
  gather_facts: true
  roles:
    - role: install_k3s
      tags: ['k3s']

- name: Install k3s on Red Hat EC2
  hosts: rhel_x86_64
  become: true
  gather_facts: true
  roles:
    - role: install_k3s
      tags: ['k3s']