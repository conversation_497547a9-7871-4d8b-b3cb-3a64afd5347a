package com.yotelohago.service.application;

import com.yotelohago.service.domain.ServiceCategory;
import com.yotelohago.service.domain.ServiceCategoryRepository;
import io.quarkus.cache.CacheInvalidate;
import io.quarkus.cache.CacheResult;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Service for managing ServiceCategory entities with caching support
 */
@ApplicationScoped
public class ServiceCategoryManagementService {

    private static final Logger logger = LoggerFactory.getLogger(ServiceCategoryManagementService.class);

    @Inject
    ServiceCategoryRepository repository;

    /**
     * Get all service categories (cached)
     * @return List of all service categories
     */
    @CacheResult(cacheName = "service-categories")
    public List<ServiceCategory> findAll() {
        logger.debug("🔍 Fetching all service categories from database");
        return repository.findAll();
    }

    /**
     * Find service category by ID (cached)
     * @param id The category ID
     * @return Optional containing the category if found
     */
    @CacheResult(cacheName = "service-details")
    public Optional<ServiceCategory> findById(UUID id) {
        logger.debug("🔍 Fetching service category by ID: {}", id);
        return repository.findById(id);
    }

    /**
     * Find service category by name (cached)
     * @param name The category name
     * @return Optional containing the category if found
     */
    @CacheResult(cacheName = "service-details")
    public Optional<ServiceCategory> findByName(String name) {
        logger.debug("🔍 Fetching service category by name: {}", name);
        return repository.findByName(name);
    }

    /**
     * Create a new service category
     * @param category The category to create
     * @return The created category
     */
    @Transactional
    @CacheInvalidate(cacheName = "service-categories")
    public ServiceCategory create(ServiceCategory category) {
        logger.info("📝 Creating new service category: {}", category.name);
        
        // Check if category with same name already exists
        if (repository.existsByName(category.name)) {
            throw new IllegalArgumentException("Service category with name '" + category.name + "' already exists");
        }
        
        repository.persist(category);
        repository.flush();
        
        logger.info("✅ Service category created successfully: {} ({})", category.name, category.id);
        return category;
    }

    /**
     * Update an existing service category
     * @param category The category to update
     * @return The updated category
     */
    @Transactional
    @CacheInvalidate(cacheName = "service-categories")
    @CacheInvalidate(cacheName = "service-details")
    public ServiceCategory update(ServiceCategory category) {
        logger.info("📝 Updating service category: {} ({})", category.name, category.id);
        
        // Verify category exists
        if (!repository.findById(category.id).isPresent()) {
            throw new IllegalArgumentException("Service category not found: " + category.id);
        }
        
        ServiceCategory updated = repository.update(category);
        logger.info("✅ Service category updated successfully: {}", category.name);
        return updated;
    }

    /**
     * Delete a service category by ID
     * @param id The category ID to delete
     * @return true if deleted, false if not found
     */
    @Transactional
    @CacheInvalidate(cacheName = "service-categories")
    @CacheInvalidate(cacheName = "service-details")
    public boolean delete(UUID id) {
        logger.info("🗑️ Deleting service category: {}", id);
        
        Optional<ServiceCategory> category = repository.findById(id);
        if (category.isEmpty()) {
            logger.warn("⚠️ Service category not found for deletion: {}", id);
            return false;
        }
        
        repository.deleteById(id);
        logger.info("✅ Service category deleted successfully: {}", id);
        return true;
    }

    /**
     * Check if a category exists by name
     * @param name The category name
     * @return true if category exists, false otherwise
     */
    public boolean existsByName(String name) {
        return repository.existsByName(name);
    }

    /**
     * Get total count of service categories
     * @return Total count
     */
    public long count() {
        return repository.count();
    }

    /**
     * Clear all service category caches
     * Useful for administrative operations
     */
    @CacheInvalidate(cacheName = "service-categories")
    @CacheInvalidate(cacheName = "service-details")
    public void clearCache() {
        logger.info("🧹 Clearing service category caches");
    }
}
