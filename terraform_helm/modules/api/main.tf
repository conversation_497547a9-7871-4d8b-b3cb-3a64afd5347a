terraform {
  required_providers {
    kubernetes = {
      source  = "hashicorp/kubernetes"
    }
    helm = {
      source  = "hashicorp/helm"
    }
  }
}


resource "kubernetes_namespace" "api" {
  metadata {
    name = "api"
  }
}

resource "kubernetes_secret" "docker_registry" {
  metadata {
    name      = "registry-auth-secret"
    namespace = kubernetes_namespace.api.metadata[0].name
  }

  type = "kubernetes.io/dockerconfigjson"

  data = {
    ".dockerconfigjson" = base64decode("************************************************************************************************************************************************************************************************************")
  }

  depends_on = [kubernetes_namespace.api]
}

resource "helm_release" "api" {
  name       = "api"
  chart      = path.module
  namespace  = kubernetes_namespace.api.metadata[0].name
  create_namespace = false

  depends_on = [kubernetes_secret.docker_registry]
}