package com.yotelohago.availability.api;

import com.yotelohago.availability.domain.DayTimeRange;
import com.yotelohago.availability.domain.WorkSchedule;
import jakarta.enterprise.context.ApplicationScoped;

import java.time.DayOfWeek;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Mapper for converting between WorkSchedule entities and DTOs.
 *
 * Handles the conversion between domain objects and API DTOs,
 * including the mapping of DayTimeRange objects to DayScheduleDTO and TimeRangeDTO.
 */
@ApplicationScoped
public class WorkScheduleMapper {

    /**
     * Convert WorkSchedule entity to DTO
     */
    public WorkScheduleDTO toDTO(WorkSchedule workSchedule) {
        if (workSchedule == null) {
            return null;
        }

        WorkScheduleDTO dto = new WorkScheduleDTO();
        dto.professionalId = workSchedule.getProfessionalId();
        dto.notes = workSchedule.getNotes();
        dto.createdAt = workSchedule.getCreatedAt();
        dto.updatedAt = workSchedule.getUpdatedAt();

        // Convert day schedules - ensure all 7 days are represented
        dto.daySchedules = convertTimeRangesToDayScheduleDTO(workSchedule.getTimeRanges());

        // Set computed fields for UI convenience
        dto.hasAnyActiveDays = workSchedule.hasAnyActiveDays();
        dto.totalWeeklyMinutes = workSchedule.getTotalWeeklyAvailableMinutes();
        dto.summary = workSchedule.getSummary();

        return dto;
    }

    /**
     * Convert DayTimeRange set to DayScheduleDTO list (ensuring all 7 days are represented)
     */
    private List<DayScheduleDTO> convertTimeRangesToDayScheduleDTO(Set<DayTimeRange> timeRanges) {
        // Group time ranges by day of week
        Map<DayOfWeek, List<DayTimeRange>> dayMap = timeRanges.stream()
                .collect(Collectors.groupingBy(DayTimeRange::getDayOfWeek));

        // Create DTOs for all 7 days of the week (Monday to Sunday)
        List<DayScheduleDTO> dtoList = new ArrayList<>();
        for (DayOfWeek day : DayOfWeek.values()) {
            List<DayTimeRange> dayTimeRanges = dayMap.getOrDefault(day, new ArrayList<>());
            dtoList.add(toDayScheduleDTO(day, dayTimeRanges));
        }

        return dtoList;
    }

    /**
     * Convert DayTimeRange list to DayScheduleDTO
     */
    private DayScheduleDTO toDayScheduleDTO(DayOfWeek dayOfWeek, List<DayTimeRange> dayTimeRanges) {
        if (dayTimeRanges == null || dayTimeRanges.isEmpty()) {
            // Return inactive day schedule
            return new DayScheduleDTO(dayOfWeek, new ArrayList<>(), false);
        }

        List<TimeRangeDTO> timeRangeDTOs = dayTimeRanges.stream()
                .sorted(Comparator.comparing(DayTimeRange::getStartTime))
                .map(this::dayTimeRangeToTimeRangeDTO)
                .collect(Collectors.toList());

        return new DayScheduleDTO(dayOfWeek, timeRangeDTOs, true);
    }

    /**
     * Convert DayTimeRange to TimeRangeDTO
     */
    private TimeRangeDTO dayTimeRangeToTimeRangeDTO(DayTimeRange dayTimeRange) {
        if (dayTimeRange == null) {
            return null;
        }
        return new TimeRangeDTO(dayTimeRange.getStartTime(), dayTimeRange.getEndTime());
    }

    /**
     * Update WorkSchedule entity from UpdateWorkScheduleRequest
     */
    public WorkSchedule updateEntity(WorkSchedule existingSchedule, UpdateWorkScheduleRequest request) {
        if (existingSchedule == null || request == null) {
            return existingSchedule;
        }

        // Update notes if provided
        if (request.notes != null) {
            existingSchedule.setNotes(request.notes.trim().isEmpty() ? null : request.notes);
        }

        // Update day schedules if provided
        if (request.daySchedules != null) {
            Set<DayTimeRange> newTimeRanges = convertDayScheduleDTOsToTimeRanges(request.daySchedules);
            existingSchedule.setTimeRanges(newTimeRanges);
        }

        return existingSchedule;
    }

    /**
     * Convert DTO list to DayTimeRange set
     */
    private Set<DayTimeRange> convertDayScheduleDTOsToTimeRanges(List<DayScheduleDTO> dayScheduleDTOs) {
        if (dayScheduleDTOs == null) {
            return new HashSet<>();
        }

        Set<DayTimeRange> timeRanges = new HashSet<>();
        for (DayScheduleDTO dto : dayScheduleDTOs) {
            if (dto != null && dto.dayOfWeek != null && dto.timeRanges != null) {
                for (TimeRangeDTO timeRangeDTO : dto.timeRanges) {
                    DayTimeRange dayTimeRange = timeRangeDTOToDayTimeRange(dto.dayOfWeek, timeRangeDTO);
                    if (dayTimeRange != null) {
                        timeRanges.add(dayTimeRange);
                    }
                }
            }
        }

        return timeRanges;
    }

    /**
     * Convert TimeRangeDTO to DayTimeRange
     */
    private DayTimeRange timeRangeDTOToDayTimeRange(DayOfWeek dayOfWeek, TimeRangeDTO dto) {
        if (dto == null || dto.startTime == null || dto.endTime == null || dayOfWeek == null) {
            return null;
        }

        try {
            return new DayTimeRange(dayOfWeek, dto.startTime, dto.endTime);
        } catch (IllegalArgumentException e) {
            // Invalid time range (e.g., not 5-minute granularity, start >= end)
            return null;
        }
    }

    /**
     * Create a new WorkSchedule from UpdateWorkScheduleRequest (for creation)
     */
    public WorkSchedule fromUpdateRequest(UpdateWorkScheduleRequest request, UUID professionalId) {
        if (request == null) {
            return null;
        }

        WorkSchedule workSchedule = new WorkSchedule();
        workSchedule.setProfessionalId(professionalId);
        workSchedule.setNotes(request.notes);

        if (request.daySchedules != null) {
            Set<DayTimeRange> timeRanges = convertDayScheduleDTOsToTimeRanges(request.daySchedules);
            workSchedule.setTimeRanges(timeRanges);
        }

        return workSchedule;
    }
}
