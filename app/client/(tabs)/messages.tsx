import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image, FlatList, StatusBar, RefreshControl, ActivityIndicator } from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import Colors from '../constants/Colors';
import { useMessaging } from '../../../contexts/MessagingContext';
import { useAuth } from '../../../contexts/AuthContext';
import { ConversationSummaryDTO } from '../../../types/api';
import { UserMode } from '../../../types/auth';
import { formatDistanceToNow } from '../../../utils/dateUtils';
import { AuthGuard } from '../../../components/auth';

export default function MessagesScreen() {
  const router = useRouter();
  const { state: authState } = useAuth();
  const { state: messagingState, loadConversations } = useMessaging();
  const [refreshing, setRefreshing] = useState(false);

  // Note: Conversations are automatically loaded by MessagingContext when user authenticates
  // No need to load them again here to avoid duplicate API calls

  // Handle pull to refresh
  const handleRefresh = async () => {
    if (!authState.isAuthenticated) return;

    setRefreshing(true);
    try {
      await loadConversations();
    } catch (error) {
      console.error('Failed to refresh conversations:', error);
    } finally {
      setRefreshing(false);
    }
  };

  // Navigate to chat screen
  const handleConversationPress = (conversation: ConversationSummaryDTO) => {
    router.push(`/client/chat/${conversation.bookingId}` as any);
  };

  const renderConversation = ({ item }: { item: ConversationSummaryDTO }) => {
    const timestamp = formatDistanceToNow(new Date(item.lastMessageTime));
    const hasUnread = item.unreadCount > 0;

    // In client mode, show professional name; in professional mode, show client name
    const participantName = authState.currentMode === UserMode.CLIENT
      ? item.professionalName
      : item.clientName;

    return (
      <TouchableOpacity
        style={styles.conversationItem}
        onPress={() => handleConversationPress(item)}
      >
        <View style={styles.avatar}>
          <Ionicons
            name="person-circle"
            size={50}
            color={Colors.text.secondary}
          />
        </View>
        <View style={styles.conversationInfo}>
          <View style={styles.conversationHeader}>
            <Text style={styles.providerName}>{participantName}</Text>
            <Text style={styles.timestamp}>{timestamp}</Text>
          </View>
          <View style={styles.messagePreview}>
            <Text
              style={[
                styles.lastMessage,
                hasUnread && styles.unreadMessage
              ]}
              numberOfLines={1}
            >
              {item.lastMessageFromCurrentUser ? 'You: ' : ''}{item.lastMessageContent}
            </Text>
            {hasUnread && (
              <View style={styles.unreadBadge}>
                <Text style={styles.unreadCount}>
                  {item.unreadCount > 99 ? '99+' : item.unreadCount}
                </Text>
              </View>
            )}
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={Colors.background.primary} />
      <View style={styles.header}>
        <Text style={styles.title}>Messages</Text>
      </View>

      <AuthGuard
        actionType="messaging"
        promptTitle="Start Messaging"
        promptMessage="Log in to message professionals and manage your conversations."
        tabIcon="chatbubble-outline"
      >
        {messagingState.isLoading && messagingState.conversations.length === 0 ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={Colors.primary} />
            <Text style={styles.loadingText}>Loading conversations...</Text>
          </View>
        ) : messagingState.conversations.length > 0 ? (
          <FlatList
            data={messagingState.conversations}
            renderItem={renderConversation}
            keyExtractor={(item) => item.bookingId.toString()}
            contentContainerStyle={styles.listContent}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={handleRefresh}
                colors={[Colors.primary]}
                tintColor={Colors.primary}
              />
            }
          />
        ) : (
          <View style={styles.emptyContainer}>
            <Ionicons name="chatbubble-outline" size={64} color={Colors.text.secondary} />
            <Text style={styles.emptyTitle}>No messages yet</Text>
            <Text style={styles.emptySubtitle}>
              When you book services, you can message professionals here
            </Text>
          </View>
        )}

        {messagingState.error && (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>{messagingState.error}</Text>
          </View>
        )}
      </AuthGuard>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.primary,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 30,
    paddingBottom: 16,
    paddingHorizontal: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: Colors.text.primary,
    flex: 1,
    textAlign: 'center',
  },
  listContent: {
    //backgroundColor: Colors.background.secondary,
    padding: 10,
    flex: 1,
  },
  conversationItem: {
    flexDirection: 'row',
    padding: 12,
    backgroundColor: Colors.background.primary,
    borderRadius: 12,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 12,
  },
  conversationInfo: {
    flex: 1,
    justifyContent: 'center',
  },
  conversationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  providerName: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text.primary,
  },
  timestamp: {
    fontSize: 12,
    color: Colors.text.secondary,
  },
  messagePreview: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  lastMessage: {
    flex: 1,
    fontSize: 14,
    color: Colors.text.secondary,
    marginRight: 8,
  },
  unreadMessage: {
    color: Colors.text.primary,
    fontWeight: '500',
  },
  unreadBadge: {
    backgroundColor: Colors.primary,
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 6,
  },
  unreadCount: {
    color: Colors.background.primary,
    fontSize: 12,
    fontWeight: '600',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text.primary,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 16,
    color: Colors.text.secondary,
    textAlign: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  loadingText: {
    fontSize: 16,
    color: Colors.text.secondary,
    marginTop: 12,
  },
  errorContainer: {
    backgroundColor: Colors.error + '20',
    padding: 12,
    margin: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.error + '40',
  },
  errorText: {
    color: Colors.error,
    fontSize: 14,
    textAlign: 'center',
  },
});