import { WebSocketMessage, MessageDTO, WebSocketConnectionState } from '../../types/api';
import { getWebSocketUrl } from '../api/messagingService';

/**
 * WebSocket Service for Real-time Messaging
 * Handles WebSocket connections, message sending, and real-time updates
 */

export type MessageHandler = (message: MessageDTO) => void;
export type TypingHandler = (bookingId: number, userIds: string[]) => void;
export type ConnectionHandler = (state: WebSocketConnectionState) => void;
export type ErrorHandler = (error: string) => void;

export interface WebSocketServiceConfig {
  bookingId: number;
  userId: string;
  onMessage?: MessageHandler;
  onTyping?: TypingHandler;
  onConnectionChange?: ConnectionHandler;
  onError?: ErrorHandler;
  reconnectInterval?: number;
  maxReconnectAttempts?: number;
}

export class WebSocketService {
  private ws: WebSocket | null = null;
  private config: WebSocketServiceConfig;
  private connectionState: WebSocketConnectionState;
  private reconnectTimer: NodeJS.Timeout | null = null;
  private pingTimer: NodeJS.Timeout | null = null;
  private typingTimer: NodeJS.Timeout | null = null;

  constructor(config: WebSocketServiceConfig) {
    this.config = {
      reconnectInterval: 3000,
      maxReconnectAttempts: 5,
      ...config
    };

    this.connectionState = {
      isConnected: false,
      isConnecting: false,
      error: null,
      lastConnected: null,
      reconnectAttempts: 0
    };
  }

  /**
   * Connect to WebSocket
   */
  public connect(): void {
    if (this.ws?.readyState === WebSocket.OPEN) {
      console.log('🔗 WebSocket already connected');
      return;
    }

    if (this.connectionState.isConnecting) {
      console.log('🔄 WebSocket connection already in progress');
      return;
    }

    this.updateConnectionState({
      isConnecting: true,
      error: null
    });

    try {
      const wsUrl = getWebSocketUrl(this.config.bookingId, this.config.userId);
      console.log('🔄 Connecting to WebSocket:', wsUrl);

      this.ws = new WebSocket(wsUrl);
      this.setupEventHandlers();
    } catch (error) {
      console.error('❌ Failed to create WebSocket connection:', error);
      this.handleConnectionError('Failed to create WebSocket connection');
    }
  }

  /**
   * Disconnect from WebSocket
   */
  public disconnect(): void {
    console.log('🔌 Disconnecting WebSocket...');
    
    this.clearTimers();
    
    if (this.ws) {
      this.ws.close(1000, 'Client disconnect');
      this.ws = null;
    }

    this.updateConnectionState({
      isConnected: false,
      isConnecting: false,
      error: null
    });
  }

  /**
   * Send a message through WebSocket
   */
  public sendMessage(receiverUserId: string, content: string): void {
    console.log('🚀 WebSocket sendMessage called:', { receiverUserId, content });
    console.log('🔍 WebSocket connection state:', {
      isConnected: this.isConnected(),
      readyState: this.ws?.readyState,
      userId: this.config.userId
    });

    if (!this.isConnected()) {
      console.error('❌ WebSocket not connected');
      throw new Error('WebSocket not connected');
    }

    const message: WebSocketMessage = {
      type: 'send_message',
      data: {
        senderId: this.config.userId,  // Use internal user ID
        receiverId: receiverUserId,    // Use internal user ID
        content
      }
    };

    console.log('📦 Sending WebSocket message:', message);
    this.sendWebSocketMessage(message);
    console.log('📤 Message sent via WebSocket');
  }

  /**
   * Mark message as read
   */
  public markAsRead(messageId: number): void {
    if (!this.isConnected()) {
      console.warn('⚠️ Cannot mark as read - WebSocket not connected');
      return;
    }

    const message: WebSocketMessage = {
      type: 'mark_read',
      data: { messageId }
    };

    this.sendWebSocketMessage(message);
  }

  /**
   * Send typing indicator
   */
  public sendTyping(isTyping: boolean): void {
    if (!this.isConnected()) {
      return;
    }

    // Clear existing typing timer
    if (this.typingTimer) {
      clearTimeout(this.typingTimer);
      this.typingTimer = null;
    }

    const message: WebSocketMessage = {
      type: 'typing',
      data: { 
        userId: this.config.userId,
        isTyping 
      }
    };

    this.sendWebSocketMessage(message);

    // Auto-stop typing after 3 seconds
    if (isTyping) {
      this.typingTimer = setTimeout(() => {
        this.sendTyping(false);
      }, 3000);
    }
  }

  /**
   * Check if WebSocket is connected
   */
  public isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN;
  }

  /**
   * Get current connection state
   */
  public getConnectionState(): WebSocketConnectionState {
    return { ...this.connectionState };
  }

  /**
   * Setup WebSocket event handlers
   */
  private setupEventHandlers(): void {
    if (!this.ws) return;

    this.ws.onopen = () => {
      console.log('✅ WebSocket connected');
      this.updateConnectionState({
        isConnected: true,
        isConnecting: false,
        error: null,
        lastConnected: new Date(),
        reconnectAttempts: 0
      });

      this.startPingTimer();
    };

    this.ws.onmessage = (event) => {
      try {
        const wsMessage: WebSocketMessage = JSON.parse(event.data);
        this.handleWebSocketMessage(wsMessage);
      } catch (error) {
        console.error('❌ Failed to parse WebSocket message:', error);
      }
    };

    this.ws.onclose = (event) => {
      console.log('🔌 WebSocket disconnected:', event.code, event.reason);
      this.clearTimers();
      
      this.updateConnectionState({
        isConnected: false,
        isConnecting: false
      });

      // Attempt reconnection if not a clean close
      if (event.code !== 1000 && this.connectionState.reconnectAttempts < this.config.maxReconnectAttempts!) {
        this.scheduleReconnect();
      }
    };

    this.ws.onerror = (error) => {
      console.error('❌ WebSocket error:', error);
      this.handleConnectionError('WebSocket connection error');
    };
  }

  /**
   * Handle incoming WebSocket messages
   */
  private handleWebSocketMessage(wsMessage: WebSocketMessage): void {
    console.log('📥 WebSocket message received:', wsMessage.type);

    switch (wsMessage.type) {
      case 'new_message':
        if (this.config.onMessage && wsMessage.data) {
          this.config.onMessage(wsMessage.data as MessageDTO);
        }
        break;

      case 'typing_indicator':
        if (this.config.onTyping && wsMessage.data) {
          const { bookingId, userIds } = wsMessage.data;
          this.config.onTyping(bookingId, userIds);
        }
        break;

      case 'pong':
        // Ping response - connection is alive
        break;

      case 'error':
        console.error('❌ WebSocket server error:', wsMessage.message);
        if (this.config.onError) {
          this.config.onError(wsMessage.message || 'Unknown server error');
        }
        break;

      default:
        console.warn('⚠️ Unknown WebSocket message type:', wsMessage.type);
    }
  }

  /**
   * Send WebSocket message
   */
  private sendWebSocketMessage(message: WebSocketMessage): void {
    console.log('🔌 sendWebSocketMessage called');
    console.log('📊 WebSocket state:', {
      exists: !!this.ws,
      readyState: this.ws?.readyState,
      isOpen: this.ws?.readyState === WebSocket.OPEN
    });

    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      console.error('❌ WebSocket not ready for sending');
      throw new Error('WebSocket not connected');
    }

    const messageStr = JSON.stringify(message);
    console.log('📡 Sending raw WebSocket message:', messageStr);
    this.ws.send(messageStr);
    console.log('✅ WebSocket message sent successfully');
  }

  /**
   * Update connection state and notify listeners
   */
  private updateConnectionState(updates: Partial<WebSocketConnectionState>): void {
    this.connectionState = { ...this.connectionState, ...updates };
    
    if (this.config.onConnectionChange) {
      this.config.onConnectionChange(this.connectionState);
    }
  }

  /**
   * Handle connection errors
   */
  private handleConnectionError(error: string): void {
    this.updateConnectionState({
      isConnected: false,
      isConnecting: false,
      error
    });

    if (this.config.onError) {
      this.config.onError(error);
    }
  }

  /**
   * Schedule reconnection attempt
   */
  private scheduleReconnect(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
    }

    const attempts = this.connectionState.reconnectAttempts + 1;
    const delay = Math.min(this.config.reconnectInterval! * Math.pow(2, attempts - 1), 30000);

    console.log(`🔄 Scheduling reconnect attempt ${attempts} in ${delay}ms`);

    this.updateConnectionState({
      reconnectAttempts: attempts
    });

    this.reconnectTimer = setTimeout(() => {
      console.log(`🔄 Reconnect attempt ${attempts}`);
      this.connect();
    }, delay);
  }

  /**
   * Start ping timer to keep connection alive
   */
  private startPingTimer(): void {
    this.pingTimer = setInterval(() => {
      if (this.isConnected()) {
        this.sendWebSocketMessage({ type: 'ping' });
      }
    }, 30000); // Ping every 30 seconds
  }

  /**
   * Clear all timers
   */
  private clearTimers(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }

    if (this.pingTimer) {
      clearInterval(this.pingTimer);
      this.pingTimer = null;
    }

    if (this.typingTimer) {
      clearTimeout(this.typingTimer);
      this.typingTimer = null;
    }
  }
}
