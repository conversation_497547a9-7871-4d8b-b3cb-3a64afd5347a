package com.yotelohago.booking.api;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.UUID;

/**
 * DTO that combines Booking data with Client, Professional and Service information
 * This eliminates N+1 queries by fetching all data in a single join query
 * Follows the same pattern as ServiceWithProfessionalDTO
 *
 * Includes:
 * - Booking core data
 * - Client information (always present)
 * - Professional information (null for OPEN bookings)
 * - Service information
 */
public class BookingWithDetailsDTO {
    
    // Booking fields
    public Long id;
    public UUID clientId;
    public UUID professionalId;
    public UUID serviceId;
    public String title;
    public String description;
    public Instant requestedDate;
    public Instant scheduledAt;
    public BigDecimal price;
    public String status;
    public Instant createdAt;
    public Instant updatedAt;
    
    // Professional fields (null for OPEN bookings without assigned professional)
    public String professionalFirstName;
    public String professionalLastName;
    public String professionalFullName; // Computed field
    public String professionalEmail;
    public String professionalCity;
    public String professionalProfileImageUrl;
    public Boolean professionalAvailable;

    // Client fields (always present since every booking has a client)
    public String clientFirstName;
    public String clientLastName;
    public String clientFullName; // Computed field
    public String clientEmail;
    public String clientProfileImageUrl;

    // Service fields
    public String serviceTitle;
    public String serviceDescription;
    public String serviceCategoryName;
    public BigDecimal servicePrice;

    // Default constructor
    public BookingWithDetailsDTO() {}

    // Constructor for JPA projection queries
    public BookingWithDetailsDTO(
            Long id, UUID clientId, UUID professionalId, UUID serviceId,
            String title, String description, Instant requestedDate, Instant scheduledAt,
            BigDecimal price, String status, Instant createdAt, Instant updatedAt,
            String professionalFirstName, String professionalLastName, String professionalEmail,
            String professionalCity, String professionalProfileImageUrl, Boolean professionalAvailable,
            String clientFirstName, String clientLastName, String clientEmail, String clientProfileImageUrl,
            String serviceTitle, String serviceDescription, String serviceCategoryName, BigDecimal servicePrice) {

        // Booking data
        this.id = id;
        this.clientId = clientId;
        this.professionalId = professionalId;
        this.serviceId = serviceId;
        this.title = title;
        this.description = description;
        this.requestedDate = requestedDate;
        this.scheduledAt = scheduledAt;
        this.price = price;
        this.status = status;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        
        // Professional data (may be null for OPEN bookings)
        this.professionalFirstName = professionalFirstName;
        this.professionalLastName = professionalLastName;
        this.professionalEmail = professionalEmail;
        this.professionalCity = professionalCity;
        this.professionalProfileImageUrl = professionalProfileImageUrl;
        this.professionalAvailable = professionalAvailable;

        // Client data (always present)
        this.clientFirstName = clientFirstName;
        this.clientLastName = clientLastName;
        this.clientEmail = clientEmail;
        this.clientProfileImageUrl = clientProfileImageUrl;

        // Service data
        this.serviceTitle = serviceTitle;
        this.serviceDescription = serviceDescription;
        this.serviceCategoryName = serviceCategoryName;
        this.servicePrice = servicePrice;

        // Compute full names
        this.professionalFullName = computeFullName(professionalFirstName, professionalLastName);
        this.clientFullName = computeFullName(clientFirstName, clientLastName);
    }

    /**
     * Compute professional full name from first and last name
     */
    private String computeFullName(String firstName, String lastName) {
        if (firstName == null && lastName == null) {
            return null; // No professional assigned yet
        }
        if (firstName == null) {
            return lastName;
        }
        if (lastName == null) {
            return firstName;
        }
        return firstName + " " + lastName;
    }

    /**
     * Convert to regular BookingDTO for backward compatibility
     */
    public BookingDTO toBookingDTO() {
        BookingDTO dto = new BookingDTO();
        dto.id = this.id;
        dto.clientId = this.clientId;
        dto.professionalId = this.professionalId;
        dto.serviceId = this.serviceId;
        dto.title = this.title;
        dto.description = this.description;
        dto.requestedDate = this.requestedDate;
        dto.scheduledAt = this.scheduledAt;
        dto.price = this.price;
        dto.status = this.status;
        dto.createdAt = this.createdAt;
        dto.updatedAt = this.updatedAt;
        return dto;
    }

    // Getters and setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public UUID getClientId() { return clientId; }
    public void setClientId(UUID clientId) { this.clientId = clientId; }

    public UUID getProfessionalId() { return professionalId; }
    public void setProfessionalId(UUID professionalId) { this.professionalId = professionalId; }

    public UUID getServiceId() { return serviceId; }
    public void setServiceId(UUID serviceId) { this.serviceId = serviceId; }

    public String getTitle() { return title; }
    public void setTitle(String title) { this.title = title; }

    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }

    public Instant getRequestedDate() { return requestedDate; }
    public void setRequestedDate(Instant requestedDate) { this.requestedDate = requestedDate; }

    public Instant getScheduledAt() { return scheduledAt; }
    public void setScheduledAt(Instant scheduledAt) { this.scheduledAt = scheduledAt; }

    public BigDecimal getPrice() { return price; }
    public void setPrice(BigDecimal price) { this.price = price; }

    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }

    public Instant getCreatedAt() { return createdAt; }
    public void setCreatedAt(Instant createdAt) { this.createdAt = createdAt; }

    public Instant getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(Instant updatedAt) { this.updatedAt = updatedAt; }

    public String getProfessionalFirstName() { return professionalFirstName; }
    public void setProfessionalFirstName(String professionalFirstName) { 
        this.professionalFirstName = professionalFirstName;
        this.professionalFullName = computeFullName(professionalFirstName, professionalLastName);
    }

    public String getProfessionalLastName() { return professionalLastName; }
    public void setProfessionalLastName(String professionalLastName) { 
        this.professionalLastName = professionalLastName;
        this.professionalFullName = computeFullName(professionalFirstName, professionalLastName);
    }

    public String getProfessionalFullName() { return professionalFullName; }

    public String getProfessionalEmail() { return professionalEmail; }
    public void setProfessionalEmail(String professionalEmail) { this.professionalEmail = professionalEmail; }

    public String getProfessionalCity() { return professionalCity; }
    public void setProfessionalCity(String professionalCity) { this.professionalCity = professionalCity; }

    public String getProfessionalProfileImageUrl() { return professionalProfileImageUrl; }
    public void setProfessionalProfileImageUrl(String professionalProfileImageUrl) { this.professionalProfileImageUrl = professionalProfileImageUrl; }

    public Boolean getProfessionalAvailable() { return professionalAvailable; }
    public void setProfessionalAvailable(Boolean professionalAvailable) { this.professionalAvailable = professionalAvailable; }

    public String getClientFirstName() { return clientFirstName; }
    public void setClientFirstName(String clientFirstName) {
        this.clientFirstName = clientFirstName;
        this.clientFullName = computeFullName(clientFirstName, clientLastName);
    }

    public String getClientLastName() { return clientLastName; }
    public void setClientLastName(String clientLastName) {
        this.clientLastName = clientLastName;
        this.clientFullName = computeFullName(clientFirstName, clientLastName);
    }

    public String getClientFullName() { return clientFullName; }

    public String getClientEmail() { return clientEmail; }
    public void setClientEmail(String clientEmail) { this.clientEmail = clientEmail; }

    public String getClientProfileImageUrl() { return clientProfileImageUrl; }
    public void setClientProfileImageUrl(String clientProfileImageUrl) { this.clientProfileImageUrl = clientProfileImageUrl; }

    public String getServiceTitle() { return serviceTitle; }
    public void setServiceTitle(String serviceTitle) { this.serviceTitle = serviceTitle; }

    public String getServiceDescription() { return serviceDescription; }
    public void setServiceDescription(String serviceDescription) { this.serviceDescription = serviceDescription; }

    public String getServiceCategoryName() { return serviceCategoryName; }
    public void setServiceCategoryName(String serviceCategoryName) { this.serviceCategoryName = serviceCategoryName; }

    public BigDecimal getServicePrice() { return servicePrice; }
    public void setServicePrice(BigDecimal servicePrice) { this.servicePrice = servicePrice; }
}
