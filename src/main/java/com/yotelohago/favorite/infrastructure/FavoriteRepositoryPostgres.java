package com.yotelohago.favorite.infrastructure;

import com.yotelohago.favorite.domain.Favorite;
import com.yotelohago.favorite.domain.FavoriteId;
import com.yotelohago.favorite.domain.FavoriteRepository;
import com.yotelohago.service.api.ServiceWithProfessionalDTO;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;
import org.eclipse.microprofile.config.inject.ConfigProperty;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@ApplicationScoped
public class FavoriteRepositoryPostgres implements FavoriteRepository {

    @PersistenceContext
    EntityManager entityManager;

    @ConfigProperty(name = "media.url-resolver")
    String mediaBaseUrl;

    @Override
    public void persist(Favorite favorite) {
        entityManager.persist(favorite);
    }

    @Override
    public Optional<Favorite> findByUserAndService(UUID userId, UUID serviceId) {
        FavoriteId id = new FavoriteId(userId, serviceId);
        Favorite favorite = entityManager.find(Favorite.class, id);
        return Optional.ofNullable(favorite);
    }

    @Override
    public List<UUID> findServiceIdsByUser(UUID userId) {
        String jpql = "SELECT f.service.id FROM Favorite f WHERE f.user.id = :userId ORDER BY f.createdAt DESC";
        TypedQuery<UUID> query = entityManager.createQuery(jpql, UUID.class);
        query.setParameter("userId", userId);
        return query.getResultList();
    }

    @Override
    public List<ServiceWithProfessionalDTO> findFavoriteServicesWithProfessionalByUser(UUID userId) {
        String jpql = String.format("""
            SELECT new com.yotelohago.service.api.ServiceWithProfessionalDTO(
                s.id, s.professionalUser.id, s.title, s.description, s.price,
                s.category.id, s.category.name, CAST(s.status AS string), s.createdAt, s.updatedAt,
                u.firstName, u.lastName, p.city, p.avgRating, p.ratingCount,
                p.available, p.bio,
                (SELECT CASE
                    WHEN pm.filePath IS NOT NULL
                    THEN CONCAT('%s', pm.filePath)
                    ELSE NULL
                 END
                 FROM Media pm
                 WHERE pm.user.id = u.id AND pm.mediaType = 'PROFESSIONAL_PROFILE_PICTURE'
                 ORDER BY pm.createdAt DESC LIMIT 1)
            )
            FROM Favorite f
            JOIN f.service s
            JOIN Professional p ON s.professionalUser.id = p.user.id
            JOIN p.user u
            WHERE f.user.id = :userId
            AND s.status = 'PUBLISHED'
            ORDER BY f.createdAt DESC
            """, mediaBaseUrl);

        TypedQuery<ServiceWithProfessionalDTO> query = entityManager.createQuery(jpql, ServiceWithProfessionalDTO.class);
        query.setParameter("userId", userId);
        return query.getResultList();
    }

    @Override
    public boolean isFavorited(UUID userId, UUID serviceId) {
        String jpql = "SELECT COUNT(f) FROM Favorite f WHERE f.user.id = :userId AND f.service.id = :serviceId";
        TypedQuery<Long> query = entityManager.createQuery(jpql, Long.class);
        query.setParameter("userId", userId);
        query.setParameter("serviceId", serviceId);
        return query.getSingleResult() > 0;
    }

    @Override
    public boolean removeByUserAndService(UUID userId, UUID serviceId) {
        FavoriteId id = new FavoriteId(userId, serviceId);
        Favorite favorite = entityManager.find(Favorite.class, id);
        if (favorite != null) {
            entityManager.remove(favorite);
            return true;
        }
        return false;
    }

    @Override
    public long countByUser(UUID userId) {
        String jpql = "SELECT COUNT(f) FROM Favorite f WHERE f.user.id = :userId";
        TypedQuery<Long> query = entityManager.createQuery(jpql, Long.class);
        query.setParameter("userId", userId);
        return query.getSingleResult();
    }

    @Override
    public int removeAllByUser(UUID userId) {
        String jpql = "DELETE FROM Favorite f WHERE f.user.id = :userId";
        return entityManager.createQuery(jpql)
                .setParameter("userId", userId)
                .executeUpdate();
    }

    @Override
    public int removeAllByService(UUID serviceId) {
        String jpql = "DELETE FROM Favorite f WHERE f.service.id = :serviceId";
        return entityManager.createQuery(jpql)
                .setParameter("serviceId", serviceId)
                .executeUpdate();
    }

    @Override
    public void flush() {
        entityManager.flush();
    }
}
