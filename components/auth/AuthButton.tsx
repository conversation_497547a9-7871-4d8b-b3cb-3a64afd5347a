import React, { useState } from 'react';
import { TouchableOpacity, Text, StyleSheet, View, Platform } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../../contexts/AuthContext';
import { UserMode } from '../../types/auth';
import crossPlatformStorage from '../../services/storage/crossPlatformStorage';
import CustomAuthModal from './CustomAuthModal';

interface AuthButtonProps {
  style?: any;
  size?: 'small' | 'medium' | 'large';
  variant?: 'primary' | 'secondary' | 'minimal';
  showText?: boolean;
  onLoginSuccess?: () => void;
  onLogoutSuccess?: () => void;
}

export default function AuthButton({
  style,
  size = 'medium',
  variant = 'primary',
  showText = true,
  onLoginSuccess,
  onLogoutSuccess,
}: AuthButtonProps) {
  const { state: authState, login, logout } = useAuth();
  const [showAuthModal, setShowAuthModal] = useState(false);

  const handlePress = async () => {
    try {
      if (authState.isAuthenticated) {
        await logout();
        onLogoutSuccess?.();
      } else {
        // Store current path before login for redirect after auth
        try {
          const currentPath = crossPlatformStorage.getCurrentPath();
          await crossPlatformStorage.setSessionItem('auth_redirect_path', currentPath);
          console.log('🔄 Storing redirect path:', currentPath);
        } catch (error) {
          console.error('Failed to store redirect path:', error);
        }

        // Show custom auth modal for both mobile and web
        setShowAuthModal(true);
      }
    } catch (error) {
      console.error('Auth action failed:', error);
    }
  };

  const handleAuthModalSuccess = () => {
    setShowAuthModal(false);
    onLoginSuccess?.();
  };

  const getButtonStyle = () => {
    const baseStyle = [styles.button, styles[size]];

    // Simple: Black buttons for professional mode, coral for client mode
    if (authState.currentMode === UserMode.PROFESSIONAL) {
      return [...baseStyle, styles.professionalButton];
    }
    return [...baseStyle, styles.clientButton];
  };

  const getTextStyle = () => {
    const baseStyle = [styles.buttonText, styles[`${size}Text`]];

    // Simple: White text for both modes (works on both black and coral backgrounds)
    return [...baseStyle, { color: 'white' }];
  };

  const getIconSize = () => {
    switch (size) {
      case 'small':
        return 16;
      case 'medium':
        return 20;
      case 'large':
        return 24;
      default:
        return 20;
    }
  };

  const getIconColor = () => {
    // Simple: White icons for both modes (works on both black and coral backgrounds)
    return 'white';
  };

  if (authState.isLoading) {
    return (
      <TouchableOpacity style={[getButtonStyle(), style]} disabled>
        <Ionicons name="refresh" size={getIconSize()} color={getIconColor()} />
        {showText && <Text style={getTextStyle()}>Loading...</Text>}
      </TouchableOpacity>
    );
  }

  return (
    <>
      <TouchableOpacity
        style={[getButtonStyle(), style]}
        onPress={handlePress}
        disabled={authState.isLoading}
      >
        <Ionicons
          name={authState.isAuthenticated ? "log-out" : "log-in"}
          size={getIconSize()}
          color={getIconColor()}
        />
        {showText && (
          <Text style={getTextStyle()}>
            {authState.isAuthenticated ? "Logout" : "Login"}
          </Text>
        )}
      </TouchableOpacity>

      {/* Custom Auth Modal for Mobile and Web */}
      <CustomAuthModal
        visible={showAuthModal}
        onClose={() => setShowAuthModal(false)}
        onSuccess={handleAuthModalSuccess}
        title="Welcome Back"
        subtitle="Sign in to your account"
      />
    </>
  );
}

const styles = StyleSheet.create({
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,
    gap: 8,
  },
  
  // Size variants
  small: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    gap: 4,
  },
  medium: {
    paddingHorizontal: 16,
    paddingVertical: 10,
    gap: 6,
  },
  large: {
    paddingHorizontal: 20,
    paddingVertical: 14,
    gap: 8,
  },
  
  // Simple button variants
  professionalButton: {
    backgroundColor: '#000000', // Black for professional mode
  },
  clientButton: {
    backgroundColor: '#FF5A5F', // Coral for client mode
  },
  
  // Text styles
  buttonText: {
    fontWeight: '600',
  },
  smallText: {
    fontSize: 12,
  },
  mediumText: {
    fontSize: 14,
  },
  largeText: {
    fontSize: 16,
  },
  
  // Simple text style - white text for both modes
  buttonTextColor: {
    color: 'white',
  },
});
