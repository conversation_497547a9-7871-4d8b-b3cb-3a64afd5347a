package com.yotelohago.user.domain;

import io.quarkus.hibernate.orm.panache.PanacheEntityBase;
import org.hibernate.annotations.CreationTimestamp;
import jakarta.persistence.*;

import java.time.Instant;
import java.util.Objects;
import java.util.UUID;

@Entity
@Table(name = "user_external_identities",
       uniqueConstraints = @UniqueConstraint(columnNames = {"provider", "external_id"}),
       indexes = {
           @Index(name = "idx_user_external_identities_user_id", columnList = "user_id"),
           @Index(name = "idx_user_external_identities_provider_external_id", columnList = "provider, external_id")
       })
public class UserExternalIdentity extends PanacheEntityBase {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(nullable = false, updatable = false)
    public UUID id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false, foreignKey = @ForeignKey(name = "fk_user_external_identity_user"))
    public User user;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 50)
    public AuthProvider provider;

    @Column(name = "external_id", nullable = false)
    public String externalId;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    public Instant createdAt;

    public UserExternalIdentity() {
        // JPA constructor
    }

    public UserExternalIdentity(User user, AuthProvider provider, String externalId) {
        this.user = user;
        this.provider = provider;
        this.externalId = externalId;
        // createdAt will be set automatically by @CreationTimestamp
    }

    // Getters and setters
    public UUID getId() { return id; }
    public User getUser() { return user; }
    public UUID getUserId() { return user != null ? user.id : null; }
    public AuthProvider getProvider() { return provider; }
    public String getExternalId() { return externalId; }
    public Instant getCreatedAt() { return createdAt; }

    public void setId(UUID id) { this.id = id; }
    public void setUser(User user) { this.user = user; }
    public void setProvider(AuthProvider provider) { this.provider = provider; }
    public void setExternalId(String externalId) { this.externalId = externalId; }
    public void setCreatedAt(Instant createdAt) { this.createdAt = createdAt; }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        UserExternalIdentity that = (UserExternalIdentity) o;

        // Use primary key for equality if both entities are persisted
        if (id != null && that.id != null) {
            return Objects.equals(id, that.id);
        }

        // Fall back to business key equality for non-persisted entities
        return Objects.equals(provider, that.provider) &&
               Objects.equals(externalId, that.externalId);
    }

    @Override
    public int hashCode() {
        // Use primary key for hash if entity is persisted
        if (id != null) {
            return Objects.hash(id);
        }

        // Fall back to business key hash for non-persisted entities
        return Objects.hash(provider, externalId);
    }

    @Override
    public String toString() {
        return "UserExternalIdentity{" +
                "id=" + id +
                ", userId=" + getUserId() +
                ", provider=" + provider +
                ", externalId='" + externalId + '\'' +
                ", createdAt=" + createdAt +
                '}';
    }
}
