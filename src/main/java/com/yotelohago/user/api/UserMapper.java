package com.yotelohago.user.api;

import com.yotelohago.user.domain.User;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

@ApplicationScoped
public class UserMapper {

    @PersistenceContext
    EntityManager entityManager; // CDI pattern like we use on ServiceMapper class

    /**
     * Convert User entity to basic UserWithDetailsDTO (for backwards compatibility)
     * This method only populates basic user fields, not professional data or media URLs
     */
    public UserWithDetailsDTO toDTO(User user) {
        UserWithDetailsDTO dto = new UserWithDetailsDTO();
        dto.id = user.getId();
        dto.username = user.getUsername();
        dto.firstName = user.getFirstName();
        dto.lastName = user.getLastName();
        dto.email = user.getEmail();
        dto.phone = user.getPhone();
        dto.isProfessional = user.isProfessional();
        dto.createdAt = user.getCreatedAt();
        dto.updatedAt = user.getUpdatedAt();
        dto.fullName = user.getFirstName() + " " + user.getLastName();
        return dto;
    }

    /**
     * Convert User entity to enhanced UserWithDetailsDTO with professional data and media URLs
     * This method is used for the /users/me endpoint to provide complete profile information
     */
    public UserWithDetailsDTO toEnhancedDTO(User user, String bio, String city, Double avgRating,
                                Integer ratingCount, Boolean available,
                                String clientProfilePhotoUrl, String professionalProfilePhotoUrl) {
        UserWithDetailsDTO dto = new UserWithDetailsDTO();

        // Basic user data
        dto.id = user.getId();
        dto.username = user.getUsername();
        dto.firstName = user.getFirstName();
        dto.lastName = user.getLastName();
        dto.email = user.getEmail();
        dto.phone = user.getPhone();
        dto.isProfessional = user.isProfessional();
        dto.createdAt = user.getCreatedAt();
        dto.updatedAt = user.getUpdatedAt();
        dto.fullName = user.getFirstName() + " " + user.getLastName();

        // Professional data (only if user is professional)
        dto.bio = bio;
        dto.city = city;
        dto.avgRating = avgRating;
        dto.ratingCount = ratingCount;
        dto.available = available != null ? available : false;

        // Media URLs
        dto.clientProfilePhotoUrl = clientProfilePhotoUrl;
        dto.professionalProfilePhotoUrl = professionalProfilePhotoUrl;

        return dto;
    }

    public User toEntity(UserWithDetailsDTO dto) {
        return new User(
                dto.id,
                dto.username,
                dto.firstName,
                dto.lastName,
                dto.email,
                dto.phone,
                dto.isProfessional,
                dto.createdAt,
                dto.updatedAt
        );
    }
}