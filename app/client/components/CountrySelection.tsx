import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, FlatList } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Colors from '../constants/Colors';

// Mock countries data - replace with real data
const COUNTRIES = [
  { id: '1', name: 'United States', code: 'US' },
  { id: '2', name: 'Canada', code: 'CA' },
  { id: '3', name: 'United Kingdom', code: 'GB' },
  { id: '4', name: 'Australia', code: 'AU' },
  { id: '5', name: 'Germany', code: 'DE' },
  { id: '6', name: 'France', code: 'FR' },
  { id: '7', name: 'Spain', code: 'ES' },
  { id: '8', name: 'Italy', code: 'IT' },
  { id: '9', name: 'Japan', code: 'JP' },
  { id: '10', name: 'South Korea', code: 'KR' },
];

interface CountrySelectionProps {
  onSelectCountry: (country: { id: string; name: string; code: string }) => void;
}

export default function CountrySelection({ onSelectCountry }: CountrySelectionProps) {
  const renderCountry = ({ item }: { item: typeof COUNTRIES[0] }) => (
    <TouchableOpacity
      style={styles.countryItem}
      onPress={() => onSelectCountry(item)}
    >
      <Text style={styles.countryName}>{item.name}</Text>
      <Ionicons name="chevron-forward" size={20} color={Colors.text.secondary} />
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <FlatList
        data={COUNTRIES}
        renderItem={renderCountry}
        keyExtractor={(item) => item.id}
        showsVerticalScrollIndicator={false}
        ItemSeparatorComponent={() => <View style={styles.separator} />}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  countryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    paddingHorizontal: 4,
  },
  countryName: {
    fontSize: 16,
    color: Colors.text.primary,
  },
  separator: {
    height: 1,
    backgroundColor: Colors.border,
  },
}); 