terraform {
  required_providers {
    postgresql = {
      source  = "jbg/postgresql"
      version = "~> 1.19.0"
    }
  }
}

provider "aws" {
  region = "us-east-1"
}

provider "kubernetes" {
  config_path = local.k8s_context_path
}

provider "helm" {
  kubernetes {
    config_path = local.k8s_context_path
  }
}

provider "postgresql" {
  host            = "127.0.0.1"
  port            = "5432"
  database        = "postgres"
  username        = "postgres"
  password        = "5HKhoc51bvKkX9BhtvbQEqJ8tHvoAuq5U3bMNwpXqugzf7f5dRYZelqaEt2LdWFa"
  sslmode         = "disable"
  connect_timeout = 15
  superuser = true
}


