---

- name: Get EC2 internal and public IPs
  shell: |
    echo INTERNAL=$(hostname -I | awk '{print $1}')
    echo PUBLIC=$(curl -s http://***************/latest/meta-data/public-ipv4)
  register: ip_vars
  changed_when: false

- name: Set facts for internal/public IPs
  set_fact:
    internal_ip: "{{ ip_vars.stdout_lines[0].split('=')[1] }}"
    public_ip: "{{ ip_vars.stdout_lines[1].split('=')[1] }}"

- name: Install required networking tools for K3s
  package:
    name:
      - iptables
      - iproute
      - iptables-nft
    state: present

- name: Disable NetworkManager cloud setup to avoid K3s conflict
  systemd:
    name: nm-cloud-setup.service
    enabled: no
    state: stopped

- name: Install K3s (single node)
  shell: |
    curl -sfL https://get.k3s.io | INSTALL_K3S_EXEC="--tls-san {{ public_ip }}" sh -
  args:
    creates: /etc/rancher/k3s/k3s.yaml

- name: Wait for /etc/rancher/k3s/k3s.yaml to be created
  wait_for:
    path: /etc/rancher/k3s/k3s.yaml
    timeout: 120

- name: Create directory on Red Hat systems
  ansible.builtin.file:
    path: /home/<USER>/.kube
    state: directory
    mode: '0755'  # Optional: Set permissions (rwxr-xr-x)
    owner: ec2-user    # Optional: Set owner
    group: ec2-user    # Optional: Set group

- name: Copy kubeconfig to user home
  copy:
    src: /etc/rancher/k3s/k3s.yaml
    dest: /home/<USER>/.kube/config
    remote_src: true
    owner: ec2-user
    group: ec2-user
    mode: '0600'

- name: Patch kubeconfig server URL to use public IP
  replace:
    path: /home/<USER>/.kube/config
    regexp: 'server: https://.*:6443'
    replace: 'server: https://{{ public_ip }}:6443'
  become_user: ec2-user

- name: Copy kubeconfig to local machine
  fetch:
    src: /home/<USER>/.kube/config
    dest: ../config/ansible_kubeconfig-ec2-k3s.generated
    flat: true

- name: Display K3s access instructions
  debug:
    msg: |
      ✅ K3s is now running on EC2
      To access it remotely:
        export KUBECONFIG=./config/ansible_kubeconfig-ec2-k3s.generated && kubectl get all -A
