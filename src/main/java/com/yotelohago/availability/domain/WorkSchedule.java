package com.yotelohago.availability.domain;

import io.quarkus.hibernate.orm.panache.PanacheEntityBase;
import jakarta.persistence.*;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import com.yotelohago.user.domain.User;

import java.time.DayOfWeek;
import java.time.Instant;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Work Schedule entity representing a professional's weekly availability pattern.
 * 
 * This is the source of truth for professional availability. It represents
 * recurring weekly schedules that professionals set once and apply to all future weeks.
 * 
 * Key design principles:
 * - One WorkSchedule per professional (1:1 relationship)
 * - Maximum 7 days with time ranges (one per day of week)
 * - Zero flag logic: presence of time ranges = active day
 * - 5-minute granularity enforced through DayTimeRange
 * - Automatic merging of overlapping time ranges
 * 
 * This approach is superior to individual time slots because:
 * - Professionals think in weekly patterns, not individual appointments
 * - Minimal data storage (7 rows max vs potentially thousands)
 * - Simple UI binding (7-row form for days of week)
 * - Easy maintenance (update once, applies to all future weeks)
 */
@Entity
@Table(name = "work_schedules")
public class WorkSchedule extends PanacheEntityBase {

    @Id
    @Column(name = "professional_id")
    public UUID professionalId;

    @OneToOne(fetch = FetchType.LAZY, optional = false)
    @MapsId
    @JoinColumn(name = "professional_id", nullable = false,
                foreignKey = @ForeignKey(name = "fk_work_schedules_professional"))
    public User professional;

    @ElementCollection(fetch = FetchType.EAGER)
    @CollectionTable(
        name = "work_schedule_time_ranges",
        joinColumns = @JoinColumn(name = "work_schedule_id"),
        indexes = {
            @Index(name = "idx_work_schedule_time_ranges_schedule_id", columnList = "work_schedule_id"),
            @Index(name = "idx_work_schedule_time_ranges_day", columnList = "day_of_week")
        }
    )
    @AttributeOverrides({
        @AttributeOverride(name = "dayOfWeek", column = @Column(name = "day_of_week")),
        @AttributeOverride(name = "startTime", column = @Column(name = "start_time")),
        @AttributeOverride(name = "endTime", column = @Column(name = "end_time"))
    })
    private Set<DayTimeRange> timeRanges = new HashSet<>();

    @Column(columnDefinition = "TEXT")
    public String notes;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    public Instant createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at")
    public Instant updatedAt;

    // JPA constructor
    public WorkSchedule() {}

    public WorkSchedule(User professional) {
        this.professional = professional;
        this.professionalId = professional.id;
        this.timeRanges = new HashSet<>();
    }

    public WorkSchedule(User professional, String notes) {
        this.professional = professional;
        this.professionalId = professional.id;
        this.notes = notes;
        this.timeRanges = new HashSet<>();
    }

    public WorkSchedule(UUID professionalId) {
        this.professionalId = professionalId;
        this.timeRanges = new HashSet<>();
    }

    // Getters
    public UUID getProfessionalId() { return professionalId; }
    public User getProfessional() { return professional; }
    public String getNotes() { return notes; }
    public Instant getCreatedAt() { return createdAt; }
    public Instant getUpdatedAt() { return updatedAt; }

    /**
     * Get all time ranges
     */
    public Set<DayTimeRange> getTimeRanges() {
        return new HashSet<>(timeRanges);
    }



    // Setters
    public void setProfessionalId(UUID professionalId) { this.professionalId = professionalId; }
    public void setProfessional(User professional) {
        this.professional = professional;
        this.professionalId = professional != null ? professional.id : null;
    }
    public void setNotes(String notes) { this.notes = notes; }
    public void setCreatedAt(Instant createdAt) { this.createdAt = createdAt; }
    public void setUpdatedAt(Instant updatedAt) { this.updatedAt = updatedAt; }

    /**
     * Set all time ranges (replaces existing ones)
     */
    public void setTimeRanges(Set<DayTimeRange> timeRanges) {
        this.timeRanges.clear();
        if (timeRanges != null) {
            this.timeRanges.addAll(timeRanges);
            // Merge overlapping ranges
            this.timeRanges = mergeOverlappingRanges(this.timeRanges);
        }
    }





    // Business logic methods

    /**
     * Get time ranges for a specific day of week
     */
    public List<DayTimeRange> getTimeRangesForDay(DayOfWeek dayOfWeek) {
        return timeRanges.stream()
                .filter(dtr -> dtr.getDayOfWeek().equals(dayOfWeek))
                .sorted(Comparator.comparing(DayTimeRange::getStartTime))
                .collect(Collectors.toList());
    }

    /**
     * Set time ranges for a specific day of week
     */
    public void setTimeRangesForDay(DayOfWeek dayOfWeek, List<DayTimeRange> dayTimeRanges) {
        if (dayOfWeek == null) return;

        // Remove existing ranges for this day
        timeRanges.removeIf(dtr -> dtr.getDayOfWeek().equals(dayOfWeek));

        // Add new ranges if provided
        if (dayTimeRanges != null && !dayTimeRanges.isEmpty()) {
            // Validate all ranges are for the correct day
            for (DayTimeRange range : dayTimeRanges) {
                if (range != null && range.getDayOfWeek().equals(dayOfWeek)) {
                    timeRanges.add(range);
                }
            }
            // Merge overlapping ranges
            timeRanges = mergeOverlappingRanges(timeRanges);
        }
    }

    /**
     * Add time range to a specific day
     */
    public void addTimeRange(DayOfWeek dayOfWeek, LocalTime startTime, LocalTime endTime) {
        DayTimeRange newRange = new DayTimeRange(dayOfWeek, startTime, endTime);
        timeRanges.add(newRange);
        // Merge overlapping ranges
        timeRanges = mergeOverlappingRanges(timeRanges);
    }

    /**
     * Add time range to a specific day using string times
     */
    public void addTimeRange(DayOfWeek dayOfWeek, String startTime, String endTime) {
        addTimeRange(dayOfWeek, LocalTime.parse(startTime), LocalTime.parse(endTime));
    }

    /**
     * Remove all time ranges for a specific day (makes it inactive)
     */
    public void clearDay(DayOfWeek dayOfWeek) {
        timeRanges.removeIf(dtr -> dtr.getDayOfWeek().equals(dayOfWeek));
    }

    /**
     * Check if professional is available on a specific day
     */
    public boolean isAvailableOn(DayOfWeek dayOfWeek) {
        return timeRanges.stream().anyMatch(dtr -> dtr.getDayOfWeek().equals(dayOfWeek));
    }

    /**
     * Check if professional is available on a specific day at a specific time
     */
    public boolean isAvailableAt(DayOfWeek dayOfWeek, LocalTime time) {
        return timeRanges.stream()
                .filter(dtr -> dtr.getDayOfWeek().equals(dayOfWeek))
                .anyMatch(dtr -> dtr.contains(time));
    }

    /**
     * Get all active days (days with time ranges)
     */
    public Set<DayOfWeek> getActiveDays() {
        return timeRanges.stream()
                        .map(DayTimeRange::getDayOfWeek)
                        .collect(Collectors.toSet());
    }

    /**
     * Get total weekly available hours
     */
    public long getTotalWeeklyAvailableMinutes() {
        return timeRanges.stream()
                        .mapToLong(DayTimeRange::getDurationMinutes)
                        .sum();
    }

    /**
     * Check if the work schedule has any active days
     */
    public boolean hasAnyActiveDays() {
        return !timeRanges.isEmpty();
    }

    /**
     * Get a summary of the work schedule for display
     */
    public String getSummary() {
        if (!hasAnyActiveDays()) {
            return "No availability set";
        }

        List<String> activeDays = getActiveDays().stream()
                                                .sorted()
                                                .map(day -> day.toString().substring(0, 3)) // Mon, Tue, etc.
                                                .collect(Collectors.toList());

        return String.format("Available: %s (%d hours/week)", 
                           String.join(", ", activeDays),
                           getTotalWeeklyAvailableMinutes() / 60);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        WorkSchedule that = (WorkSchedule) o;
        return Objects.equals(professionalId, that.professionalId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(professionalId);
    }

    /**
     * Merge overlapping and adjacent time ranges for the same day
     */
    private Set<DayTimeRange> mergeOverlappingRanges(Set<DayTimeRange> ranges) {
        if (ranges == null || ranges.isEmpty()) {
            return new HashSet<>();
        }

        // Group by day of week
        Map<DayOfWeek, List<DayTimeRange>> dayGroups = ranges.stream()
                .collect(Collectors.groupingBy(DayTimeRange::getDayOfWeek));

        Set<DayTimeRange> merged = new HashSet<>();

        for (Map.Entry<DayOfWeek, List<DayTimeRange>> entry : dayGroups.entrySet()) {
            List<DayTimeRange> dayRanges = entry.getValue();

            // Sort by start time
            dayRanges.sort(Comparator.comparing(DayTimeRange::getStartTime));

            if (dayRanges.isEmpty()) continue;

            DayTimeRange current = dayRanges.get(0);

            for (int i = 1; i < dayRanges.size(); i++) {
                DayTimeRange next = dayRanges.get(i);

                if (current.canMergeWith(next)) {
                    // Merge the ranges
                    current = current.mergeWith(next);
                } else {
                    // No overlap, add current and move to next
                    merged.add(current);
                    current = next;
                }
            }

            // Add the last range
            merged.add(current);
        }

        return merged;
    }

    @Override
    public String toString() {
        return "WorkSchedule{" +
                "professionalId=" + professionalId +
                ", activeDays=" + getActiveDays().size() +
                ", totalMinutes=" + getTotalWeeklyAvailableMinutes() +
                '}';
    }
}
