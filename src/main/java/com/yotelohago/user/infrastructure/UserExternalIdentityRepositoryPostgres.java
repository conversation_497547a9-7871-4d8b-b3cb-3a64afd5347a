package com.yotelohago.user.infrastructure;

import com.yotelohago.user.domain.AuthProvider;
import com.yotelohago.user.domain.UserExternalIdentity;
import com.yotelohago.user.domain.UserExternalIdentityRepository;
import com.yotelohago.user.domain.AuthProvider;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@ApplicationScoped
public class UserExternalIdentityRepositoryPostgres implements UserExternalIdentityRepository {

    @PersistenceContext
    EntityManager entityManager;

    @Override
    public void persist(UserExternalIdentity identity) {
        entityManager.persist(identity);
    }

    /**
     * Attempt to insert user external identity using ON CONFLICT DO NOTHING.
     * Returns true if insert succeeded (we own the identity), false if conflict occurred.
     */
    public boolean tryInsertIdentity(UUID identityId, UUID userId, AuthProvider provider, String externalId, Instant createdAt) {
        String sql = """
            INSERT INTO user_external_identities (id, user_id, provider, external_id, created_at)
            VALUES (?, ?, ?, ?, ?)
            ON CONFLICT (provider, external_id) DO NOTHING
            """;

        int rowsAffected = entityManager.createNativeQuery(sql)
            .setParameter(1, identityId)
            .setParameter(2, userId)
            .setParameter(3, provider.name())
            .setParameter(4, externalId)
            .setParameter(5, createdAt)
            .executeUpdate();

        return rowsAffected > 0;
    }

    @Override
    public Optional<UserExternalIdentity> findById(UUID id) {
        UserExternalIdentity identity = entityManager.find(UserExternalIdentity.class, id);
        return Optional.ofNullable(identity);
    }

    @Override
    public Optional<UserExternalIdentity> findByProviderAndExternalId(AuthProvider provider, String externalId) {
        return entityManager.createQuery(
                "FROM UserExternalIdentity WHERE provider = :provider AND externalId = :externalId",
                UserExternalIdentity.class)
                .setParameter("provider", provider)
                .setParameter("externalId", externalId)
                .getResultStream()
                .findFirst();
    }

    @Override
    public Optional<UserExternalIdentity> findByUserId(UUID userId) {
        return entityManager.createQuery(
                "FROM UserExternalIdentity WHERE user.id = :userId",
                UserExternalIdentity.class)
                .setParameter("userId", userId)
                .getResultStream()
                .findFirst();
    }

    @Override
    public List<UserExternalIdentity> findAllByUserId(UUID userId) {
        return entityManager.createQuery(
                "FROM UserExternalIdentity WHERE user.id = :userId ORDER BY createdAt",
                UserExternalIdentity.class)
                .setParameter("userId", userId)
                .getResultList();
    }

    @Override
    public List<UserExternalIdentity> findByProvider(AuthProvider provider) {
        return entityManager.createQuery(
                "FROM UserExternalIdentity WHERE provider = :provider ORDER BY createdAt",
                UserExternalIdentity.class)
                .setParameter("provider", provider)
                .getResultList();
    }

    @Override
    public List<UserExternalIdentity> findAll() {
        return entityManager.createQuery("FROM UserExternalIdentity", UserExternalIdentity.class)
                .getResultList();
    }

    @Override
    public UserExternalIdentity update(UserExternalIdentity identity) {
        return entityManager.merge(identity);
    }

    @Override
    public void deleteById(UUID id) {
        UserExternalIdentity identity = entityManager.find(UserExternalIdentity.class, id);
        if (identity != null) {
            entityManager.remove(identity);
        }
    }

    @Override
    public void deleteByUserIdAndProvider(UUID userId, AuthProvider provider) {
        entityManager.createQuery(
                "DELETE FROM UserExternalIdentity WHERE user.id = :userId AND provider = :provider")
                .setParameter("userId", userId)
                .setParameter("provider", provider)
                .executeUpdate();
    }

    @Override
    public void flush() {
        entityManager.flush();
    }
}
