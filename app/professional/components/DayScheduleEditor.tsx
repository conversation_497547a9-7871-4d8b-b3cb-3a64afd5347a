import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Switch,
} from 'react-native';
import { DayScheduleDTO, TimeRangeDTO, DayOfWeek } from '../../../types/api';
import TimeRangePicker from './TimeRangePicker';

interface DayScheduleEditorProps {
  daySchedule: DayScheduleDTO;
  onDayScheduleChange: (daySchedule: DayScheduleDTO) => void;
  disabled?: boolean;
}

/**
 * Day Schedule Editor Component for Professional Mode
 * Handles toggle logic and multiple time ranges for each day
 * Follows the black/white theme established in the calendar
 */
export const DayScheduleEditor: React.FC<DayScheduleEditorProps> = ({
  daySchedule,
  onDayScheduleChange,
  disabled = false,
}) => {
  // Get display name for day of week
  const getDayDisplayName = (dayOfWeek: DayOfWeek): string => {
    const dayNames = {
      [DayOfWeek.MONDAY]: 'Monday',
      [DayOfWeek.TUESDAY]: 'Tuesday',
      [DayOfWeek.WEDNESDAY]: 'Wednesday',
      [DayOfWeek.THURSDAY]: 'Thursday',
      [DayOfWeek.FRIDAY]: 'Friday',
      [DayOfWeek.SATURDAY]: 'Saturday',
      [DayOfWeek.SUNDAY]: 'Sunday',
    };
    return dayNames[dayOfWeek] || dayOfWeek;
  };

  // Toggle day availability
  const handleToggleDay = (isActive: boolean) => {
    if (isActive) {
      // When enabling, add a default time range if none exist
      const timeRanges = daySchedule.timeRanges.length > 0 
        ? daySchedule.timeRanges 
        : [{ startTime: '09:00', endTime: '17:00' }];
      
      onDayScheduleChange({
        ...daySchedule,
        timeRanges,
        isActive: true,
      });
    } else {
      // When disabling, clear all time ranges
      onDayScheduleChange({
        ...daySchedule,
        timeRanges: [],
        isActive: false,
      });
    }
  };

  // Add new time range
  const handleAddTimeRange = () => {
    const newTimeRange: TimeRangeDTO = {
      startTime: '09:00',
      endTime: '17:00',
    };

    // If there are existing time ranges, try to set a smart default
    if (daySchedule.timeRanges.length > 0) {
      const lastRange = daySchedule.timeRanges[daySchedule.timeRanges.length - 1];
      const [lastEndHours, lastEndMinutes] = lastRange.endTime.split(':').map(Number);
      
      // Start new range 1 hour after the last one ends
      const newStartHours = lastEndHours + 1;
      const newEndHours = newStartHours + 8; // 8-hour default duration
      
      if (newStartHours < 24 && newEndHours <= 24) {
        newTimeRange.startTime = `${newStartHours.toString().padStart(2, '0')}:${lastEndMinutes.toString().padStart(2, '0')}`;
        newTimeRange.endTime = `${newEndHours.toString().padStart(2, '0')}:${lastEndMinutes.toString().padStart(2, '0')}`;
      }
    }

    onDayScheduleChange({
      ...daySchedule,
      timeRanges: [...daySchedule.timeRanges, newTimeRange],
    });
  };

  // Update specific time range
  const handleTimeRangeChange = (index: number, timeRange: TimeRangeDTO) => {
    const updatedTimeRanges = [...daySchedule.timeRanges];
    updatedTimeRanges[index] = timeRange;

    onDayScheduleChange({
      ...daySchedule,
      timeRanges: updatedTimeRanges,
    });
  };

  // Remove specific time range
  const handleRemoveTimeRange = (index: number) => {
    const updatedTimeRanges = daySchedule.timeRanges.filter((_, i) => i !== index);
    
    // If removing the last time range, disable the day
    const isActive = updatedTimeRanges.length > 0;

    onDayScheduleChange({
      ...daySchedule,
      timeRanges: updatedTimeRanges,
      isActive,
    });
  };

  return (
    <View style={styles.container}>
      {/* Day Header with Toggle */}
      <View style={styles.dayHeader}>
        <View style={styles.dayInfo}>
          <Text style={styles.dayName}>
            {getDayDisplayName(daySchedule.dayOfWeek as DayOfWeek)}
          </Text>
          <Text style={[
            styles.dayStatus,
            daySchedule.isActive ? styles.availableStatus : styles.unavailableStatus
          ]}>
            {daySchedule.isActive ? 'Available' : 'Not available'}
          </Text>
        </View>
        
        <Switch
          value={daySchedule.isActive}
          onValueChange={handleToggleDay}
          disabled={disabled}
          trackColor={{ false: '#E5E5E5', true: '#000000' }}
          thumbColor={daySchedule.isActive ? '#FFFFFF' : '#FFFFFF'}
          ios_backgroundColor="#E5E5E5"
        />
      </View>

      {/* Time Ranges (only shown when day is active) */}
      {daySchedule.isActive && (
        <View style={styles.timeRangesContainer}>
          {daySchedule.timeRanges.map((timeRange, index) => (
            <TimeRangePicker
              key={index}
              timeRange={timeRange}
              onTimeRangeChange={(updatedRange) => handleTimeRangeChange(index, updatedRange)}
              onRemove={daySchedule.timeRanges.length > 1 ? () => handleRemoveTimeRange(index) : undefined}
              disabled={disabled}
            />
          ))}

          {/* Add Time Range Button */}
          <TouchableOpacity
            style={[styles.addTimeRangeButton, disabled && styles.disabledButton]}
            onPress={handleAddTimeRange}
            disabled={disabled}
          >
            <Text style={[styles.addTimeRangeText, disabled && styles.disabledText]}>
              + Add time range
            </Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 8,
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E5E5',
    overflow: 'hidden',
  },
  dayHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#F8F8F8',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E5',
  },
  dayInfo: {
    flex: 1,
  },
  dayName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 2,
  },
  dayStatus: {
    fontSize: 12,
    fontWeight: '500',
  },
  availableStatus: {
    color: '#000000',
  },
  unavailableStatus: {
    color: '#666666',
  },
  timeRangesContainer: {
    padding: 16,
  },
  addTimeRangeButton: {
    marginTop: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#000000',
    borderStyle: 'dashed',
    backgroundColor: '#FFFFFF',
    alignItems: 'center',
  },
  disabledButton: {
    borderColor: '#CCCCCC',
    backgroundColor: '#F5F5F5',
  },
  addTimeRangeText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#000000',
  },
  disabledText: {
    color: '#CCCCCC',
  },
});

export default DayScheduleEditor;
