package com.yotelohago.messaging.api;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.yotelohago.auth.TokenIdentityProvider;
import com.yotelohago.common.ApiVersion;
import com.yotelohago.messaging.application.MessagingManagementService;
import com.yotelohago.messaging.domain.Message;
import io.quarkus.arc.Arc;
import io.quarkus.arc.ManagedContext;
import io.smallrye.mutiny.Uni;
import io.vertx.core.Context;
import io.vertx.core.Vertx;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.enterprise.context.control.ActivateRequestContext;
import jakarta.inject.Inject;
import jakarta.websocket.*;
import jakarta.websocket.server.PathParam;
import jakarta.websocket.server.ServerEndpoint;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.CompletableFuture;

import java.io.IOException;
import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * WebSocket endpoint for real-time messaging
 * Handles WebSocket connections for live chat functionality
 *
 * URL: ws://localhost:8080/v1/messaging/ws/{bookingId}?userId={userId}
 */
@ServerEndpoint(ApiVersion.BASE + "/messaging/ws/{bookingId}")
@ApplicationScoped
public class MessagingWebSocket {

    private static final Logger logger = LoggerFactory.getLogger(MessagingWebSocket.class);

    // Store active sessions by booking ID -> user ID -> session
    private static final Map<Long, Map<UUID, Session>> bookingSessions = new ConcurrentHashMap<>();

    @Inject
    MessagingManagementService messagingService;

    @Inject
    MessageMapper messageMapper;

    @Inject
    ObjectMapper objectMapper;

    @Inject
    Vertx vertx;

    @OnOpen
    public void onOpen(Session session, @PathParam("bookingId") Long bookingId) {
        try {
            // Extract user ID from query parameters
            List<String> userIdParams = session.getRequestParameterMap().get("userId");
            if (userIdParams == null || userIdParams.isEmpty()) {
                logger.error("❌ WebSocket connection failed for booking {}: Missing userId parameter", bookingId);
                session.close(new CloseReason(CloseReason.CloseCodes.CANNOT_ACCEPT, "Missing userId parameter"));
                return;
            }

            String userIdParam = userIdParams.get(0);
            if (userIdParam == null || userIdParam.trim().isEmpty() || "undefined".equals(userIdParam) || "null".equals(userIdParam)) {
                logger.error("❌ WebSocket connection failed for booking {}: Invalid userId parameter: '{}'", bookingId, userIdParam);
                session.close(new CloseReason(CloseReason.CloseCodes.CANNOT_ACCEPT, "Invalid userId parameter"));
                return;
            }

            UUID userId;
            try {
                userId = UUID.fromString(userIdParam.trim());
            } catch (IllegalArgumentException e) {
                logger.error("❌ WebSocket connection failed for booking {}: Invalid UUID format for userId: '{}'", bookingId, userIdParam);
                session.close(new CloseReason(CloseReason.CloseCodes.CANNOT_ACCEPT, "Invalid UUID format for userId"));
                return;
            }

            logger.info("✅ WebSocket connection opened for booking {} by user {}", bookingId, userId);

            // Store session
            bookingSessions.computeIfAbsent(bookingId, k -> new ConcurrentHashMap<>())
                          .put(userId, session);

            // Send connection confirmation
            WebSocketMessage confirmationMessage = new WebSocketMessage(
                "connection",
                "Connected to booking " + bookingId,
                Map.of("bookingId", bookingId, "userId", userId.toString())
            );
            sendToSession(session, confirmationMessage);

            logger.info("User {} connected to booking {}. Total sessions for booking: {}",
                       userId, bookingId, bookingSessions.get(bookingId).size());

        } catch (Exception e) {
            logger.error("Error opening WebSocket connection for booking {}", bookingId, e);
            try {
                session.close(new CloseReason(CloseReason.CloseCodes.UNEXPECTED_CONDITION, "Connection failed"));
            } catch (IOException ioException) {
                logger.error("Error closing session after connection failure", ioException);
            }
        }
    }

    @OnMessage
    public void onMessage(String messageJson, Session session, @PathParam("bookingId") Long bookingId) {
        try {
            logger.debug("Received WebSocket message for booking {}: {}", bookingId, messageJson);

            // Parse incoming message
            WebSocketMessage wsMessage = objectMapper.readValue(messageJson, WebSocketMessage.class);

            switch (wsMessage.type) {
                case "send_message":
                    handleSendMessage(wsMessage, bookingId, session);
                    break;
                case "mark_read":
                    handleMarkAsRead(wsMessage, bookingId, session);
                    break;
                case "typing":
                    handleTypingIndicator(wsMessage, bookingId, session);
                    break;
                case "ping":
                    handlePing(session);
                    break;
                default:
                    logger.warn("Unknown message type: {}", wsMessage.type);
                    sendErrorToSession(session, "Unknown message type: " + wsMessage.type);
            }

        } catch (Exception e) {
            logger.error("Error processing WebSocket message for booking {}", bookingId, e);
            sendErrorToSession(session, "Error processing message: " + e.getMessage());
        }
    }

    @OnClose
    public void onClose(Session session, @PathParam("bookingId") Long bookingId, CloseReason closeReason) {
        try {
            // Remove session from active sessions
            Map<UUID, Session> sessions = bookingSessions.get(bookingId);
            if (sessions != null) {
                UUID removedUserId = null;
                for (Map.Entry<UUID, Session> entry : sessions.entrySet()) {
                    if (entry.getValue().equals(session)) {
                        removedUserId = entry.getKey();
                        break;
                    }
                }

                if (removedUserId != null) {
                    sessions.remove(removedUserId);
                    logger.info("User {} disconnected from booking {}. Reason: {}",
                               removedUserId, bookingId, closeReason.getReasonPhrase());
                }

                if (sessions.isEmpty()) {
                    bookingSessions.remove(bookingId);
                    logger.info("No more sessions for booking {}, removed from active bookings", bookingId);
                }
            }

        } catch (Exception e) {
            logger.error("Error handling WebSocket close for booking {}", bookingId, e);
        }
    }

    @OnError
    public void onError(Session session, Throwable throwable, @PathParam("bookingId") Long bookingId) {
        logger.error("WebSocket error for booking {}", bookingId, throwable);
        try {
            if (session.isOpen()) {
                session.close(new CloseReason(CloseReason.CloseCodes.UNEXPECTED_CONDITION, "Server error"));
            }
        } catch (IOException e) {
            logger.error("Error closing session after error", e);
        }
    }

    private void handleSendMessage(WebSocketMessage wsMessage, Long bookingId, Session senderSession) {
        try {
            @SuppressWarnings("unchecked")
            Map<String, Object> messageData = (Map<String, Object>) wsMessage.data;

            // Debug logging to see what data we're receiving
            logger.debug("Received message data for booking {}: {}", bookingId, messageData);

            // Create MessageDTO from WebSocket data with validation
            String senderIdStr = (String) messageData.get("senderId");
            String receiverIdStr = (String) messageData.get("receiverId");
            String content = (String) messageData.get("content");

            // Validate required fields
            if (senderIdStr == null || senderIdStr.trim().isEmpty()) {
                throw new IllegalArgumentException("senderId is required and cannot be null or empty");
            }
            if (receiverIdStr == null || receiverIdStr.trim().isEmpty()) {
                throw new IllegalArgumentException("receiverId is required and cannot be null or empty");
            }
            if (content == null || content.trim().isEmpty()) {
                throw new IllegalArgumentException("content is required and cannot be null or empty");
            }

            MessageDTO messageDTO = new MessageDTO();
            messageDTO.bookingId = bookingId;
            messageDTO.senderId = UUID.fromString(senderIdStr.trim());
            messageDTO.receiverId = UUID.fromString(receiverIdStr.trim());
            messageDTO.content = content.trim();
            messageDTO.sentAt = Instant.now();
            messageDTO.isRead = false;

            // Execute database operation on worker thread
            // Using CompletableFuture to avoid deprecated Vert.x executeBlocking
            CompletableFuture.supplyAsync(() -> {
                // Activate request context for CDI beans
                ManagedContext requestContext = Arc.container().requestContext();
                requestContext.activate();
                try {
                    // Save message to database (without auth check since WebSocket connection is already authorized)
                    Message message = messageMapper.toEntity(messageDTO);
                    messagingService.sendWithoutAuthCheck(message);

                    // Convert back to DTO with the generated ID
                    MessageDTO savedMessageDTO = messageMapper.toDTO(message);
                    return savedMessageDTO;

                } finally {
                    requestContext.terminate();
                }
            }).whenComplete((savedMessageDTO, throwable) -> {
                if (throwable == null) {

                    // Broadcast to all participants in this booking
                    WebSocketMessage broadcastMessage = new WebSocketMessage(
                        "new_message",
                        "New message received",
                        savedMessageDTO
                    );

                    broadcastToBooking(bookingId, broadcastMessage);

                    logger.info("Message sent and broadcasted for booking {} by user {}",
                               bookingId, messageDTO.senderId);
                } else {
                    logger.error("Error handling send message for booking {}", bookingId, throwable);
                    sendErrorToSession(senderSession, "Failed to send message: " + throwable.getMessage());
                }
            });

        } catch (Exception e) {
            logger.error("Error handling send message for booking {}", bookingId, e);
            sendErrorToSession(senderSession, "Failed to send message: " + e.getMessage());
        }
    }

    private void handleMarkAsRead(WebSocketMessage wsMessage, Long bookingId, Session session) {
        try {
            Long messageId = ((Number) wsMessage.data).longValue();

            // Execute database operation on worker thread
            // Using CompletableFuture to avoid deprecated Vert.x executeBlocking
            CompletableFuture.supplyAsync(() -> {
                // Activate request context for CDI beans
                ManagedContext requestContext = Arc.container().requestContext();
                requestContext.activate();
                try {
                    messagingService.markAsRead(messageId);
                    return messageId;
                } finally {
                    requestContext.terminate();
                }
            }).whenComplete((result, throwable) -> {
                if (throwable == null) {
                    // Broadcast read status to all participants
                    WebSocketMessage broadcastMessage = new WebSocketMessage(
                        "message_read",
                        "Message marked as read",
                        Map.of("messageId", messageId, "bookingId", bookingId)
                    );
                    broadcastToBooking(bookingId, broadcastMessage);

                    logger.debug("Message {} marked as read for booking {}", messageId, bookingId);
                } else {
                    logger.error("Error marking message as read for booking {}", bookingId, throwable);
                    sendErrorToSession(session, "Failed to mark message as read: " + throwable.getMessage());
                }
            });

        } catch (Exception e) {
            logger.error("Error marking message as read for booking {}", bookingId, e);
            sendErrorToSession(session, "Failed to mark message as read: " + e.getMessage());
        }
    }

    private void handleTypingIndicator(WebSocketMessage wsMessage, Long bookingId, Session senderSession) {
        try {
            // Broadcast typing indicator to other participants (not the sender)
            Map<UUID, Session> sessions = bookingSessions.get(bookingId);
            if (sessions != null) {
                UUID senderUserId = findUserIdBySession(sessions, senderSession);
                if (senderUserId != null) {
                    WebSocketMessage typingMessage = new WebSocketMessage(
                        "typing",
                        "User is typing",
                        Map.of("userId", senderUserId.toString(), "isTyping", wsMessage.data)
                    );

                    sessions.entrySet().stream()
                        .filter(entry -> !entry.getValue().equals(senderSession))
                        .forEach(entry -> sendToSession(entry.getValue(), typingMessage));
                }
            }
        } catch (Exception e) {
            logger.error("Error handling typing indicator for booking {}", bookingId, e);
        }
    }

    private void handlePing(Session session) {
        sendToSession(session, new WebSocketMessage("pong", "Server alive", Instant.now()));
    }

    private UUID findUserIdBySession(Map<UUID, Session> sessions, Session targetSession) {
        return sessions.entrySet().stream()
            .filter(entry -> entry.getValue().equals(targetSession))
            .map(Map.Entry::getKey)
            .findFirst()
            .orElse(null);
    }

    private void broadcastToBooking(Long bookingId, WebSocketMessage message) {
        Map<UUID, Session> sessions = bookingSessions.get(bookingId);
        if (sessions != null) {
            sessions.values().forEach(session -> sendToSession(session, message));
        }
    }

    private void sendToSession(Session session, WebSocketMessage message) {
        if (session != null && session.isOpen()) {
            try {
                String messageJson = objectMapper.writeValueAsString(message);
                session.getAsyncRemote().sendText(messageJson);
            } catch (Exception e) {
                logger.error("Error sending message to session", e);
            }
        }
    }

    private void sendErrorToSession(Session session, String errorMessage) {
        sendToSession(session, new WebSocketMessage("error", errorMessage, null));
    }

    /**
     * WebSocket message wrapper for structured communication
     */
    public static class WebSocketMessage {
        public String type;
        public String message;
        public Object data;

        public WebSocketMessage() {}

        public WebSocketMessage(String type, String message, Object data) {
            this.type = type;
            this.message = message;
            this.data = data;
        }
    }

    /**
     * Get active session count for a booking (for monitoring)
     */
    public static int getActiveSessionCount(Long bookingId) {
        Map<UUID, Session> sessions = bookingSessions.get(bookingId);
        return sessions != null ? sessions.size() : 0;
    }

    /**
     * Get total active sessions across all bookings (for monitoring)
     */
    public static int getTotalActiveSessions() {
        return bookingSessions.values().stream()
            .mapToInt(Map::size)
            .sum();
    }
}
