import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Platform } from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import Colors from '../constants/Colors';
import { users, services } from '@/utils/mockData';

// Mock availability data - in a real app, this would come from an API
const mockAvailability = {
  // Format: YYYY-MM-DD
  availableDates: [
    '2025-06-08', '2025-06-09', '2025-06-10', '2025-06-11', 
    '2025-06-12', '2025-06-13', '2025-06-14', '2025-06-15',
    '2025-06-16', '2025-06-17', '2025-06-18', '2025-06-19'
  ],
  // Available time slots
  timeSlots: [
    '09:00', '10:00', '11:00', '13:00', '14:00', '15:00', '16:00'
  ]
};

export default function AvailabilityScreen() {
  const params = useLocalSearchParams();
  const router = useRouter();
  const [selectedDate, setSelectedDate] = useState<string | null>(null);
  const [selectedTime, setSelectedTime] = useState<string | null>(null);
  
  // Ensure id is a string and exists
  const id = typeof params.id === 'string' ? params.id : undefined;
  
  // In a real app, this would be fetched from an API
  const professional = id ? users.find(u => u.id === id) : undefined;
  const professionalServices = id ? services.filter(s => s.providerId === id) : [];
  
  if (!id || !professional) {
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>Professional not found</Text>
      </View>
    );
  }

  const handleBack = () => {
    router.back();
  };

  const handleDateSelect = (date: string) => {
    setSelectedDate(date);
    setSelectedTime(null); // Reset time when date changes
  };

  const handleTimeSelect = (time: string) => {
    setSelectedTime(time);
  };

  const handleBookNow = () => {
    if (selectedDate && selectedTime) {
      router.push({
        pathname: '/client/booking-confirmation',
        params: {
          professionalId: id,
          date: selectedDate,
          time: selectedTime
        }
      });
    }
  };

  // Generate calendar days for current month
  const renderCalendar = () => {
    const today = new Date();
    const currentMonth = today.getMonth();
    const currentYear = today.getFullYear();
    
    // Get days in current month
    const daysInMonth = new Date(currentYear, currentMonth + 1, 0).getDate();
    
    const calendarDays = [];
    for (let day = 1; day <= daysInMonth; day++) {
      const dateString = `${currentYear}-${String(currentMonth + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
      const isAvailable = mockAvailability.availableDates.includes(dateString);
      const isSelected = selectedDate === dateString;
      
      calendarDays.push(
        <TouchableOpacity
          key={dateString}
          style={[
            styles.calendarDay,
            isAvailable ? styles.availableDay : styles.unavailableDay,
            isSelected && styles.selectedDay
          ]}
          onPress={() => isAvailable && handleDateSelect(dateString)}
          disabled={!isAvailable}
        >
          <Text style={[
            styles.calendarDayText,
            isAvailable ? styles.availableDayText : styles.unavailableDayText,
            isSelected && styles.selectedDayText
          ]}>
            {day}
          </Text>
        </TouchableOpacity>
      );
    }
    
    return (
      <View style={styles.calendarContainer}>
        <Text style={styles.monthTitle}>
          {today.toLocaleString('default', { month: 'long' })} {currentYear}
        </Text>
        <View style={styles.calendarGrid}>
          {calendarDays}
        </View>
      </View>
    );
  };

  // Render time slots for selected date
  const renderTimeSlots = () => {
    if (!selectedDate) return null;
    
    return (
      <View style={styles.timeSlotsContainer}>
        <Text style={styles.sectionTitle}>Available Times</Text>
        <View style={styles.timeSlotGrid}>
          {mockAvailability.timeSlots.map(time => (
            <TouchableOpacity
              key={time}
              style={[
                styles.timeSlot,
                selectedTime === time && styles.selectedTimeSlot
              ]}
              onPress={() => handleTimeSelect(time)}
            >
              <Text style={[
                styles.timeSlotText,
                selectedTime === time && styles.selectedTimeSlotText
              ]}>
                {time}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      {/* Back Button */}
      <TouchableOpacity 
        style={styles.backButton}
        onPress={handleBack}
      >
        <Ionicons name="arrow-back" size={24} color={Colors.text.primary} />
      </TouchableOpacity>

      <ScrollView 
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>Check Availability</Text>
          <Text style={styles.subtitle}>
            {professional.name} • {professionalServices.map(s => s.category).join(', ')}
          </Text>
        </View>

        {/* Calendar */}
        {renderCalendar()}

        {/* Time Slots */}
        {renderTimeSlots()}

        {/* Bottom Spacing */}
        <View style={styles.bottomSpacing} />
      </ScrollView>

      {/* Book Now Button */}
      <View style={styles.bookingContainer}>
        <TouchableOpacity 
          style={[
            styles.bookingButton,
            (!selectedDate || !selectedTime) && styles.disabledButton
          ]}
          onPress={handleBookNow}
          disabled={!selectedDate || !selectedTime}
        >
          <Text style={[
            styles.bookingButtonText,
            (!selectedDate || !selectedTime) && styles.disabledButtonText
          ]}>
            Book Now
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.primary,
  },
  backButton: {
    position: 'absolute',
    top: 60,
    left: 16,
    zIndex: 1,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.background.secondary,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.border,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 80, // Space for fixed button
  },
  header: {
    alignItems: 'center',
    paddingTop: 60,
    paddingBottom: 24,
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: Colors.text.primary,
    marginBottom: 8,
    marginTop: 40,
  },
  subtitle: {
    fontSize: 16,
    color: Colors.text.secondary,
  },
  calendarContainer: {
    padding: 16,
    backgroundColor: Colors.background.secondary,
    borderRadius: 12,
    marginHorizontal: 16,
    marginTop: 16,
  },
  monthTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text.primary,
    marginBottom: 16,
    textAlign: 'center',
  },
  calendarGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  calendarDay: {
    width: '13%', // ~7 days per row with spacing
    aspectRatio: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
    borderRadius: 8,
  },
  availableDay: {
    backgroundColor: Colors.background.primary,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  unavailableDay: {
    backgroundColor: Colors.background.secondary,
  },
  selectedDay: {
    backgroundColor: Colors.primary,
    borderColor: Colors.primary,
  },
  calendarDayText: {
    fontSize: 16,
  },
  availableDayText: {
    color: Colors.text.primary,
  },
  unavailableDayText: {
    color: Colors.text.secondary,
  },
  selectedDayText: {
    color: Colors.text.light,
    fontWeight: '600',
  },
  timeSlotsContainer: {
    padding: 16,
    marginTop: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text.primary,
    marginBottom: 16,
  },
  timeSlotGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'flex-start',
  },
  timeSlot: {
    width: '30%',
    backgroundColor: Colors.background.secondary,
    padding: 12,
    borderRadius: 8,
    marginRight: '3%',
    marginBottom: 12,
    alignItems: 'center',
  },
  selectedTimeSlot: {
    backgroundColor: Colors.primary,
  },
  timeSlotText: {
    fontSize: 14,
    color: Colors.text.primary,
  },
  selectedTimeSlotText: {
    color: Colors.text.light,
    fontWeight: '600',
  },
  bottomSpacing: {
    height: 100,
  },
  bookingContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 16,
    backgroundColor: Colors.background.primary,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  bookingButton: {
    backgroundColor: Colors.primary,
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  disabledButton: {
    backgroundColor: Colors.inactive,
  },
  bookingButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text.light,
  },
  disabledButtonText: {
    color: Colors.text.secondary,
  },
  errorText: {
    fontSize: 16,
    color: Colors.text.secondary,
    textAlign: 'center',
    marginTop: 40,
  },
});