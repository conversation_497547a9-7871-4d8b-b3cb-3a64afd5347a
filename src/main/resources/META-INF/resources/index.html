<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>ProConnect - Users</title>
    <style>
        body { font-family: sans-serif; margin: 2em; }
        .card { border: 1px solid #ddd; border-radius: 8px; padding: 1em; margin-bottom: 1em; max-width: 600px; }
        .card img { max-width: 100px; border-radius: 50%; float: left; margin-right: 1em; }
        .card h3 { margin: 0 0 0.5em 0; }
    </style>
</head>
<body>
<h1>Registered Users</h1>

<!-- Login Button -->
<button onclick="login()">Login</button>

<div id="user-list"></div>

<script>
    function login() {
        const redirectUri = encodeURIComponent("http://localhost:8080/");
        const loginUrl = `http://localhost:8081/realms/yotelohago/protocol/openid-connect/auth` +
            `?response_type=code` +
            `&client_id=yotelohago-app` +
            `&scope=openid` +
            `&redirect_uri=${redirectUri}`;
        window.location.href = loginUrl;
    }

    fetch('/api/users')
        .then(res => {
            if (res.status === 401) {
                console.warn("Not logged in");
                return [];
            }
            return res.json();
        })
        .then(users => {
            const list = document.getElementById('user-list');
            users.forEach(user => {
                const div = document.createElement('div');
                div.className = 'card';
                div.innerHTML = `
                    <h3>${user.name}</h3>
                    <p><strong>${user.role}</strong></p>
                    <p>Email: ${user.email}</p>
                    <div style="clear:both;"></div>
                `;
                list.appendChild(div);
            });
        });
</script>
</body>
</html>