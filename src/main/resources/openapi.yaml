openapi: 3.0.3
info:
  title: Yotelohago Backend API
  version: 1.0.0
  description: REST API for the Yotelohago services marketplace

paths:
  /auth/login:
    post:
      summary: User login
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoginRequest'
      responses:
        '200':
          description: JWT token

  /auth/register:
    post:
      summary: User registration
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RegisterRequest'
      responses:
        '201':
          description: User created

  /users/{id}:
    get:
      summary: Get user profile
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: User details

  /services:
    get:
      summary: List available services
      responses:
        '200':
          description: List of services

    post:
      summary: Create a service
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ServiceRequest'
      responses:
        '201':
          description: Service created

  /bookings:
    post:
      summary: Book a service
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BookingRequest'
      responses:
        '201':
          description: Booking created

  /reviews:
    get:
      summary: Get reviews for a service
      parameters:
        - name: service_id
          in: query
          required: true
          schema:
            type: string
      responses:
        '200':
          description: List of reviews

    post:
      summary: Submit a review
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ReviewRequest'
      responses:
        '201':
          description: Review submitted

  /uploads:
    post:
      summary: Upload a file
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                file:
                  type: string
                  format: binary
      responses:
        '200':
          description: File uploaded

  /health:
    get:
      summary: Health check
      responses:
        '200':
          description: OK

  /metrics:
    get:
      summary: Prometheus metrics
      responses:
        '200':
          description: Prometheus format

components:
  schemas:
    LoginRequest:
      type: object
      properties:
        email:
          type: string
        password:
          type: string

    RegisterRequest:
      type: object
      properties:
        name:
          type: string
        email:
          type: string
        password:
          type: string

    ServiceRequest:
      type: object
      properties:
        title:
          type: string
        description:
          type: string
        price:
          type: number
        category:
          type: string

    BookingRequest:
      type: object
      properties:
        service_id:
          type: string
        user_id:
          type: string
        time:
          type: string
          format: date-time

    ReviewRequest:
      type: object
      properties:
        service_id:
          type: string
        rating:
          type: integer
        comment:
          type: string