import React, { createContext, useContext, useState, useEffect } from 'react';
import keycloakService, { KeycloakUser } from '../services/keycloakService';

interface User {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'manager' | 'staff';
  avatar?: string;
}

interface AuthContextType {
  user: User | null;
  login: () => Promise<void>;
  logout: () => void;
  loading: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

const mapKeycloakUserToUser = (keycloakUser: KeycloakUser): User => {
  return {
    id: keycloakUser.id,
    name: `${keycloakUser.firstName} ${keycloakUser.lastName}`.trim() || keycloakUser.username,
    email: keycloakUser.email,
    role: keycloakUser.isAdmin ? 'admin' : 'staff',
    avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(keycloakUser.firstName + ' ' + keycloakUser.lastName)}&background=ff5a5f&color=fff`
  };
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const initKeycloak = async () => {
      setLoading(true);
      try {
        const authenticated = await keycloakService.init();
        if (authenticated) {
          const keycloakUser = keycloakService.getUser();
          if (keycloakUser && keycloakUser.isAdmin) {
            setUser(mapKeycloakUserToUser(keycloakUser));
          } else {
            // User is authenticated but not an admin
            console.warn('User is not an admin, logging out');
            await keycloakService.logout();
          }
        }
      } catch (error) {
        console.error('Keycloak initialization failed:', error);
      } finally {
        setLoading(false);
      }
    };

    initKeycloak();
  }, []);

  const login = async () => {
    setLoading(true);
    try {
      await keycloakService.login();
      // After successful login, the useEffect will handle setting the user
    } catch (error) {
      console.error('Login failed:', error);
      throw new Error('Login failed');
    } finally {
      setLoading(false);
    }
  };

  const logout = () => {
    keycloakService.logout();
    setUser(null);
  };

  return (
    <AuthContext.Provider value={{ user, login, logout, loading }}>
      {children}
    </AuthContext.Provider>
  );
};