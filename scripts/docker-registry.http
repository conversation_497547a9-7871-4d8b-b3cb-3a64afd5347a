@registry  = registry.yotelohago.co
@image     = frontend/main
@tag       = v6
@auth      = eW90ZWxvaGFnbzp5b3RlbG9oYWdv
@digest    = sha256:b388ca1e98aecbde0ca96d14e0e5823afebe8ea773f28bb45649d1490dc19578

### GET request with a header
GET https://registry.yotelohago.co/v2/_catalog
Authorization: Basic {{auth}}

### GET request with a header
GET https://{{registry}}/v2/{{image}}/tags/list
Authorization: Basic {{auth}}

### Get the manifest digest for a specific tag
GET https://{{registry}}/v2/{{image}}/manifests/{{tag}}
Authorization: Basic {{auth}}
Accept: application/vnd.docker.distribution.manifest.v2+json
Accept: application/vnd.oci.image.index.v1+json

### Delete image by digest
DELETE https://{{registry}}/v2/{{image}}/manifests/{{digest}}
Authorization: Basic {{auth}}
