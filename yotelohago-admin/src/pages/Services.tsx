import React, { useState, useEffect } from 'react';
import { Typography, Button, Space, Tag, Avatar, Dropdown, Modal, Form, Input, Select, InputNumber, message, Descriptions } from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  MoreOutlined,
  ExclamationCircleOutlined,
  UserOutlined
} from '@ant-design/icons';
import { ColumnsType } from 'antd/es/table';
import DataTable from '../components/common/DataTable';
import { Service, ServiceCategory } from '../types';
import apiService from '../services/apiService';
import { useUrlHighlight } from '../hooks/useUrlHighlight';
import { useNavigate } from 'react-router-dom';
import dayjs from 'dayjs';

const { Title } = Typography;
const { Option } = Select;
const { TextArea } = Input;
const { confirm } = Modal;

const Services: React.FC = () => {
  const [services, setServices] = useState<Service[]>([]);
  const [categories, setCategories] = useState<ServiceCategory[]>([]);
  const [loading, setLoading] = useState(true);
  const [modalVisible, setModalVisible] = useState(false);
  const [detailsVisible, setDetailsVisible] = useState(false);
  const [editingService, setEditingService] = useState<Service | null>(null);
  const [selectedService, setSelectedService] = useState<Service | null>(null);
  const [form] = Form.useForm();
  const highlightId = useUrlHighlight();
  const navigate = useNavigate();

  // Load services and categories from API
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        console.log('Loading services and categories...');
        const [servicesData, categoriesData] = await Promise.all([
          apiService.getServices(),
          apiService.getServiceCategories()
        ]);
        console.log('Services loaded:', servicesData);
        console.log('Categories loaded:', categoriesData);
        setServices(servicesData || []);
        setCategories(categoriesData || []);
      } catch (error) {
        console.error('Failed to load data:', error);
        message.error('Failed to load services and categories');
        setServices([]);
        setCategories([]);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  // Get enriched services with category data
  const servicesWithDetails = services.map(service => ({
    ...service,
    category: categories.find(cat => cat.id === service.categoryId),
    // Professional data should come from the backend ServiceWithProfessionalDTO
    professionalUser: null // Will be handled by backend
  }));

  const handleAddService = () => {
    setEditingService(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleEditService = (service: Service) => {
    setEditingService(service);
    form.setFieldsValue(service);
    setModalVisible(true);
  };

  const handleViewService = (service: Service) => {
    const enrichedService = {
      ...service,
      category: categories.find(cat => cat.id === service.categoryId),
      professional: null, // Will be handled by backend
      professionalUser: null // Will be handled by backend
    };
    setSelectedService(enrichedService);
    setDetailsVisible(true);
  };

  const handleDeleteService = (service: Service) => {
    confirm({
      title: 'Are you sure you want to delete this service?',
      icon: <ExclamationCircleOutlined />,
      content: `This action cannot be undone. Service "${service.title}" will be permanently deleted.`,
      okText: 'Yes, Delete',
      okType: 'danger',
      cancelText: 'Cancel',
      onOk() {
        setServices(services.filter(s => s.id !== service.id));
        message.success('Service deleted successfully');
      },
    });
  };

  const handleSaveService = async (values: any) => {
    setLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call
      
      const serviceData = {
        ...values,
        id: editingService?.id || Date.now().toString(),
        createdAt: editingService?.createdAt || new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      if (editingService) {
        setServices(services.map(s => s.id === editingService.id ? serviceData : s));
        message.success('Service updated successfully');
      } else {
        setServices([...services, serviceData]);
        message.success('Service created successfully');
      }

      setModalVisible(false);
      form.resetFields();
    } catch (error) {
      message.error('Failed to save service');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'green';
      case 'inactive': return 'red';
      case 'draft': return 'orange';
      default: return 'default';
    }
  };

  const getCategoryIcon = (categoryName: string) => {
    switch (categoryName?.toLowerCase()) {
      case 'electricians':
      case 'electrician':
        return <ion-icon name="flash-outline" style={{ fontSize: '16px', color: '#faad14' }}></ion-icon>;
      case 'plumbers':
      case 'plumber':
        return <ion-icon name="water-outline" style={{ fontSize: '16px', color: '#1890ff' }}></ion-icon>;
      case 'movers':
      case 'mover':
        return <ion-icon name="cube-outline" style={{ fontSize: '16px', color: '#52c41a' }}></ion-icon>;
      case 'locksmiths':
      case 'locksmith':
        return <ion-icon name="key-outline" style={{ fontSize: '16px', color: '#722ed1' }}></ion-icon>;
      default:
        return <ion-icon name="construct-outline" style={{ fontSize: '16px', color: '#8c8c8c' }}></ion-icon>;
    }
  };

  const columns: ColumnsType<Service & { category?: any; professional?: any; professionalUser?: any }> = [
    {
      title: 'Category',
      key: 'category',
      width: 180,
      render: (_, record) => (
        <div className="flex items-center space-x-2">
          {getCategoryIcon(record.category?.name)}
          <span 
            className="text-coral-500 hover:text-coral-600 cursor-pointer font-medium"
            onClick={() => navigate(`/categories?highlight=${record.categoryId}`)}
          >
            {record.category?.name || 'Unknown'}
          </span>
        </div>
      ),
    },
    {
      title: 'Service',
      key: 'service',
      width: 300,
      render: (_, record) => (
        <div className={`${
          highlightId === record.id ? 'bg-coral-100 p-2 rounded animate-pulse' : ''
        }`}>
          <div className="font-medium text-gray-900 truncate">{record.title}</div>
          <div className="text-sm text-gray-500 truncate">{record.description}</div>
        </div>
      ),
    },
    {
      title: 'Professional',
      key: 'professional',
      width: 180,
      render: (_, record) => (
        <div className="flex items-center space-x-2">
          <Avatar icon={<UserOutlined />} size="small" />
          <span 
            className="text-coral-500 hover:text-coral-600 cursor-pointer font-medium"
            onClick={() => navigate(`/professionals?highlight=${record.professionalUserId}`)}
          >
            {record.professionalUser 
              ? `${record.professionalUser.firstName} ${record.professionalUser.lastName}`
              : 'Unknown'
            }
          </span>
        </div>
      ),
    },
    {
      title: 'Price',
      dataIndex: 'price',
      key: 'price',
      width: 100,
      render: (price) => `$${price.toFixed(2)}`,
      sorter: (a, b) => a.price - b.price,
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => (
        <Tag color={getStatusColor(status)} className="capitalize">
          {status}
        </Tag>
      ),
      filters: [
        { text: 'Active', value: 'active' },
        { text: 'Inactive', value: 'inactive' },
        { text: 'Draft', value: 'draft' },
      ],
      onFilter: (value, record) => record.status === value,
    },
    {
      title: 'Created',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 120,
      render: (createdAt) => (
        <span className="text-sm">
          {dayjs(createdAt).format('MMM DD, YYYY')}
        </span>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 120,
      render: (_, record) => (
        <Dropdown
          menu={{
            items: [
              {
                key: 'view',
                icon: <EyeOutlined />,
                label: 'View Details',
                onClick: () => handleViewService(record),
              },
              {
                key: 'edit',
                icon: <EditOutlined />,
                label: 'Edit',
                onClick: () => handleEditService(record),
              },
              {
                type: 'divider',
              },
              {
                key: 'delete',
                icon: <DeleteOutlined />,
                label: 'Delete',
                danger: true,
                onClick: () => handleDeleteService(record),
              },
            ],
          }}
          trigger={['click']}
        >
          <Button type="text" icon={<MoreOutlined />} />
        </Dropdown>
      ),
    },
  ];

  const filterOptions = [
    {
      key: 'categoryId',
      label: 'Category',
      options: categories.map(cat => ({ value: cat.id, label: cat.name })),
    },
    {
      key: 'status',
      label: 'Status',
      options: [
        { value: 'active', label: 'Active' },
        { value: 'inactive', label: 'Inactive' },
        { value: 'draft', label: 'Draft' },
      ],
    },
  ];

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <Title level={2} className="m-0">Service Management</Title>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={handleAddService}
          size="large"
        >
          Add Service
        </Button>
      </div>

      <DataTable
        columns={columns}
        dataSource={servicesWithDetails}
        rowKey="id"
        loading={loading}
        filterOptions={filterOptions}
        onSearch={(value) => {
          console.log('Search:', value);
        }}
        onFilter={(filters) => {
          console.log('Filters:', filters);
        }}
        onExport={() => {
          console.log('Export services');
        }}
      />

      <Modal
        title={editingService ? 'Edit Service' : 'Add New Service'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        onOk={form.submit}
        confirmLoading={loading}
        width={600}
        destroyOnClose
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSaveService}
          className="mt-4"
        >
          <Form.Item
            name="title"
            label="Service Title"
            rules={[{ required: true, message: 'Please enter service title' }]}
          >
            <Input placeholder="Enter service title" />
          </Form.Item>

          <Form.Item
            name="description"
            label="Description"
            rules={[{ required: true, message: 'Please enter description' }]}
          >
            <TextArea
              rows={4}
              placeholder="Enter service description"
            />
          </Form.Item>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Form.Item
              name="categoryId"
              label="Category"
              rules={[{ required: true, message: 'Please select category' }]}
            >
              <Select placeholder="Select category">
                {categories.map(cat => (
                  <Option key={cat.id} value={cat.id}>{cat.name}</Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item
              name="professionalUserId"
              label="Professional"
              rules={[{ required: true, message: 'Please select professional' }]}
            >
              <Select placeholder="Select professional" disabled>
                <Option value="">Professional selection not implemented yet</Option>
              </Select>
            </Form.Item>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Form.Item
              name="price"
              label="Price"
              rules={[{ required: true, message: 'Please enter price' }]}
            >
              <InputNumber
                min={0}
                precision={2}
                placeholder="0.00"
                className="w-full"
                addonBefore="$"
              />
            </Form.Item>

            <Form.Item
              name="status"
              label="Status"
              rules={[{ required: true, message: 'Please select status' }]}
            >
              <Select placeholder="Select status">
                <Option value="active">Active</Option>
                <Option value="inactive">Inactive</Option>
                <Option value="draft">Draft</Option>
              </Select>
            </Form.Item>
          </div>
        </Form>
      </Modal>

      <Modal
        title={`Service Details - ${selectedService?.title}`}
        open={detailsVisible}
        onCancel={() => setDetailsVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailsVisible(false)}>
            Close
          </Button>,
          <Button key="edit" type="primary" icon={<EditOutlined />} onClick={() => {
            if (selectedService) {
              setDetailsVisible(false);
              handleEditService(selectedService);
            }
          }}>
            Edit Service
          </Button>,
        ]}
        width={800}
      >
        {selectedService && (
          <div className="space-y-6">
            <Descriptions column={2} bordered>
              <Descriptions.Item label="Title" span={2}>
                {selectedService.title}
              </Descriptions.Item>
              <Descriptions.Item label="Description" span={2}>
                {selectedService.description}
              </Descriptions.Item>
              <Descriptions.Item label="Category">
                <div className="flex items-center space-x-2">
                  {getCategoryIcon(selectedService.category?.name)}
                  <span 
                    className="text-coral-500 hover:text-coral-600 cursor-pointer font-medium"
                    onClick={() => {
                      setDetailsVisible(false);
                      navigate(`/categories?highlight=${selectedService.categoryId}`);
                    }}
                  >
                    {selectedService.category?.name || 'Unknown'}
                  </span>
                </div>
              </Descriptions.Item>
              <Descriptions.Item label="Professional">
                <span 
                  className="text-coral-500 hover:text-coral-600 cursor-pointer font-medium"
                  onClick={() => {
                    setDetailsVisible(false);
                    navigate(`/professionals?highlight=${selectedService.professionalUserId}`);
                  }}
                >
                  {selectedService.professionalUser 
                    ? `${selectedService.professionalUser.firstName} ${selectedService.professionalUser.lastName}`
                    : 'Unknown'
                  }
                </span>
              </Descriptions.Item>
              <Descriptions.Item label="Price">
                ${selectedService.price.toFixed(2)}
              </Descriptions.Item>
              <Descriptions.Item label="Status">
                <Tag color={getStatusColor(selectedService.status)} className="capitalize">
                  {selectedService.status}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="Created">
                {dayjs(selectedService.createdAt).format('MMM DD, YYYY HH:mm')}
              </Descriptions.Item>
              <Descriptions.Item label="Last Updated">
                {dayjs(selectedService.updatedAt).format('MMM DD, YYYY HH:mm')}
              </Descriptions.Item>
            </Descriptions>
          </div>
        )}
      </Modal>

      {/* Load Ionicons */}
      <script type="module" src="https://unpkg.com/ionicons@7.1.0/dist/ionicons/ionicons.esm.js"></script>
      <script noModule src="https://unpkg.com/ionicons@7.1.0/dist/ionicons/ionicons.js"></script>
    </div>
  );
};

export default Services;