import { apiPost } from './client';
import { ApiEndpoints } from './types';
import { UserRegistrationRequest, UserRegistrationResponse } from '../../types/api';

/**
 * User Registration API Service
 * Handles user account creation with the backend
 */

/**
 * Register a new user account
 * 
 * @param registrationData User registration information
 * @returns Promise with registration response
 * @throws Error if registration fails
 */
export const registerUser = async (
  registrationData: UserRegistrationRequest
): Promise<UserRegistrationResponse> => {
  try {
    console.log('🚀 Starting user registration for:', registrationData.email);
    
    // Validate input before sending to backend
    validateRegistrationData(registrationData);
    
    // Call backend registration endpoint
    const response = await apiPost<UserRegistrationResponse>(
      ApiEndpoints.USER_REGISTER,
      registrationData
    );
    
    console.log('✅ User registration successful:', {
      email: response.email,
      keycloakId: response.keycloakId,
      message: response.message
    });
    
    return response;
    
  } catch (error) {
    console.error('❌ User registration failed:', error);
    
    // Re-throw with user-friendly message
    if (error instanceof Error) {
      throw new Error(error.message);
    } else {
      throw new Error('Registration failed. Please try again later.');
    }
  }
};

/**
 * Validate registration data before sending to backend
 * 
 * @param data Registration data to validate
 * @throws Error if validation fails
 */
const validateRegistrationData = (data: UserRegistrationRequest): void => {
  // Email validation
  if (!data.email || !data.email.trim()) {
    throw new Error('Email is required');
  }
  
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(data.email)) {
    throw new Error('Please enter a valid email address');
  }
  
  // Password validation
  if (!data.password || data.password.length < 8) {
    throw new Error('Password must be at least 8 characters long');
  }
  
  // First name validation
  if (!data.firstName || !data.firstName.trim()) {
    throw new Error('First name is required');
  }
  
  if (data.firstName.length > 50) {
    throw new Error('First name must not exceed 50 characters');
  }
  
  // Last name validation
  if (!data.lastName || !data.lastName.trim()) {
    throw new Error('Last name is required');
  }
  
  if (data.lastName.length > 50) {
    throw new Error('Last name must not exceed 50 characters');
  }
};

/**
 * Check if an email is already registered
 * Note: This would require a separate backend endpoint
 * TODO: Implement when backend provides email check endpoint
 * 
 * @param email Email to check
 * @returns Promise<boolean> true if email exists
 */
export const checkEmailExists = async (email: string): Promise<boolean> => {
  // TODO: Implement when backend provides email check endpoint
  // This would help provide better UX by checking email availability
  // before the user fills out the entire form
  console.log('📧 Email check not implemented yet for:', email);
  return false;
};

// Export default service object
export default {
  registerUser,
  checkEmailExists,
};
