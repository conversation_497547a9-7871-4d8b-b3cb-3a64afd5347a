import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { Platform } from 'react-native';
import { AuthState, AuthAction, AuthActionType, AuthUser, AuthTokens, UserMode } from '../types/auth';
import tokenStorage from '../services/auth/tokenStorage';
import keycloakService from '../services/auth/keycloakService';

// Initial state
const initialState: AuthState = {
  isAuthenticated: false,
  isLoading: true,
  user: null,
  tokens: null,
  error: null,
  currentMode: UserMode.CLIENT, // Default to client mode
};

// Auth reducer
function authReducer(state: AuthState, action: AuthAction): AuthState {
  switch (action.type) {
    case AuthActionType.LOGIN_START:
      return {
        ...state,
        isLoading: true,
        error: null,
      };

    case AuthActionType.LOGIN_SUCCESS:
      return {
        ...state,
        isAuthenticated: true,
        isLoading: false,
        user: action.payload.user,
        tokens: action.payload.tokens,
        error: null,
      };

    case AuthActionType.LOGIN_FAILURE:
      return {
        ...state,
        isAuthenticated: false,
        isLoading: false,
        user: null,
        tokens: null,
        error: action.payload.error,
      };

    case AuthActionType.LOGOUT:
      return {
        ...state,
        isAuthenticated: false,
        isLoading: false,
        user: null,
        tokens: null,
        error: null,
      };

    case AuthActionType.TOKEN_REFRESH_START:
      return {
        ...state,
        isLoading: true,
      };

    case AuthActionType.TOKEN_REFRESH_SUCCESS:
      return {
        ...state,
        isLoading: false,
        tokens: action.payload.tokens,
        error: null,
      };

    case AuthActionType.TOKEN_REFRESH_FAILURE:
      return {
        ...state,
        isAuthenticated: false,
        isLoading: false,
        user: null,
        tokens: null,
        error: action.payload.error,
      };

    case AuthActionType.RESTORE_SESSION:
      return {
        ...state,
        isAuthenticated: action.payload.isAuthenticated,
        isLoading: false,
        user: action.payload.user,
        tokens: action.payload.tokens,
        currentMode: action.payload.currentMode || state.currentMode,
      };

    case AuthActionType.CLEAR_ERROR:
      return {
        ...state,
        error: null,
      };

    case AuthActionType.SET_USER_MODE:
      return {
        ...state,
        currentMode: action.payload.mode,
      };

    default:
      return state;
  }
}

// Auth context interface
interface AuthContextType {
  state: AuthState;
  login: (username?: string, password?: string) => Promise<void>;
  logout: () => Promise<void>;
  refreshToken: () => Promise<void>;
  clearError: () => void;
  refreshAuthState: () => Promise<void>;
  setUserMode: (mode: UserMode) => Promise<void>;
}

// Create context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Auth provider props
interface AuthProviderProps {
  children: ReactNode;
}

// Auth provider component
export function AuthProvider({ children }: AuthProviderProps) {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Restore session on app start
  useEffect(() => {
    restoreSession();
  }, []);



  const restoreSession = async () => {
    try {
      console.log('🔄 Restoring session...');

      const tokens = await tokenStorage.getTokens();
      const userInfo = await tokenStorage.getUserInfo();
      const storedMode = await tokenStorage.getUserMode();
      const isExpired = await tokenStorage.isTokenExpired();

      // Parse stored mode or default to CLIENT
      const currentMode = storedMode === UserMode.PROFESSIONAL ? UserMode.PROFESSIONAL : UserMode.CLIENT;
      console.log('🔄 Restored user mode:', currentMode);

      if (tokens && userInfo) {
        if (!isExpired) {
          // Tokens are valid, but refresh user info to get latest internalId
          try {
            console.log('🔄 Refreshing user info to get latest internalId...');
            const refreshedUserInfo = await keycloakService.getUserInfoFromToken(tokens.accessToken);

            // Update stored user info with refreshed data
            await tokenStorage.storeUserInfo(refreshedUserInfo);

            dispatch({
              type: AuthActionType.RESTORE_SESSION,
              payload: {
                isAuthenticated: true,
                user: refreshedUserInfo,
                tokens,
                currentMode,
              },
            });
            console.log('✅ Session restored successfully with refreshed user info');
          } catch (refreshError) {
            console.warn('⚠️ Failed to refresh user info, using stored data:', refreshError);
            // Fall back to stored user info
            dispatch({
              type: AuthActionType.RESTORE_SESSION,
              payload: {
                isAuthenticated: true,
                user: userInfo,
                tokens,
                currentMode,
              },
            });
            console.log('✅ Session restored successfully with stored user info');
          }
        } else {
          // Tokens are expired, try to refresh them
          console.log('🔄 Tokens expired, attempting refresh...');
          try {
            if (tokens.refreshToken) {
              const newTokens = await keycloakService.refreshToken(tokens.refreshToken);
              await tokenStorage.storeTokens(newTokens);

              // Also refresh user info with new tokens
              try {
                const refreshedUserInfo = await keycloakService.getUserInfoFromToken(newTokens.accessToken);
                await tokenStorage.storeUserInfo(refreshedUserInfo);

                dispatch({
                  type: AuthActionType.RESTORE_SESSION,
                  payload: {
                    isAuthenticated: true,
                    user: refreshedUserInfo,
                    tokens: newTokens,
                    currentMode,
                  },
                });
                console.log('✅ Session restored with refreshed tokens and user info');
              } catch (userInfoError) {
                console.warn('⚠️ Failed to refresh user info with new tokens, using stored data:', userInfoError);
                dispatch({
                  type: AuthActionType.RESTORE_SESSION,
                  payload: {
                    isAuthenticated: true,
                    user: userInfo,
                    tokens: newTokens,
                    currentMode,
                  },
                });
                console.log('✅ Session restored with refreshed tokens (stored user info)');
              }
            } else {
              throw new Error('No refresh token available');
            }
          } catch (refreshError) {
            console.error('❌ Token refresh failed:', refreshError);
            // Clear expired tokens that can't be refreshed
            await tokenStorage.clearAll();
            dispatch({
              type: AuthActionType.RESTORE_SESSION,
              payload: {
                isAuthenticated: false,
                user: null,
                tokens: null,
              },
            });
            console.log('ℹ️ Session cleared due to refresh failure');
          }
        }
      } else {
        // No tokens or user info
        dispatch({
          type: AuthActionType.RESTORE_SESSION,
          payload: {
            isAuthenticated: false,
            user: null,
            tokens: null,
          },
        });
        console.log('ℹ️ No session data found');
      }
    } catch (error) {
      console.error('❌ Failed to restore session:', error);
      dispatch({
        type: AuthActionType.RESTORE_SESSION,
        payload: {
          isAuthenticated: false,
          user: null,
          tokens: null,
        },
      });
    }
  };

  const login = async (username?: string, password?: string): Promise<void> => {
    dispatch({ type: AuthActionType.LOGIN_START });

    try {
      console.log('🔄 Starting Keycloak login process...');

      // Use real Keycloak authentication
      const { user, tokens } = await keycloakService.login(username, password);

      // Store tokens and user info
      await tokenStorage.storeTokens(tokens);
      await tokenStorage.storeUserInfo(user);

      dispatch({
        type: AuthActionType.LOGIN_SUCCESS,
        payload: {
          user,
          tokens,
        },
      });

      console.log('✅ Keycloak login successful');
    } catch (error) {
      console.error('❌ Keycloak login failed:', error);

      let errorMessage = 'Login failed';
      if (error instanceof Error) {
        if (error.message.includes('Redirect initiated')) {
          // This is expected for web redirect flow - don't treat as error
          console.log('🔄 Redirect initiated for web login');
          return;
        } else if (error.message.includes('Credentials required for web login')) {
          // This is expected for web when no credentials provided - don't treat as error
          // The UI will handle showing the login form
          console.log('🔄 Credentials required for web login - showing login form');
          return;
        } else if (error.message.includes('cancelled')) {
          errorMessage = 'Login was cancelled';
        } else if (error.message.includes('dismiss')) {
          errorMessage = 'Login window was closed';
        } else {
          errorMessage = error.message;
        }
      }

      dispatch({
        type: AuthActionType.LOGIN_FAILURE,
        payload: {
          error: errorMessage,
        },
      });
    }
  };

  const logout = async (): Promise<void> => {
    try {
      console.log('🔄 Logging out...');

      // Get current tokens for proper logout
      const tokens = await tokenStorage.getTokens();

      // Clear stored tokens first (before redirect)
      await tokenStorage.clearAll();
      dispatch({ type: AuthActionType.LOGOUT });

      // Then logout from Keycloak (this will redirect for web)
      await keycloakService.logout(tokens?.idToken);

      console.log('✅ Logout successful');
    } catch (error) {
      console.error('❌ Logout failed:', error);
      // Still dispatch logout even if Keycloak logout fails
      await tokenStorage.clearAll();
      dispatch({ type: AuthActionType.LOGOUT });
    }
  };

  const refreshToken = async (): Promise<void> => {
    dispatch({ type: AuthActionType.TOKEN_REFRESH_START });

    try {
      console.log('🔄 Refreshing token...');

      const currentTokens = await tokenStorage.getTokens();
      if (!currentTokens?.refreshToken) {
        throw new Error('No refresh token available');
      }

      // Use real Keycloak token refresh
      const newTokens = await keycloakService.refreshToken(currentTokens.refreshToken);

      // Store new tokens
      await tokenStorage.storeTokens(newTokens);

      dispatch({
        type: AuthActionType.TOKEN_REFRESH_SUCCESS,
        payload: {
          tokens: newTokens,
        },
      });

      console.log('✅ Token refresh successful');
    } catch (error) {
      console.error('❌ Token refresh failed:', error);
      dispatch({
        type: AuthActionType.TOKEN_REFRESH_FAILURE,
        payload: {
          error: error instanceof Error ? error.message : 'Token refresh failed',
        },
      });
    }
  };

  const clearError = () => {
    dispatch({ type: AuthActionType.CLEAR_ERROR });
  };

  const refreshAuthState = async (): Promise<void> => {
    console.log('🔄 Refreshing auth state...');
    await restoreSession();
  };

  const setUserMode = async (mode: UserMode): Promise<void> => {
    console.log('🔄 Setting user mode to:', mode);

    // Persist the mode to storage
    try {
      await tokenStorage.storeUserMode(mode);
    } catch (error) {
      console.error('❌ Failed to persist user mode:', error);
    }

    dispatch({
      type: AuthActionType.SET_USER_MODE,
      payload: { mode }
    });
  };

  const contextValue: AuthContextType = {
    state,
    login,
    logout,
    refreshToken,
    clearError,
    refreshAuthState,
    setUserMode,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
}

// Custom hook to use auth context
export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

export default AuthContext;
