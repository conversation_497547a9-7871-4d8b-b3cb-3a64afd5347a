package com.yotelohago.service.api;

import com.yotelohago.service.domain.ServiceCategory;
import jakarta.enterprise.context.ApplicationScoped;

/**
 * Mapper for converting between ServiceCategory entity and ServiceCategoryDTO
 */
@ApplicationScoped
public class ServiceCategoryMapper {

    /**
     * Convert ServiceCategory entity to DTO
     * @param category The ServiceCategory entity
     * @return ServiceCategoryDTO
     */
    public ServiceCategoryDTO toDTO(ServiceCategory category) {
        if (category == null) {
            return null;
        }
        
        return new ServiceCategoryDTO(
                category.id,
                category.name,
                category.description,
                category.iconUrl
        );
    }

    /**
     * Convert ServiceCategoryDTO to entity
     * @param dto The ServiceCategoryDTO
     * @return ServiceCategory entity
     */
    public ServiceCategory toEntity(ServiceCategoryDTO dto) {
        if (dto == null) {
            return null;
        }
        
        ServiceCategory category = new ServiceCategory();
        category.id = dto.id;
        category.name = dto.name;
        category.description = dto.description;
        category.iconUrl = dto.iconUrl;
        
        return category;
    }

    /**
     * Convert DTO to entity for updates, preserving the ID
     * @param dto The ServiceCategoryDTO with updated data
     * @param existingCategory The existing ServiceCategory entity
     * @return Updated ServiceCategory entity
     */
    public ServiceCategory toEntityForUpdate(ServiceCategoryDTO dto, ServiceCategory existingCategory) {
        if (dto == null || existingCategory == null) {
            return null;
        }
        
        // Update fields while preserving the existing ID
        existingCategory.name = dto.name;
        existingCategory.description = dto.description;
        existingCategory.iconUrl = dto.iconUrl;
        
        return existingCategory;
    }
}
