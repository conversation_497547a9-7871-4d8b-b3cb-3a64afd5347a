mode: standalone

auth:
  rootUser: yotelohago
  rootPassword: yotelohago

persistence:
  enabled: true
  size: 10Gi
  storageClass: local-path


ingress:
  enabled: true
  ingressClassName: traefik
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-prod
    traefik.ingress.kubernetes.io/router.entrypoints: websecure
    traefik.ingress.kubernetes.io/router.tls: "true"

  hostname: minio.yotelohago.co  # Console served here
  tls: true

apiIngress:
  enabled: true
  ingressClassName: traefik
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-prod
    traefik.ingress.kubernetes.io/router.entrypoints: websecure
    traefik.ingress.kubernetes.io/router.tls: "true"

  hostname: api.minio.yotelohago.co  # Console served here
  tls: true


# TODO: Specify env vars:
#  - name: MINIO_SERVER_URL
#    value: "http://0.0.0.0:9000"
#  - name: MINIO_BROWSER_REDIRECT_URL
#    value: "http://0.0.0.0:9001"
