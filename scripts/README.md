# YoteLoHago Development Tools

A comprehensive, modular toolkit for managing and troubleshooting the YoteLoHago platform. This unified system replaces multiple scattered scripts with a single, well-organized command-line interface.

## 🚀 Quick Start

```bash
# Make the master script executable (if not already)
chmod +x scripts/yotelohago-tools.sh

# Show all available commands
./scripts/yotelohago-tools.sh

# Test authentication
./scripts/yotelohago-tools.sh <NAME_EMAIL> 1234

# Check Keycloak status
./scripts/yotelohago-tools.sh keycloak realm-status

# Run full system check
./scripts/yotelohago-tools.sh utils full-check
```

## 📁 Architecture

The tools are organized into a clean, modular architecture:

```
scripts/
├── yotelohago-tools.sh          # 🚀 Master script - SINGLE ENTRY POINT
├── lib/                         # 📚 Complete modular library
│   ├── utils.sh                 # Common utilities and HTTP functions
│   ├── keycloak.sh              # Keycloak management and configuration
│   ├── users.sh                 # User and role management
│   ├── testing.sh               # Authentication and API testing
│   ├── api.sh                   # API endpoint testing
│   └── admin.sh                 # Administrative tasks and setup
└── README.md                    # 📖 This documentation
```

**Key Design Principles:**
- **🎯 Single Entry Point**: Only `yotelohago-tools.sh` is visible in the main directory
- **📚 Pure Library**: All functionality implemented as modular libraries
- **🧹 Zero Clutter**: No legacy scripts or duplicated functionality
- **🔧 Complete Integration**: All original functionality preserved and enhanced
- **🚀 Professional Interface**: Clean, organized, and maintainable

## 🔧 Command Categories

### 📋 Keycloak Management (`keycloak`)
Manage Keycloak configuration and troubleshoot authentication issues.

```bash
# Check realm accessibility
./scripts/yotelohago-tools.sh keycloak realm-status

# List client configurations
./scripts/yotelohago-tools.sh keycloak clients

# Compare client configurations
./scripts/yotelohago-tools.sh keycloak compare-clients yotelohago-app-dev yotelohago-app-prod

# Check OpenID Connect configuration
./scripts/yotelohago-tools.sh keycloak oidc-config

# Show client scopes
./scripts/yotelohago-tools.sh keycloak client-scopes

# List realm roles
./scripts/yotelohago-tools.sh keycloak realm-roles

# Fix missing sub mapper
./scripts/yotelohago-tools.sh keycloak fix-missing-sub

# Add basic scope to clients
./scripts/yotelohago-tools.sh keycloak add-basic-scope
```
### 👥 User Management (`users`)
Manage Keycloak users and their roles.

```bash
# List all users
./scripts/yotelohago-tools.sh users list

# Show detailed user information
./scripts/yotelohago-tools.sh <NAME_EMAIL>

# Create a new user
./scripts/yotelohago-tools.sh users <NAME_EMAIL> Test User password123

# Assign realm role to user
./scripts/yotelohago-tools.sh users assign-role <EMAIL> professional

# Remove realm role from user
./scripts/yotelohago-tools.sh users remove-role <EMAIL> professional

# Migrate user from client roles to realm roles
./scripts/yotelohago-tools.sh <NAME_EMAIL>
```

### 🧪 Testing (`testing`)
Comprehensive authentication and API testing suite.

```bash
# Test token generation and validity
./scripts/yotelohago-tools.sh <NAME_EMAIL> 1234

# Test backend authentication
./scripts/yotelohago-tools.sh <NAME_EMAIL> 1234

# Test all API endpoints
./scripts/yotelohago-tools.sh <NAME_EMAIL> 1234

# Test user synchronization
./scripts/yotelohago-tools.sh <NAME_EMAIL> 1234

# Stress test authentication (10 iterations)
./scripts/yotelohago-tools.sh <NAME_EMAIL> 1234 10

# Compare localhost vs production environments
./scripts/yotelohago-tools.sh testing compare-envs <EMAIL> 1234

# Analyze JWT token contents
./scripts/yotelohago-tools.sh <NAME_EMAIL> 1234

# Test custom endpoint
./scripts/yotelohago-tools.sh testing custom /v1/<NAME_EMAIL> 1234
```

### 🌐 API Testing (`api`)
Test API endpoints, performance, and configuration.

```bash
# Test health endpoints
./scripts/yotelohago-tools.sh api health

# Test public endpoints
./scripts/yotelohago-tools.sh api public

# Test protected endpoints (should return 401)
./scripts/yotelohago-tools.sh api protected

# Test CORS configuration
./scripts/yotelohago-tools.sh api cors

# Full production API test
./scripts/yotelohago-tools.sh api production

# Test API performance (20 requests)
./scripts/yotelohago-tools.sh api performance http://localhost:8080 /v1/services 20

# Test data consistency
./scripts/yotelohago-tools.sh api consistency
```

### 🔧 Utilities (`utils`)
Common utility functions and system checks.

```bash
# Decode and analyze JWT token
./scripts/yotelohago-tools.sh utils decode-token <EMAIL> 1234

# Run complete system health check
./scripts/yotelohago-tools.sh utils full-check

# Clean up temporary files
./scripts/yotelohago-tools.sh utils cleanup

# Validate required tools are installed
./scripts/yotelohago-tools.sh utils validate-tools
```

### 🏗️ Administration (`admin`)
Administrative tasks, environment setup, and maintenance.

```bash
# Setup production Keycloak
./scripts/yotelohago-tools.sh admin setup-production

# Create backend client
./scripts/yotelohago-tools.sh admin create-client yotelohago

# Add backend client to dev realm
./scripts/yotelohago-tools.sh admin add-dev-client

# Export realm configuration
./scripts/yotelohago-tools.sh admin export-realm yotelohago backup.json

# Fix Keycloak admin client
./scripts/yotelohago-tools.sh admin fix-admin-client

# Fix service account roles
./scripts/yotelohago-tools.sh admin fix-service-roles

# Test Keycloak configuration
./scripts/yotelohago-tools.sh admin test-keycloak-config

# Test production configuration
./scripts/yotelohago-tools.sh admin test-production-config

# Test public endpoints
./scripts/yotelohago-tools.sh admin test-public-endpoints

# Test user registration
./scripts/yotelohago-tools.sh admin test-user-registration <EMAIL> password123

# Run complete environment setup
./scripts/yotelohago-tools.sh admin setup-complete

# Run all environment tests
./scripts/yotelohago-tools.sh admin test-complete
```

## ⚙️ Configuration

The tools use the following default configuration (defined in `lib/utils.sh`):

```bash
# Keycloak Configuration
KEYCLOAK_URL="https://keycloak.yotelohago.co"
REALM_NAME="yotelohago"

# API Configuration
BACKEND_URL="http://localhost:8080"
PRODUCTION_API_URL="https://api.yotelohago.co"

# Client Configuration
DEV_CLIENT_ID="yotelohago-app-dev"
DEV_CLIENT_SECRET="7tH60G8BGCLWDTHXIlNcKjwWSWg87VYi"
PROD_CLIENT_ID="yotelohago-app-prod"

# Default Test Credentials
DEFAULT_USERNAME="<EMAIL>"
DEFAULT_PASSWORD="1234"

# Admin Credentials
ADMIN_USERNAME="admin"
ADMIN_PASSWORD="yotelohago"
```

## 📋 Requirements

Ensure the following tools are installed:

- **curl** - For HTTP requests
- **jq** - For JSON processing
- **base64** - For JWT token decoding

The tools will automatically check for these dependencies and report any missing tools.

## 🎯 Common Use Cases

### Daily Development Workflow

```bash
# 1. Check system status
./scripts/yotelohago-tools.sh utils full-check

# 2. Test authentication with your user
./scripts/yotelohago-tools.sh <NAME_EMAIL> yourpassword

# 3. Test backend integration
./scripts/yotelohago-tools.sh <NAME_EMAIL> yourpassword
```

### Troubleshooting Authentication Issues

```bash
# 1. Check Keycloak realm status
./scripts/yotelohago-tools.sh keycloak realm-status

# 2. Analyze token contents
./scripts/yotelohago-tools.sh <NAME_EMAIL> 1234

# 3. Check user roles and permissions
./scripts/yotelohago-tools.sh <NAME_EMAIL>

# 4. Compare client configurations
./scripts/yotelohago-tools.sh keycloak compare-clients yotelohago-app-dev yotelohago-app-prod
```

### Production Deployment Verification

```bash
# 1. Test production API health
./scripts/yotelohago-tools.sh api production

# 2. Test production authentication
./scripts/yotelohago-tools.sh testing compare-envs <EMAIL> 1234

# 3. Check API performance
./scripts/yotelohago-tools.sh api performance https://api.yotelohago.co /v1/services 10

# 4. Verify data consistency
./scripts/yotelohago-tools.sh api consistency https://api.yotelohago.co
```

### User Management Tasks

```bash
# Create a new test user
./scripts/yotelohago-tools.sh users <NAME_EMAIL> Test Professional password123

# Assign professional role
./scripts/yotelohago-tools.sh users assign-role testpro professional

# Verify user setup
./scripts/yotelohago-tools.sh users show testpro

# Test new user authentication
./scripts/yotelohago-tools.sh testing token testpro password123
```

## 🔍 Troubleshooting

### Common Issues

**1. "Library not found" error**
```bash
# Ensure you're running from the correct directory
cd yotelohago-backend
./scripts/yotelohago-tools.sh
```

**2. "Missing required tools" error**
```bash
# Install missing dependencies
brew install curl jq  # macOS
apt-get install curl jq  # Ubuntu/Debian
```

**3. "Failed to get admin token" error**
- Check Keycloak admin credentials in `lib/utils.sh`
- Verify Keycloak server is accessible
- Check network connectivity

**4. "Token validation failed" error**
- Verify user credentials
- Check if user exists in Keycloak
- Ensure user has proper roles assigned

### Debug Mode

For detailed debugging, you can modify the scripts to show more verbose output:

```bash
# Add debug flag to see detailed HTTP requests
export DEBUG=1
./scripts/yotelohago-tools.sh <NAME_EMAIL> 1234
```

## 🚀 Migration from Old Scripts

This new modular system replaces the following legacy scripts:

| Old Script | New Command |
|------------|-------------|
| `test-backend-auth.sh` | `testing backend` |
| `manage-keycloak-users.sh` | `users *` |
| `test-production-api.sh` | `api production` |
| `quick-troubleshoot.sh` | `utils full-check` |
| `debug-auth-flow.sh` | `testing analyze` |
| `compare-keycloak-clients.sh` | `keycloak compare-clients` |

The old scripts can be safely removed after verifying the new system works correctly.

## 🤝 Contributing

When adding new functionality:

1. **Add functions to appropriate library** (`lib/*.sh`)
2. **Update the main router** in `yotelohago-tools.sh`
3. **Add help documentation** in the respective `*_help()` function
4. **Update this README** with new commands and examples
5. **Test thoroughly** with various scenarios

### Adding a New Command Category

1. Create new library file: `lib/newcategory.sh`
2. Implement `newcategory_main()` and `newcategory_help()` functions
3. Add library loading in `yotelohago-tools.sh`
4. Add routing case in the main function
5. Update help documentation

## 📝 License

This toolset is part of the YoteLoHago project and follows the same licensing terms.
