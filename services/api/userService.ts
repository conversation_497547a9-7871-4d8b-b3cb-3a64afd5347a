import { apiGet, apiGetPublic } from './client';
import { UserWithDetailsDTO } from '../../types/api';
import { UserMode } from '../../types/auth';

/**
 * User API Service
 * Handles user data fetching and management
 */
export class UserService {
  private readonly basePath = '/users';

  /**
   * Get current user profile data with enhanced information
   * Returns complete profile including professional data and media URLs
   */
  async getCurrentUser(): Promise<UserWithDetailsDTO> {
    try {
      console.log('🔍 Fetching enhanced user profile from /users/me');

      const response = await apiGet<UserWithDetailsDTO>(`${this.basePath}/me`);

      console.log('✅ Enhanced user profile fetched successfully:', {
        id: response.id,
        username: response.username,
        isProfessional: response.isProfessional,
        hasClientPhoto: !!response.clientProfilePhotoUrl,
        hasProfessionalPhoto: !!response.professionalProfilePhotoUrl,
        professionalData: response.isProfessional ? {
          bio: !!response.bio,
          city: !!response.city,
          avgRating: response.avgRating,
          available: response.available
        } : null
      });

      return response;
    } catch (error) {
      console.error('❌ Failed to fetch user profile:', error);
      throw new Error('Failed to fetch user profile. Please try again.');
    }
  }

  /**
   * Get profile photo URL for current user based on mode
   * Uses the enhanced UserWithDetailsDTO data instead of separate Media API calls
   */
  getProfilePhotoUrl(userProfile: UserWithDetailsDTO, userMode: UserMode): string | null {
    if (!userProfile) return null;

    const photoUrl = userMode === UserMode.PROFESSIONAL
      ? userProfile.professionalProfilePhotoUrl
      : userProfile.clientProfilePhotoUrl;

    return photoUrl || null;
  }

  /**
   * Update user profile
   */
  async updateProfile(userData: Partial<UserWithDetailsDTO>): Promise<UserWithDetailsDTO> {
    try {
      console.log('🔄 Updating user profile...');

      const response = await apiGet<UserWithDetailsDTO>(`${this.basePath}/me`, {
        method: 'PUT',
        body: JSON.stringify(userData),
      });

      console.log('✅ User profile updated successfully');
      return response;
    } catch (error) {
      console.error('❌ Failed to update user profile:', error);
      throw new Error('Failed to update profile. Please try again.');
    }
  }
}

// Export singleton instance
export const userService = new UserService();
export default userService;
