import React from 'react';
import { motion } from 'framer-motion';

const LightningBolt: React.FC = () => {
  return (
    <div className="relative w-full h-full flex items-center justify-center">
      {/* Main Lightning Bolt - Responsive sizing */}
      <motion.svg
        width="100%"
        height="100%"
        viewBox="0 0 96 96"
        className="relative z-10 max-w-20 max-h-20 sm:max-w-24 sm:max-h-24"
        animate={{
          scale: [1, 1.05, 1],
          rotate: [0, 1, -1, 0],
        }}
        transition={{
          duration: 2,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      >
        <defs>
          {/* Enhanced Yellow-White Glow Filter */}
          <filter id="lightning-glow" x="-100%" y="-100%" width="300%" height="300%">
            <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
            <feFlood floodColor="#FCD34D" floodOpacity="0.9"/>
            <feComposite in2="coloredBlur" operator="in"/>
            <feMerge> 
              <feMergeNode/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
          
          {/* Yellow-White Gradient Core */}
          <linearGradient id="lightning-core" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#FFFFFF" />
            <stop offset="20%" stopColor="#FEF3C7" />
            <stop offset="50%" stopColor="#FCD34D" />
            <stop offset="80%" stopColor="#F59E0B" />
            <stop offset="100%" stopColor="#D97706" />
          </linearGradient>
          
          {/* Bright White-Yellow Highlight Gradient */}
          <linearGradient id="lightning-highlight" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#FFFFFF" />
            <stop offset="30%" stopColor="#FFFBEB" />
            <stop offset="70%" stopColor="#FEF3C7" />
            <stop offset="100%" stopColor="#FCD34D" />
          </linearGradient>
        </defs>
        
        {/* Main Lightning Bolt Path */}
        <motion.path
          d="M48 6 L30 42 L42 42 L36 90 L66 36 L54 36 L48 6 Z"
          fill="url(#lightning-core)"
          filter="url(#lightning-glow)"
          animate={{
            filter: [
              "url(#lightning-glow)",
              "url(#lightning-glow) brightness(1.4)",
              "url(#lightning-glow)"
            ]
          }}
          transition={{
            duration: 1.5,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        
        {/* Branching Lightning Spike 1 - Top Right */}
        <motion.path
          d="M45 18 L52 12 L50 20 L56 16"
          stroke="url(#lightning-core)"
          strokeWidth="2"
          fill="none"
          filter="url(#lightning-glow)"
          animate={{
            opacity: [0, 1, 0, 1, 0],
            strokeWidth: [1, 3, 1]
          }}
          transition={{
            duration: 1.2,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 0.3
          }}
        />
        
        {/* Branching Lightning Spike 2 - Top Left */}
        <motion.path
          d="M42 22 L36 16 L38 24 L32 20"
          stroke="url(#lightning-core)"
          strokeWidth="2"
          fill="none"
          filter="url(#lightning-glow)"
          animate={{
            opacity: [0, 1, 0, 1],
            strokeWidth: [1, 2.5, 1]
          }}
          transition={{
            duration: 1.8,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 0.7
          }}
        />
        
        {/* Branching Lightning Spike 3 - Middle Right */}
        <motion.path
          d="M54 38 L62 32 L58 42 L66 38"
          stroke="url(#lightning-core)"
          strokeWidth="2"
          fill="none"
          filter="url(#lightning-glow)"
          animate={{
            opacity: [0, 1, 0],
            strokeWidth: [1, 3, 1]
          }}
          transition={{
            duration: 1.5,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 1.1
          }}
        />
        
        {/* Branching Lightning Spike 4 - Middle Left */}
        <motion.path
          d="M38 46 L30 40 L34 50 L26 46"
          stroke="url(#lightning-core)"
          strokeWidth="2"
          fill="none"
          filter="url(#lightning-glow)"
          animate={{
            opacity: [0, 1, 0, 1, 0],
            strokeWidth: [1, 2, 1]
          }}
          transition={{
            duration: 2.1,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 0.5
          }}
        />
        
        {/* Branching Lightning Spike 5 - Bottom Right */}
        <motion.path
          d="M58 68 L66 62 L62 72 L70 68"
          stroke="url(#lightning-core)"
          strokeWidth="2"
          fill="none"
          filter="url(#lightning-glow)"
          animate={{
            opacity: [0, 1, 0],
            strokeWidth: [1, 2.5, 1]
          }}
          transition={{
            duration: 1.3,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 0.9
          }}
        />
        
        {/* Branching Lightning Spike 6 - Bottom Left */}
        <motion.path
          d="M40 74 L32 68 L36 78 L28 74"
          stroke="url(#lightning-core)"
          strokeWidth="2"
          fill="none"
          filter="url(#lightning-glow)"
          animate={{
            opacity: [0, 1, 0, 1],
            strokeWidth: [1, 3, 1]
          }}
          transition={{
            duration: 1.7,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 1.3
          }}
        />
        
        {/* Additional Jagged Offshoots */}
        <motion.path
          d="M48 28 L52 24 L50 30"
          stroke="url(#lightning-core)"
          strokeWidth="1.5"
          fill="none"
          filter="url(#lightning-glow)"
          animate={{
            opacity: [0, 1, 0],
            strokeWidth: [1, 2, 1]
          }}
          transition={{
            duration: 0.8,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 0.2
          }}
        />
        
        <motion.path
          d="M44 52 L40 48 L42 54"
          stroke="url(#lightning-core)"
          strokeWidth="1.5"
          fill="none"
          filter="url(#lightning-glow)"
          animate={{
            opacity: [0, 1, 0],
            strokeWidth: [1, 2, 1]
          }}
          transition={{
            duration: 1.1,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 0.6
          }}
        />
        
        <motion.path
          d="M52 58 L56 54 L54 62"
          stroke="url(#lightning-core)"
          strokeWidth="1.5"
          fill="none"
          filter="url(#lightning-glow)"
          animate={{
            opacity: [0, 1, 0],
            strokeWidth: [1, 2, 1]
          }}
          transition={{
            duration: 0.9,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 1.0
          }}
        />
        
        {/* Inner White-Yellow Highlight */}
        <motion.path
          d="M48 8 L32 40 L42 40 L38 86 L64 38 L54 38 L48 8 Z"
          fill="url(#lightning-highlight)"
          opacity={0.8}
          animate={{
            opacity: [0.6, 1, 0.4, 0.8]
          }}
          transition={{
            duration: 1,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
      </motion.svg>

      {/* Electric Energy Sparks - Yellow-White Theme */}
      <motion.div
        className="absolute top-4 right-6 w-1 h-1 bg-yellow-200 rounded-full shadow-lg shadow-yellow-400"
        animate={{
          scale: [0, 1.5, 0],
          opacity: [0, 1, 0],
          x: [0, 12, 0],
          y: [0, -6, 0]
        }}
        transition={{
          duration: 1.5,
          repeat: Infinity,
          ease: "easeOut",
          delay: 0.2
        }}
      />
      
      <motion.div
        className="absolute bottom-6 left-4 w-1 h-1 bg-yellow-200 rounded-full shadow-lg shadow-yellow-400"
        animate={{
          scale: [0, 1.2, 0],
          opacity: [0, 1, 0],
          x: [0, -8, 0],
          y: [0, 8, 0]
        }}
        transition={{
          duration: 1.2,
          repeat: Infinity,
          ease: "easeOut",
          delay: 0.8
        }}
      />
      
      <motion.div
        className="absolute top-1/2 right-2 w-1 h-1 bg-yellow-200 rounded-full shadow-lg shadow-yellow-400"
        animate={{
          scale: [0, 1, 0],
          opacity: [0, 1, 0],
          x: [0, 14, 0],
          y: [0, -3, 0]
        }}
        transition={{
          duration: 1.8,
          repeat: Infinity,
          ease: "easeOut",
          delay: 1.2
        }}
      />

      <motion.div
        className="absolute top-8 left-6 w-1 h-1 bg-yellow-200 rounded-full shadow-lg shadow-yellow-400"
        animate={{
          scale: [0, 1.3, 0],
          opacity: [0, 1, 0],
          x: [0, -10, 0],
          y: [0, -4, 0]
        }}
        transition={{
          duration: 1.4,
          repeat: Infinity,
          ease: "easeOut",
          delay: 0.5
        }}
      />

      {/* Central Energy Core - Bright Yellow-White */}
      <motion.div
        className="absolute inset-0 flex items-center justify-center"
        animate={{
          scale: [0.8, 1.1, 0.8],
        }}
        transition={{
          duration: 1,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      >
        <div className="w-3 h-3 bg-yellow-200 rounded-full blur-sm opacity-90 shadow-lg shadow-yellow-400" />
      </motion.div>
    </div>
  );
};

export default LightningBolt;