controller:

  agentListenerPort: 50000

  #jenkinsUriPrefix: "/"
  ingress:
    enabled: true
    apiVersion: "networking.k8s.io/v1"
    ingressClassName: "traefik"
    hostName: "jenkins.yotelohago.co"
    path: "/"
    tls:
      - hosts:
          - "jenkins.yotelohago.co"
        secretName: "jenkins-yotelohago-co-tls"
    annotations:
      kubernetes.io/ingress.class: "traefik"
      #traefik.ingress.kubernetes.io/router.entrypoints: "web, websecure"
      traefik.ingress.kubernetes.io/router.tls: "true"

      cert-manager.io/cluster-issuer: letsencrypt-prod

  persistence:
    enabled: true
    #existingClaim: ""           # Optional: if you already created a PVC
    storageClass: "local-path"  # Or whatever StorageClass your K3s supports
    accessMode: "ReadWriteOnce"
    size: "40Gi"

  # Disable liveness probe
  livenessProbe:
    enabled: false

  # Disable readiness probe
  readinessProbe:
    enabled: false

  # For Jenkins versions using startupProbe (Kubernetes 1.16+)
  startupProbe:
    enabled: false
#    httpGet:
#      path: /login
#      port: http
#    failureThreshold: 12
#    periodSeconds: 10

  JCasC:
    enabled: true
    defaultConfig: false
    configScripts:
      kubernetes-agent: |
        jenkins:
          clouds:
           - kubernetes:
                 containerCap: 10
                 containerCapStr: "10"
                 jenkinsTunnel: "jenkins-agent.jenkins.svc.cluster.local:50000"
                 jenkinsUrl: "http://jenkins.jenkins.svc.cluster.local:8080"
                 name: "kubernetes"
                 namespace: "jenkins"
                 podLabels:
                 - key: "jenkins-builder"
                   value: "true"
                 serverUrl: "https://kubernetes.default"
                 templates:
                 - agentInjection: true
                   containers:
                   - envVars:
                     - envVar:
                         key: "JENKINS_URL"
                         value: "http://jenkins.jenkins.svc.cluster.local:8080/"
                     image: "registry.yotelohago.co/jenkins-oci-builder:latest"
                     livenessProbe:
                       failureThreshold: 0
                       initialDelaySeconds: 0
                       periodSeconds: 0
                       successThreshold: 0
                       timeoutSeconds: 0
                     name: "jnlp"
                     privileged: true
                     resourceLimitCpu: "3"
                     resourceLimitMemory: "4Gi"
                     resourceRequestCpu: "512m"
                     resourceRequestMemory: "512Mi"
                     workingDir: "/home/<USER>/agent"
                   id: "bb1505765acedf474d53334569583a3eec0c440220cb0040cb6c34c9a36e808b"
                   imagePullSecrets:
                   - name: "registry-auth-secret"
                   label: "jenkins-builder-v0"
                   name: "jenkins-builder"
                   namespace: "jenkins"
                   podRetention: "never"
                   serviceAccount: "default"
                   slaveConnectTimeout: 100
                   slaveConnectTimeoutStr: "100"
                   yamlMergeStrategy: "override"
  installPlugins:
    - kubernetes
    - workflow-aggregator
    - git
    - credentials
    - kubernetes-credentials
    - configuration-as-code
    - pipeline-stage-view

  additionalPlugins: []

rbac:
  create: true

serviceAccount:
  create: true

#serviceType: "ClusterIP"
#servicePort: 8081