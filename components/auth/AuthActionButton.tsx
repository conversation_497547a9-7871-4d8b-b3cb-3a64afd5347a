import React from 'react';
import { TouchableOpacity, Text, StyleSheet, ViewStyle, TextStyle } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../../contexts/AuthContext';
import { navigateToAuthRequired, getCurrentPath } from '../../utils/authNavigation';
import Colors from '../../app/client/constants/Colors';

interface AuthActionButtonProps {
  // Action details
  actionType: 'booking' | 'messaging' | 'general';
  actionName: string;
  onPress: () => void; // What to do when authenticated
  
  // Professional context (optional)
  professionalId?: string;
  professionalName?: string;
  
  // Button styling
  title: string;
  icon?: string;
  style?: ViewStyle;
  textStyle?: TextStyle;
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
}

export default function AuthActionButton({
  actionType,
  actionName,
  onPress,
  professionalId,
  professionalName,
  title,
  icon,
  style,
  textStyle,
  variant = 'primary',
  size = 'medium',
  disabled = false,
}: AuthActionButtonProps) {
  const { state: authState } = useAuth();

  // Debug logging
  React.useEffect(() => {
    console.log(`🔍 AuthActionButton (${actionName}) state:`, {
      isAuthenticated: authState.isAuthenticated,
      isLoading: authState.isLoading,
      hasUser: !!authState.user,
      professionalId,
    });
  }, [authState.isAuthenticated, authState.isLoading, authState.user, actionName, professionalId]);

  const handlePress = () => {
    console.log(`🔄 AuthActionButton (${actionName}) pressed:`, {
      isAuthenticated: authState.isAuthenticated,
      isLoading: authState.isLoading,
      disabled,
    });

    if (disabled) return;

    if (authState.isAuthenticated) {
      // User is authenticated, proceed with action
      console.log(`✅ User authenticated, proceeding with ${actionName}`);
      onPress();
    } else {
      // User needs to authenticate, navigate to auth screen
      console.log(`❌ User not authenticated, redirecting to auth screen for ${actionName}`);
      const returnPath = getCurrentPath();

      navigateToAuthRequired({
        actionType,
        actionName,
        returnPath,
        professionalId,
        professionalName,
      });
    }
  };

  const getButtonStyle = () => {
    const baseStyle = [styles.button, styles[size]];
    
    if (disabled) {
      return [...baseStyle, styles.disabledButton];
    }
    
    switch (variant) {
      case 'primary':
        return [...baseStyle, styles.primaryButton];
      case 'secondary':
        return [...baseStyle, styles.secondaryButton];
      case 'outline':
        return [...baseStyle, styles.outlineButton];
      default:
        return [...baseStyle, styles.primaryButton];
    }
  };

  const getTextStyle = () => {
    const baseStyle = [styles.buttonText, styles[`${size}Text`]];
    
    if (disabled) {
      return [...baseStyle, styles.disabledText];
    }
    
    switch (variant) {
      case 'primary':
        return [...baseStyle, styles.primaryText];
      case 'secondary':
        return [...baseStyle, styles.secondaryText];
      case 'outline':
        return [...baseStyle, styles.outlineText];
      default:
        return [...baseStyle, styles.primaryText];
    }
  };

  const getIconSize = () => {
    switch (size) {
      case 'small':
        return 16;
      case 'medium':
        return 20;
      case 'large':
        return 24;
      default:
        return 20;
    }
  };

  const getIconColor = () => {
    if (disabled) return Colors.text.disabled;
    
    switch (variant) {
      case 'primary':
        return 'white';
      case 'secondary':
        return Colors.primary;
      case 'outline':
        return Colors.primary;
      default:
        return 'white';
    }
  };

  return (
    <TouchableOpacity 
      style={[getButtonStyle(), style]} 
      onPress={handlePress}
      disabled={disabled || authState.isLoading}
    >
      {icon && (
        <Ionicons 
          name={icon as any} 
          size={getIconSize()} 
          color={getIconColor()} 
        />
      )}
      <Text style={[getTextStyle(), textStyle]}>
        {title}
      </Text>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,
    gap: 8,
  },
  
  // Size variants
  small: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    gap: 4,
  },
  medium: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 6,
  },
  large: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    gap: 8,
  },
  
  // Button variants
  primaryButton: {
    backgroundColor: Colors.primary,
  },
  secondaryButton: {
    backgroundColor: Colors.background.secondary,
  },
  outlineButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: Colors.primary,
  },
  disabledButton: {
    backgroundColor: Colors.background.disabled,
  },
  
  // Text styles
  buttonText: {
    fontWeight: '600',
  },
  smallText: {
    fontSize: 12,
  },
  mediumText: {
    fontSize: 14,
  },
  largeText: {
    fontSize: 16,
  },
  
  // Text color variants
  primaryText: {
    color: 'white',
  },
  secondaryText: {
    color: Colors.text.primary,
  },
  outlineText: {
    color: Colors.primary,
  },
  disabledText: {
    color: Colors.text.disabled,
  },
});
