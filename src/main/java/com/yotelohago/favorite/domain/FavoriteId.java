package com.yotelohago.favorite.domain;

import java.io.Serializable;
import java.util.Objects;
import java.util.UUID;

/**
 * Composite primary key for Favorite entity.
 *
 * <p>Property names must match the @Id property names in the Favorite entity.
 * Since Favorite uses 'user' and 'service' as @Id properties, this class
 * must use the same names but store the actual UUID values.
 */
public class FavoriteId implements Serializable {

    // Property names must match @Id property names in Favorite entity
    private UUID user;    // Matches Favorite.user (stores user.id)
    private UUID service; // Matches Favorite.service (stores service.id)

    public FavoriteId() {
        // Default constructor required by JPA
    }

    public FavoriteId(UUID userId, UUID serviceId) {
        this.user = userId;
        this.service = serviceId;
    }

    public UUID getUser() {
        return user;
    }

    public void setUser(UUID user) {
        this.user = user;
    }

    public UUID getService() {
        return service;
    }

    public void setService(UUID service) {
        this.service = service;
    }

    // Convenience methods for backward compatibility
    public UUID getUserId() {
        return user;
    }

    public void setUserId(UUID userId) {
        this.user = userId;
    }

    public UUID getServiceId() {
        return service;
    }

    public void setServiceId(UUID serviceId) {
        this.service = serviceId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        FavoriteId that = (FavoriteId) o;
        return Objects.equals(user, that.user) &&
               Objects.equals(service, that.service);
    }

    @Override
    public int hashCode() {
        return Objects.hash(user, service);
    }

    @Override
    public String toString() {
        return "FavoriteId{" +
                "userId=" + user +
                ", serviceId=" + service +
                '}';
    }
}
