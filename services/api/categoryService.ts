import { apiGet, apiGetPublic, apiPost, apiPut, apiDelete } from './client';
import { ApiEndpoints } from './types';
import { ServiceCategoryDTO, ServiceDTO, PaginatedResponse } from '../../types/api';

/**
 * Service Category API Service
 * Uses the dedicated service categories endpoint for efficient data retrieval
 */

/**
 * Get all service categories from the dedicated endpoint
 *
 * @returns Promise with array of service categories
 */
export const getCategories = async (): Promise<ServiceCategoryDTO[]> => {
  try {
    console.log('🚀 Fetching service categories from dedicated endpoint');

    // Use the dedicated service categories endpoint
    const categories = await apiGetPublic<ServiceCategoryDTO[]>(ApiEndpoints.SERVICE_CATEGORIES);

    console.log('✅ Categories fetched successfully:', {
      count: categories.length,
      categories: categories.map(cat => ({ id: cat.id, name: cat.name }))
    });

    return categories;
  } catch (error) {
    console.error('❌ Failed to fetch categories:', error);
    throw new Error('Failed to fetch service categories. Please try again.');
  }
};

/**
 * Get a single category by ID
 *
 * @param id Category ID
 * @returns Promise with category data
 */
export const getCategoryById = async (id: string): Promise<ServiceCategoryDTO> => {
  try {
    console.log('🚀 Fetching category by ID:', id);

    const response = await apiGetPublic<ServiceCategoryDTO>(`${ApiEndpoints.SERVICE_CATEGORIES}/${id}`);

    console.log('✅ Category fetched successfully:', {
      id: response.id,
      name: response.name
    });

    return response;
  } catch (error) {
    console.error('❌ Failed to fetch category:', error);
    throw new Error('Failed to fetch category details. Please try again.');
  }
};

/**
 * Create a new service category - Admin only
 *
 * @param categoryData Category data to create
 * @returns Promise with created category data
 */
export const createCategory = async (categoryData: Omit<ServiceCategoryDTO, 'id'>): Promise<ServiceCategoryDTO> => {
  try {
    console.log('🚀 Creating new category:', categoryData.name);

    const response = await apiPost<ServiceCategoryDTO>(ApiEndpoints.SERVICE_CATEGORIES, categoryData);

    console.log('✅ Category created successfully:', {
      id: response.id,
      name: response.name
    });

    // Clear cache after creating
    clearCategoriesCache();

    return response;
  } catch (error) {
    console.error('❌ Failed to create category:', error);
    throw new Error('Failed to create service category. Please try again.');
  }
};

/**
 * Update an existing service category - Admin only
 *
 * @param id Category ID to update
 * @param categoryData Updated category data
 * @returns Promise with updated category data
 */
export const updateCategory = async (id: string, categoryData: Omit<ServiceCategoryDTO, 'id'>): Promise<ServiceCategoryDTO> => {
  try {
    console.log('🚀 Updating category:', id, categoryData.name);

    const response = await apiPut<ServiceCategoryDTO>(`${ApiEndpoints.SERVICE_CATEGORIES}/${id}`, categoryData);

    console.log('✅ Category updated successfully:', {
      id: response.id,
      name: response.name
    });

    // Clear cache after updating
    clearCategoriesCache();

    return response;
  } catch (error) {
    console.error('❌ Failed to update category:', error);
    throw new Error('Failed to update service category. Please try again.');
  }
};

/**
 * Delete a service category - Admin only
 *
 * @param id Category ID to delete
 * @returns Promise that resolves when deletion is complete
 */
export const deleteCategory = async (id: string): Promise<void> => {
  try {
    console.log('🚀 Deleting category:', id);

    await apiDelete(`${ApiEndpoints.SERVICE_CATEGORIES}/${id}`);

    console.log('✅ Category deleted successfully');

    // Clear cache after deleting
    clearCategoriesCache();
  } catch (error) {
    console.error('❌ Failed to delete category:', error);
    throw new Error('Failed to delete service category. Please try again.');
  }
};

/**
 * Get category by name - Public access
 *
 * @param name Category name
 * @returns Promise with category data
 */
export const getCategoryByName = async (name: string): Promise<ServiceCategoryDTO> => {
  try {
    console.log('🚀 Fetching category by name:', name);

    const response = await apiGetPublic<ServiceCategoryDTO>(`${ApiEndpoints.SERVICE_CATEGORIES}/by-name/${encodeURIComponent(name)}`);

    console.log('✅ Category fetched successfully:', {
      id: response.id,
      name: response.name
    });

    return response;
  } catch (error) {
    console.error('❌ Failed to fetch category by name:', error);
    throw new Error('Failed to fetch category by name. Please try again.');
  }
};

/**
 * Cache for categories to avoid repeated API calls
 * Categories don't change frequently, so we can cache them
 */
let categoriesCache: ServiceCategoryDTO[] | null = null;
let cacheTimestamp: number | null = null;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

/**
 * Get categories with caching
 * 
 * @param forceRefresh Force refresh the cache
 * @returns Promise with cached or fresh categories
 */
export const getCategoriesWithCache = async (forceRefresh = false): Promise<ServiceCategoryDTO[]> => {
  try {
    const now = Date.now();
    
    // Check if we have valid cached data
    if (!forceRefresh && 
        categoriesCache && 
        cacheTimestamp && 
        (now - cacheTimestamp) < CACHE_DURATION) {
      console.log('📦 Using cached categories');
      return categoriesCache;
    }
    
    // Fetch fresh data
    console.log('🔄 Refreshing categories cache');
    const categories = await getCategories();
    
    // Update cache
    categoriesCache = categories;
    cacheTimestamp = now;
    
    return categories;
  } catch (error) {
    // If API fails and we have cached data, return it
    if (categoriesCache) {
      console.warn('⚠️ API failed, using cached categories');
      return categoriesCache;
    }
    throw error;
  }
};

/**
 * Clear the categories cache
 * Useful when you know categories have been updated
 */
export const clearCategoriesCache = (): void => {
  console.log('🗑️ Clearing categories cache');
  categoriesCache = null;
  cacheTimestamp = null;
};

/**
 * Map backend category to frontend category format
 * This helps maintain compatibility with existing frontend code
 *
 * @param backendCategory Backend ServiceCategoryDTO
 * @returns Frontend-compatible category object
 */
export const mapToFrontendCategory = (backendCategory: ServiceCategoryDTO) => {
  return {
    id: backendCategory.id,
    name: backendCategory.name,
    description: backendCategory.description || '',
    // Use iconUrl from backend, fallback to name-based mapping if not available
    icon: backendCategory.iconUrl || getCategoryIcon(backendCategory.name)
  };
};

/**
 * Get icon name based on category name
 * This is a fallback solution when backend doesn't provide iconUrl
 *
 * @param categoryName Category name
 * @returns Icon name for the category
 */
const getCategoryIcon = (categoryName: string): string => {
  const iconMap: Record<string, string> = {
    // Primary trade categories (lowercase - current standard)
    'electrician': 'electric',
    'plumber': 'pipe-leak',
    'mover': 'truck',
    'locksmith': 'unlock',
    // Primary trade categories (capitalized - backward compatibility)
    'Electrician': 'electric',
    'Plumber': 'pipe-leak',
    'Mover': 'truck',
    'Locksmith': 'unlock',
    // Additional categories (legacy support)
    'Cleaning': 'home',
    'Plumbing': 'pipe-leak',
    'Tutoring': 'book-open',
    'Photography': 'camera',
    'Web Design': 'code',
    'Gardening': 'scissors',
    'Personal Training': 'activity',
    'Cooking': 'chef-hat',
    'Beauty': 'scissors',
    'Handyman': 'tool'
  };

  return iconMap[categoryName] || 'home';
};

/**
 * Get categories mapped to frontend format with caching
 * 
 * @param forceRefresh Force refresh the cache
 * @returns Promise with frontend-compatible categories
 */
export const getFrontendCategories = async (forceRefresh = false) => {
  try {
    const backendCategories = await getCategoriesWithCache(forceRefresh);
    return backendCategories.map(mapToFrontendCategory);
  } catch (error) {
    console.error('❌ Failed to get frontend categories:', error);
    throw error;
  }
};

// Export default service object
export default {
  getCategories,
  getCategoryById,
  createCategory,
  updateCategory,
  deleteCategory,
  getCategoryByName,
  getCategoriesWithCache,
  clearCategoriesCache,
  mapToFrontendCategory,
  getFrontendCategories,
};
