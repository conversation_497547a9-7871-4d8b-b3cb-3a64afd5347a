import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  TextInput,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { TimeRangeDTO } from '../../../types/api';

interface TimeRangePickerProps {
  timeRange: TimeRangeDTO;
  onTimeRangeChange: (timeRange: TimeRangeDTO) => void;
  onRemove?: () => void;
  disabled?: boolean;
}

/**
 * Time Range Picker Component for Professional Mode
 * Allows professionals to select start and end times for availability slots
 * Follows the black/white theme established in the calendar
 */
export const TimeRangePicker: React.FC<TimeRangePickerProps> = ({
  timeRange,
  onTimeRangeChange,
  onRemove,
  disabled = false,
}) => {

  // Round time to nearest 5-minute interval (matching backend constraint)
  const roundToNearestFiveMinutes = (timeString: string): string => {
    const [hours, minutes] = timeString.split(':').map(Number);
    const roundedMinutes = Math.round(minutes / 5) * 5;
    const adjustedHours = roundedMinutes >= 60 ? hours + 1 : hours;
    const finalMinutes = roundedMinutes >= 60 ? 0 : roundedMinutes;

    return `${adjustedHours.toString().padStart(2, '0')}:${finalMinutes.toString().padStart(2, '0')}`;
  };

  // Validate time format and convert to 24-hour format
  const validateAndFormatTime = (input: string): string | null => {
    // Remove any non-digit characters except colon
    const cleaned = input.replace(/[^\d:]/g, '');

    // Try to parse as HH:mm
    const timeMatch = cleaned.match(/^(\d{1,2}):?(\d{0,2})$/);
    if (!timeMatch) return null;

    let hours = parseInt(timeMatch[1], 10);
    let minutes = timeMatch[2] ? parseInt(timeMatch[2], 10) : 0;

    // Validate ranges
    if (hours > 23 || minutes > 59) return null;

    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
  };

  const handleStartTimeChange = (input: string) => {
    const validTime = validateAndFormatTime(input);
    if (!validTime) return;

    const roundedTime = roundToNearestFiveMinutes(validTime);

    // Ensure start time is before end time
    const [startHours, startMinutes] = roundedTime.split(':').map(Number);
    const [endHours, endMinutes] = timeRange.endTime.split(':').map(Number);

    const startTotalMinutes = startHours * 60 + startMinutes;
    const endTotalMinutes = endHours * 60 + endMinutes;

    if (startTotalMinutes >= endTotalMinutes) {
      // Auto-adjust end time to be 1 hour after start time
      const newEndTotalMinutes = startTotalMinutes + 60;
      const newEndHours = Math.floor(newEndTotalMinutes / 60) % 24;
      const newEndMinutes = newEndTotalMinutes % 60;
      const newEndTime = `${newEndHours.toString().padStart(2, '0')}:${newEndMinutes.toString().padStart(2, '0')}`;

      onTimeRangeChange({
        startTime: roundedTime,
        endTime: newEndTime,
      });
    } else {
      onTimeRangeChange({
        ...timeRange,
        startTime: roundedTime,
      });
    }
  };

  const handleEndTimeChange = (input: string) => {
    const validTime = validateAndFormatTime(input);
    if (!validTime) return;

    const roundedTime = roundToNearestFiveMinutes(validTime);

    // Ensure end time is after start time
    const [startHours, startMinutes] = timeRange.startTime.split(':').map(Number);
    const [endHours, endMinutes] = roundedTime.split(':').map(Number);

    const startTotalMinutes = startHours * 60 + startMinutes;
    const endTotalMinutes = endHours * 60 + endMinutes;

    if (endTotalMinutes <= startTotalMinutes) {
      // Auto-adjust start time to be 1 hour before end time
      const newStartTotalMinutes = Math.max(0, endTotalMinutes - 60);
      const newStartHours = Math.floor(newStartTotalMinutes / 60);
      const newStartMinutes = newStartTotalMinutes % 60;
      const newStartTime = `${newStartHours.toString().padStart(2, '0')}:${newStartMinutes.toString().padStart(2, '0')}`;

      onTimeRangeChange({
        startTime: newStartTime,
        endTime: roundedTime,
      });
    } else {
      onTimeRangeChange({
        ...timeRange,
        endTime: roundedTime,
      });
    }
  };

  const formatTimeForDisplay = (timeString: string): string => {
    const [hours, minutes] = timeString.split(':').map(Number);
    const period = hours >= 12 ? 'PM' : 'AM';
    const displayHours = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;
    return `${displayHours}:${minutes.toString().padStart(2, '0')} ${period}`;
  };

  return (
    <View style={styles.container}>
      <View style={styles.timeRangeContainer}>
        {/* Start Time Input */}
        <View style={styles.timeInputContainer}>
          <Text style={[styles.timeLabel, disabled && styles.disabledText]}>
            From
          </Text>
          <TextInput
            style={[styles.timeInput, disabled && styles.disabledInput]}
            value={timeRange.startTime}
            onChangeText={handleStartTimeChange}
            placeholder="09:00"
            editable={!disabled}
            keyboardType="numeric"
            maxLength={5}
          />
          <Text style={[styles.timeDisplayText, disabled && styles.disabledText]}>
            {formatTimeForDisplay(timeRange.startTime)}
          </Text>
        </View>

        <Text style={styles.separator}>to</Text>

        {/* End Time Input */}
        <View style={styles.timeInputContainer}>
          <Text style={[styles.timeLabel, disabled && styles.disabledText]}>
            Until
          </Text>
          <TextInput
            style={[styles.timeInput, disabled && styles.disabledInput]}
            value={timeRange.endTime}
            onChangeText={handleEndTimeChange}
            placeholder="17:00"
            editable={!disabled}
            keyboardType="numeric"
            maxLength={5}
          />
          <Text style={[styles.timeDisplayText, disabled && styles.disabledText]}>
            {formatTimeForDisplay(timeRange.endTime)}
          </Text>
        </View>

        {/* Remove Button */}
        {onRemove && (
          <TouchableOpacity
            style={[styles.removeButton, disabled && styles.disabledButton]}
            onPress={onRemove}
            disabled={disabled}
          >
            <Ionicons
              name="trash"
              size={18}
              color={disabled ? '#CCCCCC' : '#FF0000'}
            />
          </TouchableOpacity>
        )}
      </View>


    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 4,
  },
  timeRangeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 12,
    paddingVertical: 8,
    backgroundColor: '#FFFFFF',
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#E5E5E5',
  },
  timeInputContainer: {
    flex: 1,
    alignItems: 'center',
  },
  timeLabel: {
    fontSize: 11,
    color: '#666666',
    marginBottom: 4,
  },
  timeInput: {
    fontSize: 14,
    fontWeight: '600',
    color: '#000000',
    borderWidth: 1,
    borderColor: '#000000',
    borderRadius: 4,
    paddingVertical: 6,
    paddingHorizontal: 8,
    backgroundColor: '#FFFFFF',
    textAlign: 'center',
    minWidth: 60,
    marginBottom: 2,
  },
  disabledInput: {
    borderColor: '#CCCCCC',
    backgroundColor: '#F5F5F5',
    color: '#CCCCCC',
  },
  timeDisplayText: {
    fontSize: 10,
    color: '#999999',
  },
  disabledText: {
    color: '#CCCCCC',
  },
  separator: {
    fontSize: 12,
    color: '#666666',
    marginHorizontal: 8,
  },
  removeButton: {
    width: 32,
    height: 32,
    borderRadius: 6,
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#FF0000',
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 8,
  },

});

export default TimeRangePicker;
