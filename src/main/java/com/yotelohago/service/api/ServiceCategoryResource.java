package com.yotelohago.service.api;

import com.yotelohago.common.ApiVersion;
import com.yotelohago.service.application.ServiceCategoryManagementService;
import com.yotelohago.service.domain.ServiceCategory;

import jakarta.annotation.security.RolesAllowed;
import jakarta.inject.Inject;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * REST Resource for ServiceCategory management
 * Provides CRUD operations for service categories
 */
@Path(ApiVersion.BASE + "/service-categories")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public class ServiceCategoryResource {

    @Inject
    ServiceCategoryManagementService service;

    @Inject
    ServiceCategoryMapper mapper;

    /**
     * Get all service categories - Public access
     * This endpoint is used by clients to browse available service categories
     */
    @GET
    public Response getAll() {
        try {
            List<ServiceCategory> categories = service.findAll();
            List<ServiceCategoryDTO> categoryDTOs = categories.stream()
                    .map(mapper::toDTO)
                    .collect(Collectors.toList());
            
            return Response.ok(categoryDTOs).build();
        } catch (Exception e) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("{\"error\": \"Failed to retrieve service categories\"}")
                    .build();
        }
    }

    /**
     * Get a service category by ID - Public access
     */
    @GET
    @Path("/{id}")
    public Response getById(@PathParam("id") UUID id) {
        try {
            return service.findById(id)
                    .map(mapper::toDTO)
                    .map(dto -> Response.ok(dto).build())
                    .orElse(Response.status(Response.Status.NOT_FOUND)
                            .entity("{\"error\": \"Service category not found\"}")
                            .build());
        } catch (Exception e) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("{\"error\": \"Failed to retrieve service category\"}")
                    .build();
        }
    }

    /**
     * Create a new service category - Admin only
     */
    @POST
    @RolesAllowed({"admin"})
    public Response create(ServiceCategoryDTO dto) {
        try {
            if (dto == null) {
                return Response.status(Response.Status.BAD_REQUEST)
                        .entity("{\"error\": \"Service category data is required\"}")
                        .build();
            }

            if (dto.name == null || dto.name.trim().isEmpty()) {
                return Response.status(Response.Status.BAD_REQUEST)
                        .entity("{\"error\": \"Category name is required\"}")
                        .build();
            }

            ServiceCategory category = mapper.toEntity(dto);
            ServiceCategory createdCategory = service.create(category);
            ServiceCategoryDTO responseDTO = mapper.toDTO(createdCategory);

            return Response.status(Response.Status.CREATED)
                    .entity(responseDTO)
                    .build();
        } catch (IllegalArgumentException e) {
            return Response.status(Response.Status.BAD_REQUEST)
                    .entity("{\"error\": \"" + e.getMessage() + "\"}")
                    .build();
        } catch (Exception e) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("{\"error\": \"Failed to create service category\"}")
                    .build();
        }
    }

    /**
     * Update an existing service category - Admin only
     */
    @PUT
    @Path("/{id}")
    @RolesAllowed({"admin"})
    public Response update(@PathParam("id") UUID id, ServiceCategoryDTO dto) {
        try {
            if (dto == null) {
                return Response.status(Response.Status.BAD_REQUEST)
                        .entity("{\"error\": \"Service category data is required\"}")
                        .build();
            }

            if (dto.name == null || dto.name.trim().isEmpty()) {
                return Response.status(Response.Status.BAD_REQUEST)
                        .entity("{\"error\": \"Category name is required\"}")
                        .build();
            }

            // Ensure the ID in the path matches the DTO
            dto.id = id;

            // Get the existing category first
            ServiceCategory existingCategory = service.findById(id)
                    .orElseThrow(() -> new IllegalArgumentException("Service category not found"));

            // Update the existing category with new data
            ServiceCategory updatedCategory = mapper.toEntityForUpdate(dto, existingCategory);
            ServiceCategory result = service.update(updatedCategory);
            ServiceCategoryDTO responseDTO = mapper.toDTO(result);

            return Response.ok(responseDTO).build();
        } catch (IllegalArgumentException e) {
            return Response.status(Response.Status.BAD_REQUEST)
                    .entity("{\"error\": \"" + e.getMessage() + "\"}")
                    .build();
        } catch (Exception e) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("{\"error\": \"Failed to update service category\"}")
                    .build();
        }
    }

    /**
     * Delete a service category - Admin only
     */
    @DELETE
    @Path("/{id}")
    @RolesAllowed({"admin"})
    public Response delete(@PathParam("id") UUID id) {
        try {
            if (!service.delete(id)) {
                return Response.status(Response.Status.NOT_FOUND)
                        .entity("{\"error\": \"Service category not found\"}")
                        .build();
            }

            return Response.noContent().build();
        } catch (Exception e) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("{\"error\": \"Failed to delete service category\"}")
                    .build();
        }
    }

    /**
     * Get category by name - Public access
     * Useful for frontend filtering and searching
     */
    @GET
    @Path("/by-name/{name}")
    public Response getByName(@PathParam("name") String name) {
        try {
            return service.findByName(name)
                    .map(mapper::toDTO)
                    .map(dto -> Response.ok(dto).build())
                    .orElse(Response.status(Response.Status.NOT_FOUND)
                            .entity("{\"error\": \"Service category not found\"}")
                            .build());
        } catch (Exception e) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("{\"error\": \"Failed to retrieve service category\"}")
                    .build();
        }
    }
}
