# gitops/apps/jenkins/values.yaml

controller:
  serviceType: ClusterIP  # We will expose through Ingress
  ingress:
    enabled: true
    annotations:
      kubernetes.io/ingress.class: nginx
      nginx.ingress.kubernetes.io/backend-protocol: "HTTPS"
      nginx.ingress.kubernetes.io/ssl-redirect: "true"
    hostName: jenkins.example.com  # Replace with your domain
    tls:
      - hosts:
          - jenkins.example.com
        secretName: jenkins-tls
    JCasC:
      enabled: false

  # Disable default admin setup, we can control via JCasC later
  admin:
    createSecret: true
    user: admin
    password: admin

persistence:
  enabled: true
  size: 8Gi
