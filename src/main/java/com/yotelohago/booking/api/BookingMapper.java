
package com.yotelohago.booking.api;

import com.yotelohago.booking.domain.Booking;
import com.yotelohago.booking.domain.BookingStatus;
import com.yotelohago.user.domain.User;
import com.yotelohago.service.domain.Service;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

@ApplicationScoped
public class BookingMapper {

    @PersistenceContext
    EntityManager entityManager; // CDI pattern like we use on ServiceMapper class

    public BookingDTO toDTO(Booking b) {
        BookingDTO dto = new BookingDTO();
        dto.id = b.getId();
        dto.clientId = b.getClient().id;
        dto.professionalId = b.getProfessional() != null ? b.getProfessional().id : null;
        dto.serviceId = b.getService().id;
        dto.title = b.getTitle();
        dto.description = b.getDescription();
        dto.requestedDate = b.getRequestedDate();
        dto.scheduledAt = b.getScheduledAt();
        dto.price = b.getPrice();
        dto.status = b.getStatus().name();
        dto.createdAt = b.getCreatedAt();
        dto.updatedAt = b.getUpdatedAt();
        return dto;
    }

    public Booking toEntity(BookingDTO dto) {
        // Load the required entities from database
        User client = User.findById(dto.clientId);
        User professional = dto.professionalId != null ? User.findById(dto.professionalId) : null;
        Service service = Service.findById(dto.serviceId);

        if (client == null) {
            throw new IllegalArgumentException("Client not found: " + dto.clientId);
        }
        if (service == null) {
            throw new IllegalArgumentException("Service not found: " + dto.serviceId);
        }

        // Create booking with proper entity relationships
        Booking booking = new Booking(client, service, dto.title, dto.description,
                                    dto.requestedDate, dto.price, BookingStatus.valueOf(dto.status));
        booking.id = dto.id;
        booking.setProfessional(professional);
        booking.setScheduledAt(dto.scheduledAt);
        booking.setCreatedAt(dto.createdAt);
        booking.setUpdatedAt(dto.updatedAt);

        return booking;
    }
}