# Programming Assignment: Machine Problem 2 : Load Balancing + Auto Scaling

## Overview

This MP has two sections. One is about AWS Elastic Load Balancing (ELB), and the other is about Amazon EC2 Auto Scaling.

AWS Elastic Load Balancing (ELB) spreads user traffic across many instances of your applications. A load balancer
decreases the possibility of performance issues in your applications by distributing the load. It also helps with the
reliability and scalability of your product, which may be running on several virtual machines. AWS EC2 enables you to
create virtual machines on AWS infrastructure that run various operating systems, including multiple flavors of Linux,
Windows Server, and macOS. In the first section of this MP, we will launch a load balancer to balance requests between 2
web servers hosted on Amazon EC2.

Amazon EC2 Auto Scaling is an AWS feature that automatically optimizes the number of EC2 instances available to your
workloads or app based on workloads’ performance and pre-defined conditions. In the second section of this MP, we'll
create an AWS auto scaling group to monitor the CPU usage of the EC2 instances and decide when to scale up the number of
instances.

Note: To solve this problem you may want to familiarize yourself with IAM, which are covered in week 3. read

## Requirements

You need to use the terminal or command prompt on your computer to ssh into the AWS instances. For Mac and Linux users,
ssh is already available in the terminal. For Windows users, you can install and use
PuTTY
.

## Procedure

### Section 1. AWS Load Balancer

#### [Step 1.1] Web application on AWS EC2

* Launch two identical EC2 instances. We suggest using the latest Amazon Linux AMI as the operating system and the
smallest EC2 instance possible (e.g., Nano or Micro) to make the cost of your instances as small as possible (or free.)
Refer to the link below for the specific steps.

https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/EC2_GetStarted.html

* Ensure you can connect to the 2 EC2 instances using terminal (or the embedded terminal in the EC2 console on your
browser) via ssh.
You must now write a simple web server application that stores and retrieves a seed value (default 0). This program will
then run on each EC2 server. Specifically, your server should maintain the seed number and allow clients to access and
update it. The application handles the following two HTTP REST requests:

  * HTTP POST "/" with JSON body {"num": 100} where 100 can be any integer.

    * Your program should update the seed value with the given number.

  * HTTP GET "/"

    * Your program should return the integer seed value in string format. The response body for the above case will be: "100"

* The web application will run on a specific port and be deployed in both EC2 instances.

    * To access the servers publicly and make them reachable for the load balancer, you need to set an inbound firewall rule
on the EC2 instances.

* You can use your preferred programming languages or development environments to implement this server. However, we will
only assist you with Python using Flask. You need to install the required dependencies (install pip and then Flask "pip3
install flask" along with its required dependencies if you are using Python) in both EC2 instances and then start the
server. Refer to the sections of Environment and HTTP Methods in
Tutorialspoint's Flask tutorial
for more details.

#### Step 1.2. Load balancing web servers

* You will launch an AWS Application Load Balancer, which will allow you to distribute HTTP requests between the two
instances you just initiated. To get started, you might want to look at the following document.
https://docs.aws.amazon.com/elasticloadbalancing/latest/application/application-load-balancer-getting-started.html

* Connect your load balancer to a target group with the previously launched EC2 instances. Make sure to map the webserver
port to the load balancer correctly.

* When configuring the load balancer, ensure not to use the default security group. Enable the HTTP protocol and allow
traffic to be routed from anywhere when creating a custom security group. You will not pass this assignment unless our
autograder can connect to your system via the internet.

* To make the server code operate in the load balancer, you must edit the target group's health check ping path to "/".

* To check if your load balancer is running correctly, first make sure that the two EC2 instances are healthy in the
target group, and then go to the DNS address listed in the AWS load balancer's description and look for the seed value.

* If you encounter any other issues, use Google Search, AWS documentation, or Campuswire.

#### Step 1.3. (Optional) EC2 Cloudwatch Debugging Logs

Amazon CloudWatch is a service for monitoring and observing data. You can debug your web application by creating a log
group and monitoring the logs. The Python sample code for creating two custom logs and forwarding them to Cloudwatch is
provided below. You can insert the sample code in your lambda function. Refer to
the [blog](https://hands-on.cloud/working-with-cloudwatch-in-python-using-boto3/#Sending-logs-to-CloudWatch-log-group)
for more examples.

```python
import boto3
import time
from datetime import datetime

AWS_REGION = "us-east-1"

client = boto3.client('logs', region_name=AWS_REGION)

message = "YOUR_MESSAGE"  # replace here with your custom logging message
logGroupName = 'YOUR_GROUPNAME'  # replace here with your log group name
logStreamName = 'YOUR_STREAMLOG_NAME'  # replace here with your stream log name

# initial Cloudwatch configuration
response = client.create_log_stream(
  logGroupName=logGroupName,
  logStreamName=logStreamName
)

# log1
seq_token = None
log_event = {
  'logGroupName': logGroupName,
  'logStreamName': logStreamName,
  'logEvents': [
    {
      'timestamp': int(round(time.time() * 1000)),
      'message': message
    },
  ],
}
log_event['sequenceToken'] = seq_token
log1_response = client.put_log_events(**log_event)

# sleep for one second
time.sleep(1)

# log2
seq_token = log1_response['nextSequenceToken']
log_event = {
  'logGroupName': logGroupName,
  'logStreamName': logStreamName,
  'logEvents': [
    {
      'timestamp': int(round(time.time() * 1000)),
      'message': message
    },
  ],
}
log_event['sequenceToken'] = seq_token
log2_response = client.put_log_events(**log_event)
```

### Section 2. Auto Scaling

In this section, you'll use Amazon Auto Scaling to dynamically control the number of EC2 instances based on the CPU
usage.

#### Step 2.1. Implement a HTTP server

Like in Section 1, create an EC2 instance for server program development. We suggest using the Amazon Linux AMI or
Ubuntu as the operating system.

Use the **t2.micro** (free tier) instance.

**Notice that this EC2 instance is only for developing and testing your HTTP server program, so that you can create your
launch template later. It will not be used for the auto scaling.**

Ensure you can connect to the EC2 instance using terminal (or the embedded terminal in the EC2 console on your browser)
via ssh.

You will write a simple web server application called serve.py which is similar to what you wrote in Section 1 (we still
recommend using Python + Flask). This program will then run on the EC2 server. The application handles the following two
HTTP REST requests:

HTTP POST "/"

Once receive the POST request, your server program should create a separate process for running "stress_cpu.py" (
**attached below**). "stress_cpu.py" runs an intensive computation loop to stress the CPUs to 100% utilization. This
script
is for triggering the auto scaling policy that you will setup later.

In Python, use **subprocess.Popen()** to create a separate process so that your HTTP server is non-blocking. That is to
say,
after receiving a POST request for stressing the CPU on your EC2 instance, your server program should still be able to
handle further POST/GET requests.

HTTP GET "/"

Your server program should return the private IP address of the EC2 instance. In Python, you can **import socket** and
use
**socket.gethostname()** to get the IP address.

```stress_cpu.py
from multiprocessing import Pool
from multiprocessing import cpu_count
import time


# Do intensive computation to stress the CPU
def stress_cpu(n):
    total = 0
    for i in range(n):
        total += i ** 2
    return total


start_time = time.time()
# Create as many as processes as there are CPU cores
processes = cpu_count()
pool = Pool(processes)
print(pool.map(stress_cpu, [110000000, 110000000]))
print("time cost: ", time.time() - start_time)

```

Run your server program on a the same port as you did in Section 1, so that you can reuse the security group(with the
same inbound rules) created earlier in Section 1.

After finishing the development of your HTTP server, you need to upload your code to a Github repo (**make sure it's a
private repo**). You will need to create a personal access token on Github which will be used in the bash script in Step
2.2. The code will be used in the following steps. And the EC2 instance for developing and testing your server program
can be terminated now.

For security reasons, when creating your **GitHub Personal Access Token (PAT)**, ensure that it **only has access to the
MP2
repository** instead of granting access to all repositories in your account.

#### Step 2.2. Create a Launch Template

A launch template defines the configurations of launching an EC2 instance. When the auto scaling policy is triggered,
Amazon EC2 Auto Scaling can automatically creates a new EC2 instances based on a launch template. Follow "Step1: Create
a launch Template" in this tutorial to create a launch template:
https://docs.aws.amazon.com/autoscaling/ec2/userguide/get-started-with-ec2-auto-scaling.html

When creating your launch template, under Advanced details -> User data, you need to put a Bash script to automatically
install all your dependencies and run your server code.

A Bash script template looks like the following:

```bash Amazon Linux Server
#!/bin/bash
export HOME=/home/<USER>
sudo yum update
sudo yum install stress-ng -y
sudo yum install htop -y
sudo yum install python3-pip -y
pip3 install flask
sudo yum install git -y
cd /home/<USER>
git clone YOUR_CODECOMMIT_REPO_LINK (https://<github-personal-access-token>@github.com/username/repository_name)
cd /home/<USER>/YOUR_CODECOMMIT_REPO_NAME
python3 serve.py
```

```bash Ubuntu Server
#!/bin/bash
export HOME=/home/<USER>
sudo apt-get update
sudo apt-get install stress-ng -y
sudo apt-get install htop -y
sudo apt-get install python3-pip -y
pip3 install flask
sudo apt-get install git -y
cd /home/<USER>
git clone YOUR_CODECOMMIT_REPO_LINK (https://<personal-access-token>@github.com/username/repository_name)
cd /home/<USER>/YOUR_CODECOMMIT_REPO_NAME
python3 serve.py
```

To test this step, create an EC2 instance based on your launch template, and see if it can automatically download all
the dependencies and run your serve.py .

#### Step 2.3. Create auto scaling group.

* Follow "Step 2: Create a single-instance auto scaling group" in this tutorial to create your auto scaling group:
  https://docs.aws.amazon.com/autoscaling/ec2/userguide/get-started-with-ec2-auto-scaling.html

* When creating auto scaling group, in Configure advanced options - optional page, create a new load balancer. This
  allows the EC2 instances automatically created by the auto scaling group to be under the management of your load
  balancer. Select the load balancer scheme as "internet-facing".

#### Step 2.4. Ste up auto scaling policy.

* Following the section "Create a target tracking scaling policy (console)" in this tutorial to learn how to set up your
  target tracking scaling policy:
  https://docs.aws.amazon.com/autoscaling/ec2/userguide/as-scaling-target-tracking.html

* Set up your auto scaling group size by:
  * Minimum instance number = 0, maximum instance number = 3, desired instance number =1

* You'll use the target tracking scaling policy to monitor the CPU usage on your EC2 instances. Following is the policy
  that you'll set up:

1. When the CPU usage is above 50%, your auto scaling group should automatically increase the number of instance by one

2. When the CPU usage is below 50%, your auto scaling group should automatically decrease the number of instance by one

3. Set the cooldown time to be 300 seconds, in case the policy is triggered too many times.

![](images/auto-scaling.png)

By this point, you should have a functional auto scaling group monitoring on the CPU usage of your EC2 instances, a load balancer for rerouting the HTTP requests to EC2 instances. Your auto scaling group can create EC2 instances based on your launch template. When an EC2 instance is created, it automatically download the dependencies and run your HTTP server.

#### Step 2.5. Test your auto scaling group

Before submitting your auto scaling group to the autograder, follow these steps to test your auto scaling group:

1. Make sure your auto scaling group has 1 active EC2 instance, and the maximum instance number is set to 3.

2. Send 1 POST request to the load balancer bound to your auto scaling group. Check if the EC2 instance can successfully receive the request and run "stress_cpu.py". Check it by using "htop" command in terminal on your EC2 instance to show the current CPU usage. If "stress_cpu.py" is running, the CPU usage should stay 100% for around 1 minute.

![](images/htop.png)
3. After the first "stress_cpu.py" ended, send 10 POST requests to the load balancer bound to your auto scaling group. If the server program run on the EC2 instance is non-blocking, now you should have 20 "stress_cpu.py" processes running (t4g nano instance has 2 CPUs, stress_cpu.py automatically starts one stressing process for each CPU, so in total it should be 2*10=20 processes).  This will stress your EC2 instance for 10 minutes.

4. Don't stop your EC2 instance, wait for around 5 ~ 9 minutes and check if a new instance is automatically created. If so, it means that your auto scaling group scales up successfully.

5. Make sure this newly created EC2 instance is running your server code automatically. You can test it by sending a GET request to it and see if it returns its private IP address. If so, it means your launch template works.

6. You can now reset your auto scaling group size settings back to: Desired: 1, Minimum: 0, Maximum: 3. Wait for around 10 minutes and see if only 1 instance is kept. When submitting your auto scaling group to the autograder, please always make sure that only 1 instance is running.

#### Step 2.6. Submission

* In order to grade your work on section 1 and 2, make sure you have:

  * 2 EC2 instances running the server code for section 1

  * A load balancer monitoring the 2 EC2 instances for section 1

  * An auto scaling group for section 2

  * A load balancer monitoring the EC2 instances created by the auto scaling group for section 2 (so basically you should have 1 load balancer for section 1 and 1 load balancer for section 2)

* To submit your code, first fill in the variables in submit.py. Then run submit.py on your local machine. You will receive the autograder's feedback for the current submission, and your grade on Coursera will be updated automatically. Note that the grading process may take 25 minutes. Monitor the submissions section actively for any failure feedback. Intermediate failures will be promptly updated to minimize debugging time.

* Also, before every submission,  make sure updating your submission token, since the valid time for a token is only 30 minutes. Besides, before submission, make sure your auto scaling group has only 1 EC2 instance and the desired instance number equals to 1. After failing the test cases, you might need to reset it.

* In submit.py, make sure you fill in the DNS address of your load balancer (not IP address) as well as your school email and personal token.

* Here's a template for your submission script:

```submission.py
import requests
import json

''' Fill in the following information '''
# General information
YOUR_EMAIL = "<EMAIL>" # <put your coursera account email>,
YOUR_SECRET = "Ovm8iiFsErTTQEiz" # <put your secret token from coursera>

# <To get full credits, leave this blank. It will run both section 1 and section 2 tests>
SECTION = "" # <put "1" to test section 1 in isolation; put "2" to test section 2 in isolation>

# Section 1
IP_ADDRESS1 = "*************:8000" # <put your first EC2 instance's IP address:port>
IP_ADDRESS2 = "***********:8000" # <put your second instance's IP address:port>
# Do not add "https://" to your loadbalancer address.
YOUR_LOAD_BALANCER1 = "mp2LoadBalancerSection1-xxxx.us-east-1.elb.amazonaws.com" # <put your load_balancer address for section 1 (explicitly add :port if port is not 80)>
# Section 2
YOUR_LOAD_BALANCER2 = "mp2LoadBalancerSection2-xxxx.us-east-1.elb.amazonaws.com" # <put your load_balancer address for section 2 (explicitly add :port if port is not 80)>,

''' Don't change the following '''
url = "https://ekwygde36j.execute-api.us-east-1.amazonaws.com/alpha/execution"
input = {
            'ip_address1': IP_ADDRESS1,
            'ip_address2': IP_ADDRESS2,
			'load_balancer1': YOUR_LOAD_BALANCER1,
            'load_balancer2': YOUR_LOAD_BALANCER2,
			'submitterEmail': YOUR_EMAIL,
			'secret': YOUR_SECRET,
			'section': SECTION
		}
payload = { "input": json.dumps(input),
    		"stateMachineArn": "arn:aws:states:us-east-1:913708708374:stateMachine:mp2grader"
        }

r = requests.post(url, data=json.dumps(payload))

print(r.status_code, r.reason)
print(r.text)
```

**Note** - We post a dummy result on Coursera with all 0s to verify submission token validity. You can ignore this submission as it doesn't reflect the final autograder output.

#### Step 2.7.  shutdown all AWS services

Remove the auto scaling group or set the desired instance number to 0. Then shutdown your EC2 instances. Please make sure that no new EC2 instance be automatically created by the auto scaling group. Also remove the load balancer and delete your Code Commit repository. Otherwise, Amazon will keep charging you!

#### Step 2.8.  Monitor Auto Grader activity after submission.

Step 1) **Run your submit.py file with a newly generated token.**
Once you run your submit.py file using "python3 submit.py". You will see a 200 OK response if the API goes through. For example:

![](images/submit_token.png)