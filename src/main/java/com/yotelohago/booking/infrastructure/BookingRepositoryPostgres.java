package com.yotelohago.booking.infrastructure;

import com.yotelohago.booking.api.BookingWithDetailsDTO;
import com.yotelohago.booking.domain.Booking;
import com.yotelohago.booking.domain.BookingRepository;
import com.yotelohago.booking.domain.BookingStatus;
import com.yotelohago.common.dto.PageRequest;
import com.yotelohago.common.dto.PageResponse;
import com.yotelohago.professional.domain.Professional;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;
import org.eclipse.microprofile.config.inject.ConfigProperty;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@ApplicationScoped
public class BookingRepositoryPostgres implements BookingRepository {

    @PersistenceContext
    EntityManager entityManager;

    @ConfigProperty(name = "media.url-resolver")
    String mediaBaseUrl;

    @Override
    public void persist(Booking booking) {
        entityManager.persist(booking);
    }

    @Override
    public Optional<Booking> findById(Long id) {
        return Optional.ofNullable(entityManager.find(Booking.class, id));
    }

    @Override
    public List<Booking> findAll() {
        return entityManager.createQuery("FROM Booking", Booking.class).getResultList();
    }

    @Override
    public List<Booking> findByClientId(UUID clientId) {
        return entityManager.createQuery("FROM Booking b WHERE b.client.id = :clientId ORDER BY b.createdAt DESC", Booking.class)
                .setParameter("clientId", clientId)
                .getResultList();
    }

    @Override
    public List<Booking> findByProfessionalId(UUID professionalId) {
        return entityManager.createQuery("FROM Booking b WHERE b.professional.id = :professionalId ORDER BY b.createdAt DESC", Booking.class)
                .setParameter("professionalId", professionalId)
                .getResultList();
    }

    @Override
    public List<Booking> findByClientAndProfessional(UUID clientId, UUID professionalId) {
        return entityManager.createQuery("FROM Booking b WHERE b.client.id = :clientId AND b.professional.id = :professionalId ORDER BY b.createdAt DESC", Booking.class)
                .setParameter("clientId", clientId)
                .setParameter("professionalId", professionalId)
                .getResultList();
    }

    @Override
    public Booking update(Booking booking) {
        return entityManager.merge(booking);
    }

    @Override
    public void deleteById(Long id) {
        Booking booking = entityManager.find(Booking.class, id);
        if (booking != null) {
            entityManager.remove(booking);
        }
    }

    @Override
    public void flush() {
        entityManager.flush();
    }

    // Pagination methods for basic bookings
    @Override
    public PageResponse<Booking> findAllPaginated(PageRequest pageRequest) {
        String jpql = "FROM Booking b ORDER BY b.createdAt DESC";

        TypedQuery<Booking> query = entityManager.createQuery(jpql, Booking.class);
        query.setFirstResult(pageRequest.getOffset());
        query.setMaxResults(pageRequest.getSize());

        List<Booking> content = query.getResultList();

        // Count total elements
        Long totalElements = entityManager.createQuery("SELECT COUNT(b) FROM Booking b", Long.class)
                .getSingleResult();

        return PageResponse.of(content, totalElements, pageRequest.getPage(), pageRequest.getSize());
    }

    @Override
    public PageResponse<Booking> findByUserPaginated(UUID userId, PageRequest pageRequest) {
        String jpql = "FROM Booking b WHERE b.client.id = :userId OR b.professional.id = :userId ORDER BY b.createdAt DESC";

        TypedQuery<Booking> query = entityManager.createQuery(jpql, Booking.class);
        query.setParameter("userId", userId);
        query.setFirstResult(pageRequest.getOffset());
        query.setMaxResults(pageRequest.getSize());

        List<Booking> content = query.getResultList();

        // Count total elements
        Long totalElements = entityManager.createQuery(
                "SELECT COUNT(b) FROM Booking b WHERE b.client.id = :userId OR b.professional.id = :userId", Long.class)
                .setParameter("userId", userId)
                .getSingleResult();

        return PageResponse.of(content, totalElements, pageRequest.getPage(), pageRequest.getSize());
    }

    @Override
    public PageResponse<Booking> findByStatusPaginated(BookingStatus status, PageRequest pageRequest) {
        String jpql = "FROM Booking b WHERE b.status = :status ORDER BY b.createdAt DESC";

        TypedQuery<Booking> query = entityManager.createQuery(jpql, Booking.class);
        query.setParameter("status", status);
        query.setFirstResult(pageRequest.getOffset());
        query.setMaxResults(pageRequest.getSize());

        List<Booking> content = query.getResultList();

        // Count total elements
        Long totalElements = entityManager.createQuery(
                "SELECT COUNT(b) FROM Booking b WHERE b.status = :status", Long.class)
                .setParameter("status", status)
                .getSingleResult();

        return PageResponse.of(content, totalElements, pageRequest.getPage(), pageRequest.getSize());
    }

    @Override
    public PageResponse<Booking> findByUserAndStatusPaginated(UUID userId, BookingStatus status, PageRequest pageRequest) {
        String jpql = "FROM Booking b WHERE (b.client.id = :userId OR b.professional.id = :userId) AND b.status = :status ORDER BY b.createdAt DESC";

        TypedQuery<Booking> query = entityManager.createQuery(jpql, Booking.class);
        query.setParameter("userId", userId);
        query.setParameter("status", status);
        query.setFirstResult(pageRequest.getOffset());
        query.setMaxResults(pageRequest.getSize());

        List<Booking> content = query.getResultList();

        // Count total elements
        Long totalElements = entityManager.createQuery(
                "SELECT COUNT(b) FROM Booking b WHERE (b.client.id = :userId OR b.professional.id = :userId) AND b.status = :status", Long.class)
                .setParameter("userId", userId)
                .setParameter("status", status)
                .getSingleResult();

        return PageResponse.of(content, totalElements, pageRequest.getPage(), pageRequest.getSize());
    }

    // Pagination methods for enriched bookings with professional and service details
    @Override
    public PageResponse<BookingWithDetailsDTO> findAllWithDetailsPaginated(PageRequest pageRequest) {
        String jpql = String.format("""
            SELECT new com.yotelohago.booking.api.BookingWithDetailsDTO(
                b.id, b.client.id, b.professional.id, b.service.id,
                b.title, b.description, b.requestedDate, b.scheduledAt,
                b.price, CAST(b.status AS string), b.createdAt, b.updatedAt,
                b.professional.firstName, b.professional.lastName, b.professional.email,
                prof.city,
                (SELECT CASE
                    WHEN pm.filePath IS NOT NULL
                    THEN CONCAT('%s', pm.filePath)
                    ELSE NULL
                 END
                 FROM Media pm
                 WHERE pm.user.id = professionalUser.id AND pm.mediaType = 'PROFESSIONAL_PROFILE_PICTURE'
                 ORDER BY pm.createdAt DESC LIMIT 1),
                prof.available,
                b.client.firstName, b.client.lastName, b.client.email,
                (SELECT CASE
                    WHEN cm.filePath IS NOT NULL
                    THEN CONCAT('%s', cm.filePath)
                    ELSE NULL
                 END
                 FROM Media cm
                 WHERE cm.user.id = clientUser.id AND cm.mediaType = 'CLIENT_PROFILE_PICTURE'
                 ORDER BY cm.createdAt DESC LIMIT 1),
                s.title, s.description, c.name, s.price
            )
            FROM Booking b
            LEFT JOIN b.professional professionalUser
            LEFT JOIN Professional prof ON prof.user.id = professionalUser.id
            JOIN b.client clientUser
            JOIN b.service s
            JOIN s.category c
            ORDER BY b.createdAt DESC
            """, mediaBaseUrl, mediaBaseUrl);

        TypedQuery<BookingWithDetailsDTO> query = entityManager.createQuery(jpql, BookingWithDetailsDTO.class);
        query.setFirstResult(pageRequest.getOffset());
        query.setMaxResults(pageRequest.getSize());

        List<BookingWithDetailsDTO> content = query.getResultList();

        // Count total elements
        Long totalElements = entityManager.createQuery("SELECT COUNT(b) FROM Booking b", Long.class)
                .getSingleResult();

        return PageResponse.of(content, totalElements, pageRequest.getPage(), pageRequest.getSize());
    }

    @Override
    public PageResponse<BookingWithDetailsDTO> findByUserWithDetailsPaginated(UUID userId, PageRequest pageRequest) {
        String jpql = String.format("""
            SELECT new com.yotelohago.booking.api.BookingWithDetailsDTO(
                b.id, b.client.id, b.professional.id, b.service.id,
                b.title, b.description, b.requestedDate, b.scheduledAt,
                b.price, CAST(b.status AS string), b.createdAt, b.updatedAt,
                b.professional.firstName, b.professional.lastName, b.professional.email,
                prof.city,
                (SELECT CASE
                    WHEN pm.filePath IS NOT NULL
                    THEN CONCAT('%s', pm.filePath)
                    ELSE NULL
                 END
                 FROM Media pm
                 WHERE pm.user.id = professionalUser.id AND pm.mediaType = 'PROFESSIONAL_PROFILE_PICTURE'
                 ORDER BY pm.createdAt DESC LIMIT 1),
                prof.available,
                b.client.firstName, b.client.lastName, b.client.email,
                (SELECT CASE
                    WHEN cm.filePath IS NOT NULL
                    THEN CONCAT('%s', cm.filePath)
                    ELSE NULL
                 END
                 FROM Media cm
                 WHERE cm.user.id = clientUser.id AND cm.mediaType = 'CLIENT_PROFILE_PICTURE'
                 ORDER BY cm.createdAt DESC LIMIT 1),
                s.title, s.description, c.name, s.price
            )
            FROM Booking b
            LEFT JOIN b.professional professionalUser
            LEFT JOIN Professional prof ON prof.user.id = professionalUser.id
            JOIN b.client clientUser
            JOIN b.service s
            JOIN s.category c
            WHERE b.client.id = :userId OR b.professional.id = :userId
            ORDER BY b.createdAt DESC
            """, mediaBaseUrl, mediaBaseUrl);

        TypedQuery<BookingWithDetailsDTO> query = entityManager.createQuery(jpql, BookingWithDetailsDTO.class);
        query.setParameter("userId", userId);
        query.setFirstResult(pageRequest.getOffset());
        query.setMaxResults(pageRequest.getSize());

        List<BookingWithDetailsDTO> content = query.getResultList();

        // Count total elements
        Long totalElements = entityManager.createQuery(
                "SELECT COUNT(b) FROM Booking b WHERE b.client.id = :userId OR b.professional.id = :userId", Long.class)
                .setParameter("userId", userId)
                .getSingleResult();

        return PageResponse.of(content, totalElements, pageRequest.getPage(), pageRequest.getSize());
    }

    @Override
    public PageResponse<BookingWithDetailsDTO> findByStatusWithDetailsPaginated(BookingStatus status, PageRequest pageRequest) {
        String jpql = String.format("""
            SELECT new com.yotelohago.booking.api.BookingWithDetailsDTO(
                b.id, b.client.id, b.professional.id, b.service.id,
                b.title, b.description, b.requestedDate, b.scheduledAt,
                b.price, CAST(b.status AS string), b.createdAt, b.updatedAt,
                b.professional.firstName, b.professional.lastName, b.professional.email,
                prof.city,
                (SELECT CASE
                    WHEN pm.filePath IS NOT NULL
                    THEN CONCAT('%s', pm.filePath)
                    ELSE NULL
                 END
                 FROM Media pm
                 WHERE pm.user.id = professionalUser.id AND pm.mediaType = 'PROFESSIONAL_PROFILE_PICTURE'
                 ORDER BY pm.createdAt DESC LIMIT 1),
                prof.available,
                b.client.firstName, b.client.lastName, b.client.email,
                (SELECT CASE
                    WHEN cm.filePath IS NOT NULL
                    THEN CONCAT('%s', cm.filePath)
                    ELSE NULL
                 END
                 FROM Media cm
                 WHERE cm.user.id = clientUser.id AND cm.mediaType = 'CLIENT_PROFILE_PICTURE'
                 ORDER BY cm.createdAt DESC LIMIT 1),
                s.title, s.description, c.name, s.price
            )
            FROM Booking b
            LEFT JOIN b.professional professionalUser
            LEFT JOIN Professional prof ON prof.user.id = professionalUser.id
            JOIN b.client clientUser
            JOIN b.service s
            JOIN s.category c
            WHERE b.status = :status
            ORDER BY b.createdAt DESC
            """, mediaBaseUrl, mediaBaseUrl);

        TypedQuery<BookingWithDetailsDTO> query = entityManager.createQuery(jpql, BookingWithDetailsDTO.class);
        query.setParameter("status", status);
        query.setFirstResult(pageRequest.getOffset());
        query.setMaxResults(pageRequest.getSize());

        List<BookingWithDetailsDTO> content = query.getResultList();

        // Count total elements
        Long totalElements = entityManager.createQuery(
                "SELECT COUNT(b) FROM Booking b WHERE b.status = :status", Long.class)
                .setParameter("status", status)
                .getSingleResult();

        return PageResponse.of(content, totalElements, pageRequest.getPage(), pageRequest.getSize());
    }

    @Override
    public PageResponse<BookingWithDetailsDTO> findByUserAndStatusWithDetailsPaginated(UUID userId, BookingStatus status, PageRequest pageRequest) {
        String jpql = String.format("""
            SELECT new com.yotelohago.booking.api.BookingWithDetailsDTO(
                b.id, b.client.id, b.professional.id, b.service.id,
                b.title, b.description, b.requestedDate, b.scheduledAt,
                b.price, CAST(b.status AS string), b.createdAt, b.updatedAt,
                b.professional.firstName, b.professional.lastName, b.professional.email,
                prof.city,
                (SELECT CASE
                    WHEN pm.filePath IS NOT NULL
                    THEN CONCAT('%s', pm.filePath)
                    ELSE NULL
                 END
                 FROM Media pm
                 WHERE pm.user.id = professionalUser.id AND pm.mediaType = 'PROFESSIONAL_PROFILE_PICTURE'
                 ORDER BY pm.createdAt DESC LIMIT 1),
                prof.available,
                b.client.firstName, b.client.lastName, b.client.email,
                (SELECT CASE
                    WHEN cm.filePath IS NOT NULL
                    THEN CONCAT('%s', cm.filePath)
                    ELSE NULL
                 END
                 FROM Media cm
                 WHERE cm.user.id = clientUser.id AND cm.mediaType = 'CLIENT_PROFILE_PICTURE'
                 ORDER BY cm.createdAt DESC LIMIT 1),
                s.title, s.description, c.name, s.price
            )
            FROM Booking b
            LEFT JOIN b.professional professionalUser
            LEFT JOIN Professional prof ON prof.user.id = professionalUser.id
            JOIN b.client clientUser
            JOIN b.service s
            JOIN s.category c
            WHERE (b.client.id = :userId OR b.professional.id = :userId) AND b.status = :status
            ORDER BY b.createdAt DESC
            """, mediaBaseUrl, mediaBaseUrl);

        TypedQuery<BookingWithDetailsDTO> query = entityManager.createQuery(jpql, BookingWithDetailsDTO.class);
        query.setParameter("userId", userId);
        query.setParameter("status", status);
        query.setFirstResult(pageRequest.getOffset());
        query.setMaxResults(pageRequest.getSize());

        List<BookingWithDetailsDTO> content = query.getResultList();

        // Count total elements
        Long totalElements = entityManager.createQuery(
                "SELECT COUNT(b) FROM Booking b WHERE (b.client.id = :userId OR b.professional.id = :userId) AND b.status = :status", Long.class)
                .setParameter("userId", userId)
                .setParameter("status", status)
                .getSingleResult();

        return PageResponse.of(content, totalElements, pageRequest.getPage(), pageRequest.getSize());
    }
}