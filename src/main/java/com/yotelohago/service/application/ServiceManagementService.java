package com.yotelohago.service.application;

import com.yotelohago.auth.TokenIdentityProvider;
import com.yotelohago.common.dto.PageRequest;
import com.yotelohago.common.dto.PageResponse;
import com.yotelohago.service.domain.Service;
import com.yotelohago.service.domain.ServiceRepository;
import com.yotelohago.service.api.ServiceWithProfessionalDTO;
import io.quarkus.cache.CacheInvalidate;
import io.quarkus.cache.CacheResult;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;
import jakarta.ws.rs.ForbiddenException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

@ApplicationScoped
public class ServiceManagementService {

    private static final Logger logger = LoggerFactory.getLogger(ServiceManagementService.class);

    private final ServiceRepository serviceRepository;

    @Inject
    TokenIdentityProvider tokenIdentityProvider;

    public ServiceManagementService(ServiceRepository serviceRepository) {
        this.serviceRepository = serviceRepository;
    }

    @CacheResult(cacheName = "service-details")
    public Optional<Service> findById(UUID id) {
        logger.debug("🔍 Fetching service by ID: {}", id);
        return serviceRepository.findById(id);
    }

    // Public endpoints - all return only PUBLISHED services with pagination

    /**
     * Find all published services with pagination (most used - default for all users)
     * Returns only PUBLISHED services for public consumption
     */
    @CacheResult(cacheName = "service-list-published")
    public PageResponse<Service> findAllPublishedPaginated(PageRequest pageRequest) {
        logger.debug("🔍 Fetching all published services with pagination: {}", pageRequest);
        return serviceRepository.findAllPublishedPaginated(pageRequest);
    }

    /**
     * Find published services by category with pagination (most used for filtering)
     * Returns only PUBLISHED services filtered by category (electrician, plumber, etc.)
     */
    @CacheResult(cacheName = "service-list-published")
    public PageResponse<Service> findByCategoryPublishedPaginated(UUID categoryId, PageRequest pageRequest) {
        logger.debug("🔍 Fetching published services by category {} with pagination: {}", categoryId, pageRequest);
        return serviceRepository.findByCategoryPublishedPaginated(categoryId, pageRequest);
    }

    /**
     * Search published services with filters and pagination (most used for search)
     * Returns only PUBLISHED services matching the search criteria
     */
    @CacheResult(cacheName = "service-list-published")
    public PageResponse<Service> searchPublishedPaginated(UUID categoryId, Double minPrice, Double maxPrice,
                                                        String searchTerm, PageRequest pageRequest) {
        logger.debug("🔍 Searching published services with filters - category: {}, price: {}-{}, term: '{}', page: {}",
                    categoryId, minPrice, maxPrice, searchTerm, pageRequest);
        return serviceRepository.searchPublishedPaginated(categoryId, minPrice, maxPrice, searchTerm, pageRequest);
    }

    // Professional-only endpoints - not cached, not paginated

    /**
     * Find all services by professional (professional-only endpoint - not paginated, not cached)
     * Returns ALL services (DRAFT + PUBLISHED) for the professional to manage
     * Authorization: Only the professional who owns the services can access this
     */
    public List<Service> findAllByProfessional(UUID professionalUserId) {
        // Check if current user can access this professional's data
        if (!tokenIdentityProvider.canAccessServiceData(professionalUserId)) {
            throw new ForbiddenException("Not authorized to access services for this professional");
        }

        logger.debug("🔍 Fetching all services by professional user ID: {}", professionalUserId);
        return serviceRepository.findAllByProfessional(professionalUserId);
    }

    /**
     * Find all services for the current professional (professional-only endpoint - not paginated, not cached)
     * Returns ALL services (DRAFT + PUBLISHED) for the current professional to manage
     * Authorization: Uses current user's token to get their internal user ID
     */
    public List<Service> findAllByCurrentProfessional() {
        UUID currentUserId = tokenIdentityProvider.getCurrentUserId();
        logger.debug("🔍 Fetching all services for current professional user ID: {}", currentUserId);
        return serviceRepository.findAllByProfessional(currentUserId);
    }

    /**
     * Find published services by professional (public endpoint - no authorization required)
     * Returns only PUBLISHED services for the specified professional for public consumption
     * This is used by public endpoints to show professional's available services
     */
    public List<Service> findPublishedByProfessional(UUID professionalUserId) {
        logger.debug("🔍 Fetching published services for professional user ID: {}", professionalUserId);
        return serviceRepository.findAllByProfessional(professionalUserId)
                .stream()
                .filter(Service::isPubliclyVisible) // Only return PUBLISHED services
                .collect(Collectors.toList());
    }

    @Transactional
    @CacheInvalidate(cacheName = "service-list")
    public Service create(Service service) {
        // Check if current user can create service for this professional
        if (!tokenIdentityProvider.canAccessServiceData(service.getProfessionalUser().id)) {
            throw new ForbiddenException("Not authorized to create service for this professional");
        }

        serviceRepository.persist(service);
        serviceRepository.flush(); // ensure the entity is written and ID is generated
        logger.info("✅ Service created successfully: {} ({})", service.getTitle(), service.getId());
        return service;
    }

    @Transactional
    @CacheInvalidate(cacheName = "service-list")
    @CacheInvalidate(cacheName = "service-details")
    public boolean delete(UUID id) {
        // First get the service to check authorization
        Optional<Service> serviceOpt = serviceRepository.findById(id);
        if (serviceOpt.isEmpty()) {
            return false;
        }

        Service service = serviceOpt.get();

        // Check if current user can delete this service
        if (!tokenIdentityProvider.canAccessServiceData(service.getProfessionalUser().id)) {
            throw new ForbiddenException("Not authorized to delete this service");
        }

        serviceRepository.deleteById(id);
        logger.info("✅ Service deleted successfully: {}", id);
        return true;
    }

    @Transactional
    @CacheInvalidate(cacheName = "service-list")
    @CacheInvalidate(cacheName = "service-details")
    public Service update(Service updatedService) {
        // First get the existing service to check authorization
        Service existingService = serviceRepository.findById(updatedService.getId())
                .orElseThrow(() -> new IllegalArgumentException("Service not found"));

        // Check if current user can update this service
        if (!tokenIdentityProvider.canAccessServiceData(existingService.getProfessionalUser().id)) {
            throw new ForbiddenException("Not authorized to update this service");
        }

        // Ensure the professional doesn't change (security measure)
        updatedService.setProfessionalUser(existingService.getProfessionalUser());

        Service updated = serviceRepository.update(updatedService);
        logger.info("✅ Service updated successfully: {} ({})", updated.getTitle(), updated.getId());
        return updated;
    }

    // =====================================================
    // NEW: Methods with Professional Data Joins (Option 2)
    // =====================================================

    /**
     * Find all published services with professional data using database joins
     * This eliminates N+1 queries by fetching service + professional data in a single query
     * @param pageRequest Pagination parameters
     * @return Paginated response with services including professional information
     */
    @CacheResult(cacheName = "service-list-with-professional")
    public PageResponse<ServiceWithProfessionalDTO> findAllPublishedWithProfessionalPaginated(PageRequest pageRequest) {
        logger.debug("🔍 Fetching all published services with professional data: {}", pageRequest);
        return serviceRepository.findAllPublishedWithProfessionalPaginated(pageRequest);
    }

    /**
     * Find published services by category with professional data using database joins
     * @param categoryId The category ID to filter by
     * @param pageRequest Pagination parameters
     * @return Paginated response with services including professional information
     */
    @CacheResult(cacheName = "service-list-category-with-professional")
    public PageResponse<ServiceWithProfessionalDTO> findByCategoryPublishedWithProfessionalPaginated(UUID categoryId, PageRequest pageRequest) {
        logger.debug("🔍 Fetching published services by category with professional data: category={}, page={}", categoryId, pageRequest);
        return serviceRepository.findByCategoryPublishedWithProfessionalPaginated(categoryId, pageRequest);
    }

    /**
     * Search published services with professional data using database joins
     * @param categoryId Optional category filter
     * @param minPrice Optional minimum price filter
     * @param maxPrice Optional maximum price filter
     * @param searchTerm Optional search term for title/description
     * @param pageRequest Pagination parameters
     * @return Paginated response with services including professional information
     */
    @CacheResult(cacheName = "service-search-with-professional")
    public PageResponse<ServiceWithProfessionalDTO> searchPublishedWithProfessionalPaginated(
            UUID categoryId, Double minPrice, Double maxPrice, String searchTerm, PageRequest pageRequest) {
        logger.debug("🔍 Searching published services with professional data: category={}, price=[{}-{}], term='{}', page={}",
                    categoryId, minPrice, maxPrice, searchTerm, pageRequest);
        return serviceRepository.searchPublishedWithProfessionalPaginated(categoryId, minPrice, maxPrice, searchTerm, pageRequest);
    }

    /**
     * Clear all service caches including the new professional data caches
     * Useful for administrative operations
     */
    @CacheInvalidate(cacheName = "service-list")
    @CacheInvalidate(cacheName = "service-details")
    @CacheInvalidate(cacheName = "service-list-with-professional")
    @CacheInvalidate(cacheName = "service-list-category-with-professional")
    @CacheInvalidate(cacheName = "service-search-with-professional")
    public void clearCache() {
        logger.info("🧹 Clearing all service caches including professional data caches");
    }

}