auth:
  adminUser: admin
  adminPassword: yotelohago

externalDatabase:
  host: yotelohago-db-rw.cnpg-system.svc.cluster.local
  user: keycloak_user
  password: yotelohago
  database: keycloak
  port: 5432

postgresql:
  enabled: false

ingress:
  enabled: true
  hostname: keycloak.yotelohago.co
  ingressClassName: traefik
  tls: true
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-prod
  extraTls:
    - hosts:
        - keycloak.yotelohago.co
      secretName: keycloak-yotelohago-co-tls