package com.yotelohago.messaging.domain;

import com.yotelohago.common.dto.PageRequest;
import com.yotelohago.common.dto.PageResponse;
import com.yotelohago.messaging.api.ConversationDTO;
import com.yotelohago.messaging.api.MessageWithDetailsDTO;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface MessageRepository {
    void persist(Message message);
    Optional<Message> findById(Long id);
    List<Message> findByBookingId(Long bookingId);
    void markAsRead(Long messageId);
    void deleteById(Long id);
    void flush();

    // New methods for conversation management
    List<Message> findByParticipant(UUID participantId);
    int countUnreadForUser(UUID userId);

    // Pagination methods for conversations and messages
    /**
     * Find all conversations with pagination (admin only)
     * @param pageRequest Pagination parameters
     * @return Paginated response with all conversations
     */
    PageResponse<ConversationDTO.ConversationSummary> findAllConversationsPaginated(PageRequest pageRequest);

    /**
     * Find conversations for a specific participant with pagination
     * @param participantId The user ID to find conversations for
     * @param pageRequest Pagination parameters
     * @return Paginated response with conversations for the user
     */
    PageResponse<ConversationDTO.ConversationSummary> findConversationsByParticipantPaginated(UUID participantId, PageRequest pageRequest);

    /**
     * Find all messages for a specific booking/conversation with pagination
     * @param bookingId The booking ID to find messages for
     * @param pageRequest Pagination parameters
     * @return Paginated response with messages for the conversation
     */
    PageResponse<MessageWithDetailsDTO> findMessagesByBookingPaginated(Long bookingId, PageRequest pageRequest);
}
