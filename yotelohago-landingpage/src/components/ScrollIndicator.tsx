import React from 'react';
import { motion } from 'framer-motion';

interface ScrollIndicatorProps {
  currentPanel: number;
  totalPanels: number;
}

const ScrollIndicator: React.FC<ScrollIndicatorProps> = ({ currentPanel, totalPanels }) => {
  if (currentPanel >= totalPanels - 1) return null;

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed top-1/2 right-8 transform -translate-y-1/2 z-40"
    >
      <motion.div
        animate={{ x: [0, 10, 0] }}
        transition={{ repeat: Infinity, duration: 2, ease: "easeInOut" }}
        className="text-coral-300 text-sm writing-vertical-rl transform rotate-180 hidden md:block"
      >
        Scroll to explore more
      </motion.div>
      
      <motion.div
        animate={{ y: [0, 10, 0] }}
        transition={{ repeat: Infinity, duration: 1.5, ease: "easeInOut" }}
        className="mt-4 w-1 h-12 bg-gradient-to-b from-coral-400/60 to-transparent rounded-full"
      />
    </motion.div>
  );
};

export default ScrollIndicator;