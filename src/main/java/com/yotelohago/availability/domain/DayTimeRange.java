package com.yotelohago.availability.domain;

import jakarta.persistence.*;

import java.time.DayOfWeek;
import java.time.LocalTime;
import java.util.Objects;

/**
 * Embeddable class representing a time range for a specific day of the week.
 * 
 * This flattened approach avoids the nested @ElementCollection issue by combining
 * the day of week with the time range in a single embeddable object.
 * 
 * This maintains the same business logic while being compatible with Hibernate's
 * mapping constraints.
 */
@Embeddable
public class DayTimeRange {

    @Column(name = "day_of_week")
    @Enumerated(EnumType.STRING)
    private DayOfWeek dayOfWeek;

    @Column(name = "start_time")
    private LocalTime startTime;

    @Column(name = "end_time")
    private LocalTime endTime;

    // JPA constructor
    public DayTimeRange() {}

    public DayTimeRange(DayOfWeek dayOfWeek, LocalTime startTime, LocalTime endTime) {
        validateAndSet(dayOfWeek, startTime, endTime);
    }



    // Getters
    public DayOfWeek getDayOfWeek() { return dayOfWeek; }
    public LocalTime getStartTime() { return startTime; }
    public LocalTime getEndTime() { return endTime; }

    // Setters with validation
    public void setDayOfWeek(DayOfWeek dayOfWeek) {
        validateAndSet(dayOfWeek, this.startTime, this.endTime);
    }

    public void setStartTime(LocalTime startTime) {
        validateAndSet(this.dayOfWeek, startTime, this.endTime);
    }

    public void setEndTime(LocalTime endTime) {
        validateAndSet(this.dayOfWeek, this.startTime, endTime);
    }

    /**
     * Validate and set all fields ensuring business rules
     */
    private void validateAndSet(DayOfWeek day, LocalTime start, LocalTime end) {
        if (day == null) {
            throw new IllegalArgumentException("Day of week cannot be null");
        }
        if (start == null || end == null) {
            throw new IllegalArgumentException("Start time and end time cannot be null");
        }

        // Enforce 5-minute granularity
        if (start.getMinute() % 5 != 0 || start.getSecond() != 0 || start.getNano() != 0) {
            throw new IllegalArgumentException("Start time must be in 5-minute increments (e.g., 10:00, 10:05, 10:10)");
        }
        if (end.getMinute() % 5 != 0 || end.getSecond() != 0 || end.getNano() != 0) {
            throw new IllegalArgumentException("End time must be in 5-minute increments (e.g., 10:00, 10:05, 10:10)");
        }

        if (!start.isBefore(end)) {
            throw new IllegalArgumentException("Start time must be before end time");
        }

        this.dayOfWeek = day;
        this.startTime = start;
        this.endTime = end;
    }



    /**
     * Check if this time range overlaps with another on the same day
     */
    public boolean overlapsWith(DayTimeRange other) {
        if (other == null || !this.dayOfWeek.equals(other.dayOfWeek)) {
            return false;
        }
        return this.startTime.isBefore(other.endTime) && this.endTime.isAfter(other.startTime);
    }

    /**
     * Check if this time range is adjacent to another on the same day
     */
    public boolean isAdjacentTo(DayTimeRange other) {
        if (other == null || !this.dayOfWeek.equals(other.dayOfWeek)) {
            return false;
        }
        return this.endTime.equals(other.startTime) || other.endTime.equals(this.startTime);
    }

    /**
     * Check if this time range can be merged with another
     */
    public boolean canMergeWith(DayTimeRange other) {
        return overlapsWith(other) || isAdjacentTo(other);
    }

    /**
     * Merge this time range with another, returning a new merged range
     */
    public DayTimeRange mergeWith(DayTimeRange other) {
        if (!canMergeWith(other)) {
            throw new IllegalArgumentException("Cannot merge non-overlapping and non-adjacent time ranges");
        }

        LocalTime newStart = this.startTime.isBefore(other.startTime) ? this.startTime : other.startTime;
        LocalTime newEnd = this.endTime.isAfter(other.endTime) ? this.endTime : other.endTime;

        return new DayTimeRange(this.dayOfWeek, newStart, newEnd);
    }

    /**
     * Check if this time range contains a specific time
     */
    public boolean contains(LocalTime time) {
        return time != null && 
               !time.isBefore(startTime) && 
               time.isBefore(endTime); // End time is exclusive
    }

    /**
     * Get duration in minutes
     */
    public long getDurationMinutes() {
        return java.time.Duration.between(startTime, endTime).toMinutes();
    }

    /**
     * Check if this is a valid time range
     */
    public boolean isValid() {
        return dayOfWeek != null && startTime != null && endTime != null && startTime.isBefore(endTime);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        DayTimeRange that = (DayTimeRange) o;
        return dayOfWeek == that.dayOfWeek &&
               Objects.equals(startTime, that.startTime) &&
               Objects.equals(endTime, that.endTime);
    }

    @Override
    public int hashCode() {
        return Objects.hash(dayOfWeek, startTime, endTime);
    }

    @Override
    public String toString() {
        if (dayOfWeek == null || startTime == null || endTime == null) {
            return "DayTimeRange{invalid}";
        }
        return String.format("DayTimeRange{%s %s-%s}", 
                           dayOfWeek.toString().substring(0, 3),
                           startTime.toString(),
                           endTime.toString());
    }
}
