import { apiGet, apiGetPublic } from './client';
import { ApiEndpoints } from './types';
import { 
  ServiceDTO, 
  PaginatedResponse, 
  ServiceSearchParams,
  PaginationParams 
} from '../../types/api';

/**
 * Service API Service
 * Handles all service-related API calls with caching and error handling
 */

/**
 * Get paginated services with optional filters
 * 
 * @param params Search and pagination parameters
 * @returns Promise with paginated service response
 */
export const getServices = async (
  params: ServiceSearchParams = {}
): Promise<PaginatedResponse<ServiceDTO>> => {
  try {
    console.log('🚀 Fetching services with params:', params);

    // Build query parameters
    const queryParams = new URLSearchParams();

    // Pagination parameters
    if (params.page !== undefined) queryParams.append('page', params.page.toString());
    if (params.size !== undefined) queryParams.append('size', params.size.toString());
    if (params.sort) queryParams.append('sort', params.sort);
    if (params.direction) queryParams.append('direction', params.direction);

    // Filter parameters
    if (params.category) queryParams.append('category', params.category);
    if (params.city) queryParams.append('city', params.city);
    if (params.minPrice !== undefined) queryParams.append('minPrice', params.minPrice.toString());
    if (params.maxPrice !== undefined) queryParams.append('maxPrice', params.maxPrice.toString());
    if (params.available !== undefined) queryParams.append('available', params.available.toString());
    if (params.query) queryParams.append('search', params.query);

    const url = `${ApiEndpoints.SERVICES}?${queryParams.toString()}`;
    console.log('🔗 Full API URL:', url);

    const response = await apiGetPublic<PaginatedResponse<ServiceDTO>>(url);

    console.log('✅ Services fetched successfully:', {
      totalElements: response.totalElements,
      totalPages: response.totalPages,
      currentPage: response.number,
      contentLength: response.content.length,
      firstService: response.content[0] ? {
        id: response.content[0].id,
        title: response.content[0].title,
        categoryName: response.content[0].categoryName
      } : null
    });

    return response;
  } catch (error) {
    console.error('❌ Failed to fetch services:', error);
    console.error('❌ Error details:', {
      message: error.message,
      stack: error.stack,
      url: `${ApiEndpoints.SERVICES}?${new URLSearchParams(params as any).toString()}`
    });
    throw new Error('Failed to fetch services. Please try again.');
  }
};

/**
 * Get a single service by ID
 * 
 * @param id Service ID
 * @returns Promise with service data
 */
export const getServiceById = async (id: string): Promise<ServiceDTO> => {
  try {
    console.log('🚀 Fetching service by ID:', id);
    
    const response = await apiGetPublic<ServiceDTO>(`${ApiEndpoints.SERVICES}/${id}`);
    
    console.log('✅ Service fetched successfully:', {
      id: response.id,
      title: response.title,
      price: response.price
    });
    
    return response;
  } catch (error) {
    console.error('❌ Failed to fetch service:', error);
    throw new Error('Failed to fetch service details. Please try again.');
  }
};

/**
 * Search services with text query and filters
 * 
 * @param query Search query string
 * @param filters Additional search filters
 * @returns Promise with paginated search results
 */
export const searchServices = async (
  query: string,
  filters: Omit<ServiceSearchParams, 'query'> = {}
): Promise<PaginatedResponse<ServiceDTO>> => {
  try {
    console.log('🔍 Searching services:', { query, filters });
    
    const searchParams: ServiceSearchParams = {
      ...filters,
      query: query.trim()
    };
    
    return await getServices(searchParams);
  } catch (error) {
    console.error('❌ Failed to search services:', error);
    throw new Error('Failed to search services. Please try again.');
  }
};

/**
 * Get services by professional ID
 * 
 * @param professionalId Professional's Keycloak ID
 * @param params Pagination parameters
 * @returns Promise with paginated services
 */
export const getServicesByProfessional = async (
  professionalId: string,
  params: PaginationParams = {}
): Promise<PaginatedResponse<ServiceDTO>> => {
  try {
    console.log('🚀 Fetching services by professional:', professionalId);
    
    // Build query parameters
    const queryParams = new URLSearchParams();
    queryParams.append('professional', professionalId);
    
    if (params.page !== undefined) queryParams.append('page', params.page.toString());
    if (params.size !== undefined) queryParams.append('size', params.size.toString());
    if (params.sort) queryParams.append('sort', params.sort);
    if (params.direction) queryParams.append('direction', params.direction);
    
    const url = `${ApiEndpoints.SERVICES}?${queryParams.toString()}`;
    
    const response = await apiGetPublic<PaginatedResponse<ServiceDTO>>(url);
    
    console.log('✅ Professional services fetched successfully:', {
      professionalId,
      totalElements: response.totalElements,
      contentLength: response.content.length
    });
    
    return response;
  } catch (error) {
    console.error('❌ Failed to fetch professional services:', error);
    throw new Error('Failed to fetch professional services. Please try again.');
  }
};

/**
 * Get featured services (services marked as featured)
 * This is a convenience method that filters for featured services
 * 
 * @param params Pagination parameters
 * @returns Promise with paginated featured services
 */
export const getFeaturedServices = async (
  params: PaginationParams = {}
): Promise<PaginatedResponse<ServiceDTO>> => {
  try {
    console.log('🌟 Fetching featured services');
    
    // For now, we'll get all services and filter on frontend
    // TODO: Add featured flag to backend API
    const response = await getServices({
      ...params,
      size: params.size || 10 // Limit featured services
    });
    
    console.log('✅ Featured services fetched successfully:', {
      totalElements: response.totalElements,
      contentLength: response.content.length
    });
    
    return response;
  } catch (error) {
    console.error('❌ Failed to fetch featured services:', error);
    throw new Error('Failed to fetch featured services. Please try again.');
  }
};

// Export default service object
export default {
  getServices,
  getServiceById,
  searchServices,
  getServicesByProfessional,
  getFeaturedServices,
};
