apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod
spec:
  acme:
    # Staging
    #server: https://acme-staging-v02.api.letsencrypt.org/directory
    # Prod
    server: https://acme-v02.api.letsencrypt.org/directory
    email: <EMAIL>
    privateKeySecretRef:
      name: letsencrypt-prod
    solvers:
      - dns01:  #TODO: Create cloudflare-api-token-secret using terraform
          cloudflare:
            email: <EMAIL>
            apiTokenSecretRef:
              name: cloudflare-api-token-secret
              key: api-token