import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  Image,
  Alert,
  ActivityIndicator,
  Platform,
} from 'react-native';

// Web-specific type declarations
declare global {
  interface Window {
    File: any;
    FileReader: any;
  }
}
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import { useAuth } from '../../contexts/AuthContext';
import { mediaService } from '../../services/api/mediaService';
import { ProfilePhotoUploadState, ImagePickerResult } from '../../types/api';
import Colors from '../../app/client/constants/Colors';

// Default user image
const defaultUserImage = require('../../assets/images/user.png');

interface ProfilePhotoUploadModalProps {
  visible: boolean;
  onClose: () => void;
  onUploadSuccess: (imageUrl: string) => void;
  currentImageUrl?: string;
}

export default function ProfilePhotoUploadModal({
  visible,
  onClose,
  onUploadSuccess,
  currentImageUrl,
}: ProfilePhotoUploadModalProps) {
  const { state: authState } = useAuth();
  const [uploadState, setUploadState] = useState<ProfilePhotoUploadState>({
    isLoading: false,
    isUploading: false,
    error: null,
    progress: 0,
  });
  const [selectedImage, setSelectedImage] = useState<ImagePickerResult | null>(null);

  const resetState = () => {
    setUploadState({
      isLoading: false,
      isUploading: false,
      error: null,
      progress: 0,
    });
    setSelectedImage(null);
  };

  const handleClose = () => {
    resetState();
    onClose();
  };

  const requestPermissions = async () => {
    if (Platform.OS !== 'web') {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(
          'Permission Required',
          'Sorry, we need camera roll permissions to upload your profile photo.'
        );
        return false;
      }
    }
    return true;
  };

  const pickImageFromGallery = async () => {
    try {
      // For web, use HTML file input
      if (Platform.OS === 'web') {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = 'image/*';
        input.onchange = (event: any) => {
          const file = event.target.files[0];
          if (file) {
            const reader = new FileReader();
            reader.onload = (e: any) => {
              setSelectedImage({
                uri: e.target.result,
                type: file.type,
                name: 'profile.jpg', // Always use profile.jpg
                size: file.size,
                file: file, // Store the actual File object for web
              });
            };
            reader.readAsDataURL(file);
          }
        };
        input.click();
        return;
      }

      const hasPermission = await requestPermissions();
      if (!hasPermission) return;

      setUploadState(prev => ({ ...prev, isLoading: true, error: null }));

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
        base64: false,
      });

      if (!result.canceled && result.assets[0]) {
        const asset = result.assets[0];
        setSelectedImage({
          uri: asset.uri,
          type: asset.type || 'image/jpeg',
          name: 'profile.jpg', // Always use profile.jpg
          size: asset.fileSize || 0,
        });
      }
    } catch (error) {
      console.error('Error picking image:', error);
      setUploadState(prev => ({
        ...prev,
        error: 'Failed to select image. Please try again.',
      }));
    } finally {
      setUploadState(prev => ({ ...prev, isLoading: false }));
    }
  };

  const takePhoto = async () => {
    try {
      // Camera not available on web
      if (Platform.OS === 'web') {
        Alert.alert(
          'Camera Not Available',
          'Camera functionality is not available on web. Please use the photo library option.'
        );
        return;
      }

      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(
          'Permission Required',
          'Sorry, we need camera permissions to take your profile photo.'
        );
        return;
      }

      setUploadState(prev => ({ ...prev, isLoading: true, error: null }));

      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
        base64: false,
      });

      if (!result.canceled && result.assets[0]) {
        const asset = result.assets[0];
        setSelectedImage({
          uri: asset.uri,
          type: asset.type || 'image/jpeg',
          name: 'profile.jpg', // Always use profile.jpg
          size: asset.fileSize || 0,
        });
      }
    } catch (error) {
      console.error('Error taking photo:', error);
      setUploadState(prev => ({
        ...prev,
        error: 'Failed to take photo. Please try again.',
      }));
    } finally {
      setUploadState(prev => ({ ...prev, isLoading: false }));
    }
  };

  const uploadImage = async () => {
    if (!selectedImage || !authState.currentMode) {
      return;
    }

    try {
      setUploadState(prev => ({ ...prev, isUploading: true, error: null }));

      let fileToUpload: Blob | File;

      if (Platform.OS === 'web' && selectedImage.file) {
        // On web, use the stored File object directly
        fileToUpload = selectedImage.file;
      } else {
        // On mobile or web fallback, convert URI to blob
        const response = await fetch(selectedImage.uri);
        fileToUpload = await response.blob();
      }

      // Upload using media service with fixed filename 'profile.jpg'
      const mediaRecord = await mediaService.uploadProfilePhoto(
        fileToUpload,
        'profile.jpg', // Always use profile.jpg as filename
        authState.currentMode
      );

      // Success - call callback with new image URL (add cache busting)
      const cacheBustedUrl = `${mediaRecord.publicUrl}?t=${Date.now()}`;
      console.log('✅ Upload successful, calling onUploadSuccess with URL:', cacheBustedUrl);
      onUploadSuccess(cacheBustedUrl);
      handleClose();
    } catch (error) {
      console.error('Upload failed:', error);
      setUploadState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Upload failed. Please try again.',
      }));
    } finally {
      setUploadState(prev => ({ ...prev, isUploading: false }));
    }
  };

  const showImageOptions = () => {
    console.log('📱 Platform:', Platform.OS);

    if (Platform.OS === 'web') {
      // On web, only show photo library option
      console.log('🌐 Using web file picker');
      pickImageFromGallery();
    } else {
      // On mobile, show both options
      console.log('📱 Using mobile image picker');
      Alert.alert(
        'Select Photo',
        'Choose how you want to select your profile photo',
        [
          { text: 'Camera', onPress: takePhoto },
          { text: 'Photo Library', onPress: pickImageFromGallery },
          { text: 'Cancel', style: 'cancel' },
        ]
      );
    }
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={handleClose}
    >
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={handleClose} style={styles.closeButton}>
            <Ionicons name="close" size={24} color={Colors.text.primary} />
          </TouchableOpacity>
          <Text style={styles.title}>Profile Photo</Text>
          <View style={styles.placeholder} />
        </View>

        {/* Content */}
        <View style={styles.content}>
          {/* Current/Selected Image */}
          <View style={styles.imageContainer}>
            <Image
              source={
                selectedImage?.uri
                  ? { uri: selectedImage.uri }
                  : currentImageUrl && currentImageUrl !== defaultUserImage
                  ? { uri: currentImageUrl }
                  : defaultUserImage
              }
              style={styles.profileImage}
            />
            {uploadState.isUploading && (
              <View style={styles.uploadingOverlay}>
                <ActivityIndicator size="large" color={Colors.primary} />
                <Text style={styles.uploadingText}>Uploading...</Text>
              </View>
            )}
          </View>

          {/* Error Message */}
          {uploadState.error && (
            <View style={styles.errorContainer}>
              <Text style={styles.errorText}>{uploadState.error}</Text>
            </View>
          )}

          {/* Action Buttons */}
          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={[styles.button, styles.selectButton]}
              onPress={showImageOptions}
              disabled={uploadState.isLoading || uploadState.isUploading}
            >
              {uploadState.isLoading ? (
                <ActivityIndicator size="small" color={Colors.white} />
              ) : (
                <>
                  <Ionicons name="camera" size={20} color={Colors.white} />
                  <Text style={styles.buttonText}>
                    {selectedImage ? 'Change Photo' : Platform.OS === 'web' ? 'Choose File' : 'Select Photo'}
                  </Text>
                </>
              )}
            </TouchableOpacity>

            {selectedImage && (
              <TouchableOpacity
                style={[styles.button, styles.uploadButton]}
                onPress={uploadImage}
                disabled={uploadState.isUploading}
              >
                {uploadState.isUploading ? (
                  <ActivityIndicator size="small" color={Colors.white} />
                ) : (
                  <>
                    <Ionicons name="cloud-upload" size={20} color={Colors.white} />
                    <Text style={styles.buttonText}>Upload</Text>
                  </>
                )}
              </TouchableOpacity>
            )}
          </View>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.primary,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 60,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  closeButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.background.secondary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text.primary,
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
    padding: 24,
    alignItems: 'center',
  },
  imageContainer: {
    position: 'relative',
    marginBottom: 32,
  },
  profileImage: {
    width: 150,
    height: 150,
    borderRadius: 75,
    borderWidth: 3,
    borderColor: Colors.primary,
  },
  uploadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    borderRadius: 75,
    justifyContent: 'center',
    alignItems: 'center',
  },
  uploadingText: {
    color: Colors.text.light,
    marginTop: 8,
    fontSize: 14,
    fontWeight: '500',
  },
  errorContainer: {
    backgroundColor: Colors.error + '20',
    padding: 12,
    borderRadius: 8,
    marginBottom: 24,
    width: '100%',
  },
  errorText: {
    color: Colors.error,
    fontSize: 14,
    textAlign: 'center',
  },
  buttonContainer: {
    width: '100%',
    gap: 12,
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    gap: 8,
  },
  selectButton: {
    backgroundColor: Colors.secondary,
  },
  uploadButton: {
    backgroundColor: Colors.primary,
  },
  buttonText: {
    color: Colors.text.light,
    fontSize: 16,
    fontWeight: '600',
  },
});
