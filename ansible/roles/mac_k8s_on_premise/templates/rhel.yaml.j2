# ~/.lima/rhel.yaml (generated by Ansible)
images:
  - location: "{{ lima_image }}"
    arch: "aarch64"
    digest: ""

cpus: {{ lima_cpus }}
memory: "{{ lima_memory }}"
disk: "{{ lima_disk }}"

mounts:
  - location: "{{ lima_mount_location }}"
    writable: true

ssh:
  localPort: {{ lima_ssh_port }}

containerd:
  system: false
  user: false

provision:
  - mode: system
    script: |
      {{ lima_provision_script | indent(6) }}

firmware:
  legacyBIOS: false

portForwards:

  # kubectl access
  - guestPort: 6443
    hostPort: 6443

  # https port
  - guestPort: 443
    hostPort: 443
    hostIP: ************  # your Mac mini’s Tailscale IP. TODO: Automate it to prevent hardcode it

  # http port
  - guestPort: 80
    hostPort: 80
    hostIP: ************  # your Mac mini’s Tailscale IP. TODO: Automate it to prevent hardcode it

env:
  TERM: xterm-256color

message: |
  Welcome to your RHEL VM in Lima!
  Run `limactl shell rhel` to access.