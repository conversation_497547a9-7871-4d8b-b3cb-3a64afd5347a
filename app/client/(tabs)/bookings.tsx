import React, { useState, useMemo, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Dimensions, Platform, RefreshControl, ActivityIndicator } from 'react-native';
import { useRouter } from 'expo-router';
import { LegendList } from '@legendapp/list';
import BookingItem from '../components/BookingItem';
import Colors from '../constants/Colors';
import { Service, Booking } from '@/types';
import { BookingStatus } from '../../../types/api';
import { AuthGuard } from '../../../components/auth';
import { useBookings } from '../../../hooks';

type FrontendBookingStatus = 'open' | 'accepted' | 'completed' | 'cancelled';

export default function BookingsScreen() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState<FrontendBookingStatus>('open');
  const { width } = Dimensions.get('window');
  const isWeb = Platform.OS === 'web';

  // Use the useBookings hook for real API integration
  const {
    bookings: apiBookings,
    isLoading,
    error,
    isEmpty,
    refresh,
  } = useBookings({ autoLoad: true });

  // Debug logging for API bookings
  useEffect(() => {
    console.log('📱 BookingsScreen - API bookings updated:', {
      bookingsCount: apiBookings?.length || 0,
      isLoading,
      error,
      isEmpty,
      bookings: apiBookings?.map(b => ({ id: b.id, status: b.status, title: b.title })) || []
    });
  }, [apiBookings, isLoading, error, isEmpty]);

  // Calculate grid configuration
  const gridConfig = useMemo(() => {
    if (isWeb) {
      const columns = width >= 1200 ? 3 : width >= 768 ? 2 : 1;
      const padding = 32;
      const gap = 16;
      const cardWidth = (width - padding - (gap * (columns - 1))) / columns;
      return { columns, cardWidth, gap };
    } else {
      return { columns: 1, cardWidth: width - 32, gap: 0 };
    }
  }, [width, isWeb]);

  // Filter bookings by status from API data
  const filteredBookings = useMemo(() => {
    if (!apiBookings || apiBookings.length === 0) {
      console.log('🔍 No API bookings available for filtering');
      return [];
    }

    const filtered = apiBookings.filter(booking => booking.status === activeTab);

    console.log('🔍 Filtering bookings:', {
      activeTab,
      totalBookings: apiBookings.length,
      filteredCount: filtered.length,
      allStatuses: apiBookings.map(b => ({ id: b.id, status: b.status, title: b.title })),
      filteredBookings: filtered.map(b => ({ id: b.id, status: b.status, title: b.title }))
    });

    return filtered;
  }, [apiBookings, activeTab]);
  // Group bookings into rows for grid layout
  const groupedBookings = useMemo(() => {
    const rows: Booking[][] = [];
    for (let i = 0; i < filteredBookings.length; i += gridConfig.columns) {
      rows.push(filteredBookings.slice(i, i + gridConfig.columns));
    }
    return rows;
  }, [filteredBookings, gridConfig.columns]);

  const handleBookingPress = (booking: Booking) => {
    router.push({
      pathname: '/client/service/[id]' as any,
      params: { id: booking.serviceId }
    });
  };

  const handleMessagePress = (providerId: string) => {
    // In a real app, navigate to message screen with this provider
    console.log('Message provider:', providerId);
  };

  const handleRefresh = async () => {
    try {
      await refresh();
    } catch (error) {
      console.error('Failed to refresh bookings:', error);
    }
  };

  // Render booking row for grid layout
  const renderBookingRow = ({ item: row, index }: { item: Booking[], index: number }) => {
    if (gridConfig.columns === 1) {
      // Single column layout
      const booking = row[0];

      // Create service and provider objects from enriched booking data
      const service: Service = {
        id: booking.serviceId,
        providerId: booking.providerId,
        title: booking.serviceTitle || booking.title,
        description: booking.description || '',
        category: booking.serviceCategoryName || 'General',
        price: booking.price,
        location: 'Location not available', // TODO: Add location to enriched DTO
        images: [],
        status: booking.status,
        date: booking.date,
        time: booking.time,
        rating: 0, // TODO: Add rating to enriched DTO
        reviews: 0, // TODO: Add reviews to enriched DTO
        instantBook: false,
      };

      const provider = {
        id: booking.providerId,
        name: booking.professionalName || 'Professional',
        email: '',
        avatar: booking.professionalAvatar || '',
        bio: '',
        role: 'provider' as const,
        services: [booking.serviceId],
        favorites: [],
      };

      return (
        <View style={styles.bookingItemContainer}>
          <BookingItem
            booking={booking}
            service={service}
            provider={provider}
            onPress={handleBookingPress}
            onMessagePress={handleMessagePress}
          />
        </View>
      );
    }

    // Multi-column grid layout
    return (
      <View style={styles.bookingRowContainer}>
        {row.map((booking, bookingIndex) => {
          // Create service and provider objects from enriched booking data
          const service: Service = {
            id: booking.serviceId,
            providerId: booking.providerId,
            title: booking.serviceTitle || booking.title,
            description: booking.description || '',
            category: booking.serviceCategoryName || 'General',
            price: booking.price,
            location: 'Location not available',
            images: [],
            status: booking.status,
            date: booking.date,
            time: booking.time,
            rating: 0,
            reviews: 0,
            instantBook: false,
          };

          const provider = {
            id: booking.providerId,
            name: booking.professionalName || 'Professional',
            email: '',
            avatar: booking.professionalAvatar || '',
            bio: '',
            role: 'provider' as const,
            services: [booking.serviceId],
            favorites: [],
          };

          return (
            <View
              key={booking.id}
              style={[
                styles.bookingColumnContainer,
                { width: gridConfig.cardWidth }
              ]}
            >
              <BookingItem
                booking={booking}
                service={service}
                provider={provider}
                onPress={handleBookingPress}
                onMessagePress={handleMessagePress}
              />
            </View>
          );
        })}
        {/* Fill empty spaces in the last row */}
        {row.length < gridConfig.columns &&
          Array.from({ length: gridConfig.columns - row.length }).map((_, emptyIndex) => (
            <View
              key={`empty-${emptyIndex}`}
              style={[
                styles.bookingColumnContainer,
                { width: gridConfig.cardWidth }
              ]}
            />
          ))
        }
      </View>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Text style={styles.emptyTitle}>
        No {activeTab} bookings
      </Text>
      <Text style={styles.emptySubtitle}>
        Your {activeTab} service bookings will appear here.
      </Text>
    </View>
  );

  const renderLoadingState = () => (
    <View style={styles.loadingContainer}>
      <ActivityIndicator size="large" color={Colors.primary} />
      <Text style={styles.loadingText}>Loading bookings...</Text>
    </View>
  );

  const renderErrorState = () => (
    <View style={styles.errorContainer}>
      <Text style={styles.errorTitle}>Unable to load bookings</Text>
      <Text style={styles.errorText}>{error}</Text>
      <TouchableOpacity style={styles.retryButton} onPress={handleRefresh}>
        <Text style={styles.retryButtonText}>Try Again</Text>
      </TouchableOpacity>
    </View>
  );
  
  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Bookings</Text>
      </View>

      <AuthGuard
        actionType="booking"
        promptTitle="Manage Your Bookings"
        promptMessage="Log in to view and manage your service bookings and appointments."
      >
        <View style={styles.tabContainer}>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'open' && styles.activeTab]}
            onPress={() => setActiveTab('open')}
          >
            <Text
              style={[
                styles.tabText,
                activeTab === 'open' && styles.activeTabText
              ]}
            >
              Open
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.tab, activeTab === 'accepted' && styles.activeTab]}
            onPress={() => setActiveTab('accepted')}
          >
            <Text
              style={[
                styles.tabText,
                activeTab === 'accepted' && styles.activeTabText
              ]}
            >
              Accepted
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.tab, activeTab === 'completed' && styles.activeTab]}
            onPress={() => setActiveTab('completed')}
          >
            <Text
              style={[
                styles.tabText,
                activeTab === 'completed' && styles.activeTabText
              ]}
            >
              Completed
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.tab, activeTab === 'cancelled' && styles.activeTab]}
            onPress={() => setActiveTab('cancelled')}
          >
            <Text
              style={[
                styles.tabText,
                activeTab === 'cancelled' && styles.activeTabText
              ]}
            >
              Cancelled
            </Text>
          </TouchableOpacity>
        </View>

        {/* Bookings List with LegendList */}
        <View style={styles.listContainer}>
          {isLoading ? (
            renderLoadingState()
          ) : error ? (
            renderErrorState()
          ) : filteredBookings.length === 0 ? (
            renderEmptyState()
          ) : (
            <LegendList
              data={groupedBookings}
              renderItem={renderBookingRow}
              keyExtractor={(item: Booking[], index: number) =>
                gridConfig.columns === 1 ? item[0].id : `row-${index}-${item.map(b => b.id).join('-')}`
              }
              refreshControl={
                <RefreshControl
                  refreshing={isLoading}
                  onRefresh={handleRefresh}
                  colors={[Colors.primary]}
                  tintColor={Colors.primary}
                />
              }
              contentContainerStyle={styles.listContent}
              showsVerticalScrollIndicator={false}
              estimatedItemSize={gridConfig.columns > 1 ? 180 : 120}
              recycleItems
            />
          )}
        </View>
      </AuthGuard>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.primary,
  },
  header: {
    backgroundColor: Colors.background.primary,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 30,
    paddingBottom: 16,
    paddingHorizontal: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: Colors.text.primary,
    flex: 1,
    textAlign: 'center',
  },
  tabContainer: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    backgroundColor: Colors.background.primary,
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: Colors.primary,
  },
  tabText: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.text.secondary,
  },
  activeTabText: {
    color: Colors.primary,
  },
  listContainer: {
    flex: 1,
  },
  listContent: {
    padding: 10,
  },
  bookingItemContainer: {
    marginBottom: 12,
  },
  bookingRowContainer: {
    flexDirection: 'row',
    marginBottom: 12,
    gap: 10,
  },
  bookingColumnContainer: {
    flex: 1,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
    paddingHorizontal: 20,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text.primary,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptySubtitle: {
    fontSize: 16,
    color: Colors.text.secondary,
    textAlign: 'center',
    lineHeight: 22,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    fontSize: 16,
    color: Colors.text.secondary,
    marginTop: 12,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
    paddingHorizontal: 20,
  },
  errorTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text.primary,
    marginBottom: 8,
    textAlign: 'center',
  },
  errorText: {
    fontSize: 14,
    color: Colors.text.secondary,
    textAlign: 'center',
    marginBottom: 20,
    lineHeight: 20,
  },
  retryButton: {
    backgroundColor: Colors.primary,
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  retryButtonText: {
    color: Colors.text.light,
    fontSize: 14,
    fontWeight: '600',
  },
});