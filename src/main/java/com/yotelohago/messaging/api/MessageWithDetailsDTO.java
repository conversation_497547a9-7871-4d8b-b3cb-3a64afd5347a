package com.yotelohago.messaging.api;

import java.time.Instant;
import java.util.UUID;

/**
 * DTO for messages with full sender/receiver/booking details
 * Used by admin panel to display individual messages with context
 * Compatible with BackendMessageDTO interface while providing additional nested objects
 */
public class MessageWithDetailsDTO {
    // Basic fields (compatible with BackendMessageDTO)
    public Long id;
    public Long bookingId;
    public String senderId;    // String for admin panel compatibility
    public String receiverId;  // String for admin panel compatibility
    public String content;
    public String sentAt;      // String for admin panel compatibility
    public boolean isRead;

    // Nested objects for admin panel display
    public UserInfo sender;
    public UserInfo receiver;
    public BookingInfo booking;

    // Inner classes for nested data
    public static class UserInfo {
        public String id;           // String for admin panel compatibility
        public String firstName;
        public String lastName;
        public String email;
        public boolean isProfessional;

        public UserInfo() {}

        public UserInfo(String id, String firstName, String lastName, String email, boolean isProfessional) {
            this.id = id;
            this.firstName = firstName;
            this.lastName = lastName;
            this.email = email;
            this.isProfessional = isProfessional;
        }
    }

    public static class BookingInfo {
        public String id;           // String for admin panel compatibility
        public String title;
        public String description;
        public String status;

        public BookingInfo() {}

        public BookingInfo(String id, String title, String description, String status) {
            this.id = id;
            this.title = title;
            this.description = description;
            this.status = status;
        }
    }

    // Default constructor
    public MessageWithDetailsDTO() {}

    // Constructor for manual creation (used in repository)
    public MessageWithDetailsDTO(Long id, Long bookingId, String senderId, String receiverId,
                                String content, String sentAt, boolean isRead,
                                UserInfo sender, UserInfo receiver, BookingInfo booking) {
        this.id = id;
        this.bookingId = bookingId;
        this.senderId = senderId;
        this.receiverId = receiverId;
        this.content = content;
        this.sentAt = sentAt;
        this.isRead = isRead;
        this.sender = sender;
        this.receiver = receiver;
        this.booking = booking;
    }
}
