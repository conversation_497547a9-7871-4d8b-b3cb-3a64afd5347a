package com.yotelohago.media.api;

import com.yotelohago.media.domain.Media;
import com.yotelohago.media.domain.MediaType;
import jakarta.enterprise.context.ApplicationScoped;

/**
 * Mapper for converting between Media entity and MediaDTO
 * Follows the same coding convention as Service and Booking domains:
 * - No URL building logic in mapper
 * - URLs are built exclusively in repository SQL queries
 * - Simple entity ↔ DTO conversion only
 */
@ApplicationScoped
public class MediaMapper {

    /**
     * Convert Media entity to DTO (without URL - URLs should be built in repository SQL)
     * This method is only used for internal operations where URLs are not needed
     * @param media The Media entity
     * @return MediaDTO without publicUrl
     */
    public MediaDTO toDTO(Media media) {
        if (media == null) {
            return null;
        }

        return new MediaDTO(
                media.getId(),
                media.filePath,
                media.originalFilename,
                media.fileSize,
                media.contentType,
                media.mediaType.getValue(),
                media.user.id,
                media.associatedEntityId,
                media.createdAt,
                media.updatedAt,
                null // No URL building in mapper - use repository methods with SQL-based URL building
        );
    }

    /**
     * Convert MediaDTO to entity (for updates)
     * Note: This doesn't set the user relationship - that should be handled by the service layer
     * @param dto The MediaDTO
     * @return Media entity
     */
    public Media toEntity(MediaDTO dto) {
        if (dto == null) {
            return null;
        }

        Media media = new Media();
        media.id = dto.id;
        media.filePath = dto.filePath;
        media.originalFilename = dto.originalFilename;
        media.fileSize = dto.fileSize;
        media.contentType = dto.contentType;
        media.mediaType = MediaType.fromValue(dto.mediaType);
        media.associatedEntityId = dto.associatedEntityId;
        media.createdAt = dto.createdAt;
        media.updatedAt = dto.updatedAt;

        return media;
    }
}
