{"name": "bolt-expo-starter", "main": "expo-router/entry", "version": "1.0.0", "private": true, "scripts": {"dev": "EXPO_NO_TELEMETRY=1 expo start --web --port 8082", "dev:mobile": "EXPO_NO_TELEMETRY=1 expo start", "build:web": "expo export --platform web", "lint": "expo lint"}, "dependencies": {"@expo/vector-icons": "^14.0.0", "@legendapp/list": "^1.1.0", "@react-native-community/datetimepicker": "^8.4.2", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/native": "^6.1.9", "axios": "^1.9.0", "expo": "~53.0.0", "expo-auth-session": "^6.2.0", "expo-blur": "~14.1.4", "expo-camera": "~16.1.6", "expo-constants": "~17.1.6", "expo-crypto": "^14.1.5", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-linear-gradient": "~14.1.4", "expo-linking": "~7.1.5", "expo-router": "~5.0.7", "expo-secure-store": "^14.2.3", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "expo-system-ui": "~5.0.7", "expo-web-browser": "~14.1.6", "lucide-react-native": "^0.525.0", "metro": "^0.82.4", "metro-core": "^0.82.4", "metro-runtime": "^0.82.4", "metro-source-map": "^0.82.4", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.2", "react-native-calendars": "^1.1313.0", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-url-polyfill": "^2.0.0", "react-native-web": "^0.20.0", "react-native-webview": "13.13.5", "expo-image-picker": "~16.1.4"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "eslint": "^9.0.0", "eslint-config-expo": "~9.2.0", "typescript": "^5.3.0"}}