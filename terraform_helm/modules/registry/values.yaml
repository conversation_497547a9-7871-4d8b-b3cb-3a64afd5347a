replicaCount: 1

persistence:
  deleteEnabled: true
  enabled: true
  size: 20Gi
  storageClass: local-path

secrets:
  # Generated with htpasswd  -Bbn yotelohago yotelohago
  existingSecret: registry-auth-secret

ingress:
  enabled: true
  className: traefik
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-prod
    traefik.ingress.kubernetes.io/router.entrypoints: websecure
  hosts:
    - registry.yotelohago.co
  tls:
    - secretName: registry-yotelohago-co-tls
      hosts:
        - registry.yotelohago.co

garbageCollect:
  enabled: true
  deleteUntagged: true
  schedule: "0 1 * * *"
  podAnnotations: {}
  podLabels: {}
  resources: {}
