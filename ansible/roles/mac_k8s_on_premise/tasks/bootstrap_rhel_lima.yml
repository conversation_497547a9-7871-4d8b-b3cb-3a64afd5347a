- name: Ensure Lima config directory exists
  file:
    path: "~/.lima"
    state: directory
    mode: '0755'

- name: Render RHEL Lima config template
  template:
    src: templates/rhel.yaml.j2
    dest: "~/.lima/rhel.yaml"
    mode: '0644'

- name: Create Lima VM
  shell: /opt/homebrew/bin/limactl create ~/.lima/rhel.yaml --name=rhel
  register: lima_output
  changed_when: "'already running' not in lima_output.stdout"