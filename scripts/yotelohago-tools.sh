#!/bin/bash

# Yo<PERSON><PERSON><PERSON>Hago Master Tools Script
# Centralized script for all YoteLoHago development and troubleshooting tasks
# 
# This script provides a unified interface to all functionality previously
# scattered across multiple scripts. It loads topic-specific libraries and
# provides a clean command-line interface.

set -e

# Script directory and library path
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LIB_DIR="$SCRIPT_DIR/lib"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to print section headers
print_header() {
    local title=$1
    echo ""
    print_status $BLUE "🔧 $title"
    echo "$(printf '=%.0s' {1..50})"
}

# Function to load library files
load_library() {
    local lib_name=$1
    local lib_file="$LIB_DIR/${lib_name}.sh"
    
    if [[ -f "$lib_file" ]]; then
        source "$lib_file"
    else
        print_status $RED "❌ Library not found: $lib_file"
        exit 1
    fi
}

# Load all libraries
load_libraries() {
    print_status $CYAN "📚 Loading YoteLoHago tools libraries..."
    
    # Load utility functions first (other libraries may depend on them)
    load_library "utils"
    
    # Load domain-specific libraries
    load_library "keycloak"
    load_library "users"
    load_library "api"
    load_library "api-media"
    load_library "testing"
    load_library "admin"
    
    print_status $GREEN "✅ All libraries loaded successfully"
}

# Function to show main help
show_help() {
    cat << EOF
🚀 YoteLoHago Development Tools

USAGE:
    $0 <category> <command> [arguments...]

CATEGORIES:

📋 keycloak - Keycloak management and configuration
    realm-status                    Check realm accessibility
    oidc-config                     Check OpenID Connect configuration  
    clients                         List client configurations
    client-scopes                   Show client scopes
    realm-roles                     List realm roles
    compare-clients <client1> <client2>  Compare client configurations
    fix-missing-sub                 Add missing sub mapper to clients
    add-basic-scope                 Add basic scope to clients

👥 users - User and role management
    list                           List all users
    show <username>                Show user details and roles
    create <username> <email> <first> <last> <password>  Create new user
    assign-role <username> <role>   Assign realm role to user
    remove-role <username> <role>   Remove realm role from user
    migrate <username>             Migrate user from client to realm roles

🧪 testing - Authentication and API testing
    token <username> <password>     Test token generation and validity
    backend <username> <password>   Test backend authentication
    endpoints <username> <password> Test all API endpoints
    sync <username> <password>      Test user synchronization
    stress <username> <password> [count]  Stress test authentication
    compare-envs <username> <password>     Compare localhost vs production

🌐 api - API endpoint testing
    health                         Test API health endpoints
    public                         Test public endpoints
    protected                      Test protected endpoints (should return 401)
    cors                          Test CORS configuration
    production                     Full production API test

📸 media - Media API testing
    protected                      Test protected media endpoints (should return 401)
    authenticated [user] [pass]    Test media endpoints with authentication
    professional [user] [pass]     Test professional media endpoints
    admin [user] [pass]            Test admin media endpoints
    consistency                    Test media data consistency
    comprehensive                  Run all media tests

🔧 utils - Utility functions
    decode-token <username> <password>     Decode and analyze JWT token
    full-check                     Run complete system health check
    cleanup                        Clean up temporary files

🏗️ admin - Administrative tasks and environment setup
    setup-production               Setup production Keycloak
    create-client [realm]          Create backend client
    export-realm [realm] [file]    Export remote realm configuration
    fix-admin-client              Fix Keycloak admin client
    test-complete                 Run all environment tests

EXAMPLES:
    $0 keycloak realm-status
    $0 <NAME_EMAIL>
    $0 <NAME_EMAIL> 1234
    $0 api production
    $0 media comprehensive
    $0 utils full-check
    $0 admin setup-production

For detailed help on a specific category:
    $0 <category> help

EOF
}

# Function to show category-specific help
show_category_help() {
    local category=$1
    
    case "$category" in
        "keycloak")
            keycloak_help
            ;;
        "users")
            users_help
            ;;
        "testing")
            testing_help
            ;;
        "api")
            api_help
            ;;
        "media")
            media_help
            ;;
        "utils")
            utils_help
            ;;
        "admin")
            admin_help
            ;;
        *)
            print_status $RED "❌ Unknown category: $category"
            show_help
            exit 1
            ;;
    esac
}

# Main function to route commands
main() {
    # Check if libraries exist
    if [[ ! -d "$LIB_DIR" ]]; then
        print_status $RED "❌ Library directory not found: $LIB_DIR"
        print_status $YELLOW "Please ensure all library files are in the lib/ directory"
        exit 1
    fi
    
    # Load libraries
    load_libraries
    
    # Check arguments
    if [[ $# -eq 0 ]]; then
        show_help
        exit 0
    fi
    
    local category=$1
    shift
    
    # Handle help requests
    if [[ "$category" == "help" ]] || [[ "$category" == "--help" ]] || [[ "$category" == "-h" ]]; then
        show_help
        exit 0
    fi
    
    # Check if command is provided
    if [[ $# -eq 0 ]]; then
        show_category_help "$category"
        exit 0
    fi
    
    local command=$1
    shift
    
    # Handle category-specific help
    if [[ "$command" == "help" ]]; then
        show_category_help "$category"
        exit 0
    fi
    
    # Route to appropriate function
    case "$category" in
        "keycloak")
            keycloak_main "$command" "$@"
            ;;
        "users")
            users_main "$command" "$@"
            ;;
        "testing")
            testing_main "$command" "$@"
            ;;
        "api")
            api_main "$command" "$@"
            ;;
        "media")
            media_main "$command" "$@"
            ;;
        "utils")
            utils_main "$command" "$@"
            ;;
        "admin")
            admin_main "$command" "$@"
            ;;
        *)
            print_status $RED "❌ Unknown category: $category"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# Initialize and run
print_header "YoteLoHago Development Tools"
main "$@"
