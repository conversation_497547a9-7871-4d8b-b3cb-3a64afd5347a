import React, { useState, useRef } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView, Alert, TextInput } from 'react-native';
import { getServices, getCategories, getConversations, getWebSocketUrl } from '../services';
import { ENV } from '../config/environment';
import { useAuth } from '../contexts/AuthContext';
import { useMessaging } from '../contexts/MessagingContext';
import { WebSocketService } from '../services/messaging/websocketService';

export default function DebugScreen() {
  const { state: authState } = useAuth();
  const { state: messagingState } = useMessaging();
  const [servicesResult, setServicesResult] = useState<string>('');
  const [categoriesResult, setCategoriesResult] = useState<string>('');
  const [messagingResult, setMessagingResult] = useState<string>('');
  const [wsResult, setWsResult] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [testBookingId, setTestBookingId] = useState<string>('1');
  const [testUserId, setTestUserId] = useState<string>('');
  const [testMessage, setTestMessage] = useState<string>('Hello from debug!');
  const wsRef = useRef<WebSocketService | null>(null);

  const testServices = async () => {
    setLoading(true);
    setServicesResult('Loading...');
    try {
      console.log('🧪 Testing services API...');
      console.log('🧪 API Base URL:', ENV.API_BASE_URL);
      
      const result = await getServices({ page: 0, size: 5 });
      console.log('🧪 Services result:', result);
      setServicesResult(JSON.stringify(result, null, 2));
    } catch (error: any) {
      console.error('🧪 Services error:', error);
      setServicesResult(`Error: ${error.message}\n\nDetails: ${JSON.stringify(error, null, 2)}`);
    } finally {
      setLoading(false);
    }
  };

  const testCategories = async () => {
    setLoading(true);
    setCategoriesResult('Loading...');
    try {
      console.log('🧪 Testing categories API...');
      const result = await getCategories();
      console.log('🧪 Categories result:', result);
      setCategoriesResult(JSON.stringify(result, null, 2));
    } catch (error: any) {
      console.error('🧪 Categories error:', error);
      setCategoriesResult(`Error: ${error.message}\n\nDetails: ${JSON.stringify(error, null, 2)}`);
    } finally {
      setLoading(false);
    }
  };

  const testMessaging = async () => {
    setLoading(true);
    setMessagingResult('Loading...');
    try {
      console.log('🧪 Testing messaging API...');
      console.log('🧪 Auth state:', authState.isAuthenticated);
      console.log('🧪 User:', authState.user);

      if (!authState.isAuthenticated) {
        setMessagingResult('Error: User not authenticated. Please log in first.');
        return;
      }

      // Test the API endpoint directly
      setMessagingResult('Testing API endpoint...\n');

      const result = await getConversations();
      console.log('🧪 Conversations result:', result);
      setMessagingResult(`✅ API Success!\n\nConversations found: ${result.length}\n\n${JSON.stringify(result, null, 2)}`);
    } catch (error: any) {
      console.error('🧪 Messaging error:', error);
      setMessagingResult(`❌ API Error: ${error.error || error.message}\n\nStatus: ${error.status}\nMessage: ${error.message}\n\nFull Error: ${JSON.stringify(error, null, 2)}`);
    } finally {
      setLoading(false);
    }
  };

  const testDirectMessagingAPI = async () => {
    setMessagingResult('Testing direct API call...\n');
    try {
      // Test direct API call with auth
      const response = await fetch(`${ENV.API_BASE_URL}/messaging/conversations`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${await import('../services/auth/tokenStorage').then(m => m.default.getAccessToken())}`
        }
      });

      const data = await response.text();
      setMessagingResult(`Direct API Response:\nStatus: ${response.status}\nHeaders: ${JSON.stringify(Object.fromEntries(response.headers.entries()), null, 2)}\nBody: ${data}`);
    } catch (error: any) {
      setMessagingResult(`Direct API Error: ${error.message}`);
    }
  };

  const testUserDebugInfo = async () => {
    setMessagingResult('Testing user debug info...\n');
    try {
      const tokenStorage = await import('../services/auth/tokenStorage');
      const token = await tokenStorage.default.getAccessToken();

      if (!token) {
        setMessagingResult('❌ No access token found. Please log in first.');
        return;
      }

      const response = await fetch(`${ENV.API_BASE_URL}/messaging/debug/user-info`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      const data = await response.text();
      setMessagingResult(`User Debug Info:\nStatus: ${response.status}\nToken: ${token.substring(0, 50)}...\nResponse: ${data}`);
    } catch (error: any) {
      setMessagingResult(`User Debug Error: ${error.message}`);
    }
  };

  const testWebSocket = async () => {
    setWsResult('Connecting...');
    try {
      console.log('🧪 Testing WebSocket connection...');

      const bookingId = parseInt(testBookingId, 10);
      if (isNaN(bookingId)) {
        setWsResult('Error: Invalid booking ID. Please enter a valid number.');
        return;
      }

      // Use custom user ID if provided, otherwise use authenticated user
      let userId = testUserId.trim();
      if (!userId) {
        if (!authState.isAuthenticated || !authState.user) {
          setWsResult('Error: No user ID provided and user not authenticated. Please enter a user ID or log in first.');
          return;
        }
        userId = authState.user.id; // Use 'id' which contains the Keycloak ID
      }

      // Disconnect existing connection
      if (wsRef.current) {
        wsRef.current.disconnect();
      }

      const wsUrl = getWebSocketUrl(bookingId, userId);
      setWsResult(`Connecting to: ${wsUrl}\nUser ID: ${userId}\nBooking ID: ${bookingId}\n\n`);

      const wsService = new WebSocketService({
        bookingId,
        userId: userId,
        onMessage: (message) => {
          setWsResult(prev => prev + `📥 Received: ${JSON.stringify(message, null, 2)}\n\n`);
        },
        onConnectionChange: (state) => {
          setWsResult(prev => prev + `🔗 Connection: ${JSON.stringify(state, null, 2)}\n\n`);
        },
        onError: (error) => {
          setWsResult(prev => prev + `❌ Error: ${error}\n\n`);
        },
        onTyping: (bookingId, userIds) => {
          setWsResult(prev => prev + `⌨️ Typing: Booking ${bookingId}, Users: ${userIds.join(', ')}\n\n`);
        }
      });

      wsRef.current = wsService;
      wsService.connect();

    } catch (error: any) {
      console.error('🧪 WebSocket error:', error);
      setWsResult(`Error: ${error.message}`);
    }
  };

  const sendTestMessage = () => {
    if (!wsRef.current || !wsRef.current.isConnected()) {
      setWsResult(prev => prev + '❌ WebSocket not connected\n\n');
      return;
    }

    // Use custom user ID if provided, otherwise use authenticated user
    let userId = testUserId.trim();
    if (!userId) {
      if (!authState.user) {
        setWsResult(prev => prev + '❌ No user ID provided and user not authenticated\n\n');
        return;
      }
      userId = authState.user.internalId; // Use internal user ID instead of Keycloak ID
    }

    try {
      // For testing, we'll send to ourselves (same user ID)
      wsRef.current.sendMessage(userId, testMessage);
      setWsResult(prev => prev + `📤 Sent: "${testMessage}" (from ${userId})\n\n`);
    } catch (error: any) {
      setWsResult(prev => prev + `❌ Send error: ${error.message}\n\n`);
    }
  };

  const disconnectWebSocket = () => {
    if (wsRef.current) {
      wsRef.current.disconnect();
      wsRef.current = null;
      setWsResult(prev => prev + '🔌 Disconnected\n\n');
    }
  };

  const testWebSocketUrl = () => {
    const bookingId = parseInt(testBookingId, 10);
    if (isNaN(bookingId)) {
      setWsResult('Error: Invalid booking ID. Please enter a valid number.');
      return;
    }

    let userId = testUserId.trim();
    if (!userId) {
      if (!authState.isAuthenticated || !authState.user) {
        setWsResult('Error: No user ID provided and user not authenticated.');
        return;
      }
      userId = authState.user.id; // Use 'id' which contains the Keycloak ID
    }

    const wsUrl = getWebSocketUrl(bookingId, userId);
    setWsResult(`🔗 WebSocket URL Test:\n\nURL: ${wsUrl}\nUser ID: ${userId}\nBooking ID: ${bookingId}\nEnvironment: ${process.env.NODE_ENV || 'development'}\nAPI Base: ${ENV.API_BASE_URL}\n\n`);
  };

  const testDirectAPI = async () => {
    try {
      console.log('🧪 Testing direct API call...');
      const response = await fetch(`${ENV.API_BASE_URL}/services?page=0&size=5`);
      console.log('🧪 Response status:', response.status);
      const data = await response.json();
      console.log('🧪 Direct API result:', data);
      Alert.alert('Direct API Test', `Status: ${response.status}\nData: ${JSON.stringify(data, null, 2)}`);
    } catch (error: any) {
      console.error('🧪 Direct API error:', error);
      Alert.alert('Direct API Error', error.message);
    }
  };

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>API Debug Test</Text>

      <View style={styles.infoSection}>
        <Text style={styles.infoTitle}>Environment Info:</Text>
        <Text style={styles.infoText}>API Base URL: {ENV.API_BASE_URL}</Text>
        <Text style={styles.infoText}>Environment: {__DEV__ ? 'Development' : 'Production'}</Text>
        <Text style={styles.infoText}>Auth Status: {authState.isAuthenticated ? '✅ Authenticated' : '❌ Not Authenticated'}</Text>
        <Text style={styles.infoText}>User ID: {authState.user?.id || 'N/A'}</Text>
        <Text style={styles.infoText}>Unread Messages: {messagingState.unreadCount}</Text>
        <Text style={styles.infoText}>WebSocket: {messagingState.websocket.isConnected ? '✅ Connected' : '❌ Disconnected'}</Text>
      </View>
      
      <View style={styles.section}>
        <TouchableOpacity 
          style={[styles.button, loading && styles.buttonDisabled]} 
          onPress={testDirectAPI}
          disabled={loading}
        >
          <Text style={styles.buttonText}>Test Direct API Call</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.section}>
        <TouchableOpacity 
          style={[styles.button, loading && styles.buttonDisabled]} 
          onPress={testServices}
          disabled={loading}
        >
          <Text style={styles.buttonText}>Test Services API</Text>
        </TouchableOpacity>
        
        <View style={styles.resultContainer}>
          <Text style={styles.resultTitle}>Services Result:</Text>
          <Text style={styles.resultText}>{servicesResult || 'No result yet'}</Text>
        </View>
      </View>

      <View style={styles.section}>
        <TouchableOpacity
          style={[styles.button, loading && styles.buttonDisabled]}
          onPress={testCategories}
          disabled={loading}
        >
          <Text style={styles.buttonText}>Test Categories API</Text>
        </TouchableOpacity>

        <View style={styles.resultContainer}>
          <Text style={styles.resultTitle}>Categories Result:</Text>
          <Text style={styles.resultText}>{categoriesResult || 'No result yet'}</Text>
        </View>
      </View>

      <View style={styles.section}>
        <TouchableOpacity
          style={[styles.button, loading && styles.buttonDisabled]}
          onPress={testMessaging}
          disabled={loading}
        >
          <Text style={styles.buttonText}>Test Messaging API</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, loading && styles.buttonDisabled]}
          onPress={testDirectMessagingAPI}
          disabled={loading}
        >
          <Text style={styles.buttonText}>Test Direct Messaging API</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, loading && styles.buttonDisabled]}
          onPress={testUserDebugInfo}
          disabled={loading}
        >
          <Text style={styles.buttonText}>Test User Debug Info</Text>
        </TouchableOpacity>

        <View style={styles.resultContainer}>
          <Text style={styles.resultTitle}>Messaging Result:</Text>
          <Text style={styles.resultText}>{messagingResult || 'No result yet'}</Text>
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>WebSocket Testing</Text>

        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>User ID (optional):</Text>
          <TextInput
            style={styles.input}
            value={testUserId}
            onChangeText={setTestUserId}
            placeholder="Enter user ID (leave empty to use authenticated user)"
          />
          <Text style={styles.inputHint}>
            Leave empty to use authenticated user: {authState.user?.id || 'Not authenticated'}
          </Text>
        </View>

        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>Booking ID:</Text>
          <TextInput
            style={styles.input}
            value={testBookingId}
            onChangeText={setTestBookingId}
            placeholder="Enter booking ID"
            keyboardType="numeric"
          />
        </View>

        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>Test Message:</Text>
          <TextInput
            style={styles.input}
            value={testMessage}
            onChangeText={setTestMessage}
            placeholder="Enter test message"
          />
        </View>

        <TouchableOpacity
          style={[styles.button, { marginBottom: 12 }]}
          onPress={testWebSocketUrl}
        >
          <Text style={styles.buttonText}>Test WebSocket URL</Text>
        </TouchableOpacity>

        <View style={styles.buttonRow}>
          <TouchableOpacity
            style={[styles.button, styles.buttonSmall]}
            onPress={testWebSocket}
          >
            <Text style={styles.buttonText}>Connect</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.button, styles.buttonSmall]}
            onPress={sendTestMessage}
          >
            <Text style={styles.buttonText}>Send</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.button, styles.buttonSmall, styles.buttonDanger]}
            onPress={disconnectWebSocket}
          >
            <Text style={styles.buttonText}>Disconnect</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.resultContainer}>
          <Text style={styles.resultTitle}>WebSocket Result:</Text>
          <Text style={styles.resultText}>{wsResult || 'No result yet'}</Text>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  infoSection: {
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 8,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 10,
  },
  infoText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
  },
  section: {
    marginBottom: 30,
  },
  button: {
    backgroundColor: '#007AFF',
    padding: 15,
    borderRadius: 8,
    marginBottom: 10,
  },
  buttonDisabled: {
    backgroundColor: '#ccc',
  },
  buttonText: {
    color: 'white',
    textAlign: 'center',
    fontSize: 16,
    fontWeight: '600',
  },
  resultContainer: {
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ddd',
    maxHeight: 300,
  },
  resultTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 10,
  },
  resultText: {
    fontSize: 12,
    fontFamily: 'monospace',
    color: '#333',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
  },
  inputContainer: {
    marginBottom: 12,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
    marginBottom: 4,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 14,
    backgroundColor: '#fff',
  },
  inputHint: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
    fontStyle: 'italic',
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  buttonSmall: {
    flex: 1,
    marginHorizontal: 4,
  },
  buttonDanger: {
    backgroundColor: '#dc3545',
  },
});
