import React, { useState, useMemo } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, StatusBar, Modal } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { Calendar, DateData } from 'react-native-calendars';
import Colors from '../constants/Colors';
import WeeklyScheduleEditor from '../components/WeeklyScheduleEditor';
import { useMyAvailability } from '../../../hooks/useAvailability';
import { WorkScheduleDTO, DayOfWeek } from '../../../types/api';
import { AuthGuard } from '../../../components/auth';

// Helper function to get the week containing a specific date
const getWeekDates = (dateString: string): string[] => {
  // Parse date string more reliably to avoid timezone issues
  const [year, month, day] = dateString.split('-').map(Number);
  const date = new Date(year, month - 1, day); // month is 0-indexed in Date constructor

  const dayOfWeek = date.getDay();
  const diff = date.getDate() - dayOfWeek + (dayOfWeek === 0 ? -6 : 1); // Adjust when day is Sunday
  const monday = new Date(date.setDate(diff));

  const week = [];
  for (let i = 0; i < 7; i++) {
    const weekDay = new Date(monday);
    weekDay.setDate(monday.getDate() + i);
    week.push(weekDay.toISOString().split('T')[0]);
  }
  return week;
};

// Helper function to get month name
const getMonthName = (dateString: string): string => {
  // Parse date string more reliably to avoid timezone issues
  const [year, month, day] = dateString.split('-').map(Number);
  const date = new Date(year, month - 1, day); // month is 0-indexed in Date constructor

  const monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];
  return monthNames[date.getMonth()];
};

export default function CalendarScreen() {
  const [selectedDate, setSelectedDate] = useState<string>('');
  const [isCalendarExpanded, setIsCalendarExpanded] = useState<boolean>(true);
  const [currentMonth, setCurrentMonth] = useState<string>(new Date().toISOString().split('T')[0]);
  const [currentWeekReference, setCurrentWeekReference] = useState<string>(new Date().toISOString().split('T')[0]);
  const [showAvailabilityEditor, setShowAvailabilityEditor] = useState<boolean>(false);
  const today = new Date().toISOString().split('T')[0];

  // Use availability hook to get current user's work schedule
  const { workSchedule, isLoading: isLoadingAvailability } = useMyAvailability();

  const handleDayPress = (day: DateData) => {
    setSelectedDate(day.dateString);
    setCurrentWeekReference(day.dateString);
  };

  const handleAvailabilityPress = () => {
    setShowAvailabilityEditor(true);
  };

  const handleAvailabilityEditorClose = () => {
    setShowAvailabilityEditor(false);
  };

  const handleAvailabilitySave = (updatedWorkSchedule: WorkScheduleDTO) => {
    console.log('✅ Work schedule saved:', updatedWorkSchedule);
    // The hook will automatically update the workSchedule state
  };

  // Get availability for a specific date
  const getAvailabilityForDate = (dateString: string) => {
    if (!workSchedule) return null;

    // Parse date string more reliably to avoid timezone issues
    // dateString format is "YYYY-MM-DD"
    const [year, month, day] = dateString.split('-').map(Number);
    const date = new Date(year, month - 1, day); // month is 0-indexed in Date constructor

    const dayOfWeek = ['SUNDAY', 'MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 'FRIDAY', 'SATURDAY'][date.getDay()];

    const daySchedule = workSchedule.daySchedules.find(
      schedule => schedule.dayOfWeek === dayOfWeek
    );

    return daySchedule;
  };

  // Format time ranges for display
  const formatTimeRanges = (timeRanges: any[]) => {
    if (!timeRanges || timeRanges.length === 0) return 'Not available';

    return timeRanges.map(range => {
      const formatTime = (timeString: string) => {
        const [hours, minutes] = timeString.split(':').map(Number);
        const period = hours >= 12 ? 'PM' : 'AM';
        const displayHours = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;
        return `${displayHours}:${minutes.toString().padStart(2, '0')} ${period}`;
      };

      return `${formatTime(range.startTime)} - ${formatTime(range.endTime)}`;
    }).join(', ');
  };

  const toggleCalendarView = () => {
    setIsCalendarExpanded(!isCalendarExpanded);
  };

  // Get current week dates for collapsed view
  const currentWeekDates = useMemo(() => {
    const referenceDate = currentWeekReference;
    return getWeekDates(referenceDate);
  }, [currentWeekReference]);

  // Calendar theme configuration for black and white design with proper spacing
  const calendarTheme = {
    backgroundColor: '#ffffff',
    calendarBackground: '#ffffff',
    textSectionTitleColor: '#6B7280',
    textSectionTitleDisabledColor: '#d9e1e8',
    selectedDayBackgroundColor: 'transparent',
    selectedDayTextColor: '#000000',
    todayTextColor: '#ffffff',
    todayBackgroundColor: '#000000',
    dayTextColor: '#000000',
    textDisabledColor: '#d9e1e8',
    dotColor: '#000000',
    selectedDotColor: '#ffffff',
    arrowColor: '#000000',
    disabledArrowColor: '#d9e1e8',
    monthTextColor: '#000000',
    indicatorColor: '#000000',
    textDayFontFamily: 'System',
    textMonthFontFamily: 'System',
    textDayHeaderFontFamily: 'System',
    textDayFontWeight: '500',
    textMonthFontWeight: '600',
    textDayHeaderFontWeight: '600',
    textDayFontSize: 16,
    textMonthFontSize: 18,
    textDayHeaderFontSize: 14,
    'stylesheet.calendar.header': {
      week: {
        marginTop: 5,
        marginHorizontal: 0,
        paddingHorizontal: 16,
        flexDirection: 'row',
        justifyContent: 'space-around',
      }
    },
    'stylesheet.calendar.main': {
      week: {
        marginHorizontal: 0,
        paddingHorizontal: 16,
        flexDirection: 'row',
        justifyContent: 'space-around',
      }
    },
    'stylesheet.day.basic': {
      base: {
        width: 36,
        height: 36,
        alignItems: 'center',
        justifyContent: 'center',
      },
      today: {
        backgroundColor: '#000000',
        borderRadius: 18,
        width: 36,
        height: 36,
      },
      todayText: {
        color: '#ffffff',
        fontWeight: 'bold',
      },
      selected: {
        backgroundColor: 'transparent',
        borderWidth: 2,
        borderColor: '#000000',
        borderRadius: 18,
        width: 36,
        height: 36,
      },
      selectedText: {
        color: '#000000',
        fontWeight: 'bold',
      },
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={Colors.white} />

      {/* Centered Header */}
      <View style={styles.centeredHeader}>
        <Text style={styles.centeredTitle}>Calendar</Text>
      </View>

      <AuthGuard
        actionType="general"
        promptTitle="Manage Your Availability"
        promptMessage="Log in as a professional to manage your calendar and set your availability."
        tabIcon="calendar-outline"
      >
        <View style={styles.content}>
        {/* Calendar component */}
        <View style={styles.calendarContainer}>
          {isCalendarExpanded ? (
            // Full calendar view
            <Calendar
              current={currentMonth}
              onDayPress={handleDayPress}
              markedDates={{
                ...(selectedDate && selectedDate !== today ? {
                  [selectedDate]: {
                    selected: true,
                    selectedColor: 'transparent',
                    selectedTextColor: '#000000',
                    customStyles: {
                      container: {
                        backgroundColor: 'transparent',
                        borderWidth: 2,
                        borderColor: '#000000',
                        borderRadius: 16,
                      },
                      text: {
                        color: '#000000',
                        fontWeight: 'bold',
                      },
                    },
                  }
                } : {}),
                [today]: {
                  selected: selectedDate === today,
                  selectedColor: '#000000',
                  selectedTextColor: '#ffffff',
                  customStyles: {
                    container: {
                      backgroundColor: '#000000',
                      borderRadius: 16,
                    },
                    text: {
                      color: '#ffffff',
                      fontWeight: 'bold',
                    },
                  },
                }
              }}
              theme={calendarTheme}
              firstDay={1} // Monday as first day
              showWeekNumbers={false}
              hideExtraDays={true}
              disableMonthChange={false}
              hideDayNames={false}
              showScrollIndicator={false}
              style={styles.calendar}
              renderArrow={(direction) => (
                <Ionicons
                  name={direction === 'left' ? 'chevron-back' : 'chevron-forward'}
                  size={24}
                  color={Colors.black}
                />
              )}
              monthFormat={'MMMM yyyy'}
              onMonthChange={(month) => {
                setCurrentMonth(month.dateString);
              }}
            />
          ) : (
            // Collapsed week view
            <View style={styles.weekViewContainer}>
              {/* Week header with month/year and navigation */}
              <View style={styles.weekHeader}>
                <TouchableOpacity
                  style={styles.weekNavButton}
                  onPress={() => {
                    const prevWeek = new Date(currentWeekDates[0]);
                    prevWeek.setDate(prevWeek.getDate() - 7);
                    const newWeekRef = prevWeek.toISOString().split('T')[0];
                    setCurrentWeekReference(newWeekRef);
                    setCurrentMonth(newWeekRef);
                  }}
                >
                  <Ionicons name="chevron-back" size={24} color={Colors.black} />
                </TouchableOpacity>

                <Text style={styles.weekHeaderText}>
                  {getMonthName(currentWeekDates[3])} {new Date(currentWeekDates[3]).getFullYear()}
                </Text>

                <TouchableOpacity
                  style={styles.weekNavButton}
                  onPress={() => {
                    const nextWeek = new Date(currentWeekDates[0]);
                    nextWeek.setDate(nextWeek.getDate() + 7);
                    const newWeekRef = nextWeek.toISOString().split('T')[0];
                    setCurrentWeekReference(newWeekRef);
                    setCurrentMonth(newWeekRef);
                  }}
                >
                  <Ionicons name="chevron-forward" size={24} color={Colors.black} />
                </TouchableOpacity>
              </View>

              {/* Day headers */}
              <View style={styles.weekDayHeaders}>
                {['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'].map((day) => (
                  <View key={day} style={styles.weekDayHeader}>
                    <Text style={styles.weekDayHeaderText}>{day}</Text>
                  </View>
                ))}
              </View>

              {/* Week days */}
              <View style={styles.weekDays}>
                {currentWeekDates.map((date) => {
                  const dayNumber = new Date(date).getDate();
                  const isToday = date === today;
                  const isSelected = date === selectedDate && date !== today;

                  return (
                    <TouchableOpacity
                      key={date}
                      style={[
                        styles.weekDay,
                        isToday && styles.weekDayToday,
                        isSelected && styles.weekDaySelected,
                      ]}
                      onPress={() => handleDayPress({ dateString: date } as DateData)}
                    >
                      <Text style={[
                        styles.weekDayText,
                        isToday && styles.weekDayTodayText,
                        isSelected && styles.weekDaySelectedText,
                      ]}>
                        {dayNumber}
                      </Text>
                    </TouchableOpacity>
                  );
                })}
              </View>
            </View>
          )}

          {/* Collapse/Expand toggle arrow positioned at bottom boundary */}
          <TouchableOpacity
            style={styles.toggleButton}
            onPress={toggleCalendarView}
            activeOpacity={0.7}
          >
            <View style={styles.toggleButtonCircle}>
              <Ionicons
                name={isCalendarExpanded ? 'chevron-up' : 'chevron-down'}
                size={16}
                color={Colors.gray}
              />
            </View>
          </TouchableOpacity>
        </View>

        {/* Selected date section */}
        {selectedDate && (
          <View style={styles.selectedDateContainer}>
            <Text style={styles.selectedDateLabel}>Selected Date</Text>
            <Text style={styles.selectedDateText}>
              {(() => {
                // Parse date string more reliably to avoid timezone issues
                const [year, month, day] = selectedDate.split('-').map(Number);
                const date = new Date(year, month - 1, day); // month is 0-indexed
                return date.toLocaleDateString('en-US', {
                  weekday: 'long',
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                });
              })()}
            </Text>
          </View>
        )}

        {/* Available time ranges section */}
        {selectedDate && (
          <View style={styles.timeRangesContainer}>
            <Text style={styles.timeRangesLabel}>Available Time Ranges</Text>
            {(() => {
              const dayAvailability = getAvailabilityForDate(selectedDate);
              const isAvailable = dayAvailability?.isActive && dayAvailability?.timeRanges.length > 0;

              if (!isAvailable) {
                return (
                  <View style={styles.noAvailabilityContainer}>
                    <Text style={styles.noAvailabilityText}>
                      No availability set for this date
                    </Text>
                    <Text style={styles.noAvailabilitySubtext}>
                      Use the Availability button to set your working hours
                    </Text>
                  </View>
                );
              }

              return (
                <View style={styles.timeRangesList}>
                  {dayAvailability.timeRanges.map((timeRange, index) => (
                    <View key={index} style={styles.timeRangeItem}>
                      <Text style={styles.timeRangeText}>
                        {timeRange.startTime} - {timeRange.endTime}
                      </Text>
                    </View>
                  ))}
                </View>
              );
            })()}
          </View>
        )}
        </View>

        {/* Floating Availability Button */}
        <TouchableOpacity style={styles.floatingAvailabilityButton} onPress={handleAvailabilityPress}>
          <Text style={styles.floatingAvailabilityButtonText}>Availability</Text>
        </TouchableOpacity>
      </AuthGuard>

      {/* Availability Editor Modal */}
      <Modal
        visible={showAvailabilityEditor}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={handleAvailabilityEditorClose}
      >
        <WeeklyScheduleEditor
          onClose={handleAvailabilityEditorClose}
          onSave={handleAvailabilitySave}
        />
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.secondary,
  },
  centeredHeader: {
    backgroundColor: Colors.background.primary,
    paddingTop: 60,
    paddingBottom: 16,
    paddingHorizontal: 16,
    alignItems: 'center',
  },
  centeredTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: Colors.text.primary,
    textAlign: 'center',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    backgroundColor: Colors.white,
    borderBottomWidth: 1,
    borderBottomColor: Colors.lightGray,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.black,
  },
  availabilityButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderWidth: 1,
    borderColor: Colors.black,
    borderRadius: 20,
    backgroundColor: Colors.white,
  },
  availabilityButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.black,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  calendarContainer: {
    backgroundColor: Colors.white,
    borderRadius: 12,
    marginBottom: 40, // Increased to accommodate floating button with proper spacing
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    position: 'relative',
  },
  calendar: {
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    overflow: 'hidden',
    paddingBottom: 20, // Add space between calendar content and toggle button
  },
  weekViewContainer: {
    paddingHorizontal: 12,
    paddingTop: 16,
    paddingBottom: 36, // Add extra space between week view and toggle button
  },
  weekHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    paddingHorizontal: 8,
  },
  weekNavButton: {
    padding: 4,
  },
  weekHeaderText: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.black,
  },
  weekDayHeaders: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 8,
    paddingHorizontal: 16,
  },
  weekDayHeader: {
    width: 36,
    alignItems: 'center',
  },
  weekDayHeaderText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.gray,
  },
  weekDays: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 16,
  },
  weekDay: {
    width: 36,
    height: 36,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
  },
  weekDayToday: {
    backgroundColor: Colors.black,
  },
  weekDaySelected: {
    backgroundColor: 'transparent',
    borderWidth: 2,
    borderColor: Colors.black,
  },
  weekDayText: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.black,
  },
  weekDayTodayText: {
    color: Colors.white,
    fontWeight: 'bold',
  },
  weekDaySelectedText: {
    color: Colors.black,
    fontWeight: 'bold',
  },
  toggleButton: {
    position: 'absolute',
    bottom: -16, // Adjusted to account for added padding and maintain half inside/outside effect
    left: '50%',
    marginLeft: -16, // Half of the button width (32px / 2 = 16px) to center horizontally
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 10,
  },
  toggleButtonCircle: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.white,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 4,
    borderWidth: 1,
    borderColor: '#E5E5E5',
  },
  selectedDateContainer: {
    backgroundColor: Colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  selectedDateLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.gray,
    marginBottom: 4,
  },
  selectedDateText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.black,
  },
  timeRangesContainer: {
    backgroundColor: Colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  timeRangesLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.gray,
    marginBottom: 12,
  },
  timeRangesList: {
    gap: 8,
  },
  timeRangeItem: {
    backgroundColor: '#E8F5E8',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    alignSelf: 'flex-start',
  },
  timeRangeText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#000000',
  },
  noAvailabilityContainer: {
    alignItems: 'center',
    paddingVertical: 16,
  },
  noAvailabilityText: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.gray,
    marginBottom: 4,
  },
  noAvailabilitySubtext: {
    fontSize: 12,
    color: Colors.gray,
    textAlign: 'center',
  },
  // Legacy styles (keeping for backward compatibility)
  availabilityStatusText: {
    fontSize: 14,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    alignSelf: 'flex-start',
  },
  availableText: {
    color: '#000000',
    backgroundColor: '#E8F5E8',
  },
  unavailableText: {
    color: '#666666',
    backgroundColor: '#F5F5F5',
  },
  floatingAvailabilityButton: {
    position: 'absolute',
    bottom: 20, // Same distance as right margin
    right: 20,
    backgroundColor: '#000000', // Black for professional mode
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 25,
    elevation: 5, // Android shadow
    shadowColor: '#000', // iOS shadow
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  floatingAvailabilityButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
});
