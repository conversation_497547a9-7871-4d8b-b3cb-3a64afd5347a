import React, { useState, useEffect, useRef } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image, TextInput, FlatList, KeyboardAvoidingView, Platform, ActivityIndicator, Alert } from 'react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import Colors from '../constants/Colors';
import { useMessaging } from '../../../contexts/MessagingContext';
import { useAuth } from '../../../contexts/AuthContext';
import { MessageDTO } from '../../../types/api';
import { UserMode } from '../../../types/auth';
import { formatDistanceToNow } from '../../../utils/dateUtils';

export default function ChatScreen() {
  const router = useRouter();
  const { id } = useLocalSearchParams();
  const { state: authState } = useAuth();
  const {
    state: messagingState,
    loadConversations,
    loadMessages,
    sendMessage: sendMessageToBackend,
    connectToConversation,
    disconnectFromConversation,
    markConversationRead,
    sendTyping
  } = useMessaging();

  const [message, setMessage] = useState('');
  const [isSending, setIsSending] = useState(false);
  const [typingTimer, setTypingTimer] = useState<NodeJS.Timeout | null>(null);
  const flatListRef = useRef<FlatList>(null);

  const bookingId = parseInt(id as string, 10);
  const currentConversation = messagingState.currentConversation;
  const conversationFromList = messagingState.conversations.find(conv => conv.bookingId === bookingId);
  const messages = currentConversation.messages;
  const isLoading = currentConversation.isLoading;
  const error = currentConversation.error;

  // Determine receiver ID based on current user mode and conversation participants
  const getReceiverId = (): string | null => {
    if (!conversationFromList) return null;

    // In professional mode, send to client; in client mode, send to professional
    return authState.currentMode === UserMode.PROFESSIONAL
      ? conversationFromList.clientId
      : conversationFromList.professionalId;
  };

  const receiverId = getReceiverId();

  // Debug logging for conversation state
  console.log('🔍 Professional chat component state:', {
    bookingId,
    conversationsCount: messagingState.conversations.length,
    hasCurrentConversation: !!currentConversation.bookingId,
    currentConversationBookingId: currentConversation.bookingId,
    currentMode: authState.currentMode,
    receiverId,
    conversationFromList: conversationFromList ? {
      clientId: conversationFromList.clientId,
      professionalId: conversationFromList.professionalId
    } : null,
    messagesCount: messages.length,
    isLoading,
    error
  });

  // Load conversation data and connect to WebSocket when component mounts
  useEffect(() => {
    if (!authState.isAuthenticated || isNaN(bookingId)) {
      router.back();
      return;
    }

    console.log('🔄 Loading conversation data for booking (Professional):', bookingId);

    // Load both conversations (for participant info) and messages
    const loadConversationData = async () => {
      try {
        // Load conversations first to get participant information
        await loadConversations();
        console.log('✅ Conversations loaded (Professional)');

        // Then load messages for this specific booking
        await loadMessages(bookingId);
        console.log('✅ Messages loaded for booking (Professional):', bookingId);

        // Connect to WebSocket
        connectToConversation(bookingId);
        console.log('✅ WebSocket connected for booking (Professional):', bookingId);

        // Mark conversation as read when entering
        markConversationRead(bookingId).catch(console.error);
      } catch (error) {
        console.error('❌ Failed to load conversation data (Professional):', error);
      }
    };

    loadConversationData();

    // Cleanup on unmount
    return () => {
      disconnectFromConversation();
    };
  }, [bookingId, authState.isAuthenticated]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (messages.length > 0) {
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 100);
    }
  }, [messages.length]);

  // Handle typing indicators
  const handleTextChange = (text: string) => {
    setMessage(text);

    // Clear existing timer
    if (typingTimer) {
      clearTimeout(typingTimer);
    }

    // Send typing indicator
    if (text.length > 0) {
      sendTyping(true);

      // Stop typing after 2 seconds of inactivity
      const timer = setTimeout(() => {
        sendTyping(false);
      }, 2000);
      setTypingTimer(timer);
    } else {
      sendTyping(false);
    }
  };

  const renderMessage = ({ item }: { item: MessageDTO }) => {
    const isCurrentUser = item.senderId === authState.user?.internalId; // Use internal user ID for comparison
    const timestamp = formatDistanceToNow(new Date(item.sentAt));

    return (
      <View style={[
        styles.messageContainer,
        isCurrentUser ? styles.currentUserMessage : styles.otherUserMessage
      ]}>
        <View style={[
          styles.messageBubble,
          isCurrentUser ? styles.currentUserBubble : styles.otherUserBubble
        ]}>
          <Text style={[
            styles.messageText,
            isCurrentUser ? styles.currentUserText : styles.otherUserText
          ]}>
            {item.content}
          </Text>
          <View style={styles.messageFooter}>
            <Text style={[
              styles.timestamp,
              isCurrentUser ? styles.currentUserTimestamp : styles.otherUserTimestamp
            ]}>
              {timestamp}
            </Text>
            {isCurrentUser && (
              <Ionicons
                name={item.isRead ? "checkmark-done" : "checkmark"}
                size={14}
                color={item.isRead ? Colors.success : Colors.text.secondary}
                style={styles.readIndicator}
              />
            )}
          </View>
        </View>
      </View>
    );
  };

  const handleSend = async () => {
    console.log('🎯 handleSend called (Professional)');
    console.log('📝 Current state:', {
      messageLength: message.trim().length,
      isSending,
      hasConversation: !!currentConversation.bookingId,
      bookingId,
      receiverId,
      currentMode: authState.currentMode
    });

    if (!message.trim() || isSending || !currentConversation.bookingId || !receiverId) {
      console.log('❌ Send blocked:', {
        noMessage: !message.trim(),
        isSending,
        noConversation: !currentConversation.bookingId,
        noReceiver: !receiverId
      });
      return;
    }

    const messageText = message.trim();
    setMessage('');
    setIsSending(true);

    console.log('📤 Attempting to send message:', messageText);

    // Stop typing indicator
    sendTyping(false);
    if (typingTimer) {
      clearTimeout(typingTimer);
      setTypingTimer(null);
    }

    try {
      console.log('🔄 Calling sendMessageToBackend...');
      await sendMessageToBackend(
        bookingId,
        receiverId,
        messageText
      );
      console.log('✅ Message sent successfully');
    } catch (error) {
      console.error('❌ Failed to send message:', error);
      Alert.alert(
        'Failed to Send',
        'Your message could not be sent. Please try again.',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Retry', onPress: () => setMessage(messageText) }
        ]
      );
    } finally {
      setIsSending(false);
      console.log('🏁 handleSend completed (Professional)');
    }
  };

  // Show loading state
  if (isLoading && messages.length === 0) {
    return (
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <Ionicons name="arrow-back" size={24} color={Colors.text.primary} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Loading...</Text>
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors.primary} />
          <Text style={styles.loadingText}>Loading conversation...</Text>
        </View>
      </View>
    );
  }

  // Show error state
  if (error) {
    return (
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <Ionicons name="arrow-back" size={24} color={Colors.text.primary} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Error</Text>
        </View>
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle" size={48} color={Colors.error} />
          <Text style={styles.errorTitle}>Failed to load conversation</Text>
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity
            style={styles.retryButton}
            onPress={() => loadMessages(bookingId)}
          >
            <Text style={styles.retryButtonText}>Try Again</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
    >
      {/* Fixed Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Ionicons name="arrow-back" size={24} color={Colors.text.primary} />
        </TouchableOpacity>
        <View style={styles.avatar}>
          <Ionicons
            name="person-circle"
            size={40}
            color={Colors.text.secondary}
          />
        </View>
        <View style={styles.providerInfo}>
          <Text style={styles.providerName}>
            {conversationFromList
              ? (authState.currentMode === UserMode.PROFESSIONAL ? conversationFromList.clientName : conversationFromList.professionalName)
              : 'Client'
            }
          </Text>
          <View style={styles.statusContainer}>
            <View style={[
              styles.statusDot,
              { backgroundColor: messagingState.websocket.isConnected ? Colors.success : Colors.text.secondary }
            ]} />
            <Text style={styles.providerStatus}>
              {messagingState.websocket.isConnected ? 'Connected' : 'Offline'}
            </Text>
          </View>
        </View>
      </View>

      {/* Messages List */}
      <FlatList
        ref={flatListRef}
        data={messages}
        renderItem={renderMessage}
        keyExtractor={(item) => item.id?.toString() || Math.random().toString()}
        contentContainerStyle={styles.messagesList}
        inverted={false}
        showsVerticalScrollIndicator={false}
      />

      {/* Typing Indicator */}
      {messagingState.typingUsers[bookingId]?.length > 0 && (
        <View style={styles.typingContainer}>
          <Text style={styles.typingText}>
            {conversationFromList
              ? (authState.currentMode === UserMode.PROFESSIONAL ? conversationFromList.clientName : conversationFromList.professionalName)
              : 'Client'
            } is typing...
          </Text>
        </View>
      )}

      {/* Input Bar */}
      <View style={styles.inputContainer}>
        <TouchableOpacity style={styles.attachButton}>
          <Ionicons name="add-circle-outline" size={24} color={Colors.primary} />
        </TouchableOpacity>
        <TextInput
          style={styles.input}
          placeholder="Type a message..."
          placeholderTextColor={Colors.text.secondary}
          value={message}
          onChangeText={handleTextChange}
          multiline
          maxLength={1000}
        />
        <TouchableOpacity
          style={[styles.sendButton, isSending && styles.sendButtonDisabled]}
          onPress={handleSend}
          disabled={isSending || !message.trim()}
        >
          {isSending ? (
            <ActivityIndicator size="small" color={Colors.primary} />
          ) : (
            <Ionicons
              name="send"
              size={24}
              color={message.trim() ? Colors.primary : Colors.text.secondary}
            />
          )}
        </TouchableOpacity>
      </View>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.primary,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 60,
    paddingBottom: 16,
    paddingHorizontal: 16,
    backgroundColor: Colors.background.primary,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  backButton: {
    marginRight: 16,
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  providerInfo: {
    flex: 1,
  },
  providerName: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text.primary,
  },
  providerStatus: {
    fontSize: 14,
    color: Colors.text.secondary,
  },
  messagesList: {
    padding: 16,
  },
  messageContainer: {
    marginBottom: 16,
    maxWidth: '80%',
  },
  currentUserMessage: {
    alignSelf: 'flex-end',
  },
  otherUserMessage: {
    alignSelf: 'flex-start',
  },
  messageBubble: {
    padding: 12,
    borderRadius: 16,
  },
  currentUserBubble: {
    backgroundColor: Colors.primary,
  },
  otherUserBubble: {
    backgroundColor: Colors.background.secondary,
  },
  messageText: {
    fontSize: 16,
    marginBottom: 4,
  },
  currentUserText: {
    color: Colors.text.light,
  },
  otherUserText: {
    color: Colors.text.primary,
  },
  timestamp: {
    fontSize: 12,
    color: Colors.text.secondary,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
    backgroundColor: Colors.background.primary,
  },
  attachButton: {
    padding: 8,
  },
  input: {
    flex: 1,
    marginHorizontal: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    backgroundColor: Colors.background.secondary,
    borderRadius: 20,
    fontSize: 16,
    color: Colors.text.primary,
    maxHeight: 100,
  },
  sendButton: {
    padding: 8,
  },
  sendButtonDisabled: {
    opacity: 0.5,
  },
  messageFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 4,
  },
  currentUserTimestamp: {
    color: Colors.text.light + '80',
  },
  otherUserTimestamp: {
    color: Colors.text.secondary,
  },
  readIndicator: {
    marginLeft: 4,
  },
  typingContainer: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: Colors.background.secondary,
  },
  typingText: {
    fontSize: 14,
    color: Colors.text.secondary,
    fontStyle: 'italic',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  loadingText: {
    fontSize: 16,
    color: Colors.text.secondary,
    marginTop: 12,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  errorTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text.primary,
    marginTop: 16,
    marginBottom: 8,
  },
  errorText: {
    fontSize: 16,
    color: Colors.text.secondary,
    textAlign: 'center',
    marginBottom: 24,
  },
  retryButton: {
    backgroundColor: Colors.primary,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: Colors.text.light,
    fontSize: 16,
    fontWeight: '600',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text.primary,
    flex: 1,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
});