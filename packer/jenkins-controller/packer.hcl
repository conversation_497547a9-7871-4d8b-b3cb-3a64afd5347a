packer {
  required_plugins {
    docker = {
      version = ">= 1.0.0"
      source  = "github.com/hashicorp/docker"
    }
    ansible = {
      version = ">= 1.0.0"
      source  = "github.com/hashicorp/ansible"
    }
  }
}

source "docker" "jenkins_controller" {
  image  = "jenkins/jenkins:lts-jdk17"
  commit = true
}

build {
  name    = "jenkins-controller-image"
  sources = ["source.docker.jenkins_controller"]

  provisioner "ansible" {
    playbook_file = "playbook.yml"
  }
}
