package com.yotelohago.service.infrastructure;

import com.yotelohago.common.dto.PageRequest;
import com.yotelohago.common.dto.PageResponse;
import com.yotelohago.service.domain.Service;
import com.yotelohago.service.domain.ServiceRepository;
import com.yotelohago.service.domain.ServiceStatus;
import com.yotelohago.service.api.ServiceWithProfessionalDTO;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.*;
import org.eclipse.microprofile.config.inject.ConfigProperty;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@ApplicationScoped
public class ServiceRepositoryPostgres implements ServiceRepository {

    @PersistenceContext
    EntityManager entityManager;

    @ConfigProperty(name = "media.url-resolver")
    String mediaBaseUrl;

    @Override
    public void persist(Service service) {
        entityManager.persist(service);
    }

    @Override
    public Optional<Service> findById(UUID id) {
        Service service = entityManager.find(Service.class, id);
        return Optional.ofNullable(service);
    }

    @Override
    public PageResponse<Service> findAllPublishedPaginated(PageRequest pageRequest) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();

        // Query for data
        CriteriaQuery<Service> query = cb.createQuery(Service.class);
        Root<Service> root = query.from(Service.class);
        query.select(root);
        query.where(cb.equal(root.get("status"), ServiceStatus.PUBLISHED));

        // Add sorting if specified
        if (pageRequest.getSort() != null && !pageRequest.getSort().trim().isEmpty()) {
            addSorting(query, cb, root, pageRequest);
        }

        TypedQuery<Service> typedQuery = entityManager.createQuery(query);
        typedQuery.setFirstResult(pageRequest.getOffset());
        typedQuery.setMaxResults(pageRequest.getSize());

        List<Service> content = typedQuery.getResultList();

        // Query for total count
        long totalElements = countPublished();

        return PageResponse.of(content, totalElements, pageRequest.getPage(), pageRequest.getSize());
    }

    @Override
    public PageResponse<Service> findByCategoryPublishedPaginated(UUID categoryId, PageRequest pageRequest) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();

        // Query for data
        CriteriaQuery<Service> query = cb.createQuery(Service.class);
        Root<Service> root = query.from(Service.class);
        query.select(root);

        // Filter by category and published status
        Predicate categoryPredicate = cb.equal(root.get("category").get("id"), categoryId);
        Predicate statusPredicate = cb.equal(root.get("status"), ServiceStatus.PUBLISHED);
        query.where(cb.and(categoryPredicate, statusPredicate));

        // Add sorting if specified
        if (pageRequest.getSort() != null && !pageRequest.getSort().trim().isEmpty()) {
            addSorting(query, cb, root, pageRequest);
        }

        TypedQuery<Service> typedQuery = entityManager.createQuery(query);
        typedQuery.setFirstResult(pageRequest.getOffset());
        typedQuery.setMaxResults(pageRequest.getSize());

        List<Service> content = typedQuery.getResultList();

        // Query for total count
        long totalElements = countPublishedByCategory(categoryId);

        return PageResponse.of(content, totalElements, pageRequest.getPage(), pageRequest.getSize());
    }





    @Override
    public void deleteById(UUID id) {
        Service service = entityManager.find(Service.class, id);
        if (service != null) {
            entityManager.remove(service);
        }
    }

    @Override
    public Service update(Service service) {
        return entityManager.merge(service);
    }

    @Override
    public List<Service> findAllByProfessional(UUID professionalUserId) {
        return entityManager.createQuery("FROM Service WHERE professionalUser.id = :professionalId ORDER BY createdAt DESC", Service.class)
                .setParameter("professionalId", professionalUserId)
                .getResultList();
    }





    @Override
    public PageResponse<Service> searchPublishedPaginated(UUID categoryId, Double minPrice, Double maxPrice,
                                                        String searchTerm, PageRequest pageRequest) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();

        // Query for data
        CriteriaQuery<Service> query = cb.createQuery(Service.class);
        Root<Service> root = query.from(Service.class);
        query.select(root);

        // Build dynamic where clause including published status
        List<Predicate> predicates = new ArrayList<>();

        // Always filter by published status for public endpoints
        predicates.add(cb.equal(root.get("status"), ServiceStatus.PUBLISHED));

        if (categoryId != null) {
            predicates.add(cb.equal(root.get("category").get("id"), categoryId));
        }

        if (minPrice != null) {
            predicates.add(cb.greaterThanOrEqualTo(root.get("price"), minPrice));
        }

        if (maxPrice != null) {
            predicates.add(cb.lessThanOrEqualTo(root.get("price"), maxPrice));
        }

        if (searchTerm != null && !searchTerm.trim().isEmpty()) {
            String searchPattern = "%" + searchTerm.toLowerCase() + "%";
            Predicate titleMatch = cb.like(cb.lower(root.get("title")), searchPattern);
            Predicate descriptionMatch = cb.like(cb.lower(root.get("description")), searchPattern);
            predicates.add(cb.or(titleMatch, descriptionMatch));
        }

        query.where(cb.and(predicates.toArray(new Predicate[0])));

        // Add sorting if specified
        if (pageRequest.getSort() != null && !pageRequest.getSort().trim().isEmpty()) {
            addSorting(query, cb, root, pageRequest);
        }

        TypedQuery<Service> typedQuery = entityManager.createQuery(query);
        typedQuery.setFirstResult(pageRequest.getOffset());
        typedQuery.setMaxResults(pageRequest.getSize());

        List<Service> content = typedQuery.getResultList();

        // Query for total count with same filters including published status
        long totalElements = countPublishedWithFilters(categoryId, minPrice, maxPrice, searchTerm);

        return PageResponse.of(content, totalElements, pageRequest.getPage(), pageRequest.getSize());
    }

    @Override
    public void flush() {
        entityManager.flush();
    }

    // =====================================================
    // NEW: Methods with Professional Data Joins (Option 2)
    // =====================================================

    @Override
    public PageResponse<ServiceWithProfessionalDTO> findAllPublishedWithProfessionalPaginated(PageRequest pageRequest) {
        // Create the join query using JPQL with Media entity for profile image URL
        String jpql = String.format("""
            SELECT new com.yotelohago.service.api.ServiceWithProfessionalDTO(
                s.id, s.professionalUser.id, s.title, s.description, s.price,
                s.category.id, s.category.name, CAST(s.status AS string), s.createdAt, s.updatedAt,
                u.firstName, u.lastName, p.city, p.avgRating, p.ratingCount,
                p.available, p.bio,
                (SELECT CASE
                    WHEN m.filePath IS NOT NULL
                    THEN CONCAT('%s', m.filePath)
                    ELSE NULL
                 END
                 FROM Media m
                 WHERE m.user.id = u.id AND m.mediaType = 'PROFESSIONAL_PROFILE_PICTURE'
                 ORDER BY m.createdAt DESC LIMIT 1)
            )
            FROM Service s
            JOIN Professional p ON s.professionalUser.id = p.user.id
            JOIN User u ON p.user.id = u.id
            WHERE s.status = :status
            ORDER BY s.createdAt DESC
            """, mediaBaseUrl);

        TypedQuery<ServiceWithProfessionalDTO> query = entityManager.createQuery(jpql, ServiceWithProfessionalDTO.class);
        query.setParameter("status", ServiceStatus.PUBLISHED);
        query.setFirstResult(pageRequest.getOffset());
        query.setMaxResults(pageRequest.getSize());

        List<ServiceWithProfessionalDTO> content = query.getResultList();

        // Count query
        long totalElements = countPublished();

        return PageResponse.of(content, totalElements, pageRequest.getPage(), pageRequest.getSize());
    }

    @Override
    public PageResponse<ServiceWithProfessionalDTO> findByCategoryPublishedWithProfessionalPaginated(UUID categoryId, PageRequest pageRequest) {
        String jpql = String.format("""
            SELECT new com.yotelohago.service.api.ServiceWithProfessionalDTO(
                s.id, s.professionalUser.id, s.title, s.description, s.price,
                s.category.id, s.category.name, CAST(s.status AS string), s.createdAt, s.updatedAt,
                u.firstName, u.lastName, p.city, p.avgRating, p.ratingCount,
                p.available, p.bio,
                (SELECT CASE
                    WHEN m.filePath IS NOT NULL
                    THEN CONCAT('%s', m.filePath)
                    ELSE NULL
                 END
                 FROM Media m
                 WHERE m.user.id = u.id AND m.mediaType = 'PROFESSIONAL_PROFILE_PICTURE'
                 ORDER BY m.createdAt DESC LIMIT 1)
            )
            FROM Service s
            JOIN Professional p ON s.professionalUser.id = p.user.id
            JOIN User u ON p.user.id = u.id
            WHERE s.status = :status AND s.category.id = :categoryId
            ORDER BY s.createdAt DESC
            """, mediaBaseUrl);

        TypedQuery<ServiceWithProfessionalDTO> query = entityManager.createQuery(jpql, ServiceWithProfessionalDTO.class);
        query.setParameter("status", ServiceStatus.PUBLISHED);
        query.setParameter("categoryId", categoryId);
        query.setFirstResult(pageRequest.getOffset());
        query.setMaxResults(pageRequest.getSize());

        List<ServiceWithProfessionalDTO> content = query.getResultList();

        // Count query
        long totalElements = countPublishedByCategory(categoryId);

        return PageResponse.of(content, totalElements, pageRequest.getPage(), pageRequest.getSize());
    }

    @Override
    public PageResponse<ServiceWithProfessionalDTO> searchPublishedWithProfessionalPaginated(
            UUID categoryId, Double minPrice, Double maxPrice, String searchTerm, PageRequest pageRequest) {

        StringBuilder jpqlBuilder = new StringBuilder();
        jpqlBuilder.append(String.format("""
            SELECT new com.yotelohago.service.api.ServiceWithProfessionalDTO(
                s.id, s.professionalUser.id, s.title, s.description, s.price,
                s.category.id, s.category.name, CAST(s.status AS string), s.createdAt, s.updatedAt,
                u.firstName, u.lastName, p.city, p.avgRating, p.ratingCount,
                p.available, p.bio,
                (SELECT CASE
                    WHEN m.filePath IS NOT NULL
                    THEN CONCAT('%s', m.filePath)
                    ELSE NULL
                 END
                 FROM Media m
                 WHERE m.user.id = u.id AND m.mediaType = 'PROFESSIONAL_PROFILE_PICTURE'
                 ORDER BY m.createdAt DESC LIMIT 1)
            )
            FROM Service s
            JOIN Professional p ON s.professionalUser.id = p.user.id
            JOIN User u ON p.user.id = u.id
            WHERE s.status = :status
            """, mediaBaseUrl));

        // Build dynamic WHERE conditions
        if (categoryId != null) {
            jpqlBuilder.append(" AND s.category.id = :categoryId");
        }
        if (minPrice != null) {
            jpqlBuilder.append(" AND s.price >= :minPrice");
        }
        if (maxPrice != null) {
            jpqlBuilder.append(" AND s.price <= :maxPrice");
        }
        if (searchTerm != null && !searchTerm.trim().isEmpty()) {
            jpqlBuilder.append(" AND (LOWER(s.title) LIKE :searchTerm OR LOWER(s.description) LIKE :searchTerm)");
        }

        jpqlBuilder.append(" ORDER BY s.createdAt DESC");

        TypedQuery<ServiceWithProfessionalDTO> query = entityManager.createQuery(jpqlBuilder.toString(), ServiceWithProfessionalDTO.class);

        // Set parameters
        query.setParameter("status", ServiceStatus.PUBLISHED);
        if (categoryId != null) {
            query.setParameter("categoryId", categoryId);
        }
        if (minPrice != null) {
            query.setParameter("minPrice", minPrice);
        }
        if (maxPrice != null) {
            query.setParameter("maxPrice", maxPrice);
        }
        if (searchTerm != null && !searchTerm.trim().isEmpty()) {
            query.setParameter("searchTerm", "%" + searchTerm.toLowerCase() + "%");
        }

        query.setFirstResult(pageRequest.getOffset());
        query.setMaxResults(pageRequest.getSize());

        List<ServiceWithProfessionalDTO> content = query.getResultList();

        // Count query with same filters
        long totalElements = countPublishedWithFilters(categoryId, minPrice, maxPrice, searchTerm);

        return PageResponse.of(content, totalElements, pageRequest.getPage(), pageRequest.getSize());
    }

    // Helper methods for sorting and counting
    private void addSorting(CriteriaQuery<Service> query, CriteriaBuilder cb, Root<Service> root, PageRequest pageRequest) {
        String sortField = pageRequest.getSort();

        // Validate sort field to prevent injection
        if (!isValidSortField(sortField)) {
            sortField = "title"; // Default sort field
        }

        Path<?> sortPath;
        if ("categoryName".equals(sortField)) {
            sortPath = root.get("category").get("name");
        } else {
            sortPath = root.get(sortField);
        }

        if (pageRequest.isDescending()) {
            query.orderBy(cb.desc(sortPath));
        } else {
            query.orderBy(cb.asc(sortPath));
        }
    }

    private boolean isValidSortField(String field) {
        return field != null && (
            "title".equals(field) ||
            "price".equals(field) ||
            "categoryName".equals(field) ||
            "professionalUser".equals(field)
        );
    }



    private long countPublished() {
        return entityManager.createQuery("SELECT COUNT(s) FROM Service s WHERE s.status = :status", Long.class)
                .setParameter("status", ServiceStatus.PUBLISHED)
                .getSingleResult();
    }

    private long countPublishedByCategory(UUID categoryId) {
        return entityManager.createQuery("SELECT COUNT(s) FROM Service s WHERE s.category.id = :categoryId AND s.status = :status", Long.class)
                .setParameter("categoryId", categoryId)
                .setParameter("status", ServiceStatus.PUBLISHED)
                .getSingleResult();
    }



    private long countPublishedWithFilters(UUID categoryId, Double minPrice, Double maxPrice, String searchTerm) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<Long> query = cb.createQuery(Long.class);
        Root<Service> root = query.from(Service.class);
        query.select(cb.count(root));

        // Build same filters as search query including published status
        List<Predicate> predicates = new ArrayList<>();

        // Always filter by published status
        predicates.add(cb.equal(root.get("status"), ServiceStatus.PUBLISHED));

        if (categoryId != null) {
            predicates.add(cb.equal(root.get("category").get("id"), categoryId));
        }

        if (minPrice != null) {
            predicates.add(cb.greaterThanOrEqualTo(root.get("price"), minPrice));
        }

        if (maxPrice != null) {
            predicates.add(cb.lessThanOrEqualTo(root.get("price"), maxPrice));
        }

        if (searchTerm != null && !searchTerm.trim().isEmpty()) {
            String searchPattern = "%" + searchTerm.toLowerCase() + "%";
            Predicate titleMatch = cb.like(cb.lower(root.get("title")), searchPattern);
            Predicate descriptionMatch = cb.like(cb.lower(root.get("description")), searchPattern);
            predicates.add(cb.or(titleMatch, descriptionMatch));
        }

        query.where(cb.and(predicates.toArray(new Predicate[0])));

        return entityManager.createQuery(query).getSingleResult();
    }
}
