package com.yotelohago.user.domain;

import com.yotelohago.user.api.UserWithDetailsDTO;
import com.yotelohago.common.dto.PageRequest;
import com.yotelohago.common.dto.PageResponse;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface UserRepository {
    void persist(User user);
    User update(User user);
    void deleteById(UUID id);
    void flush();

    // Entity methods for internal operations
    Optional<User> findEntityById(UUID id);
    Optional<User> findEntityByEmail(String email);
    List<User> findAllEntities(); // Legacy method - use paginated versions for better performance

    // DTO methods for API responses with enriched data
    Optional<UserWithDetailsDTO> findById(UUID id);
    Optional<UserWithDetailsDTO> findByEmail(String email);
    List<UserWithDetailsDTO> findAll(); // Legacy method - use paginated versions for better performance

    // Pagination methods for enriched users with professional and media details
    PageResponse<UserWithDetailsDTO> findAllWithDetailsPaginated(PageRequest pageRequest);
    PageResponse<UserWithDetailsDTO> findByProfessionalStatusWithDetailsPaginated(boolean isProfessional, PageRequest pageRequest);
}
