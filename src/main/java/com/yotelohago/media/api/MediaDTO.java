package com.yotelohago.media.api;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;

import java.time.Instant;
import java.util.UUID;

/**
 * Data Transfer Object for Media
 * Used for API responses and data transfer
 */
public class MediaDTO {

    public UUID id;
    
    @NotBlank
    public String filePath;
    
    @NotBlank
    public String originalFilename;
    
    @Positive
    public Long fileSize;
    
    @NotBlank
    public String contentType;
    
    @NotBlank
    public String mediaType;
    
    @NotNull
    public UUID userId;
    
    public UUID associatedEntityId;
    
    @NotNull
    public Instant createdAt;
    
    @NotNull
    public Instant updatedAt;

    /**
     * Public URL for accessing the media (if applicable)
     * This will be constructed by the service layer
     */
    public String publicUrl;

    /**
     * Default constructor
     */
    public MediaDTO() {}

    /**
     * Constructor for creating DTOs
     */
    public MediaDTO(UUID id, String filePath, String originalFilename, Long fileSize, 
                    String contentType, String mediaType, UUID userId, UUID associatedEntityId,
                    Instant createdAt, Instant updatedAt, String publicUrl) {
        this.id = id;
        this.filePath = filePath;
        this.originalFilename = originalFilename;
        this.fileSize = fileSize;
        this.contentType = contentType;
        this.mediaType = mediaType;
        this.userId = userId;
        this.associatedEntityId = associatedEntityId;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.publicUrl = publicUrl;
    }
}
