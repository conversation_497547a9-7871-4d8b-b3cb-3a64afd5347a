- name: Ensure CoreDNS config directory exists
  file:
    path: "~/.coredns"
    state: directory
    mode: '0755'

- name: Write CoreDNS config inline
  copy:
    dest: "~/.coredns/Corefile"
    content: |
      .:53 {
          hosts {
              {{ coredns_host_ip }} jenkins.yotelohago.co
              {{ coredns_host_ip }} yotelohago.co
              fallthrough
          }
          bind 127.0.0.1
          log
          errors
      }
    mode: '0644'

- name: Create launchd plist to run CoreDNS as a system daemon (root)
  copy:
    dest: "{{ coredns_launchctl_daemon_file_path }}"
    content: |
      <?xml version="1.0" encoding="UTF-8"?>
      <!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
      <plist version="1.0">
        <dict>
          <key>Label</key>
          <string>dev.coredns</string>
          <key>ProgramArguments</key>
          <array>
            <string>/opt/homebrew/bin/coredns</string>
            <string>-conf</string>
            <string>/Users/<USER>/.coredns/Corefile</string>
          </array>
          <key>RunAtLoad</key>
          <true/>
          <key>KeepAlive</key>
          <true/>
        </dict>
      </plist>
    owner: root
    group: wheel
    mode: '0644'
  become: true

- name: UnLoad CoreDNS launchd service in case running, will throw error if not launched but it is ok.
  command: "launchctl bootout system {{ coredns_launchctl_daemon_file_path }}"
  become: true
  ignore_errors: true

- name: Load CoreDNS launchd service
  command: "launchctl bootstrap system {{ coredns_launchctl_daemon_file_path }}"
  become: true

- name: Ensure /etc/resolver directory exists
  file:
    path: /etc/resolver
    state: directory
    mode: '0755'
  become: true

- name: Configure resolver to use CoreDNS
  copy:
    dest: "/etc/resolver/{{ coredns_jekins_hostname }}"
    content: |
      nameserver 127.0.0.1
      port 53
    mode: '0644'
  become: true

- name: Flush macOS DNS cache
  become: true
  command: dscacheutil -flushcache

- name: Send HUP to mDNSResponder
  become: true
  command: killall -HUP mDNSResponder
