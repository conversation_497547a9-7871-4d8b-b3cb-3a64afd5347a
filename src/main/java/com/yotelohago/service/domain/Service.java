package com.yotelohago.service.domain;

import io.quarkus.hibernate.orm.panache.PanacheEntityBase;
import jakarta.persistence.*;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import com.yotelohago.user.domain.User;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.UUID;

@Entity
@Table(name = "services",
       indexes = {
           @Index(name = "idx_services_professional_user_id", columnList = "professional_user_id"),
           @Index(name = "idx_services_category_id", columnList = "category_id"),
           @Index(name = "idx_services_status", columnList = "status"),
           @Index(name = "idx_services_price", columnList = "price"),
           @Index(name = "idx_services_created_at", columnList = "created_at"),
           @Index(name = "idx_services_status_category", columnList = "status, category_id")
       })
public class Service extends PanacheEntityBase {

    @Id
    @GeneratedValue
    @Column(nullable = false, updatable = false)
    public UUID id;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "professional_user_id", nullable = false,
                foreignKey = @ForeignKey(name = "fk_services_professional_user"))
    public User professionalUser;

    @Column(nullable = false)
    public String title;

    @Column(nullable = false)
    public String description;

    @Column(nullable = false, precision = 10, scale = 2)
    public BigDecimal price;

    @ManyToOne
    @JoinColumn(name = "category_id", nullable = false)
    public ServiceCategory category;

    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    public ServiceStatus status;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    public Instant createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at")
    public Instant updatedAt;

    public Service() {}

    public Service(UUID id, String title, String description, ServiceCategory category, User professionalUser, BigDecimal price) {
        this.id = id;
        this.title = title;
        this.description = description;
        this.category = category;
        this.professionalUser = professionalUser;
        this.price = price;
        this.status = ServiceStatus.DRAFT; // Default to DRAFT
        // createdAt and updatedAt will be set automatically by annotations
    }

    public Service(UUID id, String title, String description, ServiceCategory category, User professionalUser, BigDecimal price, ServiceStatus status) {
        this.id = id;
        this.title = title;
        this.description = description;
        this.category = category;
        this.professionalUser = professionalUser;
        this.price = price;
        this.status = status;
        // createdAt and updatedAt will be set automatically by annotations
    }

    public UUID getId() { return id; }
    public String getTitle() { return title; }
    public String getDescription() { return description; }
    public ServiceCategory getCategory() { return category; }
    public User getProfessionalUser() { return professionalUser; }
    public BigDecimal getPrice() { return price; }
    public ServiceStatus getStatus() { return status; }
    public Instant getCreatedAt() { return createdAt; }
    public Instant getUpdatedAt() { return updatedAt; }



    public void setId(UUID id) { this.id = id; }
    public void setTitle(String title) { this.title = title; }
    public void setDescription(String description) { this.description = description; }
    public void setCategory(ServiceCategory category) { this.category = category; }
    public void setProfessionalUser(User professionalUser) { this.professionalUser = professionalUser; }


    public void setPrice(BigDecimal price) { this.price = price; }
    public void setStatus(ServiceStatus status) {
        this.status = status;
        this.updatedAt = Instant.now();
    }
    public void setCreatedAt(Instant createdAt) { this.createdAt = createdAt; }
    public void setUpdatedAt(Instant updatedAt) { this.updatedAt = updatedAt; }

    /**
     * Check if this service is publicly visible
     * @return true if service status allows public visibility
     */
    public boolean isPubliclyVisible() {
        return status != null && status.isPubliclyVisible();
    }
}