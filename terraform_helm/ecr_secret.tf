# data "aws_ecr_authorization_token" "token" {}
#
# locals {
#   docker_config = jsonencode({
#     auths = {
#       "${data.aws_ecr_authorization_token.token.proxy_endpoint}" = {
#         username = data.aws_ecr_authorization_token.token.user_name
#         password = data.aws_ecr_authorization_token.token.password
#         email    = "none"
#         auth     = base64encode("${data.aws_ecr_authorization_token.token.user_name}:${data.aws_ecr_authorization_token.token.password}")
#       }
#     }
#   })
# }
#
# resource "kubernetes_secret" "ecr_pull_secret" {
#   metadata {
#     name      = var.image_pull_secret_name
#     namespace = var.k8s_namespace
#   }
#
#   data = {
#     ".dockerconfigjson" = local.docker_config
#   }
#
#   type = "kubernetes.io/dockerconfigjson"
# }