package com.yotelohago.professional.domain;

import io.quarkus.hibernate.orm.panache.PanacheEntityBase;
import jakarta.persistence.*;
import com.yotelohago.user.domain.User;

import java.util.Objects;
import java.util.UUID;

@Entity
@Table(name = "professionals")
public class Professional extends PanacheEntityBase {

    @Id
    @OneToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "user_id", nullable = false, updatable = false,
                foreignKey = @ForeignKey(name = "fk_professionals_user"))
    public User user;

    @Column(columnDefinition = "TEXT")
    public String bio;

    @Column(length = 100)
    public String city;

    @Column(name = "avg_rating")
    public Double avgRating = 0.0;

    @Column(name = "rating_count")
    public Integer ratingCount = 0;

    @Column(nullable = false)
    public boolean available;

    public Professional() {
        // JPA constructor
    }

    public Professional(User user, String bio, String city, Double avgRating, Integer ratingCount, boolean available) {
        this.user = user;
        this.bio = bio;
        this.city = city;
        this.avgRating = avgRating;
        this.ratingCount = ratingCount;
        this.available = available;
    }

    public User getUser() { return user; }
    public String getBio() { return bio; }
    public String getCity() { return city; }
    public Double getAvgRating() { return avgRating; }
    public Integer getRatingCount() { return ratingCount; }
    public boolean isAvailable() { return available; }

    public void setUser(User user) { this.user = user; }
    public void setBio(String bio) { this.bio = bio; }
    public void setCity(String city) { this.city = city; }
    public void setAvgRating(Double avgRating) { this.avgRating = avgRating; }
    public void setRatingCount(Integer ratingCount) { this.ratingCount = ratingCount; }
    public void setAvailable(boolean available) { this.available = available; }

    // Helper method to get user ID
    public UUID getUserId() {
        return user != null ? user.id : null;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        Professional that = (Professional) o;

        // Use user ID for equality since user is the primary key
        return Objects.equals(getUserId(), that.getUserId());
    }

    @Override
    public int hashCode() {
        // Use user ID for hash code since user is the primary key
        return Objects.hash(getUserId());
    }

    @Override
    public String toString() {
        return "Professional{" +
                "userId=" + getUserId() +
                ", bio='" + bio + '\'' +
                ", city='" + city + '\'' +
                ", avgRating=" + avgRating +
                ", ratingCount=" + ratingCount +
                ", available=" + available +
                '}';
    }
}