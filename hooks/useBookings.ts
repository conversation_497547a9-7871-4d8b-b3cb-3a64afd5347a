import { useState, useEffect, useCallback, useRef } from 'react';
import { 
  BookingDTO, 
  BookingStatus,
  PaginatedResponse 
} from '../types/api';
import { 
  getBookings, 
  getBookingsByStatus, 
  getBookingsPaginated,
  getBookingById,
  createBooking,
  updateBooking,
  deleteBooking,
  updateBookingStatus,
  transformBookingForFrontend 
} from '../services/api/bookingService';

export interface BookingListState {
  bookings: any[]; // Using any for now to match current frontend structure
  totalElements: number;
  currentPage: number;
  isLoading: boolean;
  isLoadingMore: boolean;
  error: string | null;
  hasMore: boolean;
  isEmpty: boolean;
  isFirstLoad: boolean;
}

export interface UseBookingsOptions {
  size?: number;
  status?: BookingStatus;
  autoLoad?: boolean;
}

export interface UseBookingsReturn extends BookingListState {
  refresh: () => Promise<void>;
  loadMore: () => Promise<void>;
  filterByStatus: (status: BookingStatus | null) => void;
  clearFilters: () => void;
  createNewBooking: (bookingData: any) => Promise<BookingDTO>;
  updateExistingBooking: (id: string, updateData: any) => Promise<BookingDTO>;
  deleteExistingBooking: (id: string) => Promise<void>;
  updateStatus: (id: string, status: BookingStatus) => Promise<BookingDTO>;
}

export const useBookings = (options: UseBookingsOptions = {}): UseBookingsReturn => {
  const {
    size = 20,
    status,
    autoLoad = true
  } = options;

  const [state, setState] = useState<BookingListState>({
    bookings: [],
    totalElements: 0,
    currentPage: 0,
    isLoading: false,
    isLoadingMore: false,
    error: null,
    hasMore: true,
    isEmpty: true,
    isFirstLoad: true,
  });

  const [currentStatus, setCurrentStatus] = useState<BookingStatus | null>(status || null);
  const abortControllerRef = useRef<AbortController | null>(null);

  // Cancel any ongoing requests
  const cancelRequests = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
  }, []);

  // Load bookings
  const loadBookings = useCallback(async (
    page: number = 0,
    append: boolean = false,
    statusFilter?: BookingStatus | null
  ) => {
    try {
      // Cancel any ongoing requests
      cancelRequests();
      
      // Create new abort controller
      abortControllerRef.current = new AbortController();

      setState(prev => ({
        ...prev,
        isLoading: !append,
        isLoadingMore: append,
        error: null,
      }));

      let response: BookingDTO[];
      
      if (statusFilter) {
        response = await getBookingsByStatus(statusFilter);
      } else {
        response = await getBookings();
      }

      // Transform backend bookings to frontend format
      const transformedBookings = response.map(transformBookingForFrontend);

      console.log('📊 useBookings - Transformed bookings:', {
        originalCount: response.length,
        transformedCount: transformedBookings.length,
        statuses: transformedBookings.map(b => ({ id: b.id, status: b.status, title: b.title })),
        statusFilter: statusFilter
      });

      setState(prev => ({
        ...prev,
        bookings: append ? [...prev.bookings, ...transformedBookings] : transformedBookings,
        totalElements: transformedBookings.length,
        currentPage: page,
        isLoading: false,
        isLoadingMore: false,
        hasMore: false, // For now, since we're not using pagination
        isEmpty: transformedBookings.length === 0,
        isFirstLoad: false,
      }));

    } catch (error: any) {
      if (error.name === 'AbortError') {
        return; // Request was cancelled, don't update state
      }

      console.error('Failed to load bookings:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        isLoadingMore: false,
        error: error.message || 'Failed to load bookings',
      }));
    }
  }, [cancelRequests]);

  // Refresh bookings (reload from first page)
  const refresh = useCallback(async () => {
    await loadBookings(0, false, currentStatus);
  }, [loadBookings, currentStatus]);

  // Load more bookings (pagination)
  const loadMore = useCallback(async () => {
    if (state.isLoadingMore || !state.hasMore) return;
    await loadBookings(state.currentPage + 1, true, currentStatus);
  }, [loadBookings, state.isLoadingMore, state.hasMore, state.currentPage, currentStatus]);

  // Filter by status
  const filterByStatus = useCallback((status: BookingStatus | null) => {
    setCurrentStatus(status);
    loadBookings(0, false, status);
  }, [loadBookings]);

  // Clear filters
  const clearFilters = useCallback(() => {
    setCurrentStatus(null);
    loadBookings(0, false, null);
  }, [loadBookings]);

  // Create new booking
  const createNewBooking = useCallback(async (bookingData: any): Promise<BookingDTO> => {
    try {
      const newBooking = await createBooking(bookingData);
      // Refresh the list to include the new booking
      await refresh();
      return newBooking;
    } catch (error: any) {
      console.error('Failed to create booking:', error);
      throw error;
    }
  }, [refresh]);

  // Update existing booking
  const updateExistingBooking = useCallback(async (id: string, updateData: any): Promise<BookingDTO> => {
    try {
      const updatedBooking = await updateBooking(id, updateData);
      // Refresh the list to reflect changes
      await refresh();
      return updatedBooking;
    } catch (error: any) {
      console.error('Failed to update booking:', error);
      throw error;
    }
  }, [refresh]);

  // Delete existing booking
  const deleteExistingBooking = useCallback(async (id: string): Promise<void> => {
    try {
      await deleteBooking(id);
      // Refresh the list to remove the deleted booking
      await refresh();
    } catch (error: any) {
      console.error('Failed to delete booking:', error);
      throw error;
    }
  }, [refresh]);

  // Update booking status
  const updateStatus = useCallback(async (id: string, status: BookingStatus): Promise<BookingDTO> => {
    try {
      const updatedBooking = await updateBookingStatus(id, status);
      // Refresh the list to reflect status change
      await refresh();
      return updatedBooking;
    } catch (error: any) {
      console.error('Failed to update booking status:', error);
      throw error;
    }
  }, [refresh]);

  // Auto-load on mount and when status changes
  useEffect(() => {
    if (autoLoad) {
      loadBookings(0, false, currentStatus);
    }

    // Cleanup on unmount
    return () => {
      cancelRequests();
    };
  }, [autoLoad, currentStatus]); // Don't include loadBookings to avoid infinite loops

  return {
    ...state,
    refresh,
    loadMore,
    filterByStatus,
    clearFilters,
    createNewBooking,
    updateExistingBooking,
    deleteExistingBooking,
    updateStatus,
  };
};

export default useBookings;
