import Constants from 'expo-constants';

export interface EnvironmentConfig {
  API_BASE_URL: string;
  KEYCLOAK_URL: string;
  KEYCLOAK_REALM: string;
  KEYCLOAK_CLIENT_ID: string;
  APP_SCHEME: string;
  WEB_PORT: number;
}

const developmentConfig: EnvironmentConfig = {
  API_BASE_URL: 'http://localhost:8080/v1',
  KEYCLOAK_URL: 'https://keycloak.yotelohago.co',
  KEYCLOAK_REALM: 'yotelohago',
  KEYCLOAK_CLIENT_ID: 'yotelohago-app-dev',
  APP_SCHEME: 'yotelohago',
  WEB_PORT: 8082,
};

const productionConfig: EnvironmentConfig = {
  API_BASE_URL: 'https://api.yotelohago.co/v1',
  KEYCLOAK_URL: 'https://keycloak.yotelohago.co',
  KEYCLOAK_REALM: 'yotelohago',
  K<PERSON><PERSON><PERSON><PERSON>K_CLIENT_ID: 'yotelohago-app-prod',
  APP_SCHEME: 'yotelohago',
  WEB_PORT: 8082,
};

const getEnvironment = (): EnvironmentConfig => {
  if (__DEV__ || Constants.expoConfig?.extra?.environment === 'development') {
    return developmentConfig;
  }
  return productionConfig;
};

export const ENV = getEnvironment();

// Helper functions for common URLs
export const getApiUrl = (endpoint: string): string => {
  return `${ENV.API_BASE_URL}${endpoint}`;
};

export const getKeycloakUrl = (path: string = ''): string => {
  return `${ENV.KEYCLOAK_URL}/realms/${ENV.KEYCLOAK_REALM}${path}`;
};

export const getAuthUrl = (): string => {
  return getKeycloakUrl('/protocol/openid-connect/auth');
};

export const getTokenUrl = (): string => {
  return getKeycloakUrl('/protocol/openid-connect/token');
};

export const getUserInfoUrl = (): string => {
  return getKeycloakUrl('/protocol/openid-connect/userinfo');
};

export const getLogoutUrl = (): string => {
  return getKeycloakUrl('/protocol/openid-connect/logout');
};

export const getWebAppUrl = (path: string = ''): string => {
  if (__DEV__ || Constants.expoConfig?.extra?.environment === 'development') {
    return `http://localhost:${ENV.WEB_PORT}${path}`;
  }
  return `https://yotelohago.co${path}`;
};

export default ENV;
