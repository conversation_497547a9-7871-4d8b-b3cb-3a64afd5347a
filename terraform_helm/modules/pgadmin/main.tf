terraform {
  required_providers {
    kubernetes = {
      source  = "hashicorp/kubernetes"
    }
    helm = {
      source  = "hashicorp/helm"
    }
  }
}

resource "kubernetes_namespace" "pgadmin" {
  metadata {
    name = "pgadmin"
  }
}

resource "helm_release" "pgadmin" {
  name             = "pgadmin"
  repository       = "https://helm.runix.net"
  chart            = "pgadmin4"
  version          = "1.44.0"
  namespace        = kubernetes_namespace.pgadmin.metadata[0].name
  create_namespace = false

  values = [file("${path.module}/values.yaml")]
  depends_on = [kubernetes_namespace.pgadmin]
}