env:
  email: <EMAIL>
  password: yotelohago
  servers:
    yotelohago:
      Name: yotelohago-db
      Group: Servers
      Host: yotelohago-db-rw.cnpg-system.svc.cluster.local
      Port: 5432
      MaintenanceDB: yotelohago
      Username: yotelohago
      SSLMode: prefer

service:
  type: ClusterIP

ingress:
  enabled: true
  ingressClassName: traefik
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-prod
    traefik.ingress.kubernetes.io/router.entrypoints: websecure
  hosts:
    - host: pgadmin.yotelohago.co
      paths:
        - path: /
          pathType: Prefix
  tls:
    - hosts:
        - pgadmin.yotelohago.co
      secretName: pgadmin-yotelohago-co-tls