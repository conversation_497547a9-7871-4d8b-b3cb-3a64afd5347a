// Base API Response Types
export interface ApiResponse<T> {
  data: T;
  message?: string;
  success: boolean;
}

export interface ApiError {
  error: string;
  message?: string;
  status?: number;
}

// User Types (matching backend DTOs)
export interface UserDTO {
  // Basic user data
  id: string;
  username: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  isProfessional: boolean;
  createdAt: string;
  updatedAt?: string;

  // Professional data (only populated if isProfessional = true)
  bio?: string;
  city?: string;
  avgRating?: number;
  ratingCount?: number;
  available?: boolean;

  // Profile photo URLs (derived from Media entities)
  clientProfilePhotoUrl?: string;
  professionalProfilePhotoUrl?: string;

  // Computed convenience field
  fullName?: string;
}

export interface CreateUserRequest {
  username: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  isProfessional?: boolean;
}

export interface UpdateUserRequest {
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
}

// User Registration Types (matching backend DTOs)
export interface UserRegistrationRequest {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  // TODO: Add email verification fields when implementing email verification
  // verificationCode?: string;
  // emailVerified?: boolean;
}

export interface UserRegistrationResponse {
  keycloakId: string;
  email: string;
  firstName: string;
  lastName: string;
  message: string;
  // TODO: Add email verification status when implementing email verification
  // emailVerificationSent?: boolean;
  // verificationInstructions?: string;
}

// Professional Types
export interface ProfessionalDTO {
  userId: string;
  bio?: string;
  city?: string;
  avgRating?: number;
  ratingCount?: number;
  profileImageUrl?: string;
  available: boolean;
  serviceIds: string[];
}

// Composite Professional Profile DTO (matches backend ProfessionalProfileDTO)
export interface ProfessionalProfileDTO {
  // Professional domain data
  userId: string;
  bio?: string;
  city?: string;
  avgRating?: number;
  ratingCount?: number;
  profileImageUrl?: string;
  available: boolean;
  serviceIds: string[];

  // User domain data
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  username?: string;

  // Computed fields
  fullName?: string;
}

export interface CreateProfessionalRequest {
  bio?: string;
  city?: string;
  profileImageUrl?: string;
  available?: boolean;
}

export interface UpdateProfessionalRequest {
  bio?: string;
  city?: string;
  profileImageUrl?: string;
  available?: boolean;
}

// Backend DTO that matches the ProfessionalWithDetailsDTO from the backend
export interface ProfessionalWithDetailsDTO {
  // Professional domain data
  userId: string;
  bio?: string;
  city?: string;
  avgRating?: number;
  ratingCount?: number;
  professionalProfileImageUrl?: string; // Note: different field name from frontend
  available: boolean;

  // User domain data
  firstName?: string;
  lastName?: string;
  fullName?: string;
  email?: string;
  phone?: string;
  username?: string;
}

// Service Types
export interface ServiceDTO {
  id: string;
  professionalUserId: string;
  title: string;
  description: string;
  price: number;
  categoryId: string;
  categoryName?: string;
  status?: string; // 'draft' | 'published'
  createdAt?: string;
  updatedAt?: string;
}

export interface CreateServiceRequest {
  title: string;
  description: string;
  price: number;
  categoryId: string;
}

export interface UpdateServiceRequest {
  title?: string;
  description?: string;
  price?: number;
  categoryId?: string;
  status?: string; // 'draft' | 'published'
}

// Service Category Types
export interface ServiceCategoryDTO {
  id: string;
  name: string;
  description?: string;
  iconUrl?: string;
}

// Booking Types
export interface BookingDTO {
  id: number;
  clientId: string;
  professionalId: string | null;
  serviceId: string;
  title: string;
  description?: string;
  requestedDate?: string;
  scheduledAt?: string;
  price: number;
  status: string; // Will be converted from enum
  createdAt: string;
  updatedAt?: string;
}

// Enriched booking DTO with professional and service details
export interface BookingWithDetailsDTO {
  // Booking fields
  id: number;
  clientId: string;
  professionalId: string | null;
  serviceId: string;
  title: string;
  description?: string;
  requestedDate?: string;
  scheduledAt?: string;
  price: number;
  status: string;
  createdAt: string;
  updatedAt?: string;

  // Professional fields (null for OPEN bookings)
  professionalFirstName?: string;
  professionalLastName?: string;
  professionalFullName?: string;
  professionalEmail?: string;
  professionalCity?: string;
  professionalProfileImageUrl?: string;
  professionalAvailable?: boolean;

  // Client fields (always present)
  clientFirstName: string;
  clientLastName: string;
  clientFullName: string;
  clientEmail: string;
  clientProfileImageUrl?: string;

  // Service fields
  serviceTitle: string;
  serviceDescription?: string;
  serviceCategoryName: string;
  servicePrice: number;
}

export enum BookingStatus {
  OPEN = 'OPEN',
  ACCEPTED = 'ACCEPTED',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED'
}

export interface CreateBookingRequest {
  professionalId: string;
  serviceId: string;
  title: string;
  description?: string;
  scheduledDate?: string;
  totalPrice: number;
}

export interface UpdateBookingRequest {
  title?: string;
  description?: string;
  scheduledDate?: string;
  status?: BookingStatus;
  totalPrice?: number;
}

// Review Types
export interface ReviewDTO {
  id: string;
  bookingId: string;
  clientId: string;
  professionalId: string;
  rating: number;
  comment?: string;
  createdAt: string;
}

export interface CreateReviewRequest {
  bookingId: string;
  rating: number;
  comment?: string;
}

// Pagination Types
export interface PaginationParams {
  page?: number;
  size?: number;
  sort?: string;
  direction?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
  content: T[];
  totalElements: number;
  totalPages: number;
  size: number;
  number: number;
  first: boolean;
  last: boolean;
}

// Search and Filter Types
export interface ServiceSearchParams extends PaginationParams {
  category?: string;
  city?: string;
  minPrice?: number;
  maxPrice?: number;
  available?: boolean;
  query?: string;
}

export interface ProfessionalSearchParams extends PaginationParams {
  city?: string;
  available?: boolean;
  minRating?: number;
  serviceCategory?: string;
}

// Frontend-specific types for UI state management
export interface SearchState {
  query: string;
  filters: ServiceSearchParams;
  isLoading: boolean;
  error: string | null;
  hasMore: boolean;
}

export interface CategoryState {
  categories: ServiceCategoryDTO[];
  selectedCategory: string | null;
  isLoading: boolean;
  error: string | null;
}

export interface ServiceListState {
  services: ServiceDTO[];
  totalElements: number;
  currentPage: number;
  isLoading: boolean;
  isLoadingMore: boolean;
  error: string | null;
  hasMore: boolean;
}

// Frontend-compatible category type (for backward compatibility)
export interface FrontendCategory {
  id: string;
  name: string;
  description: string;
  icon: string;
}

// Enhanced service type with frontend-specific fields
export interface FrontendService extends ServiceDTO {
  categoryName: string;
  isFavorite?: boolean;
  isBookmarked?: boolean;
  distance?: number; // Distance from user location
  responseTime?: string; // Professional's typical response time
}

// API Response wrapper for better error handling
export interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
  timestamp: string;
}

// Loading states for different UI components
export enum LoadingState {
  IDLE = 'idle',
  LOADING = 'loading',
  SUCCESS = 'success',
  ERROR = 'error',
  REFRESHING = 'refreshing',
  LOADING_MORE = 'loading_more'
}

// Messaging Types (matching backend DTOs)
export interface MessageDTO {
  id?: number;
  bookingId: number;
  senderId: string;      // Internal user ID
  receiverId: string;    // Internal user ID
  content: string;
  sentAt: string;
  isRead: boolean;
}

export interface ConversationSummaryDTO {
  bookingId: number;
  bookingTitle?: string;
  bookingDescription?: string;
  lastMessageContent: string;
  lastMessageTime: string;
  lastMessageFromCurrentUser: boolean;
  unreadCount: number;
  // Participant details - always populated for both client and professional
  clientId: string;
  clientName: string;
  clientEmail: string;
  professionalId: string;
  professionalName: string;
  professionalEmail: string;
}

export interface SendMessageRequest {
  bookingId: number;
  receiverUserId: string;  // Internal user ID
  content: string;
}

// WebSocket Message Types
export interface WebSocketMessage {
  type: 'send_message' | 'mark_read' | 'typing' | 'ping' | 'new_message' | 'message_read' | 'typing_indicator' | 'pong' | 'error';
  message?: string;
  data?: any;
}

export interface WebSocketConnectionState {
  isConnected: boolean;
  isConnecting: boolean;
  error: string | null;
  lastConnected: Date | null;
  reconnectAttempts: number;
}

// Frontend Messaging State Types
export interface MessagingState {
  conversations: ConversationSummaryDTO[];
  currentConversation: {
    bookingId: number | null;
    messages: MessageDTO[];
    isLoading: boolean;
    error: string | null;
  };
  unreadCount: number;
  isLoading: boolean;
  error: string | null;
  websocket: WebSocketConnectionState;
  typingUsers: Record<number, string[]>; // bookingId -> array of user IDs typing
}

// Availability Types (matching backend DTOs)
export interface TimeRangeDTO {
  startTime: string; // LocalTime format "HH:mm"
  endTime: string;   // LocalTime format "HH:mm"
}

export interface DayScheduleDTO {
  dayOfWeek: string; // DayOfWeek enum: MONDAY, TUESDAY, etc.
  timeRanges: TimeRangeDTO[];
  isActive: boolean; // Computed field for UI convenience
}

export interface WorkScheduleDTO {
  professionalId: string;
  daySchedules: DayScheduleDTO[];
  notes?: string;
  createdAt: string;
  updatedAt?: string;

  // Computed fields for UI convenience
  hasAnyActiveDays: boolean;
  totalWeeklyMinutes: number;
  summary: string;
}

export interface UpdateWorkScheduleRequest {
  daySchedules?: DayScheduleDTO[];
  notes?: string;
}

// Availability check response
export interface AvailabilityCheckResponse {
  available: boolean;
}

// Days of week enum for type safety
export enum DayOfWeek {
  MONDAY = 'MONDAY',
  TUESDAY = 'TUESDAY',
  WEDNESDAY = 'WEDNESDAY',
  THURSDAY = 'THURSDAY',
  FRIDAY = 'FRIDAY',
  SATURDAY = 'SATURDAY',
  SUNDAY = 'SUNDAY'
}

// Frontend-specific availability state
export interface AvailabilityState {
  workSchedule: WorkScheduleDTO | null;
  isLoading: boolean;
  isSaving: boolean;
  error: string | null;
  lastUpdated: Date | null;
}

// Error types for better error handling
export interface ApiErrorDetails {
  code: string;
  message: string;
  field?: string;
  details?: Record<string, any>;
}

export interface ApiErrorResponse {
  error: string;
  details?: ApiErrorDetails[];
  timestamp: string;
  path: string;
}

// Media Types (matching backend DTOs)
export interface MediaDTO {
  id: string;
  filePath: string;
  originalFilename: string;
  fileSize: number;
  contentType: string;
  mediaType: string;
  userId: string;
  associatedEntityId?: string;
  createdAt: string;
  updatedAt: string;
  publicUrl: string;
}

export interface PresignedUrlRequestDTO {
  filename: string;
  contentType: string;
  fileSize: number;
  mediaType: string;
  associatedEntityId?: string;
}

export interface PresignedUrlResponseDTO {
  uploadUrl: string;
  filePath: string;
  mediaId: string;
  expiresAt: string;
  maxFileSize: number;
  instructions: string;
}

export enum MediaType {
  CLIENT_PROFILE_PICTURE = 'client_profile_picture',
  PROFESSIONAL_PROFILE_PICTURE = 'professional_profile_picture',
  SERVICE_PHOTO = 'service_photo',
  DOCUMENT = 'document'
}

// Frontend-specific media types
export interface ProfilePhotoUploadState {
  isLoading: boolean;
  isUploading: boolean;
  error: string | null;
  progress: number;
}

export interface ImagePickerResult {
  uri: string;
  type: string;
  name: string;
  size: number;
  file?: File; // For web compatibility
}
