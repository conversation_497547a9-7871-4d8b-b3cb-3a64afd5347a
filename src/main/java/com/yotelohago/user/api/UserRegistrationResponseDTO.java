package com.yotelohago.user.api;

import java.util.UUID;

/**
 * DTO for user registration response
 */
public class UserRegistrationResponseDTO {

    public UUID userId;
    public String email;
    public String firstName;
    public String lastName;
    public String message;
    
    // TODO: Add email verification status when implementing email verification
    // public boolean emailVerificationSent;
    // public String verificationInstructions;

    public UserRegistrationResponseDTO() {
        // Default constructor for JSON serialization
    }

    public UserRegistrationResponseDTO(UUID userId, String email, String firstName, String lastName, String message) {
        this.userId = userId;
        this.email = email;
        this.firstName = firstName;
        this.lastName = lastName;
        this.message = message;
    }

    public static UserRegistrationResponseDTO success(UUID userId, String email, String firstName, String lastName) {
        return new UserRegistrationResponseDTO(
            userId,
            email,
            firstName,
            lastName,
            "User registration successful. You can now log in with your credentials."
        );
    }

    @Override
    public String toString() {
        return "UserRegistrationResponseDTO{" +
                "userId=" + userId +
                ", email='" + email + '\'' +
                ", firstName='" + firstName + '\'' +
                ", lastName='" + lastName + '\'' +
                ", message='" + message + '\'' +
                '}';
    }
}
