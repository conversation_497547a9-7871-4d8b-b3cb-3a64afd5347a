package com.yotelohago.favorite.application;

import com.yotelohago.auth.TokenIdentityProvider;
import com.yotelohago.favorite.domain.Favorite;
import com.yotelohago.favorite.domain.FavoriteRepository;
import com.yotelohago.service.api.ServiceWithProfessionalDTO;
import com.yotelohago.user.application.UserSyncService;
import com.yotelohago.user.domain.User;
import com.yotelohago.service.domain.Service;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;
import jakarta.ws.rs.ForbiddenException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Instant;
import java.util.List;
import java.util.UUID;

@ApplicationScoped
public class FavoriteManagementService {

    private static final Logger logger = LoggerFactory.getLogger(FavoriteManagementService.class);

    @Inject
    FavoriteRepository favoriteRepository;

    @Inject
    TokenIdentityProvider tokenIdentityProvider;

    @Inject
    UserSyncService userSyncService;

    /**
     * Get all favorite services for the current authenticated user
     * @return List of favorite services with professional information
     */
    public List<ServiceWithProfessionalDTO> getCurrentUserFavorites() {
        // Ensure current user exists in database
        userSyncService.ensureCurrentUserExists();

        UUID currentUserId = tokenIdentityProvider.getCurrentUserId();
        if (currentUserId == null) {
            throw new ForbiddenException("Authentication required");
        }

        logger.debug("🔍 Fetching favorites for user: {}", currentUserId);
        return favoriteRepository.findFavoriteServicesWithProfessionalByUser(currentUserId);
    }

    /**
     * Get favorite service IDs for the current authenticated user
     * @return List of service IDs that are favorited
     */
    public List<UUID> getCurrentUserFavoriteServiceIds() {
        // Ensure current user exists in database
        userSyncService.ensureCurrentUserExists();

        UUID currentUserId = tokenIdentityProvider.getCurrentUserId();
        if (currentUserId == null) {
            throw new ForbiddenException("Authentication required");
        }

        logger.debug("🔍 Fetching favorite service IDs for user: {}", currentUserId);
        return favoriteRepository.findServiceIdsByUser(currentUserId);
    }

    /**
     * Add a service to current user's favorites
     * @param serviceId The service ID to add to favorites
     * @return true if added successfully, false if already favorited
     */
    @Transactional
    public boolean addToFavorites(UUID serviceId) {
        // Ensure current user exists in database
        userSyncService.ensureCurrentUserExists();

        UUID currentUserId = tokenIdentityProvider.getCurrentUserId();
        if (currentUserId == null) {
            throw new ForbiddenException("Authentication required");
        }

        // Check if already favorited
        if (favoriteRepository.isFavorited(currentUserId, serviceId)) {
            logger.debug("⚠️ Service {} already favorited by user {}", serviceId, currentUserId);
            return false;
        }

        // Load entities and create favorite
        User user = User.findById(currentUserId);
        Service service = Service.findById(serviceId);

        if (user == null) {
            throw new IllegalStateException("Current user not found in database");
        }
        if (service == null) {
            throw new IllegalArgumentException("Service not found: " + serviceId);
        }

        Favorite favorite = new Favorite(user, service);
        favoriteRepository.persist(favorite);
        favoriteRepository.flush();

        logger.info("✅ Service {} added to favorites by user {}", serviceId, currentUserId);
        return true;
    }

    /**
     * Remove a service from current user's favorites
     * @param serviceId The service ID to remove from favorites
     * @return true if removed successfully, false if not favorited
     */
    @Transactional
    public boolean removeFromFavorites(UUID serviceId) {
        // Ensure current user exists in database
        userSyncService.ensureCurrentUserExists();

        UUID currentUserId = tokenIdentityProvider.getCurrentUserId();
        if (currentUserId == null) {
            throw new ForbiddenException("Authentication required");
        }

        boolean removed = favoriteRepository.removeByUserAndService(currentUserId, serviceId);

        if (removed) {
            logger.info("✅ Service {} removed from favorites by user {}", serviceId, currentUserId);
        } else {
            logger.debug("⚠️ Service {} was not in favorites for user {}", serviceId, currentUserId);
        }

        return removed;
    }

    /**
     * Check if a service is favorited by the current user
     * @param serviceId The service ID to check
     * @return true if the service is favorited by the current user
     */
    public boolean isFavorited(UUID serviceId) {
        UUID currentUserId = tokenIdentityProvider.getCurrentUserId();
        if (currentUserId == null) {
            return false; // Not authenticated, so no favorites
        }

        return favoriteRepository.isFavorited(currentUserId, serviceId);
    }

    /**
     * Get the count of favorites for the current user
     * @return Number of favorites for the current user
     */
    public long getCurrentUserFavoritesCount() {
        UUID currentUserId = tokenIdentityProvider.getCurrentUserId();
        if (currentUserId == null) {
            return 0; // Not authenticated, so no favorites
        }

        return favoriteRepository.countByUser(currentUserId);
    }

    /**
     * Toggle favorite status for a service
     * @param serviceId The service ID to toggle
     * @return true if added to favorites, false if removed from favorites
     */
    @Transactional
    public boolean toggleFavorite(UUID serviceId) {
        if (isFavorited(serviceId)) {
            removeFromFavorites(serviceId);
            return false;
        } else {
            addToFavorites(serviceId);
            return true;
        }
    }
}
