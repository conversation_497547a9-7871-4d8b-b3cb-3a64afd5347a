import React, { useState, useEffect } from 'react';
import { Typography, Button, Space, Tag, Avatar, Dropdown, Modal, Descriptions, Rate, message } from 'antd';
import {
  EyeOutlined,
  EditOutlined,
  CheckOutlined,
  CloseOutlined,
  UserOutlined,
  MoreOutlined,
  StarOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { ColumnsType } from 'antd/es/table';
import DataTable from '../components/common/DataTable';
import { Professional, User } from '../types';
import apiService from '../services/apiService';

const { Title, Text } = Typography;
const { confirm } = Modal;

const Professionals: React.FC = () => {
  const [professionals, setProfessionals] = useState<Professional[]>([]);
  const [loading, setLoading] = useState(true);
  const [detailsVisible, setDetailsVisible] = useState(false);
  const [selectedProfessional, setSelectedProfessional] = useState<Professional | null>(null);

  // Load professionals from API
  useEffect(() => {
    const loadProfessionals = async () => {
      try {
        setLoading(true);
        console.log('Loading professionals...');
        const professionalsData = await apiService.getProfessionals();
        console.log('Professionals loaded:', professionalsData);
        setProfessionals(professionalsData || []);
      } catch (error) {
        console.error('Failed to load professionals:', error);
        message.error('Failed to load professionals');
        setProfessionals([]); // Set empty array on error
      } finally {
        setLoading(false);
      }
    };

    loadProfessionals();
  }, []);

  // Professionals already include user data from the profile endpoint
  const professionalsWithUsers = professionals;

  const handleViewProfessional = (professional: Professional) => {
    setSelectedProfessional(professional);
    setDetailsVisible(true);
  };

  const handleApproveProfessional = (professional: Professional) => {
    confirm({
      title: 'Approve Professional Application?',
      icon: <CheckOutlined />,
      content: `Approve ${professional.user?.firstName} ${professional.user?.lastName} as a professional?`,
      onOk() {
        setProfessionals(professionals.map(p => 
          p.userId === professional.userId 
            ? { ...p, status: 'approved' as const }
            : p
        ));
        message.success('Professional approved successfully');
      },
    });
  };

  const handleRejectProfessional = (professional: Professional) => {
    confirm({
      title: 'Reject Professional Application?',
      icon: <CloseOutlined />,
      content: `Reject ${professional.user?.firstName} ${professional.user?.lastName}'s professional application?`,
      okType: 'danger',
      onOk() {
        setProfessionals(professionals.map(p => 
          p.userId === professional.userId 
            ? { ...p, status: 'rejected' as const }
            : p
        ));
        message.success('Professional application rejected');
      },
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'green';
      case 'pending': return 'orange';
      case 'rejected': return 'red';
      case 'suspended': return 'red';
      default: return 'default';
    }
  };

  const getVerificationColor = (status: string) => {
    switch (status) {
      case 'verified': return 'green';
      case 'pending': return 'orange';
      case 'failed': return 'red';
      default: return 'default';
    }
  };

  const columns: ColumnsType<Professional & { user?: User }> = [
    {
      title: 'Professional',
      key: 'professional',
      width: 250,
      render: (_, record) => (
        <div className="flex items-center space-x-3">
          <Avatar 
            src={record.profileImageUrl} 
            icon={<UserOutlined />} 
            className="flex-shrink-0" 
          />
          <div className="flex-1 min-w-0">
            <div className="font-medium text-gray-900 truncate">
              {record.user?.firstName} {record.user?.lastName}
            </div>
            <div className="text-sm text-gray-500 truncate">{record.city}</div>
          </div>
        </div>
      ),
    },
    {
      title: 'Rating',
      key: 'rating',
      width: 150,
      render: (_, record) => (
        <div className="flex items-center space-x-2">
          <Rate disabled defaultValue={record.avgRating} allowHalf className="text-sm" />
          <span className="text-sm text-gray-500">
            {record.avgRating.toFixed(1)} ({record.ratingCount})
          </span>
        </div>
      ),
      sorter: (a, b) => a.avgRating - b.avgRating,
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status) => (
        <Tag color={getStatusColor(status)} className="capitalize">
          {status}
        </Tag>
      ),
      filters: [
        { text: 'Approved', value: 'approved' },
        { text: 'Pending', value: 'pending' },
        { text: 'Rejected', value: 'rejected' },
        { text: 'Suspended', value: 'suspended' },
      ],
      onFilter: (value, record) => record.status === value,
    },
    {
      title: 'Verification',
      dataIndex: 'verificationStatus',
      key: 'verificationStatus',
      width: 120,
      render: (verificationStatus) => (
        <Tag color={getVerificationColor(verificationStatus)} className="capitalize">
          {verificationStatus}
        </Tag>
      ),
    },
    {
      title: 'Available',
      dataIndex: 'available',
      key: 'available',
      width: 100,
      render: (available) => (
        <Tag color={available ? 'green' : 'red'}>
          {available ? 'Yes' : 'No'}
        </Tag>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 120,
      render: (_, record) => (
        <Dropdown
          menu={{
            items: [
              {
                key: 'view',
                icon: <EyeOutlined />,
                label: 'View Details',
                onClick: () => handleViewProfessional(record),
              },
              {
                key: 'edit',
                icon: <EditOutlined />,
                label: 'Edit Professional',
              },
              ...(record.status === 'pending' ? [
                {
                  type: 'divider' as const,
                },
                {
                  key: 'approve',
                  icon: <CheckOutlined />,
                  label: 'Approve',
                  onClick: () => handleApproveProfessional(record),
                },
                {
                  key: 'reject',
                  icon: <CloseOutlined />,
                  label: 'Reject',
                  danger: true,
                  onClick: () => handleRejectProfessional(record),
                },
              ] : []),
            ],
          }}
          trigger={['click']}
        >
          <Button type="text" icon={<MoreOutlined />} />
        </Dropdown>
      ),
    },
  ];

  const filterOptions = [
    {
      key: 'status',
      label: 'Status',
      options: [
        { value: 'approved', label: 'Approved' },
        { value: 'pending', label: 'Pending' },
        { value: 'rejected', label: 'Rejected' },
        { value: 'suspended', label: 'Suspended' },
      ],
    },
    {
      key: 'verificationStatus',
      label: 'Verification',
      options: [
        { value: 'verified', label: 'Verified' },
        { value: 'pending', label: 'Pending' },
        { value: 'failed', label: 'Failed' },
      ],
    },
  ];

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <Title level={2} className="m-0">Professional Management</Title>
        <Text type="secondary">Review and manage professional applications</Text>
      </div>

      <DataTable
        columns={columns}
        dataSource={professionalsWithUsers}
        rowKey="userId"
        loading={loading}
        filterOptions={filterOptions}
        onSearch={(value) => {
          console.log('Search:', value);
        }}
        onFilter={(filters) => {
          console.log('Filters:', filters);
        }}
        onExport={() => {
          console.log('Export professionals');
        }}
      />

      <Modal
        title={`Professional Details - ${selectedProfessional?.user?.firstName} ${selectedProfessional?.user?.lastName}`}
        open={detailsVisible}
        onCancel={() => setDetailsVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailsVisible(false)}>
            Close
          </Button>,
          ...(selectedProfessional?.status === 'pending' ? [
            <Button 
              key="reject" 
              danger 
              icon={<CloseOutlined />}
              onClick={() => selectedProfessional && handleRejectProfessional(selectedProfessional)}
            >
              Reject
            </Button>,
            <Button 
              key="approve" 
              type="primary" 
              icon={<CheckOutlined />}
              onClick={() => selectedProfessional && handleApproveProfessional(selectedProfessional)}
            >
              Approve
            </Button>,
          ] : [
            <Button key="edit" type="primary" icon={<EditOutlined />}>
              Edit Professional
            </Button>,
          ]),
        ]}
        width={700}
      >
        {selectedProfessional && (
          <div className="space-y-6">
            <div className="flex items-center space-x-4">
              <Avatar 
                size={64} 
                src={selectedProfessional.profileImageUrl} 
                icon={<UserOutlined />} 
              />
              <div>
                <Title level={4} className="m-0">
                  {selectedProfessional.user?.firstName} {selectedProfessional.user?.lastName}
                </Title>
                <Text type="secondary">{selectedProfessional.city}</Text>
                <div className="mt-2 flex items-center space-x-2">
                  <Tag color={getStatusColor(selectedProfessional.status)}>
                    {selectedProfessional.status}
                  </Tag>
                  <Tag color={getVerificationColor(selectedProfessional.verificationStatus)}>
                    {selectedProfessional.verificationStatus}
                  </Tag>
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <StarOutlined className="text-yellow-500" />
                <span className="font-medium">{selectedProfessional.avgRating.toFixed(1)}</span>
                <span className="text-gray-500">({selectedProfessional.ratingCount} reviews)</span>
              </div>
              <Tag color={selectedProfessional.available ? 'green' : 'red'}>
                {selectedProfessional.available ? 'Available' : 'Unavailable'}
              </Tag>
            </div>

            <Descriptions column={1} bordered>
              <Descriptions.Item label="Email">
                {selectedProfessional.user?.email}
              </Descriptions.Item>
              <Descriptions.Item label="Phone">
                {selectedProfessional.user?.phone}
              </Descriptions.Item>
              <Descriptions.Item label="City">
                {selectedProfessional.city}
              </Descriptions.Item>
              <Descriptions.Item label="Bio">
                {selectedProfessional.bio}
              </Descriptions.Item>
              <Descriptions.Item label="Average Rating">
                <div className="flex items-center space-x-2">
                  <Rate disabled defaultValue={selectedProfessional.avgRating} allowHalf />
                  <span>{selectedProfessional.avgRating.toFixed(1)} ({selectedProfessional.ratingCount} reviews)</span>
                </div>
              </Descriptions.Item>
              <Descriptions.Item label="Availability">
                <Tag color={selectedProfessional.available ? 'green' : 'red'}>
                  {selectedProfessional.available ? 'Available' : 'Unavailable'}
                </Tag>
              </Descriptions.Item>
            </Descriptions>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default Professionals;