terraform {
  required_providers {
    kubernetes = {
      source  = "hashicorp/kubernetes"
    }
    helm = {
      source  = "hashicorp/helm"
    }
  }
}

resource "kubernetes_namespace" "portainer" {
  metadata {
    name = "portainer"
  }
}

resource "helm_release" "portainer" {
  name       = "portainer"
  repository = "https://portainer.github.io/k8s/"
  chart      = "portainer"
  version    = "1.0.66"
  namespace  = kubernetes_namespace.portainer.metadata[0].name
  timeout    = 600

  create_namespace = false
  values           = [file("${path.module}/values.yaml")]

  depends_on = [kubernetes_namespace.portainer]
}