package com.yotelohago.user.api;

/**
 * DTO for user registration requests
 */
public class UserRegistrationDTO {

    public String email;
    public String password;
    public String firstName;
    public String lastName;

    // TODO: Add email verification fields when implementing email verification
    // public String verificationCode;
    // public boolean emailVerified = false;

    public UserRegistrationDTO() {
        // Default constructor for JSON deserialization
    }

    public UserRegistrationDTO(String email, String password, String firstName, String lastName) {
        this.email = email;
        this.password = password;
        this.firstName = firstName;
        this.lastName = lastName;
    }

    @Override
    public String toString() {
        return "UserRegistrationDTO{" +
                "email='" + email + '\'' +
                ", firstName='" + firstName + '\'' +
                ", lastName='" + lastName + '\'' +
                ", password='[REDACTED]'" +
                '}';
    }
}
