import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Booking, Service, User } from '@/types';
import Colors from '../constants/Colors';

interface BookingItemProps {
  booking: Booking;
  service: Service;
  provider: User;
  onPress: (booking: Booking) => void;
  onMessagePress: (providerId: string) => void;
}

const BookingItem: React.FC<BookingItemProps> = ({
  booking,
  service,
  provider,
  onPress,
  onMessagePress,
}) => {
  return (
    <TouchableOpacity
      style={styles.container}
      onPress={() => onPress(booking)}
    >
      {/* Professional Info Header */}
      <View style={styles.providerInfo}>
        <View style={styles.providerAvatar}>
          <Ionicons
            name="person-circle"
            size={40}
            color={Colors.text.secondary}
          />
        </View>
        <View style={styles.providerDetails}>
          <Text style={styles.providerName}>{provider.name}</Text>
          <Text style={styles.serviceTitle} numberOfLines={1}>
            {service.title}
          </Text>
        </View>
      </View>

      {/* Date and Time */}
      <View style={styles.dateTimeSection}>
        <View style={styles.dateContainer}>
          <Ionicons name="calendar-outline" size={16} color={Colors.text.secondary} />
          <Text style={styles.dateText}>{booking.date}</Text>
        </View>
        <View style={styles.timeContainer}>
          <Ionicons name="time-outline" size={16} color={Colors.text.secondary} />
          <Text style={styles.timeText}>{booking.time}</Text>
        </View>
      </View>

      {/* Footer with Price and Message Button */}
      <View style={styles.footer}>
        <Text style={styles.price}>
          Agreed price: ${booking.price}
        </Text>

        <TouchableOpacity
          style={styles.messageButton}
          onPress={() => onMessagePress(provider.id)}
        >
          <Ionicons name="chatbubble-outline" size={16} color={Colors.primary} />
          <Text style={styles.messageText}>Message</Text>
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.background.primary,
    borderRadius: 12,
    padding: 16,
    width: '100%',
    borderWidth: 1,
    borderColor: Colors.border,
  },
  providerInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  providerAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.background.secondary,
  },
  providerDetails: {
    flex: 1,
  },
  providerName: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text.primary,
    marginBottom: 2,
  },
  serviceTitle: {
    fontSize: 14,
    color: Colors.text.secondary,
    fontWeight: '400',
  },
  dateTimeSection: {
    marginBottom: 12,
  },
  dateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  timeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dateText: {
    fontSize: 14,
    color: Colors.text.primary,
    marginLeft: 6,
    fontWeight: '500',
  },
  timeText: {
    fontSize: 14,
    color: Colors.text.secondary,
    marginLeft: 6,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 4,
  },
  price: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.text.primary,
  },
  messageButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 6,
    paddingHorizontal: 10,
    borderRadius: 16,
    backgroundColor: Colors.background.secondary,
  },
  messageText: {
    fontSize: 12,
    fontWeight: '500',
    color: Colors.primary,
    marginLeft: 4,
  },
});

export default BookingItem;