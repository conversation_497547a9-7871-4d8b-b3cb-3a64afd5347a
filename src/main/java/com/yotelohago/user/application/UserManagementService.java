package com.yotelohago.user.application;

import com.yotelohago.auth.TokenIdentityProvider;
import com.yotelohago.media.domain.Media;
import com.yotelohago.media.domain.MediaRepository;
import com.yotelohago.media.domain.MediaType;
import com.yotelohago.professional.domain.Professional;
import com.yotelohago.professional.domain.ProfessionalRepository;
import com.yotelohago.user.api.UserWithDetailsDTO;
import com.yotelohago.user.api.UserMapper;
import com.yotelohago.common.dto.PageRequest;
import com.yotelohago.common.dto.PageResponse;
import com.yotelohago.user.domain.AuthProvider;
import com.yotelohago.user.domain.User;
import com.yotelohago.user.domain.UserRepository;
import com.yotelohago.user.domain.UserExternalIdentity;
import com.yotelohago.user.domain.UserExternalIdentityRepository;
import com.yotelohago.user.infrastructure.KeycloakAdminService;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;
import jakarta.ws.rs.ForbiddenException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@ApplicationScoped
public class UserManagementService {

    private static final Logger logger = LoggerFactory.getLogger(UserManagementService.class);

    @Inject
    UserRepository userRepository;

    @Inject
    UserExternalIdentityRepository externalIdentityRepository;

    @Inject
    TokenIdentityProvider tokenIdentityProvider;

    @Inject
    KeycloakAdminService keycloakAdminService;

    @Inject
    UserSyncService userSyncService;

    @Inject
    MediaRepository mediaRepository;

    @Inject
    ProfessionalRepository professionalRepository;

    @Inject
    UserMapper userMapper;



    /**
     * Find user entity by ID for internal operations
     */
    public Optional<User> findEntityById(UUID id) {
        return userRepository.findEntityById(id);
    }

    /**
     * Find user by ID with authorization check.
     * Users can only access their own data, admins can access any user.
     */
    public Optional<UserWithDetailsDTO> findById(UUID id) {
        // Ensure current user exists in database
        userSyncService.ensureCurrentUserExists();

        // Check authorization
        if (!tokenIdentityProvider.canAccessUserData(id)) {
            throw new ForbiddenException("Access denied to user data");
        }

        return userRepository.findById(id);
    }

    /**
     * Find all users - admin only
     */
    public List<UserWithDetailsDTO> findAll() {
        // Ensure current user exists in database
        userSyncService.ensureCurrentUserExists();

        if (!tokenIdentityProvider.hasRole("admin")) {
            throw new ForbiddenException("Admin access required");
        }

        return userRepository.findAll();
    }

    /**
     * Get current user entity for internal operations
     */
    public Optional<User> getCurrentUser() {
        return userSyncService.ensureCurrentUserExists();
    }

    /**
     * Get current user profile with details for API responses
     */
    public Optional<UserWithDetailsDTO> getCurrentUserWithDetails() {
        Optional<User> userOpt = userSyncService.ensureCurrentUserExists();
        if (userOpt.isEmpty()) {
            return Optional.empty();
        }

        User user = userOpt.get();
        return userRepository.findById(user.getId());
    }

    /**
     * Get current user profile with enhanced data including professional info and media URLs
     * This method provides complete profile information in a single call
     */
    public UserWithDetailsDTO getCurrentUserProfile() {
        User user = userSyncService.ensureCurrentUserExists()
                .orElseThrow(() -> new RuntimeException("User not found"));

        logger.debug("🔍 Getting enhanced profile for user: {}", user.getId());

        // Get profile photos using Media repository with efficient URL building
        Optional<com.yotelohago.media.api.MediaDTO> clientPhoto = mediaRepository.findProfilePhotoByUserAndTypeWithUrl(
                user.getId(), MediaType.CLIENT_PROFILE_PICTURE);
        String clientProfilePhotoUrl = clientPhoto
                .map(mediaDTO -> mediaDTO.publicUrl)
                .orElse(null);

        String professionalProfilePhotoUrl = null;
        String bio = null;
        String city = null;
        Double avgRating = null;
        Integer ratingCount = null;
        Boolean available = null;

        // If user is professional, get professional data and professional profile photo
        if (user.isProfessional()) {
            Optional<com.yotelohago.media.api.MediaDTO> professionalPhoto = mediaRepository.findProfilePhotoByUserAndTypeWithUrl(
                    user.getId(), MediaType.PROFESSIONAL_PROFILE_PICTURE);
            professionalProfilePhotoUrl = professionalPhoto
                    .map(mediaDTO -> mediaDTO.publicUrl)
                    .orElse(null);

            // Get professional data
            Optional<Professional> professional = professionalRepository.findById(user.getId());
            if (professional.isPresent()) {
                Professional prof = professional.get();
                bio = prof.getBio();
                city = prof.getCity();
                avgRating = prof.getAvgRating();
                ratingCount = prof.getRatingCount();
                available = prof.isAvailable();
            }
        }

        // Create enhanced DTO
        return userMapper.toEnhancedDTO(user, bio, city, avgRating, ratingCount, available,
                clientProfilePhotoUrl, professionalProfilePhotoUrl);
    }

    /**
     * Update user with authorization check and Keycloak role management.
     * Users can only update their own data, admins can update any user.
     */
    @Transactional
    public User updateUser(User updatedUser) {
        // Ensure current user exists in database
        userSyncService.ensureCurrentUserExists();

        // Check authorization
        if (!tokenIdentityProvider.canAccessUserData(updatedUser.getId())) {
            throw new ForbiddenException("Access denied to update user data");
        }

        // Get existing user
        User existingUser = userRepository.findEntityById(updatedUser.getId())
                .orElseThrow(() -> new RuntimeException("User not found"));

        // Check if professional status is changing
        boolean wasProfessional = existingUser.isProfessional();
        boolean willBeProfessional = updatedUser.isProfessional();

        // Update user data
        existingUser.setUsername(updatedUser.getUsername());
        existingUser.setFirstName(updatedUser.getFirstName());
        existingUser.setLastName(updatedUser.getLastName());
        existingUser.setEmail(updatedUser.getEmail());
        existingUser.setPhone(updatedUser.getPhone());
        existingUser.setProfessional(updatedUser.isProfessional());
        existingUser.setUpdatedAt(Instant.now());

        // Handle professional role changes in Keycloak
        if (wasProfessional != willBeProfessional) {
            // Get the external Keycloak ID for this user
            Optional<UserExternalIdentity> keycloakIdentity = externalIdentityRepository.findByUserId(existingUser.getId());
            if (keycloakIdentity.isPresent() && AuthProvider.KEYCLOAK.equals(keycloakIdentity.get().getProvider())) {
                try {
                    UUID keycloakId = UUID.fromString(keycloakIdentity.get().getExternalId());
                    if (willBeProfessional) {
                        keycloakAdminService.addProfessionalRole(keycloakId);
                        logger.info("Added professional role to user: {}", keycloakId);
                    } else {
                        keycloakAdminService.removeProfessionalRole(keycloakId);
                        logger.info("Removed professional role from user: {}", keycloakId);
                    }
                } catch (Exception e) {
                    logger.error("Failed to update Keycloak roles for user: {}", existingUser.getId(), e);
                    // You might want to rollback the database change here
                    throw new RuntimeException("Failed to update user roles", e);
                }
            } else {
                logger.warn("No Keycloak identity found for user: {}", existingUser.getId());
            }
        }

        return userRepository.update(existingUser);
    }

    /**
     * Delete user - admin only
     */
    @Transactional
    public void deleteUser(UUID id) {
        // Ensure current user exists in database
        userSyncService.ensureCurrentUserExists();

        if (!tokenIdentityProvider.hasRole("admin")) {
            throw new ForbiddenException("Admin access required");
        }

        userRepository.deleteById(id);
    }

    /**
     * Find all users with pagination and authorization
     */
    public PageResponse<UserWithDetailsDTO> findAllWithDetailsPaginated(PageRequest pageRequest) {
        logger.info("Finding all users with pagination: page={}, size={}", pageRequest.getPage(), pageRequest.getSize());

        // Ensure current user exists in database
        userSyncService.ensureCurrentUserExists();
        logger.debug("Current user exists in database");

        if (!tokenIdentityProvider.hasRole("admin")) {
            logger.warn("Non-admin user attempted to access user listing");
            throw new ForbiddenException("Admin access required");
        }

        logger.debug("Admin access confirmed, calling repository");
        PageResponse<UserWithDetailsDTO> result = userRepository.findAllWithDetailsPaginated(pageRequest);
        logger.info("Retrieved {} users from repository", result.getContent().size());

        return result;
    }

    /**
     * Find users by professional status with pagination and authorization
     */
    public PageResponse<UserWithDetailsDTO> findByProfessionalStatusWithDetailsPaginated(boolean isProfessional, PageRequest pageRequest) {
        logger.info("Finding users by professional status: isProfessional={}, page={}, size={}",
                   isProfessional, pageRequest.getPage(), pageRequest.getSize());

        // Ensure current user exists in database
        userSyncService.ensureCurrentUserExists();
        logger.debug("Current user exists in database");

        if (!tokenIdentityProvider.hasRole("admin")) {
            logger.warn("Non-admin user attempted to access filtered user listing");
            throw new ForbiddenException("Admin access required");
        }

        logger.debug("Admin access confirmed, calling repository with filter");
        PageResponse<UserWithDetailsDTO> result = userRepository.findByProfessionalStatusWithDetailsPaginated(isProfessional, pageRequest);
        logger.info("Retrieved {} users with professional status {} from repository", result.getContent().size(), isProfessional);

        return result;
    }
}