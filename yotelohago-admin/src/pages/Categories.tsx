import React, { useState, useEffect } from 'react';
import { Typography, Button, Dropdown, Modal, Form, Input, Select, message } from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  MoreOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { ColumnsType } from 'antd/es/table';
import DataTable from '../components/common/DataTable';
import { ServiceCategory } from '../types';
import apiService from '../services/apiService';
import { useUrlHighlight } from '../hooks/useUrlHighlight';

const { Title } = Typography;
const { Option } = Select;
const { TextArea } = Input;
const { confirm } = Modal;

const Categories: React.FC = () => {
  const [categories, setCategories] = useState<ServiceCategory[]>([]);
  const [loading, setLoading] = useState(true);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingCategory, setEditingCategory] = useState<ServiceCategory | null>(null);

  // Load categories from API
  useEffect(() => {
    const loadCategories = async () => {
      try {
        setLoading(true);
        const categoriesData = await apiService.getServiceCategories();
        setCategories(categoriesData);
      } catch (error) {
        console.error('Failed to load categories:', error);
        message.error('Failed to load categories');
      } finally {
        setLoading(false);
      }
    };

    loadCategories();
  }, []);
  const [form] = Form.useForm();
  const highlightId = useUrlHighlight();

  const handleAddCategory = () => {
    setEditingCategory(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleEditCategory = (category: ServiceCategory) => {
    setEditingCategory(category);
    form.setFieldsValue(category);
    setModalVisible(true);
  };

  const handleDeleteCategory = (category: ServiceCategory) => {
    confirm({
      title: 'Are you sure you want to delete this category?',
      icon: <ExclamationCircleOutlined />,
      content: `This action cannot be undone. Category "${category.name}" will be permanently deleted.`,
      okText: 'Yes, Delete',
      okType: 'danger',
      cancelText: 'Cancel',
      onOk() {
        setCategories(categories.filter(c => c.id !== category.id));
        message.success('Category deleted successfully');
      },
    });
  };

  const handleSaveCategory = async (values: any) => {
    setLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const categoryData = {
        ...values,
        id: editingCategory?.id || Date.now().toString(),
        serviceCount: editingCategory?.serviceCount || 0,
      };

      if (editingCategory) {
        setCategories(categories.map(c => c.id === editingCategory.id ? categoryData : c));
        message.success('Category updated successfully');
      } else {
        setCategories([...categories, categoryData]);
        message.success('Category created successfully');
      }

      setModalVisible(false);
      form.resetFields();
    } catch (error) {
      message.error('Failed to save category');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'green';
      case 'inactive': return 'red';
      default: return 'default';
    }
  };

  const getCategoryIcon = (categoryName: string) => {
    switch (categoryName.toLowerCase()) {
      case 'electricians':
        return <ion-icon name="flash-outline" style={{ fontSize: '20px', color: '#faad14' }}></ion-icon>;
      case 'plumbers':
        return <ion-icon name="water-outline" style={{ fontSize: '20px', color: '#1890ff' }}></ion-icon>;
      case 'movers':
        return <ion-icon name="cube-outline" style={{ fontSize: '20px', color: '#52c41a' }}></ion-icon>;
      case 'locksmiths':
        return <ion-icon name="key-outline" style={{ fontSize: '20px', color: '#722ed1' }}></ion-icon>;
      default:
        return <ion-icon name="construct-outline" style={{ fontSize: '20px', color: '#8c8c8c' }}></ion-icon>;
    }
  };

  const columns: ColumnsType<ServiceCategory> = [
    {
      title: 'Icon',
      key: 'icon',
      width: 80,
      render: (_, record) => (
        <div className="flex justify-center">
          {getCategoryIcon(record.name)}
        </div>
      ),
    },
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      render: (name, record) => (
        <span 
          className={`font-medium text-gray-900 ${
            highlightId === record.id ? 'bg-coral-100 px-2 py-1 rounded animate-pulse' : ''
          }`}
        >
          {name}
        </span>
      ),
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      render: (description) => (
        <span className="text-gray-600">{description}</span>
      ),
    },
    {
      title: 'Services',
      dataIndex: 'serviceCount',
      key: 'serviceCount',
      width: 100,
      render: (count) => (
        <span className="font-medium">{count || 0}</span>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => (
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
          status === 'active' 
            ? 'bg-green-100 text-green-800' 
            : 'bg-red-100 text-red-800'
        }`}>
          {status}
        </span>
      ),
      filters: [
        { text: 'Active', value: 'active' },
        { text: 'Inactive', value: 'inactive' },
      ],
      onFilter: (value, record) => record.status === value,
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 120,
      render: (_, record) => (
        <Dropdown
          menu={{
            items: [
              {
                key: 'edit',
                icon: <EditOutlined />,
                label: 'Edit',
                onClick: () => handleEditCategory(record),
              },
              {
                type: 'divider',
              },
              {
                key: 'delete',
                icon: <DeleteOutlined />,
                label: 'Delete',
                danger: true,
                onClick: () => handleDeleteCategory(record),
              },
            ],
          }}
          trigger={['click']}
        >
          <Button type="text" icon={<MoreOutlined />} />
        </Dropdown>
      ),
    },
  ];

  const filterOptions = [
    {
      key: 'status',
      label: 'Status',
      options: [
        { value: 'active', label: 'Active' },
        { value: 'inactive', label: 'Inactive' },
      ],
    },
  ];

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <Title level={2} className="m-0">Service Categories</Title>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={handleAddCategory}
          size="large"
        >
          Add Category
        </Button>
      </div>

      <DataTable
        columns={columns}
        dataSource={categories}
        rowKey="id"
        loading={loading}
        filterOptions={filterOptions}
        onSearch={(value) => {
          console.log('Search:', value);
        }}
        onFilter={(filters) => {
          console.log('Filters:', filters);
        }}
        onExport={() => {
          console.log('Export categories');
        }}
      />

      <Modal
        title={editingCategory ? 'Edit Category' : 'Add New Category'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        onOk={form.submit}
        confirmLoading={loading}
        width={600}
        destroyOnClose
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSaveCategory}
          className="mt-4"
        >
          <Form.Item
            name="name"
            label="Category Name"
            rules={[{ required: true, message: 'Please enter category name' }]}
          >
            <Input placeholder="Enter category name" />
          </Form.Item>

          <Form.Item
            name="description"
            label="Description"
            rules={[{ required: true, message: 'Please enter description' }]}
          >
            <TextArea
              rows={4}
              placeholder="Enter category description"
            />
          </Form.Item>

          <Form.Item
            name="status"
            label="Status"
            rules={[{ required: true, message: 'Please select status' }]}
          >
            <Select placeholder="Select status">
              <Option value="active">Active</Option>
              <Option value="inactive">Inactive</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>

      {/* Load Ionicons */}
      <script type="module" src="https://unpkg.com/ionicons@7.1.0/dist/ionicons/ionicons.esm.js"></script>
      <script noModule src="https://unpkg.com/ionicons@7.1.0/dist/ionicons/ionicons.js"></script>
    </div>
  );
};

export default Categories;