package com.yotelohago.common;

import io.quarkus.runtime.StartupEvent;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.enterprise.event.Observes;
import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.transaction.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Service to reset PostgreSQL sequences after application startup
 * This prevents primary key conflicts between manually inserted seed data and auto-generated IDs
 */
@ApplicationScoped
public class SequenceResetService {

    private static final Logger logger = LoggerFactory.getLogger(SequenceResetService.class);

    @PersistenceContext
    EntityManager entityManager;

    /**
     * Reset sequences after application startup to prevent ID conflicts
     * This runs after import.sql has been executed and all seed data is loaded
     */
    @Transactional
    void onStart(@Observes StartupEvent ev) {
        try {
            logger.info("🔄 Resetting database sequences to prevent ID conflicts...");

            // Reset messages sequence to start after the highest existing ID
            resetSequence("messages_id_seq", "messages");
            
            // Reset bookings sequence to start after the highest existing ID
            resetSequence("bookings_id_seq", "bookings");
            
            // Reset reviews sequence to start after the highest existing ID
            resetSequence("reviews_id_seq", "reviews");

            // Note: work_schedules no longer uses auto-generated sequences
            // It uses professional_id (UUID) as primary key, so no sequence reset needed

            logger.info("✅ Database sequences reset successfully");

        } catch (Exception e) {
            logger.error("❌ Failed to reset database sequences", e);
            // Don't throw exception to prevent application startup failure
            // The sequences will just start from 1, which might cause conflicts
        }
    }

    /**
     * Reset a specific sequence to start after the highest existing ID in the table
     */
    private void resetSequence(String sequenceName, String tableName) {
        try {
            // Get the current maximum ID from the table
            Long maxId = (Long) entityManager.createNativeQuery(
                "SELECT COALESCE(MAX(id), 0) FROM " + tableName
            ).getSingleResult();

            // Set the sequence to start from maxId + 1
            long nextValue = maxId + 1;
            
            entityManager.createNativeQuery(
                "SELECT setval('" + sequenceName + "', " + nextValue + ", false)"
            ).getSingleResult();

            logger.info("🔧 Reset sequence {} to start from {} (table {} max ID: {})", 
                       sequenceName, nextValue, tableName, maxId);

        } catch (Exception e) {
            logger.warn("⚠️ Failed to reset sequence {} for table {}: {}", 
                       sequenceName, tableName, e.getMessage());
        }
    }
}
