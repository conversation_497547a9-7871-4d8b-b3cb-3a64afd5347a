package com.yotelohago.media.api;

import java.time.Instant;
import java.util.UUID;

/**
 * Response DTO containing presigned URL and upload metadata
 */
public class PresignedUrlResponseDTO {

    /**
     * The presigned URL for uploading the file
     */
    public String uploadUrl;

    /**
     * The file path/key where the file will be stored
     */
    public String filePath;

    /**
     * Media ID that will be created after successful upload
     */
    public UUID mediaId;

    /**
     * Expiration time of the presigned URL
     */
    public Instant expiresAt;

    /**
     * Maximum file size allowed for this upload
     */
    public Long maxFileSize;

    /**
     * Instructions for the client on how to use the presigned URL
     */
    public String instructions;

    /**
     * Default constructor
     */
    public PresignedUrlResponseDTO() {}

    /**
     * Constructor for creating response DTOs
     */
    public PresignedUrlResponseDTO(String uploadUrl, String filePath, UUID mediaId, 
                                   Instant expiresAt, Long maxFileSize, String instructions) {
        this.uploadUrl = uploadUrl;
        this.filePath = filePath;
        this.mediaId = mediaId;
        this.expiresAt = expiresAt;
        this.maxFileSize = maxFileSize;
        this.instructions = instructions;
    }
}
