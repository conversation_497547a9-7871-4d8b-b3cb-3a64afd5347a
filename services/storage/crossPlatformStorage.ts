import * as SecureStore from 'expo-secure-store';
import { Platform } from 'react-native';

/**
 * Cross-platform storage service that works on both web and mobile
 * Uses SecureStore on mobile and localStorage on web
 */
class CrossPlatformStorage {
  private isWeb = Platform.OS === 'web';

  /**
   * Store a value
   */
  async setItem(key: string, value: string): Promise<void> {
    try {
      if (this.isWeb) {
        if (typeof window !== 'undefined') {
          localStorage.setItem(key, value);
        }
      } else {
        await SecureStore.setItemAsync(key, value);
      }
    } catch (error) {
      console.error(`Failed to store ${key}:`, error);
      throw new Error(`Failed to store ${key}`);
    }
  }

  /**
   * Retrieve a value
   */
  async getItem(key: string): Promise<string | null> {
    try {
      if (this.isWeb) {
        if (typeof window !== 'undefined') {
          return localStorage.getItem(key);
        }
        return null;
      } else {
        return await SecureStore.getItemAsync(key);
      }
    } catch (error) {
      console.error(`Failed to retrieve ${key}:`, error);
      return null;
    }
  }

  /**
   * Remove a value
   */
  async removeItem(key: string): Promise<void> {
    try {
      if (this.isWeb) {
        if (typeof window !== 'undefined') {
          localStorage.removeItem(key);
        }
      } else {
        await SecureStore.deleteItemAsync(key);
      }
    } catch (error) {
      console.error(`Failed to remove ${key}:`, error);
    }
  }

  /**
   * Clear all items with a specific prefix
   */
  async clearItemsWithPrefix(prefix: string): Promise<void> {
    try {
      if (this.isWeb) {
        if (typeof window !== 'undefined') {
          const keysToRemove: string[] = [];
          for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && key.startsWith(prefix)) {
              keysToRemove.push(key);
            }
          }
          keysToRemove.forEach(key => localStorage.removeItem(key));
        }
      } else {
        // For mobile, we need to track keys manually since SecureStore doesn't have enumeration
        // This is a limitation - we'll need to store a list of keys
        const keysListKey = `${prefix}_keys_list`;
        const keysList = await this.getItem(keysListKey);
        if (keysList) {
          const keys = JSON.parse(keysList);
          await Promise.all(keys.map((key: string) => this.removeItem(key)));
          await this.removeItem(keysListKey);
        }
      }
    } catch (error) {
      console.error(`Failed to clear items with prefix ${prefix}:`, error);
    }
  }

  /**
   * Store a key in the keys list (for mobile cleanup)
   */
  private async addToKeysList(prefix: string, key: string): Promise<void> {
    if (this.isWeb) return; // Not needed for web

    try {
      const keysListKey = `${prefix}_keys_list`;
      const existingKeys = await this.getItem(keysListKey);
      const keys = existingKeys ? JSON.parse(existingKeys) : [];
      if (!keys.includes(key)) {
        keys.push(key);
        await this.setItem(keysListKey, JSON.stringify(keys));
      }
    } catch (error) {
      console.error('Failed to update keys list:', error);
    }
  }

  /**
   * Store with prefix tracking (for mobile cleanup)
   */
  async setItemWithPrefix(prefix: string, key: string, value: string): Promise<void> {
    await this.setItem(key, value);
    await this.addToKeysList(prefix, key);
  }

  /**
   * Get current path (cross-platform)
   */
  getCurrentPath(): string {
    if (this.isWeb && typeof window !== 'undefined') {
      return window.location.pathname;
    }
    return '/client/(tabs)'; // fallback for mobile
  }

  /**
   * Session storage equivalent (temporary storage)
   */
  async setSessionItem(key: string, value: string): Promise<void> {
    if (this.isWeb) {
      if (typeof window !== 'undefined' && typeof window.sessionStorage !== 'undefined') {
        window.sessionStorage.setItem(key, value);
      }
    } else {
      // For mobile, use regular storage with session prefix
      await this.setItem(`session_${key}`, value);
    }
  }

  async getSessionItem(key: string): Promise<string | null> {
    if (this.isWeb) {
      if (typeof window !== 'undefined' && typeof window.sessionStorage !== 'undefined') {
        return window.sessionStorage.getItem(key);
      }
      return null;
    } else {
      return await this.getItem(`session_${key}`);
    }
  }

  async removeSessionItem(key: string): Promise<void> {
    if (this.isWeb) {
      if (typeof window !== 'undefined' && typeof window.sessionStorage !== 'undefined') {
        window.sessionStorage.removeItem(key);
      }
    } else {
      await this.removeItem(`session_${key}`);
    }
  }
}

// Export singleton instance
export const crossPlatformStorage = new CrossPlatformStorage();
export default crossPlatformStorage;
