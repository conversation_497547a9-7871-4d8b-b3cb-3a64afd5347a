resource "aws_instance" "servers" {

  ami                    = data.aws_ami.amazon-linux-2.id
  instance_type          = var.instance_type
  key_name               = aws_key_pair.rsa_key.key_name
  vpc_security_group_ids = [aws_security_group.ec2_security_group.id]
  subnet_id              = aws_subnet.ec2_subnets[0].id

  connection {
    type        = "ssh"
    user        = var.ec2_user  # Replace with the appropriate username for your EC2 instance
    private_key = file(var.ec2_key_path)  # Replace with the path to your private key
    host        = self.public_ip
  }

  # File provisioner to copy a file from local to the remote EC2 instance
#   provisioner "file" {
#     source      = "./flask-app/lambda_shortest_path.py"  # Replace with the path to your local file
#     destination = "/home/<USER>/lambda_shortest_path.py"  # Replace with the path on the remote instance
#   }

  #provisioner "remote-exec" {
  #  inline = [
  #     "echo 'Hello from the remote instance'",
#
  #    #"sudo amazon-linux-extras enable python3.8",
  #    #"sudo yum clean metadata",
  #    #"sudo yum install python3.8",
  #    #"sudo alternatives --install /usr/bin/python3 python3 /usr/bin/python3.8 2"
#
  #     ]
  #}

  tags = {
    Name = "${var.resource_base_name}"
  }

}

## Since we automate everything using Makefile we use wait until VM is is accessible so ansible can reach it.
resource "null_resource" "wait_for_ssh" {
  depends_on = [aws_instance.servers]

  provisioner "remote-exec" {
    inline = ["echo Instance is ready"]
    connection {
      type        = "ssh"
      user        = var.ec2_user
      private_key = file(var.ec2_key_path)
      host        = aws_instance.servers.public_ip
    }
  }
}