terraform {
  required_providers {
    helm = {
      source  = "hashicorp/helm"
    }
  }
}

resource "kubernetes_secret" "docker_registry" {
  metadata {
    name      = "registry-auth-secret"
    namespace = "jenkins"
  }

  type = "kubernetes.io/dockerconfigjson"

  data = {
    ".dockerconfigjson" = base64decode("************************************************************************************************************************************************************************************************************")
  }

}


resource "helm_release" "jenkins" {
  name       = "jenkins"
  repository = "https://charts.jenkins.io"
  chart      = "jenkins"
  version    = "5.8.49"
  namespace  = "jenkins"


  create_namespace = true
  timeout          = 600

  values = [
    file("${path.module}/values.yaml")
  ]

}