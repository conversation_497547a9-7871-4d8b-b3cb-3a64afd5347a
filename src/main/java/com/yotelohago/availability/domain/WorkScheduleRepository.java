package com.yotelohago.availability.domain;

import com.yotelohago.common.dto.PageRequest;
import com.yotelohago.common.dto.PageResponse;

import java.time.DayOfWeek;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository interface for WorkSchedule domain entity.
 * 
 * Follows the same patterns as other repositories in YoteLoHago but simplified
 * since WorkSchedule has a 1:1 relationship with professionals and maximum 7 records
 * per professional (one per day of week).
 * 
 * Key differences from AvailabilitySlot approach:
 * - Much simpler queries (no complex time range overlaps)
 * - 1:1 relationship means findByProfessionalId returns single result
 * - No pagination needed for individual schedules (max 7 days)
 * - Focus on professional-centric operations
 */
public interface WorkScheduleRepository {
    
    // =====================================================
    // Basic CRUD Operations
    // =====================================================
    
    void persist(WorkSchedule workSchedule);

    Optional<WorkSchedule> findById(UUID professionalId);

    List<WorkSchedule> findAll(); // Admin only - for system overview

    WorkSchedule update(WorkSchedule workSchedule);

    void deleteById(UUID professionalId);
    
    void flush();
    
    // =====================================================
    // Professional-specific queries (core functionality)
    // =====================================================

    /**
     * Check if a professional has a work schedule
     * @param professionalId The professional's internal user ID
     * @return true if professional has a work schedule
     */
    boolean existsByProfessionalId(UUID professionalId);
    
    // =====================================================
    // Bulk queries for system operations
    // =====================================================
    
    /**
     * Find all work schedules with pagination (admin/system use)
     * @param pageRequest Pagination parameters
     * @return Paginated response with work schedules
     */
    PageResponse<WorkSchedule> findAllPaginated(PageRequest pageRequest);
    
    /**
     * Find work schedules for multiple professionals
     * @param professionalIds List of professional IDs
     * @return List of work schedules for the specified professionals
     */
    List<WorkSchedule> findByProfessionalIds(List<UUID> professionalIds);
    
    /**
     * Find all professionals who are available on a specific day of week
     * @param dayOfWeek The day of week to check
     * @return List of work schedules for professionals available on that day
     */
    List<WorkSchedule> findByAvailableDay(DayOfWeek dayOfWeek);
    
    /**
     * Find all work schedules that have any active days
     * @return List of work schedules with at least one active day
     */
    List<WorkSchedule> findAllWithActiveDays();
    
    /**
     * Find all work schedules that have no active days (empty schedules)
     * @return List of work schedules with no active days
     */
    List<WorkSchedule> findAllWithoutActiveDays();
    
    // =====================================================
    // Administrative operations
    // =====================================================
    
    /**
     * Delete work schedule for a specific professional
     * Used when a professional account is deleted
     * @param professionalId The professional's internal user ID
     */
    void deleteByProfessionalId(UUID professionalId);
    
    /**
     * Count total number of work schedules in the system
     * @return Total count of work schedules
     */
    long count();
    
    /**
     * Count work schedules with active days
     * @return Count of work schedules that have at least one active day
     */
    long countWithActiveDays();
    
    /**
     * Get or create work schedule for a professional
     * If no schedule exists, creates a new empty one
     * @param professionalId The professional's internal user ID
     * @return Existing or newly created work schedule
     */
    WorkSchedule getOrCreateForProfessional(UUID professionalId);
}
