package com.yotelohago.availability.infrastructure;

import com.yotelohago.availability.domain.WorkSchedule;
import com.yotelohago.availability.domain.WorkScheduleRepository;
import com.yotelohago.common.dto.PageRequest;
import com.yotelohago.common.dto.PageResponse;
import com.yotelohago.user.domain.User;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;

import java.time.DayOfWeek;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * PostgreSQL implementation of WorkScheduleRepository.
 * 
 * Much simpler than AvailabilitySlot repository because:
 * - 1:1 relationship with professionals (no complex filtering)
 * - Maximum 7 records per professional (no pagination needed for individual schedules)
 * - No complex time overlap queries (handled by domain logic)
 * - Focus on professional-centric operations
 */
@ApplicationScoped
public class WorkScheduleRepositoryPostgres implements WorkScheduleRepository {

    @PersistenceContext
    EntityManager entityManager;

    // =====================================================
    // Basic CRUD Operations
    // =====================================================

    @Override
    public void persist(WorkSchedule workSchedule) {
        entityManager.persist(workSchedule);
    }

    @Override
    public Optional<WorkSchedule> findById(UUID professionalId) {
        return Optional.ofNullable(entityManager.find(WorkSchedule.class, professionalId));
    }

    @Override
    public List<WorkSchedule> findAll() {
        return entityManager.createQuery("FROM WorkSchedule ORDER BY professionalId ASC", WorkSchedule.class)
                .getResultList();
    }

    @Override
    public WorkSchedule update(WorkSchedule workSchedule) {
        return entityManager.merge(workSchedule);
    }

    @Override
    public void deleteById(UUID professionalId) {
        WorkSchedule workSchedule = entityManager.find(WorkSchedule.class, professionalId);
        if (workSchedule != null) {
            entityManager.remove(workSchedule);
        }
    }

    @Override
    public void flush() {
        entityManager.flush();
    }

    // =====================================================
    // Professional-specific queries
    // =====================================================

    @Override
    public boolean existsByProfessionalId(UUID professionalId) {
        return entityManager.find(WorkSchedule.class, professionalId) != null;
    }

    // =====================================================
    // Bulk queries for system operations
    // =====================================================

    @Override
    public PageResponse<WorkSchedule> findAllPaginated(PageRequest pageRequest) {
        String jpql = "FROM WorkSchedule ORDER BY professionalId ASC";

        TypedQuery<WorkSchedule> query = entityManager.createQuery(jpql, WorkSchedule.class);
        query.setFirstResult(pageRequest.getOffset());
        query.setMaxResults(pageRequest.getSize());

        List<WorkSchedule> content = query.getResultList();

        // Count total elements
        Long totalElements = entityManager.createQuery(
                "SELECT COUNT(w) FROM WorkSchedule w", Long.class)
                .getSingleResult();

        return PageResponse.of(content, totalElements, pageRequest.getPage(), pageRequest.getSize());
    }

    @Override
    public List<WorkSchedule> findByProfessionalIds(List<UUID> professionalIds) {
        if (professionalIds == null || professionalIds.isEmpty()) {
            return List.of();
        }

        return entityManager.createQuery(
                "FROM WorkSchedule w WHERE w.professionalId IN :professionalIds ORDER BY w.professionalId ASC",
                WorkSchedule.class)
                .setParameter("professionalIds", professionalIds)
                .getResultList();
    }

    @Override
    public List<WorkSchedule> findByAvailableDay(DayOfWeek dayOfWeek) {
        // This query checks if the work schedule has any time range for the specified day
        return entityManager.createQuery(
                "SELECT DISTINCT w FROM WorkSchedule w " +
                "JOIN w.timeRanges tr " +
                "WHERE tr.dayOfWeek = :dayOfWeek " +
                "ORDER BY w.professionalId ASC",
                WorkSchedule.class)
                .setParameter("dayOfWeek", dayOfWeek)
                .getResultList();
    }

    @Override
    public List<WorkSchedule> findAllWithActiveDays() {
        // Find work schedules that have at least one time range
        return entityManager.createQuery(
                "SELECT DISTINCT w FROM WorkSchedule w " +
                "WHERE SIZE(w.timeRanges) > 0 " +
                "ORDER BY w.professionalId ASC",
                WorkSchedule.class)
                .getResultList();
    }

    @Override
    public List<WorkSchedule> findAllWithoutActiveDays() {
        // Find work schedules that have no time ranges
        return entityManager.createQuery(
                "FROM WorkSchedule w " +
                "WHERE SIZE(w.timeRanges) = 0 " +
                "ORDER BY w.professionalId ASC",
                WorkSchedule.class)
                .getResultList();
    }

    // =====================================================
    // Administrative operations
    // =====================================================

    @Override
    public void deleteByProfessionalId(UUID professionalId) {
        entityManager.createQuery("DELETE FROM WorkSchedule w WHERE w.professionalId = :professionalId")
                .setParameter("professionalId", professionalId)
                .executeUpdate();
    }

    @Override
    public long count() {
        return entityManager.createQuery("SELECT COUNT(w) FROM WorkSchedule w", Long.class)
                .getSingleResult();
    }

    @Override
    public long countWithActiveDays() {
        return entityManager.createQuery(
                "SELECT COUNT(DISTINCT w) FROM WorkSchedule w " +
                "WHERE SIZE(w.timeRanges) > 0",
                Long.class)
                .getSingleResult();
    }

    @Override
    public WorkSchedule getOrCreateForProfessional(UUID professionalId) {
        Optional<WorkSchedule> existing = findById(professionalId);
        if (existing.isPresent()) {
            return existing.get();
        }

        // Create new work schedule for the professional
        User professional = entityManager.find(User.class, professionalId);
        if (professional == null) {
            throw new IllegalArgumentException("Professional not found: " + professionalId);
        }

        WorkSchedule newSchedule = new WorkSchedule(professional);
        persist(newSchedule);
        flush();

        return newSchedule;
    }
}
