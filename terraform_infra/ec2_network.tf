
resource "aws_vpc" "ec2_vpc" {
  cidr_block = var.vpc_cidr

  tags = {
    terraform = "true"
    Name = "${var.resource_base_name}-ec2-vpc"
  }
}

resource "aws_internet_gateway" "ec2_igw" {
  vpc_id = aws_vpc.ec2_vpc.id
}

resource "aws_subnet" "ec2_subnets" {
  count             = local.subnets_length
  vpc_id            = aws_vpc.ec2_vpc.id
  cidr_block        = var.subnet_cidrs[count.index]
  availability_zone = element(var.availability_zones, count.index)
  map_public_ip_on_launch = true # It means use subnet for instance launch

  tags = {
    Name = "${var.resource_base_name}-ec2-subnet"
  }
}


resource "aws_route_table" "ec2_route_table" {

  vpc_id = aws_vpc.ec2_vpc.id

  route {
    cidr_block = "0.0.0.0/0"
    gateway_id = aws_internet_gateway.ec2_igw.id
  }

  tags = {
    Name = "${var.resource_base_name}-ec2-main-route-table"
  }
}

resource "aws_main_route_table_association" "main" {
  vpc_id         = aws_vpc.ec2_vpc.id
  route_table_id = aws_route_table.ec2_route_table.id
}

# Create a security group for the ALB
resource "aws_security_group" "ec2_security_group" {
  name        = "${var.resource_base_name}-ec2-security-group"
  description = "Allow HTTP traffic"
  vpc_id      = aws_vpc.ec2_vpc.id

  // Request 
  ingress {
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # https
  ingress {
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  ingress {
    description = "SSH From Anywhere or Your-IP"
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # For k3s
  ingress {
    from_port   = 6443
    to_port     = 6443
    protocol    = "tcp"
    cidr_blocks = ["***************/32"] # 0.0.0.0/0 It is for testing please dont do this on PRODUCTION!!!
  }

  # For nginx
  ingress {
    from_port   = 30080
    to_port     = 30080
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name = "${var.resource_base_name}-ec2-security-group"
  }
}
