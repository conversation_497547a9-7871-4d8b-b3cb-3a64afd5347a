---

- name: Install required packages
  package:
    name:
      - curl
      - wget      #Another tool like curl — some installers prefer it.
      - conntrack #Required by Minikube and Kubernetes to manage connection tracking.
      - git
      - unzip     #Some Kubernetes-related CLI tools are shipped as .zip
      - firewalld #Control traffic between pods, nodes, Jenkins, etc.
    state: present

- name: Start and enable firewalld
  service:
    name: firewalld
    enabled: true
    state: started

- name: Install required dependencies for Docker
  dnf:
    name:
      - yum-utils
      - device-mapper-persistent-data
      - lvm2
    state: present

- name: Add Docker CE repo
  get_url:
    url: https://download.docker.com/linux/centos/docker-ce.repo
    dest: /etc/yum.repos.d/docker-ce.repo

- name: Install Docker CE
  dnf:
    name:
      - docker-ce
      - docker-ce-cli
      - containerd.io
    state: present

- name: Start and enable Docker
  service:
    name: docker
    enabled: true
    state: started

- name: Add ec2-user to docker group
  user:
    name: ec2-user
    groups: docker
    append: yes

- name: Download Minikube
  get_url:
    url: https://storage.googleapis.com/minikube/releases/latest/minikube-linux-amd64
    dest: /usr/local/bin/minikube
    mode: '0755'

- name: Get latest kubectl version (follow redirects)
  shell: curl -Ls https://dl.k8s.io/release/stable.txt
  register: kubectl_version
  changed_when: false

- name: Download kubectl binary
  get_url:
    url: "https://dl.k8s.io/release/{{ kubectl_version.stdout }}/bin/linux/amd64/kubectl"  #  quiet and follows redirects to get the version
    dest: /usr/local/bin/kubectl
    mode: '0755'

- name: Start Minikube using Docker driver
  become_user: ec2-user
  shell: |
    minikube start --driver=docker
  environment:
    CHANGE_MINIKUBE_NONE_USER: "true"