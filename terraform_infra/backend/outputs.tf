output "bucket" {
  description = "The name of the created S3 bucket."
  value       = aws_s3_bucket.terraform_state.bucket
}

output "region" {
  value       = var.region
}

output "bucket_key_terraform_infra" {
  value       = var.bucket_key_terraform_infra
}

output "bucket_key_terraform_helm" {
  value       = var.bucket_key_terraform_helm
}

output "dynamodb_table" {
  description = "The name of the created DynamoDB table."
  value       = aws_dynamodb_table.terraform_locks.name
}
