import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, StatusBar } from 'react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import Colors from '../client/constants/Colors';
import AuthPrompt from '../../components/auth/AuthPrompt';
import { useAuth } from '../../contexts/AuthContext';
import crossPlatformStorage from '../../services/storage/crossPlatformStorage';

export default function AuthRequiredScreen() {
  const router = useRouter();
  const params = useLocalSearchParams();
  const { state: authState, refreshAuthState } = useAuth();
  const [isNavigating, setIsNavigating] = React.useState(false);

  // Extract parameters passed from the protected action
  const actionType = (params.actionType as string) || 'general';
  const returnPath = (params.returnPath as string) || '/client/(tabs)';
  const professionalId = params.professionalId as string;
  const professionalName = params.professionalName as string;
  const actionName = params.actionName as string;

  const handleNavigation = async () => {
    if (isNavigating) {
      console.log('⚠️ Navigation already in progress, ignoring navigation');
      return;
    }

    setIsNavigating(true);
    console.log('🔄 Navigating to:', returnPath);

    // Clean up session storage
    try {
      await Promise.all([
        crossPlatformStorage.removeSessionItem('auth_redirect_path'),
        crossPlatformStorage.removeSessionItem('auth_action_type'),
        crossPlatformStorage.removeSessionItem('auth_action_name'),
        crossPlatformStorage.removeSessionItem('auth_professional_id'),
        crossPlatformStorage.removeSessionItem('auth_professional_name'),
      ]);
    } catch (error) {
      console.error('Failed to clean up auth session storage:', error);
    }

    // After Keycloak authentication, the navigation stack is broken
    // We need to reconstruct the proper navigation flow

    if (returnPath.includes('/client/professional/')) {
      // For professional screens, we need to:
      // 1. Go to search tab first (to establish the base)
      // 2. Then navigate to professional screen

      // First navigate to search tab
      router.replace('/client/(tabs)');

      // Then navigate to professional screen after a small delay
      setTimeout(() => {
        const professionalId = returnPath.split('/').pop();
        router.push({
          pathname: '/client/professional/[id]',
          params: { id: professionalId }
        });
      }, 100);
    } else {
      // For other paths, navigate directly
      router.replace(returnPath);
    }
  };

  const handleBack = () => {
    handleNavigation();
  };

  const handleContinue = () => {
    handleNavigation();
  };

  const handleAuthSuccess = async () => {
    // Store the return path for redirect after login
    try {
      await crossPlatformStorage.setSessionItem('auth_redirect_path', returnPath);
      console.log('🔄 Storing return path for professional action:', returnPath);
    } catch (error) {
      console.error('Failed to store return path:', error);
    }

    // Trigger auth state refresh to sync UI
    setTimeout(() => {
      console.log('🔄 Triggering auth state refresh...');
      refreshAuthState();
    }, 1000); // Small delay to ensure login completes
  };

  // Check auth state when component mounts
  useEffect(() => {
    console.log('🔄 Auth screen mounted, checking auth state...');
    refreshAuthState();
  }, []);

  // Debug auth state changes (but don't trigger any actions)
  useEffect(() => {
    console.log('🔍 Auth screen - Auth state changed:', {
      isAuthenticated: authState.isAuthenticated,
      isLoading: authState.isLoading,
      hasUser: !!authState.user,
      returnPath,
    });

    // IMPORTANT: Don't trigger any navigation here
    // Let user manually click Continue button
  }, [authState.isAuthenticated, authState.isLoading, authState.user, returnPath]);

  const getPromptContent = () => {
    switch (actionType) {
      case 'booking':
        return {
          title: 'Login Required for Booking',
          message: professionalName 
            ? `Please log in to ${actionName?.toLowerCase() || 'book services'} with ${professionalName}.`
            : 'Please log in to book services and manage your appointments.',
        };
      case 'messaging':
        return {
          title: 'Login Required for Messaging',
          message: professionalName
            ? `Please log in to message ${professionalName} directly.`
            : 'Please log in to message professionals and view your conversations.',
        };
      default:
        return {
          title: 'Login Required',
          message: professionalName
            ? `Please log in to ${actionName?.toLowerCase() || 'access this feature'} with ${professionalName}.`
            : 'Please log in to access this feature.',
        };
    }
  };

  const { title, message } = getPromptContent();

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={Colors.background.primary} />
      
      {/* Header with back button */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={handleBack}
          disabled={isNavigating}
        >
          <Ionicons name="arrow-back" size={24} color={Colors.text.primary} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Authentication Required</Text>
        <View style={styles.headerSpacer} />
      </View>

      {/* Auth prompt content */}
      <View style={styles.content}>
        {authState.isAuthenticated ? (
          <View style={styles.successContainer}>
            <Ionicons name="checkmark-circle" size={64} color={Colors.success} />
            <Text style={styles.successTitle}>You&apos;re logged in!</Text>
            <Text style={styles.successMessage}>
              You can now {actionName?.toLowerCase() || 'access this feature'} with {professionalName}.
            </Text>
            <TouchableOpacity
              style={styles.continueButton}
              onPress={handleContinue}
              disabled={isNavigating}
            >
              <Text style={styles.continueButtonText}>Continue</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <View style={styles.authPrompt}>
            <AuthPrompt
              title={title}
              message={message}
              actionType={actionType as any}
              onAuthSuccess={handleAuthSuccess}
            />

            {/* Debug: Manual refresh button */}
            <TouchableOpacity
              style={styles.debugButton}
              onPress={() => {
                console.log('🔄 Manual auth refresh triggered');
                refreshAuthState();
              }}
            >
              <Text style={styles.debugButtonText}>🔄 Refresh Auth State (Debug)</Text>
            </TouchableOpacity>
          </View>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.primary,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 60,
    paddingBottom: 16,
    backgroundColor: Colors.background.primary,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.background.secondary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    flex: 1,
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text.primary,
    textAlign: 'center',
    marginHorizontal: 16,
  },
  headerSpacer: {
    width: 40, // Same width as back button for centering
  },
  content: {
    flex: 1,
    justifyContent: 'center',
  },
  authPrompt: {
    flex: 1,
    justifyContent: 'center',
  },
  successContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  successTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.text.primary,
    textAlign: 'center',
    marginTop: 24,
    marginBottom: 12,
  },
  successMessage: {
    fontSize: 16,
    color: Colors.text.secondary,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 32,
    maxWidth: 300,
  },
  continueButton: {
    backgroundColor: Colors.primary,
    paddingHorizontal: 32,
    paddingVertical: 16,
    borderRadius: 8,
    minWidth: 200,
    alignItems: 'center',
  },
  continueButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },

  debugButton: {
    backgroundColor: Colors.background.secondary,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 6,
    marginTop: 20,
    alignItems: 'center',
  },
  debugButtonText: {
    fontSize: 12,
    color: Colors.text.secondary,
  },
});
