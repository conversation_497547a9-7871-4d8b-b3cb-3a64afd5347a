package com.yotelohago.booking.domain;

import com.yotelohago.booking.api.BookingWithDetailsDTO;
import com.yotelohago.common.dto.PageRequest;
import com.yotelohago.common.dto.PageResponse;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface BookingRepository {
    void persist(Booking booking);
    Optional<Booking> findById(Long id);
    List<Booking> findAll(); // Legacy method - use paginated versions for better performance
    List<Booking> findByClientId(UUID clientId);
    List<Booking> findByProfessionalId(UUID professionalId);
    List<Booking> findByClientAndProfessional(UUID clientId, UUID professionalId);
    Booking update(Booking booking);
    void deleteById(Long id);
    void flush();

    // Pagination methods for basic bookings
    PageResponse<Booking> findAllPaginated(PageRequest pageRequest);
    PageResponse<Booking> findByUserPaginated(UUID userId, PageRequest pageRequest);
    PageResponse<Booking> findByStatusPaginated(BookingStatus status, PageRequest pageRequest);
    PageResponse<Booking> findByUserAndStatusPaginated(UUID userId, BookingStatus status, PageRequest pageRequest);

    // Pagination methods for enriched bookings with professional and service details
    PageResponse<BookingWithDetailsDTO> findAllWithDetailsPaginated(PageRequest pageRequest);
    PageResponse<BookingWithDetailsDTO> findByUserWithDetailsPaginated(UUID userId, PageRequest pageRequest);
    PageResponse<BookingWithDetailsDTO> findByStatusWithDetailsPaginated(BookingStatus status, PageRequest pageRequest);
    PageResponse<BookingWithDetailsDTO> findByUserAndStatusWithDetailsPaginated(UUID userId, BookingStatus status, PageRequest pageRequest);
}