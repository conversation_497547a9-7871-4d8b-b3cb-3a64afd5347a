import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import AuthButton from './AuthButton';
import crossPlatformStorage from '../../services/storage/crossPlatformStorage';
import { useAuth } from '../../contexts/AuthContext';
import { UserMode } from '../../types/auth';

interface AuthPromptProps {
  title?: string;
  message?: string;
  actionType?: 'booking' | 'messaging' | 'favorites' | 'general';
  onAuthSuccess?: () => void;
  style?: any;
}

export default function AuthPrompt({
  title = 'Login Required',
  message = 'Please log in to access this feature.',
  actionType = 'general',
  tabIcon,
  onAuthSuccess,
  style,
}: AuthPromptProps) {
  const { state: authState } = useAuth();

  const getIcon = () => {
    // Use tabIcon if provided (matches tab icon)
    if (tabIcon) {
      return tabIcon;
    }

    // Fallback to action type based icons
    switch (actionType) {
      case 'booking':
        return 'calendar-outline';
      case 'messaging':
        return 'chatbubble-outline';
      case 'favorites':
        return 'heart-outline';
      default:
        return 'lock-closed-outline';
    }
  };

  const getIconColor = () => {
    // Simple: Black for professional mode, coral for client mode
    if (authState.currentMode === UserMode.PROFESSIONAL) {
      return '#000000'; // Black for all professional mode icons
    }
    return '#FF5A5F'; // Coral for all client mode icons
  };

  return (
    <View style={[styles.container, style]}>
      <View style={styles.iconContainer}>
        <Ionicons 
          name={getIcon()} 
          size={48} 
          color={getIconColor()} 
        />
      </View>
      
      <Text style={styles.title}>{title}</Text>
      <Text style={styles.message}>{message}</Text>
      
      <AuthButton
        variant="primary"
        size="large"
        style={styles.authButton}
        onLoginSuccess={async () => {
          // Store current path for redirect using cross-platform storage
          try {
            const currentPath = crossPlatformStorage.getCurrentPath();
            await crossPlatformStorage.setSessionItem('auth_redirect_path', currentPath);
            console.log('🔄 Storing redirect path from prompt:', currentPath);
          } catch (error) {
            console.error('Failed to store redirect path:', error);
          }
          onAuthSuccess?.();
        }}
      />
      
      <View style={styles.benefitsContainer}>
        <Text style={styles.benefitsTitle}>With an account you can:</Text>
        <View style={styles.benefitsList}>
          <View style={styles.benefitItem}>
            <Ionicons name="checkmark-circle" size={16} color="#408900" />
            <Text style={styles.benefitText}>Book and manage services</Text>
          </View>
          <View style={styles.benefitItem}>
            <Ionicons name="checkmark-circle" size={16} color="#408900" />
            <Text style={styles.benefitText}>Message professionals directly</Text>
          </View>
          <View style={styles.benefitItem}>
            <Ionicons name="checkmark-circle" size={16} color="#408900" />
            <Text style={styles.benefitText}>Save your favorite services</Text>
          </View>
          <View style={styles.benefitItem}>
            <Ionicons name="checkmark-circle" size={16} color="#408900" />
            <Text style={styles.benefitText}>Track your service history</Text>
          </View>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
    backgroundColor: '#FFFFFF',
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#F7F7F7',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#222222',
    textAlign: 'center',
    marginBottom: 12,
  },
  message: {
    fontSize: 16,
    color: '#717171',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 32,
    maxWidth: 300,
  },
  authButton: {
    minWidth: 200,
    marginBottom: 32,
  },
  benefitsContainer: {
    width: '100%',
    maxWidth: 300,
  },
  benefitsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#222222',
    marginBottom: 16,
    textAlign: 'center',
  },
  benefitsList: {
    gap: 12,
  },
  benefitItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  benefitText: {
    fontSize: 14,
    color: '#717171',
    flex: 1,
  },
});
