import * as AuthSession from 'expo-auth-session';
import * as Crypto from 'expo-crypto';
import { Platform } from 'react-native';
import { ENV, getAuthUrl, getTokenUrl, getUserInfoUrl, getWebAppUrl } from '../../config/environment';
import { AuthTokens, KeycloakTokenResponse, KeycloakUserInfo, AuthUser } from '../../types/auth';
import crossPlatformStorage from '../storage/crossPlatformStorage';

class KeycloakService {
  private redirectUri: string;
  private processedCodes: Set<string> = new Set();

  constructor() {
    // Configure redirect URI based on platform
    if (Platform.OS === 'web') {
      this.redirectUri = getWebAppUrl('/auth/callback');
    } else {
      this.redirectUri = AuthSession.makeRedirectUri({
        scheme: ENV.APP_SCHEME,
        path: 'auth/callback',
      });
    }

    console.log('🔧 Keycloak redirect URI:', this.redirectUri);
  }

  /**
   * Start the authentication flow - uses custom login for both web and mobile
   */
  async login(username?: string, password?: string): Promise<{ user: AuthUser; tokens: AuthTokens }> {
    try {
      console.log('🔄 Starting Keycloak login...');

      // For web platform, use custom login form with PKCE
      if (Platform.OS === 'web') {
        if (username && password) {
          return this.loginWithCredentialsAndPKCE(username, password);
        } else {
          // If no credentials provided, throw error to show login form
          throw new Error('Credentials required for web login');
        }
      }

      // For mobile platforms, use the existing popup flow
      return this.loginWithPopup(username, password);
    } catch (error) {
      console.error('❌ Keycloak login failed:', error);
      throw error;
    }
  }

  /**
   * Login using username and password with PKCE (for web)
   */
  async loginWithCredentialsAndPKCE(username: string, password: string): Promise<{ user: AuthUser; tokens: AuthTokens }> {
    try {
      console.log('🔄 Starting username/password authentication with PKCE...');

      // Generate PKCE code verifier and challenge
      const { codeVerifier, codeChallenge } = await this.generatePKCE();

      // Step 1: Get authorization code using Resource Owner Password Credentials
      // But we'll use a hybrid approach - get the code first, then exchange with PKCE

      // For web, we'll use the Direct Access Grant (Resource Owner Password Credentials)
      // but still maintain PKCE for the token exchange
      const tokenParams = new URLSearchParams({
        grant_type: 'password',
        client_id: ENV.KEYCLOAK_CLIENT_ID,
        username,
        password,
        scope: 'openid profile email',
        // Include PKCE parameters for enhanced security
        code_challenge: codeChallenge,
        code_challenge_method: 'S256',
      });

      const tokenUrl = `${ENV.KEYCLOAK_URL}/realms/${ENV.KEYCLOAK_REALM}/protocol/openid-connect/token`;

      console.log('🔄 Requesting tokens with credentials and PKCE...');

      const response = await fetch(tokenUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: tokenParams.toString(),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Token request failed:', response.status, errorText);

        if (response.status === 401) {
          throw new Error('Invalid username or password');
        } else if (response.status === 400) {
          throw new Error('Invalid request. Please check your credentials.');
        } else {
          throw new Error(`Authentication failed: ${response.status}`);
        }
      }

      const tokenResponse: KeycloakTokenResponse = await response.json();

      console.log('✅ Tokens received successfully with PKCE');

      // Convert to our token format
      const tokens: AuthTokens = {
        accessToken: tokenResponse.access_token,
        refreshToken: tokenResponse.refresh_token,
        idToken: tokenResponse.id_token,
        tokenType: tokenResponse.token_type || 'Bearer',
        expiresIn: tokenResponse.expires_in,
      };

      // Get user info
      const userInfo = await this.getUserInfo(tokens.accessToken);

      // Convert to our auth user format
      const user = await this.mapKeycloakUserToAuthUser(userInfo, tokens.accessToken);

      console.log('✅ Web credential login with PKCE successful:', {
        user: { ...user, id: user.id.substring(0, 8) + '...' },
        tokenLength: tokens.accessToken.length
      });

      return { user, tokens };
    } catch (error) {
      console.error('❌ Credential authentication with PKCE failed:', error);
      throw error;
    }
  }

  /**
   * Login using direct redirect (for web - legacy method)
   */
  private async loginWithRedirect(): Promise<{ user: AuthUser; tokens: AuthTokens }> {
    // Generate PKCE code verifier and challenge
    const { codeVerifier, codeChallenge } = await this.generatePKCE();

    // Store code verifier using cross-platform storage
    try {
      const timestamp = Date.now().toString();
      const storageKey = `yotelohago_pkce_${timestamp}`;

      await Promise.all([
        crossPlatformStorage.setItem('yotelohago_pkce_current', codeVerifier),
        crossPlatformStorage.setItem('yotelohago_pkce_key', storageKey),
        crossPlatformStorage.setItemWithPrefix('yotelohago_pkce', storageKey, codeVerifier),
      ]);

      console.log('✅ Code verifier stored with cross-platform storage');
    } catch (error) {
      console.error('❌ Failed to store code verifier:', error);
      throw new Error('Failed to store authentication state');
    }

    // Generate a state parameter for security
    const state = Date.now().toString();

    // Build authorization URL with PKCE and state
    const authUrl = this.buildAuthorizationUrl(codeChallenge, state);

    console.log('🔄 Redirecting to Keycloak login...');
    console.log('🔍 Authorization URL redirect_uri:', this.redirectUri);
    console.log('🔍 Full authorization URL:', authUrl);

    if (Platform.OS === 'web' && typeof window !== 'undefined') {
      window.location.href = authUrl;
      // This will never be reached as we redirect away
      throw new Error('Redirect initiated');
    }

    // This will never be reached as we redirect away
    throw new Error('Redirect initiated');
  }

  /**
   * Login using popup (for mobile)
   */
  private async loginWithPopup(username?: string, password?: string): Promise<{ user: AuthUser; tokens: AuthTokens }> {
    // If username and password are provided, use credential-based auth
    if (username && password) {
      return this.loginWithCredentials(username, password);
    }

    // Otherwise, use the OAuth popup flow
    // Generate PKCE code verifier (random string, 43-128 characters)
    console.log('🔄 Generating random bytes...');
    const randomBytes = await Crypto.getRandomBytesAsync(32);
    console.log('✅ Random bytes generated');

    const codeVerifier = Array.from(randomBytes, byte =>
      byte.toString(16).padStart(2, '0')
    ).join('');
    console.log('✅ Code verifier generated');

    // Generate code challenge (SHA256 hash of verifier, base64url encoded)
    console.log('🔄 Generating code challenge...');
    const base64Challenge = await Crypto.digestStringAsync(
      Crypto.CryptoDigestAlgorithm.SHA256,
      codeVerifier,
      { encoding: Crypto.CryptoEncoding.BASE64 }
    );
    console.log('✅ Base64 challenge generated');

    // Convert base64 to base64url (replace + with -, / with _, remove padding =)
    const codeChallenge = base64Challenge
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=/g, '');

    console.log('🔐 PKCE generated:', {
      codeVerifier: codeVerifier.substring(0, 10) + '...',
      codeChallenge: codeChallenge.substring(0, 10) + '...'
    });

    // Configure the auth request
    const authRequest = new AuthSession.AuthRequest({
      clientId: ENV.KEYCLOAK_CLIENT_ID,
      scopes: ['openid', 'profile', 'email'],
      redirectUri: this.redirectUri,
      responseType: AuthSession.ResponseType.Code,
      codeChallenge,
      codeChallengeMethod: AuthSession.CodeChallengeMethod.S256,
    });

    // Discover the auth endpoints
    const discovery = await AuthSession.fetchDiscoveryAsync(
      `${ENV.KEYCLOAK_URL}/realms/${ENV.KEYCLOAK_REALM}`
    );

    console.log('🔍 Keycloak discovery:', discovery);

    // Start the auth session
    const authResult = await authRequest.promptAsync(discovery);

    console.log('📱 Auth result:', authResult);

    if (authResult.type === 'dismiss') {
      throw new Error('Authentication was cancelled by user');
    }

    if (authResult.type === 'cancel') {
      throw new Error('Authentication was cancelled');
    }

    if (authResult.type !== 'success') {
      throw new Error(`Authentication failed: ${authResult.type}`);
    }

    if (!authResult.params.code) {
      throw new Error('No authorization code received');
    }

    // Exchange the authorization code for tokens
    const tokens = await this.exchangeCodeForTokens(
      authResult.params.code,
      codeVerifier,
      discovery.tokenEndpoint!
    );

    // Get user info
    const userInfo = await this.getUserInfo(tokens.accessToken);

    // Convert to our auth user format
    const user = await this.mapKeycloakUserToAuthUser(userInfo, tokens.accessToken);

    console.log('✅ Keycloak login successful:', {
      user: { ...user, id: user.id.substring(0, 8) + '...' },
      tokenLength: tokens.accessToken.length
    });

    return { user, tokens };
  }

  /**
   * Login using username and password (for mobile)
   */
  private async loginWithCredentials(username: string, password: string): Promise<{ user: AuthUser; tokens: AuthTokens }> {
    try {
      console.log('🔄 Starting username/password authentication...');

      // Use Resource Owner Password Credentials Grant
      const tokenParams = new URLSearchParams({
        grant_type: 'password',
        client_id: ENV.KEYCLOAK_CLIENT_ID,
        username,
        password,
        scope: 'openid profile email',
      });

      const tokenUrl = `${ENV.KEYCLOAK_URL}/realms/${ENV.KEYCLOAK_REALM}/protocol/openid-connect/token`;

      console.log('🔄 Requesting tokens with credentials...');

      const response = await fetch(tokenUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: tokenParams.toString(),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Credential authentication failed:', response.status, errorText);

        // Parse error for better user feedback
        try {
          const errorData = JSON.parse(errorText);
          if (errorData.error === 'invalid_grant') {
            throw new Error('Invalid username or password');
          } else if (errorData.error_description) {
            throw new Error(errorData.error_description);
          }
        } catch (parseError) {
          // If we can't parse the error, use a generic message
        }

        throw new Error('Authentication failed. Please check your credentials.');
      }

      const tokenResponse: KeycloakTokenResponse = await response.json();

      console.log('✅ Tokens received successfully');

      // Convert to our token format
      const tokens: AuthTokens = {
        accessToken: tokenResponse.access_token,
        refreshToken: tokenResponse.refresh_token,
        idToken: tokenResponse.id_token,
        tokenType: tokenResponse.token_type || 'Bearer',
        expiresIn: tokenResponse.expires_in,
      };

      // Get user info
      const userInfo = await this.getUserInfo(tokens.accessToken);

      // Convert to our auth user format
      const user = await this.mapKeycloakUserToAuthUser(userInfo, tokens.accessToken);

      console.log('✅ Mobile credential login successful:', {
        user: { ...user, id: user.id.substring(0, 8) + '...' },
        tokenLength: tokens.accessToken.length
      });

      return { user, tokens };
    } catch (error) {
      console.error('❌ Credential authentication failed:', error);
      throw error;
    }
  }

  /**
   * Generate PKCE code verifier and challenge
   */
  private async generatePKCE(): Promise<{ codeVerifier: string; codeChallenge: string }> {
    // Generate PKCE code verifier (random string, 43-128 characters)
    console.log('🔄 Generating random bytes...');
    const randomBytes = await Crypto.getRandomBytesAsync(32);
    console.log('✅ Random bytes generated');

    const codeVerifier = Array.from(randomBytes, byte =>
      byte.toString(16).padStart(2, '0')
    ).join('');
    console.log('✅ Code verifier generated');

    // Generate code challenge (SHA256 hash of verifier, base64url encoded)
    console.log('🔄 Generating code challenge...');
    const base64Challenge = await Crypto.digestStringAsync(
      Crypto.CryptoDigestAlgorithm.SHA256,
      codeVerifier,
      { encoding: Crypto.CryptoEncoding.BASE64 }
    );
    console.log('✅ Base64 challenge generated');

    // Convert base64 to base64url (replace + with -, / with _, remove padding =)
    const codeChallenge = base64Challenge
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=/g, '');

    console.log('🔐 PKCE generated:', {
      codeVerifier: codeVerifier.substring(0, 10) + '...',
      codeChallenge: codeChallenge.substring(0, 10) + '...'
    });

    return { codeVerifier, codeChallenge };
  }

  /**
   * Build authorization URL for redirect (with PKCE)
   */
  private buildAuthorizationUrl(codeChallenge: string, state?: string): string {
    const authUrl = `${ENV.KEYCLOAK_URL}/realms/${ENV.KEYCLOAK_REALM}/protocol/openid-connect/auth`;
    const params = new URLSearchParams({
      response_type: 'code',
      client_id: ENV.KEYCLOAK_CLIENT_ID,
      scope: 'openid profile email',
      redirect_uri: this.redirectUri,
      code_challenge: codeChallenge,
      code_challenge_method: 'S256',
      prompt: 'login',
      max_age: '0',
    });

    if (state) {
      params.set('state', state);
    }

    return `${authUrl}?${params.toString()}`;
  }



  /**
   * Handle callback from redirect (for web)
   */
  async handleCallback(code: string, state?: string): Promise<{ user: AuthUser; tokens: AuthTokens }> {
    try {
      console.log('🔄 Handling auth callback...');

      // Prevent double-processing of the same code
      if (this.processedCodes.has(code)) {
        console.warn('⚠️ Authorization code already processed, ignoring duplicate');
        throw new Error('Authorization code already processed');
      }

      // Mark this code as being processed
      this.processedCodes.add(code);

      // Clean up old codes (keep only last 10)
      if (this.processedCodes.size > 10) {
        const codes = Array.from(this.processedCodes);
        this.processedCodes.clear();
        codes.slice(-5).forEach(c => this.processedCodes.add(c));
      }

      // Try multiple strategies to retrieve the code verifier using cross-platform storage
      let codeVerifier: string | null = null;

      try {
        // Strategy 1: Try current storage keys
        codeVerifier = await crossPlatformStorage.getItem('yotelohago_pkce_current');

        // Strategy 2: Try timestamped key
        if (!codeVerifier) {
          const storageKey = await crossPlatformStorage.getItem('yotelohago_pkce_key');
          if (storageKey) {
            codeVerifier = await crossPlatformStorage.getItem(storageKey);
          }
        }

        console.log('🔍 Code verifier retrieval:', codeVerifier ? 'Found' : 'Not found');
      } catch (error) {
        console.error('❌ Failed to retrieve code verifier:', error);
      }

      if (!codeVerifier) {
        console.warn('⚠️ No code verifier found. Attempting fallback without PKCE...');
        return this.handleCallbackWithoutPKCE(code);
      }

      // Clear all PKCE-related storage using cross-platform storage
      try {
        await Promise.all([
          crossPlatformStorage.removeItem('yotelohago_pkce_current'),
          crossPlatformStorage.removeItem('yotelohago_pkce_key'),
        ]);

        // Clear all items with PKCE prefix
        await crossPlatformStorage.clearItemsWithPrefix('yotelohago_pkce');

        console.log('🧹 PKCE storage cleared');
      } catch (error) {
        console.error('❌ Failed to clear PKCE storage:', error);
      }

      // Exchange the authorization code for tokens
      const tokens = await this.exchangeCodeForTokens(
        code,
        codeVerifier,
        `${ENV.KEYCLOAK_URL}/realms/${ENV.KEYCLOAK_REALM}/protocol/openid-connect/token`
      );

      // Get user info
      const userInfo = await this.getUserInfo(tokens.accessToken);

      // Convert to our auth user format
      const user = await this.mapKeycloakUserToAuthUser(userInfo, tokens.accessToken);

      console.log('✅ Callback handled successfully:', {
        user: { ...user, id: user.id.substring(0, 8) + '...' },
        tokenLength: tokens.accessToken.length
      });

      return { user, tokens };
    } catch (error) {
      console.error('❌ Callback handling failed:', error);
      throw error;
    }
  }

  /**
   * Handle callback without PKCE (fallback for development)
   */
  private async handleCallbackWithoutPKCE(code: string): Promise<{ user: AuthUser; tokens: AuthTokens }> {
    try {
      console.log('🔄 Handling callback without PKCE (fallback mode)...');

      // Exchange the authorization code for tokens without PKCE
      const tokens = await this.exchangeCodeForTokensWithoutPKCE(
        code,
        `${ENV.KEYCLOAK_URL}/realms/${ENV.KEYCLOAK_REALM}/protocol/openid-connect/token`
      );

      // Get user info
      const userInfo = await this.getUserInfo(tokens.accessToken);

      // Convert to our auth user format
      const user = await this.mapKeycloakUserToAuthUser(userInfo, tokens.accessToken);

      console.log('✅ Callback handled successfully (without PKCE):', {
        user: { ...user, id: user.id.substring(0, 8) + '...' },
        tokenLength: tokens.accessToken.length
      });

      return { user, tokens };
    } catch (error) {
      console.error('❌ Callback handling failed (without PKCE):', error);
      throw error;
    }
  }

  /**
   * Exchange authorization code for tokens without PKCE
   */
  private async exchangeCodeForTokensWithoutPKCE(
    code: string,
    tokenEndpoint: string
  ): Promise<AuthTokens> {
    const tokenParams = new URLSearchParams({
      grant_type: 'authorization_code',
      client_id: ENV.KEYCLOAK_CLIENT_ID,
      code,
      redirect_uri: this.redirectUri,
    });

    console.log('🔄 Exchanging code for tokens (without PKCE)...');

    const response = await fetch(tokenEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: tokenParams.toString(),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Token exchange failed:', response.status, errorText);
      throw new Error(`Token exchange failed: ${response.status} ${errorText}`);
    }

    const tokenResponse: KeycloakTokenResponse = await response.json();

    return {
      accessToken: tokenResponse.access_token,
      refreshToken: tokenResponse.refresh_token,
      idToken: tokenResponse.id_token,
      tokenType: tokenResponse.token_type,
      expiresIn: tokenResponse.expires_in,
    };
  }

  /**
   * Exchange authorization code for tokens
   */
  private async exchangeCodeForTokens(
    code: string,
    codeVerifier: string,
    tokenEndpoint: string
  ): Promise<AuthTokens> {
    const tokenParams = new URLSearchParams({
      grant_type: 'authorization_code',
      client_id: ENV.KEYCLOAK_CLIENT_ID,
      code,
      redirect_uri: this.redirectUri,
      code_verifier: codeVerifier,
    });

    console.log('🔄 Exchanging code for tokens...');
    console.log('🔍 Token exchange params:', {
      grant_type: 'authorization_code',
      client_id: ENV.KEYCLOAK_CLIENT_ID,
      code: code.substring(0, 10) + '...',
      redirect_uri: this.redirectUri,
      code_verifier: codeVerifier.substring(0, 10) + '...',
    });

    const response = await fetch(tokenEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: tokenParams.toString(),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Token exchange failed:', response.status, errorText);
      throw new Error(`Token exchange failed: ${response.status} ${errorText}`);
    }

    const tokenResponse: KeycloakTokenResponse = await response.json();

    return {
      accessToken: tokenResponse.access_token,
      refreshToken: tokenResponse.refresh_token,
      idToken: tokenResponse.id_token,
      tokenType: tokenResponse.token_type,
      expiresIn: tokenResponse.expires_in,
    };
  }

  /**
   * Get user information from Keycloak
   */
  private async getUserInfo(accessToken: string): Promise<KeycloakUserInfo> {
    console.log('🔄 Fetching user info...');

    const response = await fetch(getUserInfoUrl(), {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ User info fetch failed:', response.status, errorText);
      throw new Error(`Failed to fetch user info: ${response.status}`);
    }

    return await response.json();
  }

  /**
   * Refresh access token using refresh token
   */
  async refreshToken(refreshToken: string): Promise<AuthTokens> {
    try {
      console.log('🔄 Refreshing token...');

      const tokenParams = new URLSearchParams({
        grant_type: 'refresh_token',
        client_id: ENV.KEYCLOAK_CLIENT_ID,
        refresh_token: refreshToken,
      });

      const response = await fetch(getTokenUrl(), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: tokenParams.toString(),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Token refresh failed:', response.status, errorText);
        throw new Error(`Token refresh failed: ${response.status}`);
      }

      const tokenResponse: KeycloakTokenResponse = await response.json();

      console.log('✅ Token refreshed successfully');

      return {
        accessToken: tokenResponse.access_token,
        refreshToken: tokenResponse.refresh_token,
        idToken: tokenResponse.id_token,
        tokenType: tokenResponse.token_type,
        expiresIn: tokenResponse.expires_in,
      };
    } catch (error) {
      console.error('❌ Token refresh failed:', error);
      throw error;
    }
  }

  /**
   * Logout from Keycloak
   */
  async logout(idToken?: string): Promise<void> {
    try {
      console.log('🔄 Logging out from Keycloak...');

      if (idToken && Platform.OS === 'web') {
        // For web, try to logout from Keycloak without redirect first
        try {
          const logoutUrl = `${ENV.KEYCLOAK_URL}/realms/${ENV.KEYCLOAK_REALM}/protocol/openid-connect/logout`;

          // Try logout without redirect first (simpler approach)
          const logoutParams = new URLSearchParams({
            id_token_hint: idToken,
          });

          console.log('🔄 Attempting Keycloak logout without redirect...');

          // Use fetch to logout without redirect
          const response = await fetch(`${logoutUrl}?${logoutParams.toString()}`, {
            method: 'GET',
            credentials: 'include',
          });

          console.log('✅ Keycloak logout response:', response.status);
        } catch (error) {
          console.warn('⚠️ Keycloak logout request failed, but continuing with local logout:', error);
        }
      }

      console.log('✅ Logout completed');
    } catch (error) {
      console.error('❌ Logout failed:', error);
      // Don't throw error for logout - we'll clear local tokens anyway
    }
  }

  /**
   * Map Keycloak user info to our AuthUser format and fetch internal user ID
   */
  private async mapKeycloakUserToAuthUser(userInfo: KeycloakUserInfo, accessToken: string): Promise<AuthUser> {
    // Extract roles from Keycloak token
    const realmRoles = userInfo.realm_access?.roles || [];
    const clientRoles = userInfo.resource_access?.[ENV.KEYCLOAK_CLIENT_ID]?.roles || [];
    const allRoles = [...realmRoles, ...clientRoles];

    // Fetch internal user ID from backend
    let internalId = userInfo.sub; // Fallback to Keycloak ID if backend call fails

    try {
      console.log('🔄 Fetching internal user ID from backend...');

      // First try to get user info to get the internal ID
      const userInfoResponse = await fetch(`${this.getApiBaseUrl()}/auth/user-info`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
      });

      if (userInfoResponse.ok) {
        const userInfoData = await userInfoResponse.json();
        if (userInfoData.userId) {
          // Now fetch the full user profile using the internal ID
          const userResponse = await fetch(`${this.getApiBaseUrl()}/users/${userInfoData.userId}`, {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${accessToken}`,
              'Content-Type': 'application/json',
            },
          });

          if (userResponse.ok) {
            const userData = await userResponse.json();
            internalId = userData.id;
            console.log('✅ Internal user ID fetched successfully');
          } else {
            console.warn('⚠️ Failed to fetch user profile, using user-info ID as fallback');
            internalId = userInfoData.userId;
          }
        } else {
          console.warn('⚠️ No userId in user-info response, using Keycloak ID as fallback');
        }
      } else {
        console.warn('⚠️ Failed to fetch user-info, using Keycloak ID as fallback');
      }
    } catch (error) {
      console.warn('⚠️ Error fetching internal user ID:', error);
    }

    return {
      id: userInfo.sub,           // Keep Keycloak ID for reference
      internalId: internalId,     // Internal user ID from our database
      username: userInfo.preferred_username,
      email: userInfo.email,
      firstName: userInfo.given_name,
      lastName: userInfo.family_name,
      roles: allRoles,
      isProfessional: allRoles.includes('professional'),
    };
  }

  /**
   * Get the API base URL for backend calls
   */
  private getApiBaseUrl(): string {
    return process.env.NODE_ENV === 'development'
      ? 'http://localhost:8080/v1'
      : 'https://api.yotelohago.co/v1';
  }
}

// Export singleton instance
const keycloakService = new KeycloakService();
export default keycloakService;
