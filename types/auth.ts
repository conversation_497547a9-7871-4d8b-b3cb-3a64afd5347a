// Authentication Types
export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
  idToken?: string;
  tokenType: string;
  expiresIn: number;
  scope?: string;
}

export interface AuthUser {
  id: string;           // Keycloak ID (external ID)
  internalId: string;   // Internal user ID from our database
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  roles: string[];
  isProfessional: boolean;
}

// User mode for frontend navigation
export enum UserMode {
  CLIENT = 'client',
  PROFESSIONAL = 'professional'
}

export interface AuthState {
  isAuthenticated: boolean;
  isLoading: boolean;
  user: AuthUser | null;
  tokens: AuthTokens | null;
  error: string | null;
  currentMode: UserMode;
}

// Keycloak Token Response
export interface KeycloakTokenResponse {
  access_token: string;
  refresh_token: string;
  id_token?: string;
  token_type: string;
  expires_in: number;
  refresh_expires_in: number;
  scope?: string;
  session_state?: string;
}

// Keycloak User Info Response
export interface KeycloakUserInfo {
  sub: string;
  email_verified: boolean;
  name: string;
  preferred_username: string;
  given_name: string;
  family_name: string;
  email: string;
  realm_access?: {
    roles: string[];
  };
  resource_access?: {
    [key: string]: {
      roles: string[];
    };
  };
}

// Auth Request Types
export interface LoginRequest {
  username: string;
  password: string;
}

export interface RegisterRequest {
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  password: string;
  confirmPassword: string;
}

// Auth Error Types
export interface AuthError {
  error: string;
  error_description?: string;
  error_uri?: string;
}

// Auth Configuration
export interface AuthConfig {
  clientId: string;
  redirectUri: string;
  scopes: string[];
  additionalParameters?: Record<string, string>;
  customHeaders?: Record<string, string>;
}

// Token Storage Keys
export enum TokenStorageKeys {
  ACCESS_TOKEN = 'access_token',
  REFRESH_TOKEN = 'refresh_token',
  ID_TOKEN = 'id_token',
  TOKEN_EXPIRY = 'token_expiry',
  USER_INFO = 'user_info',
  USER_MODE = 'user_mode'
}

// Auth Action Types for Context/State Management
export enum AuthActionType {
  LOGIN_START = 'LOGIN_START',
  LOGIN_SUCCESS = 'LOGIN_SUCCESS',
  LOGIN_FAILURE = 'LOGIN_FAILURE',
  LOGOUT = 'LOGOUT',
  TOKEN_REFRESH_START = 'TOKEN_REFRESH_START',
  TOKEN_REFRESH_SUCCESS = 'TOKEN_REFRESH_SUCCESS',
  TOKEN_REFRESH_FAILURE = 'TOKEN_REFRESH_FAILURE',
  RESTORE_SESSION = 'RESTORE_SESSION',
  CLEAR_ERROR = 'CLEAR_ERROR',
  SET_USER_MODE = 'SET_USER_MODE'
}

export interface AuthAction {
  type: AuthActionType;
  payload?: any;
}

// Role-based access types
export enum UserRole {
  USER = 'user',
  PROFESSIONAL = 'professional',
  ADMIN = 'admin'
}

export interface RolePermissions {
  canCreateBooking: boolean;
  canManageServices: boolean;
  canViewAllBookings: boolean;
  canManageUsers: boolean;
}
