package com.yotelohago.favorite.domain;

import io.quarkus.hibernate.orm.panache.PanacheEntityBase;
import jakarta.persistence.*;
import org.hibernate.annotations.CreationTimestamp;
import com.yotelohago.user.domain.User;
import com.yotelohago.service.domain.Service;

import java.time.Instant;
import java.util.UUID;

@Entity
@Table(name = "favorites")
@IdClass(FavoriteId.class)
public class Favorite extends PanacheEntityBase {

    @Id
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "user_id", nullable = false,
                foreignKey = @ForeignKey(name = "fk_favorites_user"))
    public User user;

    @Id
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "service_id", nullable = false,
                foreignKey = @ForeignKey(name = "fk_favorites_service"))
    public Service service;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    public Instant createdAt;

    public Favorite() {
        // JPA constructor
    }

    public Favorite(User user, Service service) {
        this.user = user;
        this.service = service;
        // createdAt will be set automatically by @CreationTimestamp
    }

    // Getters and setters
    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public Service getService() {
        return service;
    }

    public void setService(Service service) {
        this.service = service;
    }



    public Instant getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        Favorite favorite = (Favorite) o;

        if (!user.id.equals(favorite.user.id)) return false;
        return service.id.equals(favorite.service.id);
    }

    @Override
    public int hashCode() {
        int result = user.id.hashCode();
        result = 31 * result + service.id.hashCode();
        return result;
    }

    @Override
    public String toString() {
        return "Favorite{" +
                "userId=" + user.id +
                ", serviceId=" + service.id +
                ", createdAt=" + createdAt +
                '}';
    }
}
