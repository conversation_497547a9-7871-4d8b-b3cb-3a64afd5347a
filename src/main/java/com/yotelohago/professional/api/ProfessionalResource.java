package com.yotelohago.professional.api;

import com.yotelohago.common.ApiVersion;
import com.yotelohago.professional.application.ProfessionalManagementService;
import com.yotelohago.professional.domain.Professional;
import jakarta.annotation.security.RolesAllowed;
import jakarta.inject.Inject;
import jakarta.validation.Valid;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * REST API for Professional domain operations
 * Follows the same coding convention as Service, Booking, Media, and Favorite domains:
 * - Uses consolidated ProfessionalWithDetailsDTO for all responses
 * - URLs are pre-built in repository SQL queries
 * - Proper authorization controls for sensitive operations
 */
@Path(ApiVersion.BASE + "/professionals")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public class ProfessionalResource {

    private static final Logger logger = LoggerFactory.getLogger(ProfessionalResource.class);

    @Inject
    ProfessionalManagementService service;

    @Inject
    ProfessionalMapper professionalMapper;



    /**
     * Get professional with complete details by professional ID - Public access
     * Consolidated endpoint that includes all professional and user information
     * Uses efficient SQL-based URL building following established coding convention
     *
     * @param professionalId the professional user ID
     * @return Response containing ProfessionalWithDetailsDTO with all data and pre-built URLs
     */
    @GET
    @Path("/{professionalId}")
    public Response getProfessionalWithDetails(@PathParam("professionalId") UUID professionalId) {
        try {
            ProfessionalWithDetailsDTO professional = service.getProfessionalWithDetails(professionalId);
            return Response.ok(professional).build();
        } catch (IllegalArgumentException e) {
            return Response.status(Response.Status.BAD_REQUEST)
                    .entity("{\"error\": \"" + e.getMessage() + "\"}")
                    .build();
        } catch (jakarta.ws.rs.NotFoundException e) {
            return Response.status(Response.Status.NOT_FOUND)
                    .entity("{\"error\": \"" + e.getMessage() + "\"}")
                    .build();
        } catch (Exception e) {
            logger.error("❌ Failed to retrieve professional with details: {}", professionalId, e);
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("{\"error\": \"Failed to retrieve professional profile\"}")
                    .build();
        }
    }

    /**
     * Create professional profile - Professional or admin only
     */
    @POST
    @RolesAllowed({"admin", "professional"})
    public Response create(@Valid ProfessionalRequestDTO dto) {
        try {
            if (dto == null) {
                return Response.status(Response.Status.BAD_REQUEST)
                        .entity("{\"error\": \"Professional data is required\"}")
                        .build();
            }

            service.create(professionalMapper.toEntityFromRequest(dto));
            return Response.status(Response.Status.CREATED).build();
        } catch (ForbiddenException e) {
            return Response.status(Response.Status.FORBIDDEN)
                    .entity("{\"error\": \"" + e.getMessage() + "\"}")
                    .build();
        } catch (IllegalArgumentException e) {
            return Response.status(Response.Status.BAD_REQUEST)
                    .entity("{\"error\": \"" + e.getMessage() + "\"}")
                    .build();
        } catch (Exception e) {
            logger.error("❌ Failed to create professional profile", e);
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("{\"error\": \"Failed to create professional profile\"}")
                    .build();
        }
    }

    /**
     * Update professional profile - Professional or admin only
     */
    @PUT
    @RolesAllowed({"admin", "professional"})
    public Response update(@Valid ProfessionalRequestDTO dto) {
        try {
            if (dto == null) {
                return Response.status(Response.Status.BAD_REQUEST)
                        .entity("{\"error\": \"Professional data is required\"}")
                        .build();
            }

            service.update(professionalMapper.toEntityFromRequest(dto));
            return Response.ok().build();
        } catch (ForbiddenException e) {
            return Response.status(Response.Status.FORBIDDEN)
                    .entity("{\"error\": \"" + e.getMessage() + "\"}")
                    .build();
        } catch (IllegalArgumentException e) {
            return Response.status(Response.Status.BAD_REQUEST)
                    .entity("{\"error\": \"" + e.getMessage() + "\"}")
                    .build();
        } catch (Exception e) {
            logger.error("❌ Failed to update professional profile", e);
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("{\"error\": \"Failed to update professional profile\"}")
                    .build();
        }
    }

    /**
     * Delete professional profile - Admin only
     */
    @DELETE
    @Path("/{professionalId}")
    @RolesAllowed("admin")
    public Response delete(@PathParam("professionalId") UUID professionalId) {
        try {
            service.delete(professionalId);
            return Response.noContent().build();
        } catch (ForbiddenException e) {
            return Response.status(Response.Status.FORBIDDEN)
                    .entity("{\"error\": \"" + e.getMessage() + "\"}")
                    .build();
        } catch (IllegalArgumentException e) {
            return Response.status(Response.Status.BAD_REQUEST)
                    .entity("{\"error\": \"Invalid professional ID format\"}")
                    .build();
        } catch (Exception e) {
            logger.error("❌ Failed to delete professional profile: {}", professionalId, e);
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("{\"error\": \"Failed to delete professional profile\"}")
                    .build();
        }
    }
}
