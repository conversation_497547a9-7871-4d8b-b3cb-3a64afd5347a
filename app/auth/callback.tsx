import React, { useEffect, useState, useRef } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { useAuth } from '@/contexts/AuthContext';
import keycloakService from '@/services/auth/keycloakService';
import tokenStorage from '@/services/auth/tokenStorage';
import crossPlatformStorage from '@/services/storage/crossPlatformStorage';

export default function AuthCallback() {
  const router = useRouter();
  const params = useLocalSearchParams();
  const { state, login } = useAuth();
  const [message, setMessage] = useState('Processing authentication...');
  const hasProcessed = useRef(false);

  useEffect(() => {
    // Prevent multiple executions
    if (hasProcessed.current) {
      console.log('🔄 Already processed callback, skipping...');
      return;
    }

    console.log('🔄 Auth callback received:', params);
    console.log('🔍 Current URL:', typeof window !== 'undefined' ? window.location.href : 'N/A');

    const handleCallback = async () => {
      hasProcessed.current = true;
      try {
        // Check if this is a logout callback
        // Logout callbacks typically have no code, no error, and may have logout indicators
        const isLogoutCallback = typeof window !== 'undefined' && (
          window.location.href.includes('logout') ||
          window.location.search.includes('logout') ||
          (!params.code && !params.error && !params.state) // No auth parameters = likely logout
        );

        if (isLogoutCallback) {
          console.log('🔄 Logout callback detected');
          setMessage('Logout complete. Redirecting...');

          // Clear any remaining tokens
          try {
            await tokenStorage.clearAll();
          } catch (error) {
            console.error('Error clearing tokens:', error);
          }

          const timer = setTimeout(() => {
            router.replace('/client/(tabs)/profile');
          }, 1500);

          return () => clearTimeout(timer);
        }

        // Check if this has an authorization code (login success)
        if (params.code && typeof params.code === 'string') {
          console.log('🔄 Login callback with code detected:', params.code.substring(0, 10) + '...');
          setMessage('Completing login...');

          try {
            // Handle the callback with the authorization code and state
            const state = typeof params.state === 'string' ? params.state : undefined;
            const { user, tokens } = await keycloakService.handleCallback(params.code, state);

            // Store tokens and user info
            await tokenStorage.storeTokens(tokens);
            await tokenStorage.storeUserInfo(user);

            console.log('✅ Login completed successfully for user:', user.username);
            setMessage('Login successful! Redirecting...');

            // Check for stored redirect path using cross-platform storage
            let redirectPath = '/client/(tabs)/profile'; // default
            try {
              const storedPath = await crossPlatformStorage.getSessionItem('auth_redirect_path');
              if (storedPath) {
                // If the stored path is a professional screen, redirect back to auth screen
                // so it can detect authentication and show success state
                if (storedPath.includes('/client/professional/')) {
                  const [actionType, actionName, professionalId, professionalName] = await Promise.all([
                    crossPlatformStorage.getSessionItem('auth_action_type'),
                    crossPlatformStorage.getSessionItem('auth_action_name'),
                    crossPlatformStorage.getSessionItem('auth_professional_id'),
                    crossPlatformStorage.getSessionItem('auth_professional_name'),
                  ]);

                  redirectPath = '/auth/required?' + new URLSearchParams({
                    actionType: actionType || 'general',
                    actionName: actionName || 'Access Feature',
                    returnPath: storedPath,
                    professionalId: professionalId || '',
                    professionalName: professionalName || '',
                  }).toString();
                  console.log('🔄 Redirecting back to auth screen to show success:', redirectPath);
                } else {
                  redirectPath = storedPath;
                  await crossPlatformStorage.removeSessionItem('auth_redirect_path'); // Clean up
                  console.log('🔄 Redirecting to stored path:', redirectPath);
                }
              }
            } catch (error) {
              console.error('❌ Failed to retrieve redirect path:', error);
              // Ensure redirectPath is still valid even if there's an error
              redirectPath = '/client/(tabs)/profile';
            }

            // Redirect back to original location or profile
            const timer = setTimeout(() => {
              const finalPath = redirectPath || '/client/(tabs)/profile';
              router.replace(finalPath as any);
            }, 1500);

            return () => clearTimeout(timer);
          } catch (error) {
            console.error('❌ Failed to complete login:', error);
            setMessage(`Login failed: ${error instanceof Error ? error.message : 'Unknown error'}. Redirecting...`);

            const timer = setTimeout(() => {
              router.replace('/client/(tabs)/profile');
            }, 3000);

            return () => clearTimeout(timer);
          }
        } else if (params.error) {
          // Handle explicit error from Keycloak
          console.error('❌ Keycloak returned error:', params.error, params.error_description);
          setMessage(`Authentication failed: ${params.error_description || params.error}. Redirecting...`);

          const timer = setTimeout(() => {
            router.replace('/client/(tabs)/profile');
          }, 3000);

          return () => clearTimeout(timer);
        } else {
          console.log('🔄 Unknown callback type - no code, error, or logout detected');
          console.log('🔍 Params received:', params);
          setMessage('Completing authentication...');

          // Wait a bit for parameters to load, then redirect
          const timer = setTimeout(() => {
            console.log('ℹ️ No specific auth action detected, redirecting to profile');
            setMessage('Redirecting...');
            router.replace('/client/(tabs)/profile');
          }, 1500);

          return () => clearTimeout(timer);
        }
      } catch (error) {
        console.error('❌ Unexpected callback handling error:', error);
        setMessage(`Unexpected error: ${error instanceof Error ? error.message : 'Unknown error'}. Redirecting...`);

        const timer = setTimeout(() => {
          router.replace('/client/(tabs)/profile');
        }, 3000);

        return () => clearTimeout(timer);
      }
    };

    handleCallback();
  }, [params, router]);

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Authentication</Text>
      <Text style={styles.subtitle}>{message}</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#333',
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
  },
});
