import React, { useEffect, useRef } from 'react';
import {
  Modal,
  View,
  StyleSheet,
  Animated,
  Dimensions,
  PanResponder,
  Platform,
} from 'react-native';
import Colors from '../../app/client/constants/Colors';

interface SlideModalProps {
  visible: boolean;
  onClose: () => void;
  children: React.ReactNode;
}

const { width: screenWidth } = Dimensions.get('window');

export default function SlideModal({ visible, onClose, children }: SlideModalProps) {
  const slideAnim = useRef(new Animated.Value(screenWidth)).current;
  const overlayOpacity = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (visible) {
      // Slide in from right
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(overlayOpacity, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      // Slide out to right
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: screenWidth,
          duration: 250,
          useNativeDriver: true,
        }),
        Animated.timing(overlayOpacity, {
          toValue: 0,
          duration: 250,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [visible]);

  const panResponder = useRef(
    PanResponder.create({
      onMoveShouldSetPanResponder: (evt, gestureState) => {
        // Only respond to horizontal swipes from the left edge
        return Math.abs(gestureState.dx) > Math.abs(gestureState.dy) && gestureState.dx > 0;
      },
      onPanResponderMove: (evt, gestureState) => {
        // Update slide position based on gesture
        const newValue = Math.max(0, gestureState.dx);
        slideAnim.setValue(newValue);
        
        // Update overlay opacity
        const progress = newValue / screenWidth;
        overlayOpacity.setValue(1 - progress);
      },
      onPanResponderRelease: (evt, gestureState) => {
        const shouldClose = gestureState.dx > screenWidth * 0.3 || gestureState.vx > 0.5;
        
        if (shouldClose) {
          onClose();
        } else {
          // Snap back to open position
          Animated.parallel([
            Animated.spring(slideAnim, {
              toValue: 0,
              useNativeDriver: true,
            }),
            Animated.spring(overlayOpacity, {
              toValue: 1,
              useNativeDriver: true,
            }),
          ]).start();
        }
      },
    })
  ).current;

  if (!visible) {
    return null;
  }

  return (
    <Modal
      visible={visible}
      transparent
      animationType="none"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        {/* Overlay */}
        <Animated.View
          style={[
            styles.overlay,
            {
              opacity: overlayOpacity,
            },
          ]}
        />
        
        {/* Slide Content */}
        <Animated.View
          style={[
            styles.slideContainer,
            {
              transform: [{ translateX: slideAnim }],
            },
          ]}
          {...panResponder.panHandlers}
        >
          {children}
        </Animated.View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  slideContainer: {
    flex: 1,
    backgroundColor: Colors.background.primary,
    marginLeft: Platform.OS === 'ios' ? 0 : 0, // Can add margin for partial overlay effect
  },
});
