package com.yotelohago.service.api;

import com.yotelohago.common.ApiVersion;
import com.yotelohago.common.dto.PageRequest;
import com.yotelohago.common.dto.PageResponse;
import com.yotelohago.service.application.ServiceManagementService;

import jakarta.annotation.security.RolesAllowed;
import jakarta.inject.Inject;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Path(ApiVersion.BASE + "/services")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public class ServiceResource {

    @Inject
    ServiceManagementService service;

    @Inject
    ServiceMapper serviceMapper;

    @GET
    public Response listAll(@BeanParam PageRequest pageRequest,
                           @QueryParam("category") UUID categoryId,
                           @QueryParam("minPrice") Double minPrice,
                           @QueryParam("maxPrice") Double maxPrice,
                           @QueryParam("search") String searchTerm,
                           @QueryParam("withProfessional") @DefaultValue("true") boolean withProfessional) {
        try {
            // Use the new join queries by default for better performance (eliminates N+1 queries)
            if (withProfessional) {
                // Use new join queries that include professional data
                PageResponse<ServiceWithProfessionalDTO> pageResponse;

                if (categoryId != null || minPrice != null || maxPrice != null ||
                   (searchTerm != null && !searchTerm.trim().isEmpty())) {
                    // Use search with filters and professional data
                    pageResponse = service.searchPublishedWithProfessionalPaginated(
                        categoryId, minPrice, maxPrice, searchTerm, pageRequest);
                } else if (categoryId != null) {
                    // Filter by category with professional data
                    pageResponse = service.findByCategoryPublishedWithProfessionalPaginated(categoryId, pageRequest);
                } else {
                    // No filters, just pagination with professional data
                    pageResponse = service.findAllPublishedWithProfessionalPaginated(pageRequest);
                }

                // Return ServiceWithProfessionalDTO directly (includes professional names)
                return Response.ok(pageResponse).build();
            } else {
                // Fallback to original queries for backward compatibility
                PageResponse<com.yotelohago.service.domain.Service> pageResponse;

                if (categoryId != null || minPrice != null || maxPrice != null ||
                          (searchTerm != null && !searchTerm.trim().isEmpty())) {
                    // Use search with filters - published only
                    pageResponse = service.searchPublishedPaginated(categoryId, minPrice, maxPrice, searchTerm, pageRequest);
                } else if (categoryId != null) {
                    // Filter by category only - published only
                    pageResponse = service.findByCategoryPublishedPaginated(categoryId, pageRequest);
                } else {
                    // No filters, just pagination - published only (most used)
                    pageResponse = service.findAllPublishedPaginated(pageRequest);
                }

                // Convert to DTOs
                List<ServiceDTO> serviceDTOs = pageResponse.getContent().stream()
                        .map(serviceMapper::toDTO)
                        .collect(Collectors.toList());

                // Create paginated response with DTOs
                PageResponse<ServiceDTO> dtoPageResponse = PageResponse.of(
                    serviceDTOs,
                    pageResponse.getTotalElements(),
                    pageResponse.getNumber(),
                    pageResponse.getSize()
                );

                return Response.ok(dtoPageResponse).build();
            }
        } catch (Exception e) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("{\"error\": \"Failed to retrieve services: " + e.getMessage() + "\"}")
                    .build();
        }
    }

    @GET
    @Path("/{id}")
    public Response findById(@PathParam("id") UUID id) {
        try {
            return service.findById(id)
                    .map(serviceMapper::toDTO)
                    .map(dto -> Response.ok(dto).build())
                    .orElse(Response.status(Response.Status.NOT_FOUND)
                            .entity("{\"error\": \"Service not found\"}")
                            .build());
        } catch (IllegalArgumentException e) {
            return Response.status(Response.Status.BAD_REQUEST)
                    .entity("{\"error\": \"Invalid service ID format\"}")
                    .build();
        } catch (Exception e) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("{\"error\": \"Failed to retrieve service\"}")
                    .build();
        }
    }

    @POST
    @RolesAllowed({"professional", "admin"}) // Only professionals can create services
    public Response create(ServiceDTO dto) {
        try {
            if (dto == null) {
                return Response.status(Response.Status.BAD_REQUEST)
                        .entity("{\"error\": \"Service data is required\"}")
                        .build();
            }

            service.create(serviceMapper.toEntity(dto));
            return Response.status(Response.Status.CREATED).build();
        } catch (ForbiddenException e) {
            return Response.status(Response.Status.FORBIDDEN)
                    .entity("{\"error\": \"" + e.getMessage() + "\"}")
                    .build();
        } catch (IllegalArgumentException e) {
            return Response.status(Response.Status.BAD_REQUEST)
                    .entity("{\"error\": \"" + e.getMessage() + "\"}")
                    .build();
        } catch (Exception e) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("{\"error\": \"Failed to create service\"}")
                    .build();
        }
    }

    @PUT
    @Path("/{id}")
    @RolesAllowed({"professional", "admin"}) // Only professionals can update their services
    public Response update(@PathParam("id") UUID id, ServiceDTO dto) {
        try {
            if (dto == null) {
                return Response.status(Response.Status.BAD_REQUEST)
                        .entity("{\"error\": \"Service data is required\"}")
                        .build();
            }

            // Ensure the ID in the path matches the DTO
            dto.id = id;

            // Get the existing service first to use its professional user
            com.yotelohago.service.domain.Service existingService = service.findById(id)
                    .orElseThrow(() -> new IllegalArgumentException("Service not found"));

            // Use the new mapper method that preserves the existing professional user
            com.yotelohago.service.domain.Service serviceToUpdate = serviceMapper.toEntityForUpdate(dto, existingService);

            com.yotelohago.service.domain.Service updatedService = service.update(serviceToUpdate);
            return Response.ok(serviceMapper.toDTO(updatedService)).build();
        } catch (ForbiddenException e) {
            return Response.status(Response.Status.FORBIDDEN)
                    .entity("{\"error\": \"" + e.getMessage() + "\"}")
                    .build();
        } catch (IllegalArgumentException e) {
            return Response.status(Response.Status.NOT_FOUND)
                    .entity("{\"error\": \"" + e.getMessage() + "\"}")
                    .build();
        } catch (RuntimeException e) {
            if (e.getMessage() != null && e.getMessage().contains("not found")) {
                return Response.status(Response.Status.NOT_FOUND)
                        .entity("{\"error\": \"" + e.getMessage() + "\"}")
                        .build();
            }
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("{\"error\": \"Failed to update service\"}")
                    .build();
        } catch (Exception e) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("{\"error\": \"Failed to update service\"}")
                    .build();
        }
    }

    @DELETE
    @Path("/{id}")
    @RolesAllowed({"professional", "admin"}) // Only professionals can delete their services
    public Response delete(@PathParam("id") UUID id) {
        try {
            if (!service.delete(id)) {
                return Response.status(Response.Status.NOT_FOUND)
                        .entity("{\"error\": \"Service not found\"}")
                        .build();
            }
            return Response.noContent().build();
        } catch (ForbiddenException e) {
            return Response.status(Response.Status.FORBIDDEN)
                    .entity("{\"error\": \"" + e.getMessage() + "\"}")
                    .build();
        } catch (IllegalArgumentException e) {
            return Response.status(Response.Status.NOT_FOUND)
                    .entity("{\"error\": \"" + e.getMessage() + "\"}")
                    .build();
        } catch (Exception e) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("{\"error\": \"Failed to delete service\"}")
                    .build();
        }
    }

    // Public endpoint - get published services by professional
    @GET
    @Path("/professional/{professionalUserId}")
    public Response getPublishedServicesByProfessional(@PathParam("professionalUserId") UUID professionalUserId) {
        try {
            // Get only PUBLISHED services for public access
            List<com.yotelohago.service.domain.Service> services = service.findPublishedByProfessional(professionalUserId);

            // Convert to DTOs
            List<ServiceDTO> serviceDTOs = services.stream()
                    .map(serviceMapper::toDTO)
                    .collect(Collectors.toList());

            return Response.ok(serviceDTOs).build();
        } catch (Exception e) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("{\"error\": \"Failed to retrieve professional services: " + e.getMessage() + "\"}")
                    .build();
        }
    }

    // Professional-only endpoint - manage own services (not paginated)
    @GET
    @Path("/my-services")
    @RolesAllowed({"professional"})
    public Response getMyServices() {
        try {
            // Authorization logic is handled in ServiceManagementService
            List<com.yotelohago.service.domain.Service> services = service.findAllByCurrentProfessional();

            // Convert to DTOs
            List<ServiceDTO> serviceDTOs = services.stream()
                    .map(serviceMapper::toDTO)
                    .collect(Collectors.toList());

            return Response.ok(serviceDTOs).build();
        } catch (ForbiddenException e) {
            return Response.status(Response.Status.FORBIDDEN)
                    .entity("{\"error\": \"" + e.getMessage() + "\"}")
                    .build();
        } catch (Exception e) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("{\"error\": \"Failed to retrieve services: " + e.getMessage() + "\"}")
                    .build();
        }
    }
}