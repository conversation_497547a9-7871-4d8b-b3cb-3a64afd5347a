#!/bin/bash

# YoteLoHago Admin Library
# Functions for administrative tasks, setup, and configuration management

# Function to setup production Keycloak
setup_production_keycloak() {
    print_status $CYAN "🚀 Setting up production Keycloak..."

    print_status $BLUE "This would typically involve:"
    echo "  1. Configuring production Keycloak server"
    echo "  2. Setting up SSL certificates"
    echo "  3. Configuring realm settings"
    echo "  4. Setting up client configurations"
    echo "  5. Configuring user federation"

    print_status $YELLOW "⚠️ This is a complex setup that should be done manually"
    print_status $YELLOW "   Please refer to the Keycloak documentation for production setup"

    # Basic realm check
    check_realm_status
}

# Function to create backend client
create_backend_client() {
    local realm=${1:-"yotelohago"}
    local client_id=${2:-"yotelohago-backend"}

    print_status $CYAN "📱 Creating backend client '$client_id' for realm '$realm'..."

    local admin_token
    admin_token=$(get_admin_token)

    if [[ "$admin_token" == "null" ]]; then
        print_status $RED "❌ Failed to get admin token"
        return 1
    fi

    # Client configuration for backend service
    local client_data='{
        "clientId": "'$client_id'",
        "name": "YoteLoHago Backend Service",
        "description": "Backend service client for API access",
        "enabled": true,
        "clientAuthenticatorType": "client-secret",
        "secret": "backend-service-secret",
        "serviceAccountsEnabled": true,
        "authorizationServicesEnabled": false,
        "standardFlowEnabled": false,
        "implicitFlowEnabled": false,
        "directAccessGrantsEnabled": false,
        "publicClient": false,
        "protocol": "openid-connect"
    }'

    local response
    response=$(http_request POST "$KEYCLOAK_URL/admin/realms/$realm/clients" \
        -H "Authorization: Bearer $admin_token" \
        -H "Content-Type: application/json" \
        -d "$client_data" -s)
    local status_code
    status_code=$(echo "$response" | cut -d'|' -f1)

    if [[ "$status_code" == "201" ]]; then
        print_status $GREEN "✅ Backend client '$client_id' created successfully"
    else
        print_status $RED "❌ Failed to create backend client (Status: $status_code)"
        local body
        body=$(echo "$response" | cut -d'|' -f2-)
        echo "Response: $body"
    fi
}

# Function to add backend client to dev realm
add_backend_client_to_dev() {
    print_status $CYAN "🔧 Adding backend client to dev realm..."

    # This is essentially the same as create_backend_client for the dev realm
    create_backend_client "yotelohago" "yotelohago-backend-dev"
}

# Function to export remote realm
export_remote_realm() {
    local realm=${1:-"yotelohago"}
    local output_file=${2:-"exported-realm.json"}

    print_status $CYAN "📤 Exporting remote realm '$realm'..."

    local admin_token
    admin_token=$(get_admin_token)

    if [[ "$admin_token" == "null" ]]; then
        print_status $RED "❌ Failed to get admin token"
        return 1
    fi

    local response
    response=$(http_request GET "$KEYCLOAK_URL/admin/realms/$realm" \
        -H "Authorization: Bearer $admin_token" \
        -H "Accept: application/json" -s)
    local status_code
    status_code=$(echo "$response" | cut -d'|' -f1)

    if [[ "$status_code" == "200" ]]; then
        local body
        body=$(echo "$response" | cut -d'|' -f2-)
        echo "$body" > "$output_file"
        print_status $GREEN "✅ Realm '$realm' exported to '$output_file'"
    else
        print_status $RED "❌ Failed to export realm (Status: $status_code)"
    fi
}

# Function to fix Keycloak admin client
fix_keycloak_admin_client() {
    print_status $CYAN "🔧 Fixing Keycloak admin client..."

    local admin_token
    admin_token=$(get_admin_token)

    if [[ "$admin_token" == "null" ]]; then
        print_status $RED "❌ Failed to get admin token"
        return 1
    fi

    # Get admin-cli client
    local admin_client_response
    admin_client_response=$(http_request GET "$KEYCLOAK_URL/admin/realms/master/clients" \
        -H "Authorization: Bearer $admin_token" -s)
    local admin_client_id
    admin_client_id=$(echo "$admin_client_response" | cut -d'|' -f2- | jq -r '.[] | select(.clientId=="admin-cli") | .id')

    if [[ -z "$admin_client_id" ]] || [[ "$admin_client_id" == "null" ]]; then
        print_status $RED "❌ Admin CLI client not found"
        return 1
    fi

    # Update admin-cli client to allow service accounts
    local update_data='{
        "serviceAccountsEnabled": true,
        "authorizationServicesEnabled": false
    }'

    local response
    response=$(http_request PUT "$KEYCLOAK_URL/admin/realms/master/clients/$admin_client_id" \
        -H "Authorization: Bearer $admin_token" \
        -H "Content-Type: application/json" \
        -d "$update_data" -s)
    local status_code
    status_code=$(echo "$response" | cut -d'|' -f1)

    if [[ "$status_code" == "204" ]]; then
        print_status $GREEN "✅ Admin client fixed successfully"
    else
        print_status $RED "❌ Failed to fix admin client (Status: $status_code)"
    fi
}

# Function to fix service account roles
fix_service_account_roles() {
    print_status $CYAN "🔧 Fixing service account roles..."

    local admin_token
    admin_token=$(get_admin_token)

    if [[ "$admin_token" == "null" ]]; then
        print_status $RED "❌ Failed to get admin token"
        return 1
    fi

    # Get service account user for admin-cli
    local service_account_response
    service_account_response=$(http_request GET "$KEYCLOAK_URL/admin/realms/master/clients" \
        -H "Authorization: Bearer $admin_token" -s)
    local admin_client_id
    admin_client_id=$(echo "$service_account_response" | cut -d'|' -f2- | jq -r '.[] | select(.clientId=="admin-cli") | .id')

    if [[ -z "$admin_client_id" ]] || [[ "$admin_client_id" == "null" ]]; then
        print_status $RED "❌ Admin CLI client not found"
        return 1
    fi

    # Get service account user
    local service_user_response
    service_user_response=$(http_request GET "$KEYCLOAK_URL/admin/realms/master/clients/$admin_client_id/service-account-user" \
        -H "Authorization: Bearer $admin_token" -s)
    local service_user_id
    service_user_id=$(echo "$service_user_response" | cut -d'|' -f2- | jq -r '.id')

    if [[ -z "$service_user_id" ]] || [[ "$service_user_id" == "null" ]]; then
        print_status $YELLOW "⚠️ Service account user not found (may need to enable service accounts first)"
        return 0
    fi

    print_status $GREEN "✅ Service account roles checked"
}

# Function to test Keycloak configuration
test_keycloak_config() {
    print_status $CYAN "🧪 Testing Keycloak configuration..."

    echo ""
    print_status $BLUE "Testing realm accessibility..."
    check_realm_status

    echo ""
    print_status $BLUE "Testing OIDC configuration..."
    check_oidc_config

    echo ""
    print_status $BLUE "Testing client configurations..."
    list_clients

    echo ""
    print_status $BLUE "Testing realm roles..."
    list_realm_roles

    print_status $GREEN "✅ Keycloak configuration test completed"
}

# Function to test production configuration
test_production_config() {
    print_status $CYAN "🧪 Testing production configuration..."

    echo ""
    print_status $BLUE "Testing production API health..."
    test_api_endpoint "/q/health" "" "GET" "" "$PRODUCTION_API_URL"

    echo ""
    print_status $BLUE "Testing production authentication..."
    local token
    token=$(get_user_token "$DEFAULT_USERNAME" "$DEFAULT_PASSWORD")

    if [[ "$token" != "null" ]] && [[ -n "$token" ]]; then
        print_status $GREEN "✅ Production authentication working"
        test_api_endpoint "/v1/users/me" "$token" "GET" "" "$PRODUCTION_API_URL"
    else
        print_status $RED "❌ Production authentication failed"
    fi

    print_status $GREEN "✅ Production configuration test completed"
}

# Function to test public endpoints
test_public_endpoints() {
    print_status $CYAN "🌐 Testing public endpoints..."

    echo ""
    print_status $BLUE "📋 Testing Endpoints That Should Be PUBLIC (no auth required)"
    echo "------------------------------------------------------------"

    echo "Testing GET /v1/professionals (should be public)..."
    local response
    response=$(test_api_endpoint "/v1/professionals" "" "GET" "" "$BACKEND_URL")

    echo "Testing GET /v1/services (should be public)..."
    test_api_endpoint "/v1/services" "" "GET" "" "$BACKEND_URL"

    echo "Testing GET /v1/reviews/booking/1 (should be public)..."
    test_api_endpoint "/v1/reviews/booking/1" "" "GET" "" "$BACKEND_URL"

    echo ""
    print_status $BLUE "📋 Testing Endpoints That Should Be PROTECTED (auth required)"
    echo "-----------------------------------------------------------"

    echo "Testing GET /v1/bookings (should require auth)..."
    test_api_endpoint "/v1/bookings" "" "GET" "" "$BACKEND_URL"

    echo "Testing GET /v1/users (should require auth)..."
    test_api_endpoint "/v1/users" "" "GET" "" "$BACKEND_URL"

    echo "Testing POST /v1/services (should require auth)..."
    test_api_endpoint "/v1/services" "" "POST" '{"name":"Test Service","description":"Test","categoryId":1001,"price":100}' "$BACKEND_URL"

    echo "Testing POST /v1/bookings (should require auth)..."
    test_api_endpoint "/v1/bookings" "" "POST" '{"serviceId":1001,"notes":"Test"}' "$BACKEND_URL"

    print_status $GREEN "✅ Public endpoints test completed"
}

# Function to test user registration
test_user_registration() {
    local username=${1:-"<EMAIL>"}
    local password=${2:-"testpass123"}

    print_status $CYAN "👤 Testing user registration for '$username'..."

    # Test if user already exists
    local admin_token
    admin_token=$(get_admin_token)

    if [[ "$admin_token" == "null" ]]; then
        print_status $RED "❌ Failed to get admin token"
        return 1
    fi

    # Check if user exists
    local user_response
    user_response=$(http_request GET "$KEYCLOAK_URL/admin/realms/$REALM_NAME/users?username=$username" \
        -H "Authorization: Bearer $admin_token" -s)
    local existing_user
    existing_user=$(echo "$user_response" | cut -d'|' -f2- | jq -r '.[0].id // "null"')

    if [[ "$existing_user" != "null" ]]; then
        print_status $YELLOW "⚠️ User '$username' already exists"

        # Test authentication with existing user
        local token
        token=$(get_user_token "$username" "$password")

        if [[ "$token" != "null" ]] && [[ -n "$token" ]]; then
            print_status $GREEN "✅ User authentication successful"
        else
            print_status $RED "❌ User authentication failed"
        fi
    else
        print_status $BLUE "Creating test user '$username'..."

        # Create user using the users library function
        # Extract first and last name from email
        local first_name="Test"
        local last_name="User"

        # Use the create_user function from users.sh
        create_user "$username" "$username" "$first_name" "$last_name" "$password"

        # Test authentication with new user
        local token
        token=$(get_user_token "$username" "$password")

        if [[ "$token" != "null" ]] && [[ -n "$token" ]]; then
            print_status $GREEN "✅ User registration and authentication successful"
        else
            print_status $RED "❌ User registration or authentication failed"
        fi
    fi

    print_status $GREEN "✅ User registration test completed"
}

# Function to run all setup tasks
setup_complete_environment() {
    print_status $CYAN "🏗️ Setting up complete environment..."
    
    echo ""
    print_status $BLUE "Step 1: Setting up production Keycloak..."
    setup_production_keycloak
    
    echo ""
    print_status $BLUE "Step 2: Creating backend client..."
    create_backend_client
    
    echo ""
    print_status $BLUE "Step 3: Adding backend client to dev realm..."
    add_backend_client_to_dev
    
    echo ""
    print_status $BLUE "Step 4: Fixing admin client..."
    fix_keycloak_admin_client
    
    echo ""
    print_status $BLUE "Step 5: Fixing service account roles..."
    fix_service_account_roles
    
    echo ""
    print_status $GREEN "✅ Complete environment setup finished!"
}

# Function to run all tests
test_complete_environment() {
    print_status $CYAN "🧪 Testing complete environment..."
    
    echo ""
    print_status $BLUE "Test 1: Keycloak configuration..."
    test_keycloak_config
    
    echo ""
    print_status $BLUE "Test 2: Production configuration..."
    test_production_config
    
    echo ""
    print_status $BLUE "Test 3: Public endpoints..."
    test_public_endpoints
    
    echo ""
    print_status $BLUE "Test 4: User registration..."
    test_user_registration
    
    echo ""
    print_status $GREEN "✅ Complete environment testing finished!"
}

# Function to show admin help
admin_help() {
    cat << EOF
🔧 Admin - Administrative tasks and environment setup

SETUP COMMANDS:
    setup-production                Setup production Keycloak
    create-client [realm]           Create backend client
    add-dev-client                  Add backend client to dev realm
    setup-complete                  Run complete environment setup

MAINTENANCE COMMANDS:
    fix-admin-client               Fix Keycloak admin client
    fix-service-roles              Fix service account roles
    export-realm [realm] [file]    Export remote realm configuration

TESTING COMMANDS:
    test-keycloak-config           Test Keycloak configuration
    test-production-config         Test production configuration
    test-public-endpoints          Test public endpoints
    test-user-registration [user] [pass]  Test user registration
    test-complete                  Run all environment tests

EXAMPLES:
    $0 admin setup-production
    $0 admin create-client yotelohago
    $0 admin export-realm yotelohago backup.json
    $0 admin test-complete
    $0 admin setup-complete

EOF
}

# Main admin function
admin_main() {
    local command=$1
    shift
    
    case "$command" in
        "setup-production")
            setup_production_keycloak
            ;;
        "create-client")
            create_backend_client "$1"
            ;;
        "add-dev-client")
            add_backend_client_to_dev
            ;;
        "export-realm")
            export_remote_realm "$1" "$2"
            ;;
        "fix-admin-client")
            fix_keycloak_admin_client
            ;;
        "fix-service-roles")
            fix_service_account_roles
            ;;
        "test-keycloak-config")
            test_keycloak_config
            ;;
        "test-production-config")
            test_production_config
            ;;
        "test-public-endpoints")
            test_public_endpoints
            ;;
        "test-user-registration")
            test_user_registration "$1" "$2"
            ;;
        "setup-complete")
            setup_complete_environment
            ;;
        "test-complete")
            test_complete_environment
            ;;
        *)
            print_status $RED "❌ Unknown admin command: $command"
            admin_help
            exit 1
            ;;
    esac
}
