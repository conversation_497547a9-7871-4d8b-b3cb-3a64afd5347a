import React from 'react';
import { View, Text, StyleSheet, Image, Pressable, Dimensions, Platform, ActivityIndicator } from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import Colors from '../constants/Colors';
import { ServiceDTO, ServiceWithProfessionalDTO } from '../../../types/api';
import { ProfessionalWithUser } from '../../../services/api/professionalService';

// Default professional image
const defaultProfessionalImage = require('../../../assets/images/user.png');

interface ServiceCardProps {
  service: ServiceWithProfessionalDTO;
  onPress?: (service: ServiceWithProfessionalDTO) => void;
  onFavoritePress?: (service: ServiceWithProfessionalDTO) => void;
  isFavorite?: boolean;
  cardWidth?: number; // Optional prop for responsive width
}

export default function ServiceCard({
  service,
  onPress,
  onFavoritePress,
  isFavorite = false,
  cardWidth
}: ServiceCardProps) {
  const router = useRouter();

  // Fallback width calculation if cardWidth is not provided
  const { width: screenWidth } = Dimensions.get('window');
  const defaultWidth = screenWidth - 32; // Full width minus padding
  const finalCardWidth = cardWidth || defaultWidth;

  const handlePress = () => {
    console.log('ServiceCard pressed:', {
      serviceId: service.id,
      professionalUserId: service.professionalUserId,
      hasOnPress: !!onPress
    });

    if (onPress) {
      onPress(service);
    } else if (service.professionalUserId) {
      console.log('Navigating to professional profile:', service.professionalUserId);
      router.push({
        pathname: '/client/professional/[id]',
        params: { id: service.professionalUserId }
      });
    } else {
      console.log('No professionalUserId found for service:', service.id);
    }
  };

  return (
    <Pressable
      style={[styles.container, { width: finalCardWidth }]}
      onPress={handlePress}
    >
      <View style={styles.content}>
        <View style={styles.providerInfo}>
          <Image
            source={
              service.professionalProfileImageUrl
                ? { uri: service.professionalProfileImageUrl }
                : defaultProfessionalImage
            }
            style={styles.providerAvatar}
            resizeMode="cover"
          />
          <View style={styles.providerDetails}>
            {/* Use professional name from service data if available (Option 2 implementation) */}
            <Text style={styles.providerName}>
              {service.professionalFullName || 'Professional'}
            </Text>
            <View style={styles.ratingContainer}>
              <Ionicons name="star" size={14} color={Colors.warning} />
              <Text style={styles.rating}>
                {((service as any).professionalAvgRating?.toFixed(1) || '4.8')} ({(service as any).professionalRatingCount || 0})
              </Text>
            </View>
            {(service as any).professionalCity && (
              <Text style={styles.location}>📍 {(service as any).professionalCity}</Text>
            )}
          </View>
        </View>

        <View style={styles.serviceInfo}>
          <Text style={styles.title} numberOfLines={2}>
            {service.title}
          </Text>
          <Text style={styles.category}>{service.categoryName}</Text>
          <Text style={styles.description} numberOfLines={2}>
            {service.description}
          </Text>
        </View>

        <View style={styles.footer}>
          <Text style={styles.price}>
            <Text style={styles.priceBold}>${service.price}</Text>
            <Text style={styles.priceUnit}> per service</Text>
          </Text>

          <View style={styles.badges}>
            <View style={styles.categoryBadge}>
              <Text style={styles.categoryBadgeText}>{service.categoryName}</Text>
            </View>
          </View>
        </View>

        {onFavoritePress && (
          <Pressable
            style={styles.favoriteButton}
            onPress={() => onFavoritePress(service)}
          >
            <Ionicons name="heart" size={20} color={isFavorite ? Colors.error : Colors.text.secondary} />
          </Pressable>
        )}
      </View>
    </Pressable>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.background.primary,
    borderRadius: 12,
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    borderWidth: 1,
    borderColor: Colors.border,
    flex: 1, // Allow card to expand within its container
    minHeight: 160, // Ensure consistent minimum height
  },
  content: {
    padding: 12,
    flex: 1,
    justifyContent: 'space-between',
  },
  providerInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  providerAvatar: {
    width: 36,
    height: 36,
    borderRadius: 18,
    marginRight: 8,
    backgroundColor: Colors.background.secondary,
  },
  providerDetails: {
    flex: 1,
  },
  providerName: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.text.primary,
    marginBottom: 2,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rating: {
    marginLeft: 4,
    fontSize: 12,
    fontWeight: '500',
    color: Colors.text.secondary,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 2,
  },
  loadingText: {
    marginLeft: 6,
    fontSize: 12,
    color: Colors.text.secondary,
    fontStyle: 'italic',
  },
  location: {
    fontSize: 11,
    color: Colors.text.secondary,
    marginTop: 2,
  },
  serviceInfo: {
    marginBottom: 12,
    flex: 1,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text.primary,
    marginBottom: 4,
    lineHeight: 22,
  },
  category: {
    fontSize: 14,
    color: Colors.text.secondary,
    marginBottom: 2,
  },
  description: {
    fontSize: 14,
    color: Colors.text.secondary,
    lineHeight: 20,
    marginBottom: 8,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  price: {
    fontSize: 14,
    color: Colors.text.primary,
  },
  priceBold: {
    fontWeight: '600',
  },
  priceUnit: {
    fontWeight: '400',
    color: Colors.text.secondary,
  },
  badges: {
    flexDirection: 'row',
    gap: 6,
  },
  categoryBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    backgroundColor: Colors.background.secondary,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  categoryBadgeText: {
    color: Colors.text.secondary,
    fontSize: 12,
    fontWeight: '500',
  },
  favoriteButton: {
    position: 'absolute',
    top: 8,
    right: 8,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: Colors.background.secondary,
    justifyContent: 'center',
    alignItems: 'center',
  },
});