packer {
  required_plugins {
    docker = {
      version = ">= 1.0.0"
      source  = "github.com/hashicorp/docker"
    }
    ansible = {
      version = ">= 1.0.0"
      source  = "github.com/hashicorp/ansible"
    }
  }
}

source "docker" "jenkins_worker" {
  image  = "ghcr.io/jenkinsci/inbound-agent:latest-jdk17"
  commit = true
}

build {
  name    = "jenkins-worker-image"
  sources = ["source.docker.jenkins_worker"]

  provisioner "ansible" {
    playbook_file = "playbook.yml"
  }
}