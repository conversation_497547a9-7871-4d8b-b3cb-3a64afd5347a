package com.yotelohago.user.api;

import java.time.Instant;
import java.util.UUID;

public class UserWithDetailsDTO {
    // Basic user data
    public UUID id;
    public String username;
    public String firstName;
    public String lastName;
    public String email;
    public String phone;
    public boolean isProfessional;
    public Instant createdAt;
    public Instant updatedAt;

    // Professional data (only populated if isProfessional = true)
    public String bio;
    public String city;
    public Double avgRating;
    public Integer ratingCount;
    public boolean available;

    // Profile photo URLs (derived from Media entities)
    public String clientProfilePhotoUrl;
    public String professionalProfilePhotoUrl;

    // Computed convenience field
    public String fullName;

    // Default constructor
    public UserWithDetailsDTO() {}

    // Constructor for JPQL queries
    public UserWithDetailsDTO(UUID id, String username, String firstName, String lastName, String email, String phone,
                             boolean isProfessional, Instant createdAt, Instant updatedAt,
                             String bio, String city, Double avgRating, Integer ratingCount, Boolean available,
                             String clientProfilePhotoUrl, String professionalProfilePhotoUrl, String fullName) {
        this.id = id;
        this.username = username;
        this.firstName = firstName;
        this.lastName = lastName;
        this.email = email;
        this.phone = phone;
        this.isProfessional = isProfessional;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.bio = bio;
        this.city = city;
        this.avgRating = avgRating;
        this.ratingCount = ratingCount;
        this.available = available != null ? available : false;
        this.clientProfilePhotoUrl = clientProfilePhotoUrl;
        this.professionalProfilePhotoUrl = professionalProfilePhotoUrl;
        this.fullName = fullName;
    }
}