#!/bin/bash

# YoteLoHago Users Library
# Functions for managing Keycloak users and roles

# Function to list all users
list_users() {
    print_status $CYAN "📋 Listing all users in realm '$REALM_NAME'..."
    
    local admin_token
    admin_token=$(get_admin_token)
    
    if [[ "$admin_token" == "null" ]]; then
        print_status $RED "❌ Failed to get admin token"
        return 1
    fi
    
    local response
    response=$(http_request GET "$KEYCLOAK_URL/admin/realms/$REALM_NAME/users" \
        -H "Authorization: Bearer $admin_token" -s)
    local status_code
    status_code=$(echo "$response" | cut -d'|' -f1)
    
    if [[ "$status_code" == "200" ]]; then
        local body
        body=$(echo "$response" | cut -d'|' -f2-)
        echo "$body" | jq -r '.[] | "- \(.username) (\(.email // "no email")) - Enabled: \(.enabled)"'
    else
        print_status $RED "❌ Failed to list users (Status: $status_code)"
    fi
}

# Function to show user details
show_user_details() {
    local username=$1
    
    if [[ -z "$username" ]]; then
        print_status $RED "❌ Usage: show <username>"
        return 1
    fi
    
    print_status $CYAN "🔍 User Details for '$username'..."
    
    local admin_token
    admin_token=$(get_admin_token)
    
    if [[ "$admin_token" == "null" ]]; then
        print_status $RED "❌ Failed to get admin token"
        return 1
    fi
    
    local user_response
    user_response=$(http_request GET "$KEYCLOAK_URL/admin/realms/$REALM_NAME/users?username=$username" \
        -H "Authorization: Bearer $admin_token" -s)
    local status_code
    status_code=$(echo "$user_response" | cut -d'|' -f1)
    
    if [[ "$status_code" != "200" ]]; then
        print_status $RED "❌ Failed to get user data (Status: $status_code)"
        return 1
    fi
    
    local user_data
    user_data=$(echo "$user_response" | cut -d'|' -f2-)
    local user_id
    user_id=$(echo "$user_data" | jq -r '.[0].id // "null"')
    
    if [[ "$user_id" == "null" ]]; then
        print_status $RED "❌ User '$username' not found"
        return 1
    fi
    
    echo "👤 Basic Information:"
    echo "$user_data" | jq -r '.[0] | {
        id,
        username,
        email,
        firstName,
        lastName,
        enabled,
        emailVerified,
        createdTimestamp
    }'
    
    echo ""
    echo "🏰 Realm Roles:"
    local roles_response
    roles_response=$(http_request GET "$KEYCLOAK_URL/admin/realms/$REALM_NAME/users/$user_id/role-mappings/realm" \
        -H "Authorization: Bearer $admin_token" -s)
    local roles_status
    roles_status=$(echo "$roles_response" | cut -d'|' -f1)
    
    if [[ "$roles_status" == "200" ]]; then
        local roles_body
        roles_body=$(echo "$roles_response" | cut -d'|' -f2-)
        echo "$roles_body" | jq -r '.[].name' | sed 's/^/  - /'
    else
        print_status $YELLOW "⚠️ Could not retrieve realm roles"
    fi
    
    echo ""
    echo "📱 Client Roles:"
    local clients_response
    clients_response=$(http_request GET "$KEYCLOAK_URL/admin/realms/$REALM_NAME/clients" \
        -H "Authorization: Bearer $admin_token" -s)
    local clients_status
    clients_status=$(echo "$clients_response" | cut -d'|' -f1)
    
    if [[ "$clients_status" == "200" ]]; then
        local clients_body
        clients_body=$(echo "$clients_response" | cut -d'|' -f2-)
        local yotelohago_clients
        yotelohago_clients=$(echo "$clients_body" | jq -r '.[] | select(.clientId | test("yotelohago")) | .id')
        
        for client_id in $yotelohago_clients; do
            local client_name
            client_name=$(echo "$clients_body" | jq -r ".[] | select(.id==\"$client_id\") | .clientId")
            
            local client_roles_response
            client_roles_response=$(http_request GET "$KEYCLOAK_URL/admin/realms/$REALM_NAME/users/$user_id/role-mappings/clients/$client_id" \
                -H "Authorization: Bearer $admin_token" -s 2>/dev/null)
            local client_roles_status
            client_roles_status=$(echo "$client_roles_response" | cut -d'|' -f1)
            
            if [[ "$client_roles_status" == "200" ]]; then
                local client_roles_body
                client_roles_body=$(echo "$client_roles_response" | cut -d'|' -f2-)
                local client_roles
                client_roles=$(echo "$client_roles_body" | jq -r '.[].name' 2>/dev/null)
                
                if [[ -n "$client_roles" ]]; then
                    echo "  $client_name:"
                    echo "$client_roles" | sed 's/^/    - /'
                fi
            fi
        done
    fi
}

# Function to assign realm role to user
assign_realm_role() {
    local username=$1
    local role_name=$2
    
    if [[ -z "$username" ]] || [[ -z "$role_name" ]]; then
        print_status $RED "❌ Usage: assign-role <username> <role_name>"
        return 1
    fi
    
    print_status $CYAN "➕ Assigning realm role '$role_name' to user '$username'..."
    
    local admin_token
    admin_token=$(get_admin_token)
    
    if [[ "$admin_token" == "null" ]]; then
        print_status $RED "❌ Failed to get admin token"
        return 1
    fi
    
    # Get user ID
    local user_response
    user_response=$(http_request GET "$KEYCLOAK_URL/admin/realms/$REALM_NAME/users?username=$username" \
        -H "Authorization: Bearer $admin_token" -s)
    local user_id
    user_id=$(echo "$user_response" | cut -d'|' -f2- | jq -r '.[0].id // "null"')
    
    if [[ "$user_id" == "null" ]]; then
        print_status $RED "❌ User '$username' not found"
        return 1
    fi
    
    # Get role ID
    local role_response
    role_response=$(http_request GET "$KEYCLOAK_URL/admin/realms/$REALM_NAME/roles/$role_name" \
        -H "Authorization: Bearer $admin_token" -s)
    local role_status
    role_status=$(echo "$role_response" | cut -d'|' -f1)
    
    if [[ "$role_status" != "200" ]]; then
        print_status $RED "❌ Role '$role_name' not found"
        return 1
    fi
    
    local role_id
    role_id=$(echo "$role_response" | cut -d'|' -f2- | jq -r '.id')
    
    # Assign role
    local assign_data="[{\"id\":\"$role_id\",\"name\":\"$role_name\"}]"
    local assign_response
    assign_response=$(http_request POST "$KEYCLOAK_URL/admin/realms/$REALM_NAME/users/$user_id/role-mappings/realm" \
        -H "Authorization: Bearer $admin_token" \
        -H "Content-Type: application/json" \
        -d "$assign_data" -s)
    local assign_status
    assign_status=$(echo "$assign_response" | cut -d'|' -f1)
    
    if [[ "$assign_status" == "204" ]]; then
        print_status $GREEN "✅ Role '$role_name' assigned to user '$username'"
    else
        print_status $RED "❌ Failed to assign role (Status: $assign_status)"
    fi
}

# Function to remove realm role from user
remove_realm_role() {
    local username=$1
    local role_name=$2
    
    if [[ -z "$username" ]] || [[ -z "$role_name" ]]; then
        print_status $RED "❌ Usage: remove-role <username> <role_name>"
        return 1
    fi
    
    print_status $CYAN "➖ Removing realm role '$role_name' from user '$username'..."
    
    local admin_token
    admin_token=$(get_admin_token)
    
    if [[ "$admin_token" == "null" ]]; then
        print_status $RED "❌ Failed to get admin token"
        return 1
    fi
    
    # Get user ID
    local user_response
    user_response=$(http_request GET "$KEYCLOAK_URL/admin/realms/$REALM_NAME/users?username=$username" \
        -H "Authorization: Bearer $admin_token" -s)
    local user_id
    user_id=$(echo "$user_response" | cut -d'|' -f2- | jq -r '.[0].id // "null"')
    
    if [[ "$user_id" == "null" ]]; then
        print_status $RED "❌ User '$username' not found"
        return 1
    fi
    
    # Get role ID
    local role_response
    role_response=$(http_request GET "$KEYCLOAK_URL/admin/realms/$REALM_NAME/roles/$role_name" \
        -H "Authorization: Bearer $admin_token" -s)
    local role_status
    role_status=$(echo "$role_response" | cut -d'|' -f1)
    
    if [[ "$role_status" != "200" ]]; then
        print_status $RED "❌ Role '$role_name' not found"
        return 1
    fi
    
    local role_id
    role_id=$(echo "$role_response" | cut -d'|' -f2- | jq -r '.id')
    
    # Remove role
    local remove_data="[{\"id\":\"$role_id\",\"name\":\"$role_name\"}]"
    local remove_response
    remove_response=$(http_request DELETE "$KEYCLOAK_URL/admin/realms/$REALM_NAME/users/$user_id/role-mappings/realm" \
        -H "Authorization: Bearer $admin_token" \
        -H "Content-Type: application/json" \
        -d "$remove_data" -s)
    local remove_status
    remove_status=$(echo "$remove_response" | cut -d'|' -f1)
    
    if [[ "$remove_status" == "204" ]]; then
        print_status $GREEN "✅ Role '$role_name' removed from user '$username'"
    else
        print_status $RED "❌ Failed to remove role (Status: $remove_status)"
    fi
}

# Function to create a new user
create_user() {
    local username=$1
    local email=$2
    local first_name=$3
    local last_name=$4
    local password=$5
    
    if [[ -z "$username" ]] || [[ -z "$email" ]] || [[ -z "$password" ]]; then
        print_status $RED "❌ Usage: create <username> <email> <first_name> <last_name> <password>"
        return 1
    fi
    
    print_status $CYAN "👤 Creating user '$username'..."
    
    local admin_token
    admin_token=$(get_admin_token)
    
    if [[ "$admin_token" == "null" ]]; then
        print_status $RED "❌ Failed to get admin token"
        return 1
    fi
    
    # Create user data
    local user_data="{
        \"username\": \"$username\",
        \"email\": \"$email\",
        \"firstName\": \"$first_name\",
        \"lastName\": \"$last_name\",
        \"enabled\": true,
        \"emailVerified\": true
    }"
    
    local create_response
    create_response=$(http_request POST "$KEYCLOAK_URL/admin/realms/$REALM_NAME/users" \
        -H "Authorization: Bearer $admin_token" \
        -H "Content-Type: application/json" \
        -d "$user_data" -s)
    local create_status
    create_status=$(echo "$create_response" | cut -d'|' -f1)
    
    if [[ "$create_status" == "201" ]]; then
        print_status $GREEN "✅ User '$username' created successfully"
        
        # Set password
        local user_response
        user_response=$(http_request GET "$KEYCLOAK_URL/admin/realms/$REALM_NAME/users?username=$username" \
            -H "Authorization: Bearer $admin_token" -s)
        local user_id
        user_id=$(echo "$user_response" | cut -d'|' -f2- | jq -r '.[0].id')
        
        local password_data="{
            \"type\": \"password\",
            \"value\": \"$password\",
            \"temporary\": false
        }"
        
        local password_response
        password_response=$(http_request PUT "$KEYCLOAK_URL/admin/realms/$REALM_NAME/users/$user_id/reset-password" \
            -H "Authorization: Bearer $admin_token" \
            -H "Content-Type: application/json" \
            -d "$password_data" -s)
        local password_status
        password_status=$(echo "$password_response" | cut -d'|' -f1)
        
        if [[ "$password_status" == "204" ]]; then
            print_status $GREEN "✅ Password set for user '$username'"
        else
            print_status $YELLOW "⚠️ User created but password setting failed"
        fi
        
        # Assign default user role
        assign_realm_role "$username" "user"
        
    else
        print_status $RED "❌ Failed to create user '$username' (Status: $create_status)"
        local create_body
        create_body=$(echo "$create_response" | cut -d'|' -f2-)
        if [[ -n "$create_body" ]]; then
            echo "Response: $create_body"
        fi
    fi
}

# Function to migrate user from client roles to realm roles
migrate_user_roles() {
    local username=$1
    
    if [[ -z "$username" ]]; then
        print_status $RED "❌ Usage: migrate <username>"
        return 1
    fi
    
    print_status $CYAN "🔄 Migrating user '$username' from client roles to realm roles..."
    
    local admin_token
    admin_token=$(get_admin_token)
    
    if [[ "$admin_token" == "null" ]]; then
        print_status $RED "❌ Failed to get admin token"
        return 1
    fi
    
    # Get user ID
    local user_response
    user_response=$(http_request GET "$KEYCLOAK_URL/admin/realms/$REALM_NAME/users?username=$username" \
        -H "Authorization: Bearer $admin_token" -s)
    local user_id
    user_id=$(echo "$user_response" | cut -d'|' -f2- | jq -r '.[0].id // "null"')
    
    if [[ "$user_id" == "null" ]]; then
        print_status $RED "❌ User '$username' not found"
        return 1
    fi
    
    # Get dev client ID
    local clients_response
    clients_response=$(http_request GET "$KEYCLOAK_URL/admin/realms/$REALM_NAME/clients" \
        -H "Authorization: Bearer $admin_token" -s)
    local dev_client_id
    dev_client_id=$(echo "$clients_response" | cut -d'|' -f2- | jq -r ".[] | select(.clientId==\"$DEV_CLIENT_ID\") | .id")
    
    if [[ -z "$dev_client_id" ]] || [[ "$dev_client_id" == "null" ]]; then
        print_status $YELLOW "⚠️ Dev client not found, skipping migration"
        return 0
    fi
    
    # Check current client roles
    local client_roles_response
    client_roles_response=$(http_request GET "$KEYCLOAK_URL/admin/realms/$REALM_NAME/users/$user_id/role-mappings/clients/$dev_client_id" \
        -H "Authorization: Bearer $admin_token" -s 2>/dev/null)
    local client_roles_status
    client_roles_status=$(echo "$client_roles_response" | cut -d'|' -f1)
    
    if [[ "$client_roles_status" == "200" ]]; then
        local client_roles_body
        client_roles_body=$(echo "$client_roles_response" | cut -d'|' -f2-)
        local client_roles
        client_roles=$(echo "$client_roles_body" | jq -r '.[].name' 2>/dev/null)
        
        if [[ -n "$client_roles" ]]; then
            echo "Found client roles: $client_roles"
            
            # For each client role, assign corresponding realm role and remove client role
            for role in $client_roles; do
                if [[ "$role" == "user" ]] || [[ "$role" == "professional" ]] || [[ "$role" == "admin" ]]; then
                    echo "  Migrating '$role' from client to realm role..."
                    assign_realm_role "$username" "$role"
                    
                    # Remove client role
                    local client_role_response
                    client_role_response=$(http_request GET "$KEYCLOAK_URL/admin/realms/$REALM_NAME/clients/$dev_client_id/roles/$role" \
                        -H "Authorization: Bearer $admin_token" -s)
                    local client_role_id
                    client_role_id=$(echo "$client_role_response" | cut -d'|' -f2- | jq -r '.id')
                    
                    local remove_data="[{\"id\":\"$client_role_id\",\"name\":\"$role\"}]"
                    http_request DELETE "$KEYCLOAK_URL/admin/realms/$REALM_NAME/users/$user_id/role-mappings/clients/$dev_client_id" \
                        -H "Authorization: Bearer $admin_token" \
                        -H "Content-Type: application/json" \
                        -d "$remove_data" -s > /dev/null
                    
                    print_status $GREEN "  ✅ Migrated '$role' role"
                fi
            done
        else
            print_status $YELLOW "No client roles found to migrate"
        fi
    else
        print_status $YELLOW "No client roles found to migrate"
    fi
    
    print_status $GREEN "✅ Migration completed for user '$username'"
}

# Function to show users help
users_help() {
    cat << EOF
👥 Users - User and role management

COMMANDS:
    list                                    List all users
    show <username>                         Show user details and roles
    create <username> <email> <first> <last> <password>  Create new user
    assign-role <username> <role>           Assign realm role to user
    remove-role <username> <role>           Remove realm role from user
    migrate <username>                      Migrate user from client to realm roles

EXAMPLES:
    $0 users list
    $0 <NAME_EMAIL>
    $0 users <NAME_EMAIL> Test User password123
    $0 users assign-role <EMAIL> professional
    $0 <NAME_EMAIL>

EOF
}

# Main users function
users_main() {
    local command=$1
    shift
    
    case "$command" in
        "list")
            list_users
            ;;
        "show")
            show_user_details "$1"
            ;;
        "create")
            create_user "$1" "$2" "$3" "$4" "$5"
            ;;
        "assign-role")
            assign_realm_role "$1" "$2"
            ;;
        "remove-role")
            remove_realm_role "$1" "$2"
            ;;
        "migrate")
            migrate_user_roles "$1"
            ;;
        *)
            print_status $RED "❌ Unknown users command: $command"
            users_help
            exit 1
            ;;
    esac
}
