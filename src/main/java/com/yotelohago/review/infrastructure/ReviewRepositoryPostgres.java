package com.yotelohago.review.infrastructure;

import com.yotelohago.review.domain.Review;
import com.yotelohago.review.domain.ReviewRepository;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@ApplicationScoped
public class ReviewRepositoryPostgres implements ReviewRepository {

    @PersistenceContext
    EntityManager entityManager;

    @Override
    public void persist(Review review) {
        entityManager.persist(review);
    }

    @Override
    public Optional<Review> findById(Long id) {
        Review review = entityManager.find(Review.class, id);
        return Optional.ofNullable(review);
    }

    @Override
    public Optional<Review> findByBookingId(Long bookingId) {
        return entityManager.createQuery("FROM Review r WHERE r.booking.id = :bookingId", Review.class)
                .setParameter("bookingId", bookingId)
                .getResultStream()
                .findFirst();
    }

    @Override
    public List<Review> findByProfessionalUserId(UUID professionalUserId) {
        return entityManager.createQuery("FROM Review r WHERE r.professional.id = :professionalUserId ORDER BY r.createdAt DESC", Review.class)
                .setParameter("professionalUserId", professionalUserId)
                .getResultList();
    }

    @Override
    public List<Review> findAll() {
        return entityManager.createQuery("FROM Review ORDER BY createdAt DESC", Review.class)
                .getResultList();
    }

    @Override
    public void deleteById(Long id) {
        Review review = entityManager.find(Review.class, id);
        if (review != null) {
            entityManager.remove(review);
        }
    }

    @Override
    public void flush() {
        entityManager.flush();
    }
}