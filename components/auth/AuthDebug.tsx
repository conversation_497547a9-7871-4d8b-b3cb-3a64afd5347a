import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../../contexts/AuthContext';
import tokenStorage from '../../services/auth/tokenStorage';
import { apiGet } from '../../services/api/client';
import Colors from '../../app/client/constants/Colors';

export default function AuthDebug() {
  const { state: authState, refreshAuthState } = useAuth();
  const [storageInfo, setStorageInfo] = useState<any>(null);
  const [isExpanded, setIsExpanded] = useState(false);
  const [apiTestResult, setApiTestResult] = useState<string>('');

  const checkStorage = async () => {
    try {
      const tokens = await tokenStorage.getTokens();
      const userInfo = await tokenStorage.getUserInfo();
      const isExpired = await tokenStorage.isTokenExpired();
      const accessToken = await tokenStorage.getAccessToken();

      setStorageInfo({
        hasTokens: !!tokens,
        hasUserInfo: !!userInfo,
        isExpired,
        tokenLength: tokens?.accessToken?.length || 0,
        userEmail: userInfo?.email || 'N/A',
        accessTokenLength: accessToken?.length || 0,
        accessTokenPreview: accessToken ? accessToken.substring(0, 20) + '...' : 'None',
      });
    } catch (error) {
      setStorageInfo({ error: error.message });
    }
  };

  const testProtectedApi = async () => {
    try {
      setApiTestResult('Testing...');
      const response = await apiGet('/bookings');
      setApiTestResult(`Success: ${JSON.stringify(response).substring(0, 100)}...`);
    } catch (error: any) {
      setApiTestResult(`Error: ${error.status || 'Unknown'} - ${error.message || error.error || 'Failed'}`);
    }
  };

  useEffect(() => {
    checkStorage();
  }, [authState]);

  if (!isExpanded) {
    return (
      <TouchableOpacity 
        style={styles.collapsedContainer} 
        onPress={() => setIsExpanded(true)}
      >
        <Ionicons name="bug" size={16} color={Colors.text.secondary} />
        <Text style={styles.collapsedText}>Auth Debug</Text>
      </TouchableOpacity>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Auth Debug Info</Text>
        <TouchableOpacity onPress={() => setIsExpanded(false)}>
          <Ionicons name="close" size={20} color={Colors.text.secondary} />
        </TouchableOpacity>
      </View>
      
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Context State:</Text>
        <Text style={styles.debugText}>
          Authenticated: {authState.isAuthenticated ? 'Yes' : 'No'}
        </Text>
        <Text style={styles.debugText}>
          Loading: {authState.isLoading ? 'Yes' : 'No'}
        </Text>
        <Text style={styles.debugText}>
          User: {authState.user?.email || 'None'}
        </Text>
        <Text style={styles.debugText}>
          Error: {authState.error || 'None'}
        </Text>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Storage State:</Text>
        <Text style={styles.debugText}>
          Has Tokens: {storageInfo?.hasTokens ? 'Yes' : 'No'}
        </Text>
        <Text style={styles.debugText}>
          Has User Info: {storageInfo?.hasUserInfo ? 'Yes' : 'No'}
        </Text>
        <Text style={styles.debugText}>
          Token Expired: {storageInfo?.isExpired ? 'Yes' : 'No'}
        </Text>
        <Text style={styles.debugText}>
          Token Length: {storageInfo?.tokenLength || 0}
        </Text>
        <Text style={styles.debugText}>
          User Email: {storageInfo?.userEmail || 'N/A'}
        </Text>
        <Text style={styles.debugText}>
          Access Token Length: {storageInfo?.accessTokenLength || 0}
        </Text>
        <Text style={styles.debugText}>
          Access Token: {storageInfo?.accessTokenPreview || 'None'}
        </Text>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>API Test:</Text>
        <Text style={styles.debugText}>
          Result: {apiTestResult || 'Not tested'}
        </Text>
      </View>

      <View style={styles.actions}>
        <TouchableOpacity 
          style={styles.actionButton} 
          onPress={refreshAuthState}
        >
          <Text style={styles.actionText}>Refresh Auth</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={checkStorage}
        >
          <Text style={styles.actionText}>Check Storage</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={testProtectedApi}
        >
          <Text style={styles.actionText}>Test API</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  collapsedContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    backgroundColor: Colors.background.secondary,
    borderRadius: 6,
    gap: 6,
  },
  collapsedText: {
    fontSize: 12,
    color: Colors.text.secondary,
  },
  container: {
    backgroundColor: Colors.background.secondary,
    borderRadius: 8,
    padding: 12,
    margin: 8,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  title: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.text.primary,
  },
  section: {
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.text.primary,
    marginBottom: 4,
  },
  debugText: {
    fontSize: 11,
    color: Colors.text.secondary,
    fontFamily: 'monospace',
    marginBottom: 2,
  },
  actions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  actionButton: {
    backgroundColor: Colors.primary,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
  },
  actionText: {
    fontSize: 11,
    color: 'white',
    fontWeight: '500',
  },
});
