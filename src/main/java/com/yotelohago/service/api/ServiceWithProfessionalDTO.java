package com.yotelohago.service.api;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.UUID;

/**
 * DTO that combines Service data with Professional information
 * This eliminates N+1 queries by fetching all data in a single join query
 */
public class ServiceWithProfessionalDTO {
    
    // Service fields
    public UUID id;
    public UUID professionalUserId;
    public String title;
    public String description;
    public BigDecimal price;
    public UUID categoryId;
    public String categoryName;
    public String status;
    public Instant createdAt;
    public Instant updatedAt;
    
    // Professional fields
    public String professionalFirstName;
    public String professionalLastName;
    public String professionalFullName; // Computed field
    public String professionalCity;
    public Double professionalAvgRating;
    public Integer professionalRatingCount;
    public Boolean professionalAvailable;
    public String professionalBio;
    public String professionalProfileImageUrl;

    // Default constructor
    public ServiceWithProfessionalDTO() {}

    // Constructor for JPA projection queries
    public ServiceWithProfessionalDTO(
            UUID id, UUID professionalUserId, String title, String description, BigDecimal price,
            UUID categoryId, String categoryName, String status, Instant createdAt, Instant updatedAt,
            String professionalFirstName, String professionalLastName, String professionalCity,
            Double professionalAvgRating, Integer professionalRatingCount, Boolean professionalAvailable,
            String professionalBio, String professionalProfileImageUrl) {

        // Service data
        this.id = id;
        this.professionalUserId = professionalUserId;
        this.title = title;
        this.description = description;
        this.price = price;
        this.categoryId = categoryId;
        this.categoryName = categoryName;
        this.status = status;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        
        // Professional data
        this.professionalFirstName = professionalFirstName;
        this.professionalLastName = professionalLastName;
        this.professionalCity = professionalCity;
        this.professionalAvgRating = professionalAvgRating;
        this.professionalRatingCount = professionalRatingCount;
        this.professionalAvailable = professionalAvailable;
        this.professionalBio = professionalBio;
        this.professionalProfileImageUrl = professionalProfileImageUrl;
        
        // Compute full name
        this.professionalFullName = computeFullName(professionalFirstName, professionalLastName);
    }

    /**
     * Compute professional full name from first and last name
     */
    private String computeFullName(String firstName, String lastName) {
        if (firstName == null && lastName == null) {
            return "Professional";
        }
        if (firstName == null) {
            return lastName;
        }
        if (lastName == null) {
            return firstName;
        }
        return firstName + " " + lastName;
    }

    /**
     * Convert to regular ServiceDTO for backward compatibility
     */
    public ServiceDTO toServiceDTO() {
        return new ServiceDTO(
                this.id,
                this.professionalUserId,
                this.title,
                this.description,
                this.price,
                this.categoryId,
                this.categoryName,
                this.status,
                this.createdAt,
                this.updatedAt
        );
    }

    // Getters and setters
    public UUID getId() { return id; }
    public void setId(UUID id) { this.id = id; }

    public UUID getProfessionalUserId() { return professionalUserId; }
    public void setProfessionalUserId(UUID professionalUserId) { this.professionalUserId = professionalUserId; }

    public String getTitle() { return title; }
    public void setTitle(String title) { this.title = title; }

    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }

    public BigDecimal getPrice() { return price; }
    public void setPrice(BigDecimal price) { this.price = price; }

    public UUID getCategoryId() { return categoryId; }
    public void setCategoryId(UUID categoryId) { this.categoryId = categoryId; }

    public String getCategoryName() { return categoryName; }
    public void setCategoryName(String categoryName) { this.categoryName = categoryName; }

    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }

    public Instant getCreatedAt() { return createdAt; }
    public void setCreatedAt(Instant createdAt) { this.createdAt = createdAt; }

    public Instant getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(Instant updatedAt) { this.updatedAt = updatedAt; }

    public String getProfessionalFirstName() { return professionalFirstName; }
    public void setProfessionalFirstName(String professionalFirstName) { 
        this.professionalFirstName = professionalFirstName;
        this.professionalFullName = computeFullName(professionalFirstName, professionalLastName);
    }

    public String getProfessionalLastName() { return professionalLastName; }
    public void setProfessionalLastName(String professionalLastName) { 
        this.professionalLastName = professionalLastName;
        this.professionalFullName = computeFullName(professionalFirstName, professionalLastName);
    }

    public String getProfessionalFullName() { return professionalFullName; }

    public String getProfessionalCity() { return professionalCity; }
    public void setProfessionalCity(String professionalCity) { this.professionalCity = professionalCity; }

    public Double getProfessionalAvgRating() { return professionalAvgRating; }
    public void setProfessionalAvgRating(Double professionalAvgRating) { this.professionalAvgRating = professionalAvgRating; }

    public Integer getProfessionalRatingCount() { return professionalRatingCount; }
    public void setProfessionalRatingCount(Integer professionalRatingCount) { this.professionalRatingCount = professionalRatingCount; }

    public Boolean getProfessionalAvailable() { return professionalAvailable; }
    public void setProfessionalAvailable(Boolean professionalAvailable) { this.professionalAvailable = professionalAvailable; }

    public String getProfessionalBio() { return professionalBio; }
    public void setProfessionalBio(String professionalBio) { this.professionalBio = professionalBio; }

    public String getProfessionalProfileImageUrl() { return professionalProfileImageUrl; }
    public void setProfessionalProfileImageUrl(String professionalProfileImageUrl) { this.professionalProfileImageUrl = professionalProfileImageUrl; }
}
