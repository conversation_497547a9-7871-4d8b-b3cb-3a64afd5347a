import React from 'react';
import { Row, Col, Card, Typography, Space, Progress } from 'antd';
import {
  UserOutlined,
  TeamOutlined,
  CalendarOutlined,
  DollarOutlined,
  ToolOutlined,
  StarOutlined
} from '@ant-design/icons';
import { LineChart, Line, AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar, PieChart, Pie, Cell } from 'recharts';
import StatsCard from '../components/common/StatsCard';
import { mockDashboardStats, mockRevenueData, mockBookings } from '../data/mockData';

const { Title, Text } = Typography;

const Dashboard: React.FC = () => {
  const stats = mockDashboardStats;
  const revenueData = mockRevenueData;

  const bookingStatusData = [
    { name: 'Open', value: 23, color: '#faad14' },
    { name: 'Accepted', value: 45, color: '#1890ff' },
    { name: 'Completed', value: 67, color: '#52c41a' },
    { name: 'Cancelled', value: 12, color: '#ff4d4f' },
  ];

  const categoryPerformance = [
    { name: 'Electricians', bookings: 145, revenue: 43455 },
    { name: 'Plumbers', bookings: 98, revenue: 29602 },
    { name: 'Movers', bookings: 67, revenue: 20149 },
    { name: 'Locksmiths', bookings: 54, revenue: 16500 },
  ];

  const recentBookings = mockBookings.slice(0, 5);

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <Title level={2} className="m-0">Platform Dashboard</Title>
        <Text type="secondary">Welcome back! Here's your service marketplace overview.</Text>
      </div>

      {/* Stats Cards */}
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={12} lg={6}>
          <StatsCard
            title="Total Users"
            value={stats.totalUsers}
            growth={stats.userGrowth}
            icon={<UserOutlined />}
            color="#52c41a"
          />
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <StatsCard
            title="Active Professionals"
            value={stats.totalProfessionals}
            growth={stats.professionalGrowth}
            icon={<TeamOutlined />}
            color="#1890ff"
          />
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <StatsCard
            title="Total Bookings"
            value={stats.totalBookings}
            growth={stats.bookingGrowth}
            icon={<CalendarOutlined />}
            color="#722ed1"
          />
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <StatsCard
            title="Total Revenue"
            value={stats.totalRevenue}
            prefix="$"
            precision={2}
            growth={stats.revenueGrowth}
            icon={<DollarOutlined />}
            color="#fa8c16"
          />
        </Col>
      </Row>

      {/* Secondary Stats */}
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={12} lg={8}>
          <StatsCard
            title="Active Users (30d)"
            value={stats.activeUsers}
            icon={<UserOutlined />}
            color="#13c2c2"
          />
        </Col>
        <Col xs={24} sm={12} lg={8}>
          <StatsCard
            title="Completion Rate"
            value={stats.completionRate}
            suffix="%"
            precision={1}
            icon={<StarOutlined />}
            color="#52c41a"
          />
        </Col>
        <Col xs={24} sm={12} lg={8}>
          <StatsCard
            title="Avg. Rating"
            value={4.7}
            precision={1}
            suffix="/5"
            icon={<StarOutlined />}
            color="#faad14"
          />
        </Col>
      </Row>

      {/* Charts Row */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={16}>
          <Card title="Revenue & Booking Trends" className="shadow-sm">
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={revenueData}>
                <defs>
                  <linearGradient id="colorRevenue" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#1890ff" stopOpacity={0.8}/>
                    <stop offset="95%" stopColor="#1890ff" stopOpacity={0.1}/>
                  </linearGradient>
                </defs>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis yAxisId="left" />
                <YAxis yAxisId="right" orientation="right" />
                <Tooltip 
                  formatter={(value, name) => [
                    name === 'revenue' ? `$${value}` : value,
                    name === 'revenue' ? 'Revenue' : 'Bookings'
                  ]} 
                />
                <Area
                  yAxisId="left"
                  type="monotone"
                  dataKey="revenue"
                  stroke="#1890ff"
                  fillOpacity={1}
                  fill="url(#colorRevenue)"
                />
                <Line
                  yAxisId="right"
                  type="monotone"
                  dataKey="bookings"
                  stroke="#52c41a"
                  strokeWidth={2}
                />
              </AreaChart>
            </ResponsiveContainer>
          </Card>
        </Col>
        <Col xs={24} lg={8}>
          <Card title="Booking Status Distribution" className="shadow-sm">
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={bookingStatusData}
                  cx="50%"
                  cy="50%"
                  innerRadius={60}
                  outerRadius={100}
                  paddingAngle={5}
                  dataKey="value"
                >
                  {bookingStatusData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
            <div className="mt-4 space-y-2">
              {bookingStatusData.map((item, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <div
                      className="w-3 h-3 rounded-full"
                      style={{ backgroundColor: item.color }}
                    />
                    <Text>{item.name}</Text>
                  </div>
                  <Text strong>{item.value}</Text>
                </div>
              ))}
            </div>
          </Card>
        </Col>
      </Row>

      {/* Bottom Row */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={12}>
          <Card title="Category Performance" className="shadow-sm">
            <div className="space-y-4">
              {categoryPerformance.map((category, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex-1">
                    <Text strong className="block">{category.name}</Text>
                    <Text type="secondary" className="text-sm">
                      {category.bookings} bookings
                    </Text>
                  </div>
                  <div className="text-right">
                    <Text strong>${category.revenue.toLocaleString()}</Text>
                    <Progress
                      percent={(category.bookings / 145) * 100}
                      size="small"
                      showInfo={false}
                      className="mt-1"
                    />
                  </div>
                </div>
              ))}
            </div>
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card title="Recent Bookings" className="shadow-sm">
            <div className="space-y-4">
              {recentBookings.map((booking) => (
                <div key={booking.id} className="flex items-center justify-between">
                  <div className="flex-1">
                    <Text strong className="block">{booking.title}</Text>
                    <Text type="secondary" className="text-sm">
                      Client ID: {booking.clientId}
                    </Text>
                  </div>
                  <div className="text-right">
                    <Text strong>${booking.price}</Text>
                    <div className="mt-1">
                      <Text
                        className={`text-xs px-2 py-1 rounded ${
                          booking.status === 'COMPLETED'
                            ? 'bg-green-100 text-green-800'
                            : booking.status === 'ACCEPTED'
                            ? 'bg-blue-100 text-blue-800'
                            : booking.status === 'OPEN'
                            ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-red-100 text-red-800'
                        }`}
                      >
                        {booking.status}
                      </Text>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Dashboard;