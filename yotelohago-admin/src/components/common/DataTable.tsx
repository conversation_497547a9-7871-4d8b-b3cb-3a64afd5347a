import React from 'react';
import { Table, TableProps, Card, Space, Button, Input, Select, Tag } from 'antd';
import { SearchOutlined, FilterOutlined, ExportOutlined } from '@ant-design/icons';

const { Search } = Input;
const { Option } = Select;

interface DataTableProps extends TableProps<any> {
  title?: string;
  searchable?: boolean;
  filterable?: boolean;
  exportable?: boolean;
  onSearch?: (value: string) => void;
  onFilter?: (filters: any) => void;
  onExport?: () => void;
  filterOptions?: Array<{
    key: string;
    label: string;
    options: Array<{ value: string; label: string }>;
  }>;
}

const DataTable: React.FC<DataTableProps> = ({
  title,
  searchable = true,
  filterable = true,
  exportable = true,
  onSearch,
  onFilter,
  onExport,
  filterOptions = [],
  ...tableProps
}) => {
  return (
    <Card className="shadow-sm">
      {(title || searchable || filterable || exportable) && (
        <div className="mb-4 flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
          <div>
            {title && <h3 className="text-lg font-semibold">{title}</h3>}
          </div>
          
          <div className="flex flex-col sm:flex-row items-stretch sm:items-center space-y-2 sm:space-y-0 sm:space-x-2">
            {searchable && (
              <Search
                placeholder="Search..."
                onSearch={onSearch}
                className="w-full sm:w-64"
                allowClear
              />
            )}
            
            {filterable && filterOptions.length > 0 && (
              <Space wrap>
                {filterOptions.map(filter => (
                  <Select
                    key={filter.key}
                    placeholder={filter.label}
                    style={{ width: 120 }}
                    allowClear
                    onChange={(value) => onFilter?.({ [filter.key]: value })}
                  >
                    {filter.options.map(option => (
                      <Option key={option.value} value={option.value}>
                        {option.label}
                      </Option>
                    ))}
                  </Select>
                ))}
              </Space>
            )}
            
            {exportable && (
              <Button
                icon={<ExportOutlined />}
                onClick={onExport}
                className="flex items-center"
              >
                Export
              </Button>
            )}
          </div>
        </div>
      )}
      
      <Table
        {...tableProps}
        className="w-full"
        scroll={{ x: 'max-content' }}
      />
    </Card>
  );
};

export default DataTable;