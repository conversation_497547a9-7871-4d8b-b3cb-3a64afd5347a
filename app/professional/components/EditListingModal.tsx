import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  TextInput,
  ScrollView,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Colors from '../constants/Colors';
import { ServiceDTO, UpdateServiceRequest, ServiceCategoryDTO } from '../../../types/api';
import { useCategories } from '../../../hooks/useCategories';

interface EditListingModalProps {
  visible: boolean;
  service: ServiceDTO | null;
  onClose: () => void;
  onSave: (serviceId: string, serviceData: UpdateServiceRequest) => Promise<void>;
  isLoading?: boolean;
}

export default function EditListingModal({
  visible,
  service,
  onClose,
  onSave,
  isLoading = false,
}: EditListingModalProps) {
  const [formData, setFormData] = useState<UpdateServiceRequest>({
    title: '',
    description: '',
    price: 0,
    categoryId: '',
    status: 'draft',
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [selectedCategory, setSelectedCategory] = useState<ServiceCategoryDTO | null>(null);

  // Use categories hook
  const { categories, isLoading: categoriesLoading, error: categoriesError } = useCategories();

  // Initialize form when service changes
  useEffect(() => {
    if (visible && service) {
      setFormData({
        title: service.title,
        description: service.description,
        price: service.price,
        categoryId: service.categoryId,
        status: service.status || 'draft',
      });
      setErrors({});
      
      // Find and set selected category
      const category = categories.find(cat => cat.id === service.categoryId);
      setSelectedCategory(category || null);
    }
  }, [visible, service, categories]);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.title?.trim()) {
      newErrors.title = 'Title is required';
    } else if (formData.title.trim().length < 3) {
      newErrors.title = 'Title must be at least 3 characters';
    }

    if (!formData.description?.trim()) {
      newErrors.description = 'Description is required';
    } else if (formData.description.trim().length < 10) {
      newErrors.description = 'Description must be at least 10 characters';
    }

    if (!formData.price || formData.price <= 0) {
      newErrors.price = 'Price must be greater than 0';
    }

    if (!formData.categoryId) {
      newErrors.categoryId = 'Please select a category';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!service || !validateForm()) {
      return;
    }

    try {
      await onSave(service.id, formData);
      onClose();
    } catch (error) {
      console.error('Failed to update service:', error);
      Alert.alert('Error', 'Failed to update listing. Please try again.');
    }
  };

  const handleCategorySelect = (category: ServiceCategoryDTO) => {
    setSelectedCategory(category);
    setFormData(prev => ({ ...prev, categoryId: category.id }));
    setErrors(prev => ({ ...prev, categoryId: '' }));
  };

  const renderCategorySelector = () => {
    if (categoriesLoading) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="small" color={Colors.primary} />
          <Text style={styles.loadingText}>Loading categories...</Text>
        </View>
      );
    }

    if (categoriesError) {
      return (
        <Text style={styles.errorText}>Failed to load categories</Text>
      );
    }

    return (
      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.categoriesScroll}>
        {categories.map((category) => (
          <TouchableOpacity
            key={category.id}
            style={[
              styles.categoryChip,
              selectedCategory?.id === category.id && styles.selectedCategoryChip,
            ]}
            onPress={() => handleCategorySelect(category)}
          >
            <Text
              style={[
                styles.categoryChipText,
                selectedCategory?.id === category.id && styles.selectedCategoryChipText,
              ]}
            >
              {category.name}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    );
  };

  if (!service) {
    return null;
  }

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons name="close" size={24} color={Colors.text.primary} />
          </TouchableOpacity>
          <Text style={styles.title}>Edit Listing</Text>
          <TouchableOpacity
            onPress={handleSave}
            style={[styles.saveButton, isLoading && styles.disabledButton]}
            disabled={isLoading}
          >
            {isLoading ? (
              <ActivityIndicator size="small" color={Colors.white} />
            ) : (
              <Text style={styles.saveButtonText}>Save</Text>
            )}
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Service Title */}
          <View style={styles.formGroup}>
            <Text style={styles.label}>Service Title *</Text>
            <TextInput
              style={[styles.input, errors.title && styles.inputError]}
              value={formData.title}
              onChangeText={(text) => {
                setFormData(prev => ({ ...prev, title: text }));
                setErrors(prev => ({ ...prev, title: '' }));
              }}
              placeholder="e.g., Emergency Plumbing Repair"
              placeholderTextColor={Colors.text.secondary}
            />
            {errors.title && <Text style={styles.errorText}>{errors.title}</Text>}
          </View>

          {/* Description */}
          <View style={styles.formGroup}>
            <Text style={styles.label}>Description *</Text>
            <TextInput
              style={[styles.textArea, errors.description && styles.inputError]}
              value={formData.description}
              onChangeText={(text) => {
                setFormData(prev => ({ ...prev, description: text }));
                setErrors(prev => ({ ...prev, description: '' }));
              }}
              placeholder="Describe your service in detail..."
              placeholderTextColor={Colors.text.secondary}
              multiline
              numberOfLines={4}
              textAlignVertical="top"
            />
            {errors.description && <Text style={styles.errorText}>{errors.description}</Text>}
          </View>

          {/* Price */}
          <View style={styles.formGroup}>
            <Text style={styles.label}>Price (USD) *</Text>
            <TextInput
              style={[styles.input, errors.price && styles.inputError]}
              value={formData.price && formData.price > 0 ? formData.price.toString() : ''}
              onChangeText={(text) => {
                const price = parseFloat(text) || 0;
                setFormData(prev => ({ ...prev, price }));
                setErrors(prev => ({ ...prev, price: '' }));
              }}
              placeholder="0.00"
              placeholderTextColor={Colors.text.secondary}
              keyboardType="numeric"
            />
            {errors.price && <Text style={styles.errorText}>{errors.price}</Text>}
          </View>

          {/* Category */}
          <View style={styles.formGroup}>
            <Text style={styles.label}>Category *</Text>
            {renderCategorySelector()}
            {errors.categoryId && <Text style={styles.errorText}>{errors.categoryId}</Text>}
          </View>

          {/* Status */}
          <View style={styles.formGroup}>
            <Text style={styles.label}>Status</Text>
            <View style={styles.statusContainer}>
              <TouchableOpacity
                style={[
                  styles.statusChip,
                  formData.status === 'draft' && styles.selectedStatusChip,
                ]}
                onPress={() => setFormData(prev => ({ ...prev, status: 'draft' }))}
              >
                <Text
                  style={[
                    styles.statusChipText,
                    formData.status === 'draft' && styles.selectedStatusChipText,
                  ]}
                >
                  Draft
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.statusChip,
                  formData.status === 'published' && styles.selectedStatusChip,
                ]}
                onPress={() => setFormData(prev => ({ ...prev, status: 'published' }))}
              >
                <Text
                  style={[
                    styles.statusChipText,
                    formData.status === 'published' && styles.selectedStatusChipText,
                  ]}
                >
                  Published
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Bottom spacing */}
          <View style={styles.bottomSpacing} />
        </ScrollView>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.primary,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
    backgroundColor: Colors.white,
  },
  closeButton: {
    padding: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text.primary,
  },
  saveButton: {
    backgroundColor: Colors.primary,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    minWidth: 60,
    alignItems: 'center',
  },
  disabledButton: {
    backgroundColor: Colors.gray,
  },
  saveButtonText: {
    color: Colors.white,
    fontWeight: '600',
    fontSize: 14,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  formGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.text.primary,
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: Colors.text.primary,
    backgroundColor: Colors.white,
  },
  textArea: {
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: Colors.text.primary,
    backgroundColor: Colors.white,
    minHeight: 100,
  },
  inputError: {
    borderColor: Colors.error,
  },
  errorText: {
    color: Colors.error,
    fontSize: 14,
    marginTop: 4,
  },
  categoriesScroll: {
    marginTop: 8,
  },
  categoryChip: {
    backgroundColor: Colors.background.secondary,
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 8,
  },
  selectedCategoryChip: {
    backgroundColor: Colors.primary,
    borderColor: Colors.primary,
  },
  categoryChipText: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.text.primary,
  },
  selectedCategoryChipText: {
    color: Colors.white,
  },
  statusContainer: {
    flexDirection: 'row',
    gap: 8,
    marginTop: 8,
  },
  statusChip: {
    backgroundColor: Colors.background.secondary,
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  selectedStatusChip: {
    backgroundColor: Colors.primary,
    borderColor: Colors.primary,
  },
  statusChipText: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.text.primary,
  },
  selectedStatusChipText: {
    color: Colors.white,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  loadingText: {
    marginLeft: 8,
    color: Colors.text.secondary,
  },
  bottomSpacing: {
    height: 40,
  },
});
