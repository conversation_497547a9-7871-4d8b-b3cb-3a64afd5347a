package com.yotelohago.media.application;

import com.yotelohago.auth.TokenIdentityProvider;
import com.yotelohago.media.domain.Media;
import com.yotelohago.media.domain.MediaRepository;
import com.yotelohago.media.domain.MediaType;
import com.yotelohago.user.domain.User;
import com.yotelohago.user.domain.UserRepository;
import com.yotelohago.professional.domain.Professional;
import com.yotelohago.professional.domain.ProfessionalRepository;
import io.minio.MinioClient;
import io.minio.GetPresignedObjectUrlArgs;
import io.minio.RemoveObjectArgs;
import io.minio.http.Method;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;
import jakarta.ws.rs.ForbiddenException;
import jakarta.ws.rs.NotFoundException;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Service for managing media uploads and metadata
 * Follows the same patterns as other management services
 */
@ApplicationScoped
public class MediaManagementService {

    private static final Logger logger = LoggerFactory.getLogger(MediaManagementService.class);

    @Inject
    MediaRepository mediaRepository;

    @Inject
    UserRepository userRepository;

    @Inject
    ProfessionalRepository professionalRepository;

    @Inject
    TokenIdentityProvider tokenIdentityProvider;

    @Inject
    MinioClient minioClient;

    @ConfigProperty(name = "minio.bucket")
    String bucket;

    @ConfigProperty(name = "media.max-file-size", defaultValue = "10485760") // 10MB
    Long maxFileSize;

    @ConfigProperty(name = "media.allowed-content-types", defaultValue = "image/jpeg,image/png,image/gif,image/webp")
    List<String> allowedContentTypes;

    @ConfigProperty(name = "media.presigned-url-expiry-minutes", defaultValue = "15")
    Integer presignedUrlExpiryMinutes;

    /**
     * Generate a presigned URL for uploading media
     * @param filename Original filename
     * @param contentType Content type of the file
     * @param fileSize File size in bytes
     * @param mediaType Type of media being uploaded
     * @param associatedEntityId Associated entity ID (for service photos)
     * @return Presigned URL and metadata
     */
    @Transactional
    public PresignedUrlResult generatePresignedUrl(String filename, String contentType, Long fileSize, 
                                                   MediaType mediaType, UUID associatedEntityId) {
        logger.info("🔗 Generating presigned URL for file: {} ({})", filename, contentType);

        // Get current user
        UUID currentUserId = tokenIdentityProvider.getCurrentUserId();
        Optional<User> userOpt = userRepository.findEntityById(currentUserId);
        if (userOpt.isEmpty()) {
            throw new ForbiddenException("User not found");
        }
        User user = userOpt.get();

        // Validate file size
        if (fileSize > maxFileSize) {
            throw new IllegalArgumentException("File size exceeds maximum allowed size of " + maxFileSize + " bytes");
        }

        // Validate content type
        if (!allowedContentTypes.contains(contentType)) {
            throw new IllegalArgumentException("Content type not allowed: " + contentType);
        }

        // Validate media type and authorization
        validateMediaTypeAuthorization(mediaType, associatedEntityId, currentUserId);

        // For profile pictures, delete existing profile image first
        if (mediaType == MediaType.CLIENT_PROFILE_PICTURE || mediaType == MediaType.PROFESSIONAL_PROFILE_PICTURE) {
            deleteExistingProfileImage(currentUserId, mediaType);
        }

        // Generate file path (always use profile.jpg for profile pictures)
        String filePath = generateFilePath(currentUserId, mediaType, associatedEntityId, filename);

        // Create media entity (will be persisted after successful upload)
        Media media = new Media(filePath, filename, fileSize, contentType, mediaType, user, associatedEntityId);
        mediaRepository.persist(media);
        mediaRepository.flush();

        // Update profile image URL in User or Professional entity for profile pictures
        updateProfileImageUrl(media, user);

        try {
            // Generate presigned URL
            String presignedUrl = minioClient.getPresignedObjectUrl(
                GetPresignedObjectUrlArgs.builder()
                    .method(Method.PUT)
                    .bucket(bucket)
                    .object(filePath)
                    .expiry(presignedUrlExpiryMinutes * 60) // Convert minutes to seconds
                    .build()
            );

            Instant expiresAt = Instant.now().plus(presignedUrlExpiryMinutes, ChronoUnit.MINUTES);

            logger.info("✅ Presigned URL generated for media: {} ({})", media.getId(), filePath);

            return new PresignedUrlResult(presignedUrl, filePath, media.getId(), expiresAt, maxFileSize);

        } catch (Exception e) {
            logger.error("❌ Failed to generate presigned URL for file: {}", filename, e);
            throw new RuntimeException("Failed to generate presigned URL", e);
        }
    }

    /**
     * Find media by ID with URL (efficient SQL-based URL building)
     * @param id Media ID
     * @return MediaDTO with publicUrl if found and user has access
     */
    public Optional<com.yotelohago.media.api.MediaDTO> findByIdWithUrl(UUID id) {
        Optional<com.yotelohago.media.api.MediaDTO> mediaDTOOpt = mediaRepository.findByIdWithUrl(id);
        if (mediaDTOOpt.isPresent()) {
            com.yotelohago.media.api.MediaDTO mediaDTO = mediaDTOOpt.get();
            // Check if current user can access this media
            UUID currentUserId = tokenIdentityProvider.getCurrentUserId();
            if (!currentUserId.equals(mediaDTO.userId) && !tokenIdentityProvider.hasRole("admin")) {
                throw new ForbiddenException("Not authorized to access this media");
            }
        }
        return mediaDTOOpt;
    }

    /**
     * Get media by user ID with URLs (efficient SQL-based URL building)
     * @param userId User ID
     * @param mediaType Optional media type filter (null for all media)
     * @return List of MediaDTOs with publicUrls
     */
    public List<com.yotelohago.media.api.MediaDTO> findByUserIdWithUrls(UUID userId, MediaType mediaType) {
        UUID currentUserId = tokenIdentityProvider.getCurrentUserId();

        // Users can only access their own media unless they're admin
        if (!currentUserId.equals(userId) && !tokenIdentityProvider.hasRole("admin")) {
            throw new ForbiddenException("Not authorized to access media for this user");
        }

        // Use appropriate repository method based on whether mediaType filter is provided
        if (mediaType != null) {
            return mediaRepository.findByUserIdAndTypeWithUrls(userId, mediaType);
        } else {
            return mediaRepository.findByUserIdWithUrls(userId);
        }
    }

    /**
     * Get all media by user ID with URLs (backward compatibility method)
     * @param userId User ID
     * @return List of MediaDTOs with publicUrls
     */
    public List<com.yotelohago.media.api.MediaDTO> findByUserIdWithUrls(UUID userId) {
        return findByUserIdWithUrls(userId, null);
    }

    /**
     * Get profile photo for user with URL (efficient SQL-based URL building)
     * @param userId User ID
     * @param mediaType Media type (CLIENT_PROFILE_PICTURE or PROFESSIONAL_PROFILE_PICTURE)
     * @return Optional MediaDTO with publicUrl
     */
    public Optional<com.yotelohago.media.api.MediaDTO> findProfilePhotoWithUrl(UUID userId, MediaType mediaType) {
        return mediaRepository.findProfilePhotoByUserAndTypeWithUrl(userId, mediaType);
    }



    /**
     * Delete media by ID
     * @param id Media ID
     * @return true if deleted successfully
     */
    @Transactional
    public boolean deleteById(UUID id) {
        Optional<com.yotelohago.media.api.MediaDTO> mediaDTOOpt = mediaRepository.findByIdWithUrl(id);
        if (mediaDTOOpt.isEmpty()) {
            return false;
        }

        com.yotelohago.media.api.MediaDTO mediaDTO = mediaDTOOpt.get();
        UUID currentUserId = tokenIdentityProvider.getCurrentUserId();

        // Check authorization
        if (!currentUserId.equals(mediaDTO.userId) && !tokenIdentityProvider.hasRole("admin")) {
            throw new ForbiddenException("Not authorized to delete this media");
        }

        try {
            // Delete from MinIO
            minioClient.removeObject(
                RemoveObjectArgs.builder()
                    .bucket(bucket)
                    .object(mediaDTO.filePath)
                    .build()
            );

            // Delete from database
            boolean deleted = mediaRepository.deleteById(id);

            logger.info("✅ Media deleted successfully: {} ({})", id, mediaDTO.filePath);
            return true;

        } catch (Exception e) {
            logger.error("❌ Failed to delete media: {} ({})", id, mediaDTO.filePath, e);
            throw new RuntimeException("Failed to delete media", e);
        }
    }

    /**
     * Generate file path based on media type and user
     * Profile pictures always use 'profile.jpg' filename
     */
    private String generateFilePath(UUID userId, MediaType mediaType, UUID associatedEntityId, String filename) {
        String extension = getFileExtension(filename);
        String timestamp = String.valueOf(System.currentTimeMillis());

        return switch (mediaType) {
            case CLIENT_PROFILE_PICTURE -> String.format("users/%s/client/profile.jpg", userId);
            case PROFESSIONAL_PROFILE_PICTURE -> String.format("users/%s/professional/profile.jpg", userId);
            case SERVICE_PHOTO -> {
                if (associatedEntityId == null) {
                    throw new IllegalArgumentException("Service ID is required for service photos");
                }
                yield String.format("users/%s/professional/services/%s/photo_%s%s", userId, associatedEntityId, timestamp, extension);
            }
            case DOCUMENT -> String.format("users/%s/documents/doc_%s%s", userId, timestamp, extension);
        };
    }

    /**
     * Extract file extension from filename
     */
    private String getFileExtension(String filename) {
        int lastDotIndex = filename.lastIndexOf('.');
        return lastDotIndex > 0 ? filename.substring(lastDotIndex) : "";
    }

    /**
     * Validate media type authorization
     */
    private void validateMediaTypeAuthorization(MediaType mediaType, UUID associatedEntityId, UUID currentUserId) {
        switch (mediaType) {
            case PROFESSIONAL_PROFILE_PICTURE, SERVICE_PHOTO -> {
                if (!tokenIdentityProvider.hasRole("professional")) {
                    throw new ForbiddenException("Professional role required for this media type");
                }
            }
            case CLIENT_PROFILE_PICTURE, DOCUMENT -> {
                // Any authenticated user can upload these
            }
        }

        // For service photos, validate that the service belongs to the current user
        if (mediaType == MediaType.SERVICE_PHOTO && associatedEntityId != null) {
            // This would require injecting ServiceRepository to validate ownership
            // For now, we'll trust that the professional role check is sufficient
        }
    }

    /**
     * Update profile image URL in User or Professional entity after successful upload
     * Note: URLs are now built in repository SQL queries, not in service layer
     */
    @Transactional
    protected void updateProfileImageUrl(Media media, User user) {
        switch (media.mediaType) {
            case CLIENT_PROFILE_PICTURE -> {
                // Client profile images are handled via enhanced /users/me endpoint
                logger.info("✅ Client profile image uploaded for user: {}", user.getId());
            }
            case PROFESSIONAL_PROFILE_PICTURE -> {
                // Professional profile images are now fetched from Media entities in service layer
                logger.info("✅ Professional profile image uploaded for user: {}", user.getId());
            }
            default -> {
                // No profile image URL update needed for other media types
            }
        }
    }

    /**
     * Delete existing profile image for user before uploading new one
     * Ensures only one profile image per user per mode
     */
    @Transactional
    protected void deleteExistingProfileImage(UUID userId, MediaType mediaType) {
        try {
            logger.info("🗑️ Checking for existing profile images for user: {} (type: {})", userId, mediaType);

            // Find existing profile images of the same type using DTO method (we only need the IDs and file paths)
            List<com.yotelohago.media.api.MediaDTO> existingMedia = mediaRepository.findByUserIdAndTypeWithUrls(userId, mediaType);

            for (com.yotelohago.media.api.MediaDTO mediaDTO : existingMedia) {
                logger.info("🗑️ Deleting existing profile image: {}", mediaDTO.filePath);

                // Delete from MinIO
                try {
                    minioClient.removeObject(
                        RemoveObjectArgs.builder()
                            .bucket(bucket)
                            .object(mediaDTO.filePath)
                            .build()
                    );
                    logger.info("✅ Deleted file from MinIO: {}", mediaDTO.filePath);
                } catch (Exception e) {
                    logger.warn("⚠️ Failed to delete file from MinIO: {} - {}", mediaDTO.filePath, e.getMessage());
                    // Continue with database deletion even if MinIO deletion fails
                }

                // Delete from database using ID
                mediaRepository.deleteById(mediaDTO.id);
                logger.info("✅ Deleted media record from database: {}", mediaDTO.id);
            }

            if (!existingMedia.isEmpty()) {
                logger.info("✅ Deleted {} existing profile image(s) for user: {}", existingMedia.size(), userId);
            }

        } catch (Exception e) {
            logger.error("❌ Failed to delete existing profile images for user: {} - {}", userId, e.getMessage());
            // Don't throw exception - allow new upload to proceed
        }
    }





    /**
     * Result class for presigned URL generation
     */
    public static class PresignedUrlResult {
        public final String uploadUrl;
        public final String filePath;
        public final UUID mediaId;
        public final Instant expiresAt;
        public final Long maxFileSize;

        public PresignedUrlResult(String uploadUrl, String filePath, UUID mediaId, Instant expiresAt, Long maxFileSize) {
            this.uploadUrl = uploadUrl;
            this.filePath = filePath;
            this.mediaId = mediaId;
            this.expiresAt = expiresAt;
            this.maxFileSize = maxFileSize;
        }
    }
}
