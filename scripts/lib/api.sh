#!/bin/bash

# YoteLoHago API Library
# Functions for testing API endpoints and configurations

# Function to test API health endpoints
test_health_endpoints() {
    local base_url=${1:-$BACKEND_URL}
    
    print_status $CYAN "🏥 Testing health endpoints on $base_url..."
    
    echo ""
    print_status $BLUE "Basic Health Checks:"
    
    # Test root endpoint
    test_api_endpoint "/" "" "GET" "" "$base_url"
    
    # Test health endpoint
    test_api_endpoint "/q/health" "" "GET" "" "$base_url"
    
    # Test readiness endpoint
    test_api_endpoint "/q/health/ready" "" "GET" "" "$base_url"
    
    # Test liveness endpoint
    test_api_endpoint "/q/health/live" "" "GET" "" "$base_url"
    
    # Test metrics endpoint
    test_api_endpoint "/q/metrics" "" "GET" "" "$base_url"
}

# Function to test public endpoints
test_public_endpoints() {
    local base_url=${1:-$BACKEND_URL}
    
    print_status $CYAN "🌐 Testing public endpoints on $base_url..."
    
    echo ""
    print_status $BLUE "Public API Endpoints:"
    
    # Test public service endpoints
    test_api_endpoint "/v1/services" "" "GET" "" "$base_url"
    test_api_endpoint "/v1/services/categories" "" "GET" "" "$base_url"
    
    # Test public professional endpoints
    test_api_endpoint "/v1/professionals" "" "GET" "" "$base_url"
    
    # Test public review endpoints
    test_api_endpoint "/v1/reviews" "" "GET" "" "$base_url"
    
    # Test OpenAPI documentation
    test_api_endpoint "/q/openapi" "" "GET" "" "$base_url"
    test_api_endpoint "/q/swagger-ui" "" "GET" "" "$base_url"
}

# Function to test protected endpoints (should return 401)
test_protected_endpoints() {
    local base_url=${1:-$BACKEND_URL}

    print_status $CYAN "🔒 Testing protected endpoints (should return 401) on $base_url..."

    echo ""
    print_status $BLUE "Protected Endpoints (without authentication):"

    # Test user endpoints
    test_api_endpoint "/v1/users" "" "GET" "" "$base_url"
    test_api_endpoint "/v1/users/me" "" "GET" "" "$base_url"

    # Test booking endpoints
    test_api_endpoint "/v1/bookings" "" "GET" "" "$base_url"
    test_api_endpoint "/v1/bookings" "" "POST" '{"serviceId":1001,"notes":"Test"}' "$base_url"

    # Test message endpoints
    test_api_endpoint "/v1/messages" "" "POST" '{"bookingId":1001,"content":"Test"}' "$base_url"
    test_api_endpoint "/v1/messages/booking/1001" "" "GET" "" "$base_url"
    test_api_endpoint "/v1/messages/conversations" "" "GET" "" "$base_url"
    test_api_endpoint "/v1/messages/unread-count" "" "GET" "" "$base_url"

    # Test favorites endpoints
    test_api_endpoint "/v1/favorites" "" "GET" "" "$base_url"
    test_api_endpoint "/v1/favorites/service-ids" "" "GET" "" "$base_url"
    test_api_endpoint "/v1/favorites/550e8400-e29b-41d4-a716-446655440301" "" "POST" "" "$base_url"
    test_api_endpoint "/v1/favorites/550e8400-e29b-41d4-a716-446655440301" "" "DELETE" "" "$base_url"
    test_api_endpoint "/v1/favorites/check/550e8400-e29b-41d4-a716-446655440301" "" "GET" "" "$base_url"
    test_api_endpoint "/v1/favorites/550e8400-e29b-41d4-a716-446655440301/toggle" "" "PUT" "" "$base_url"
    test_api_endpoint "/v1/favorites/count" "" "GET" "" "$base_url"

    # Test service creation (professional only)
    test_api_endpoint "/v1/services" "" "POST" '{"title":"Test Service","description":"Test","categoryId":"11111111-2222-3333-4444-555555555555","price":100}' "$base_url"

    # Test admin endpoints
    test_api_endpoint "/api/admin" "" "GET" "" "$base_url"
}

# Function to test CORS configuration
test_cors_configuration() {
    local base_url=${1:-$BACKEND_URL}
    
    print_status $CYAN "🌍 Testing CORS configuration on $base_url..."
    
    echo ""
    print_status $BLUE "CORS Preflight Requests:"
    
    # Test CORS preflight for common origins
    local origins=(
        "http://localhost:8082"
        "https://yotelohago.co"
        "https://app.yotelohago.co"
    )
    
    for origin in "${origins[@]}"; do
        echo "Testing origin: $origin"
        
        local response
        response=$(http_request OPTIONS "$base_url/v1/services" \
            -H "Origin: $origin" \
            -H "Access-Control-Request-Method: GET" \
            -H "Access-Control-Request-Headers: Authorization,Content-Type" \
            -s)
        local status_code
        status_code=$(echo "$response" | cut -d'|' -f1)
        
        echo "  Status: $status_code"
        
        if [[ "$status_code" == "200" ]] || [[ "$status_code" == "204" ]]; then
            print_status $GREEN "  ✅ CORS allowed for $origin"
        else
            print_status $RED "  ❌ CORS blocked for $origin"
        fi
    done
}

# Function to test production API comprehensively
test_production_api() {
    print_status $CYAN "🚀 Testing production API comprehensively..."
    
    echo ""
    print_status $BLUE "🌐 Production Health Check:"
    test_health_endpoints "$PRODUCTION_API_URL"
    
    echo ""
    print_status $BLUE "🔓 Production Public Endpoints:"
    test_public_endpoints "$PRODUCTION_API_URL"
    
    echo ""
    print_status $BLUE "🔒 Production Protected Endpoints:"
    test_protected_endpoints "$PRODUCTION_API_URL"
    
    echo ""
    print_status $BLUE "🌍 Production CORS Configuration:"
    test_cors_configuration "$PRODUCTION_API_URL"
    
    echo ""
    print_status $BLUE "🔐 Production Authentication Test:"
    local token
    token=$(get_user_token "$DEFAULT_USERNAME" "$DEFAULT_PASSWORD")
    
    if [[ "$token" != "null" ]] && [[ -n "$token" ]]; then
        print_status $GREEN "✅ Production authentication working"
        
        echo ""
        print_status $BLUE "🧪 Production Authenticated Endpoints:"
        test_api_endpoint "/v1/users/me" "$token" "GET" "" "$PRODUCTION_API_URL"
        test_api_endpoint "/v1/bookings" "$token" "GET" "" "$PRODUCTION_API_URL"
        test_api_endpoint "/v1/professionals" "$token" "GET" "" "$PRODUCTION_API_URL"
    else
        print_status $RED "❌ Production authentication failed"
    fi
}

# Function to test API performance
test_api_performance() {
    local base_url=${1:-$BACKEND_URL}
    local endpoint=${2:-"/v1/services"}
    local iterations=${3:-10}
    
    print_status $CYAN "⚡ Testing API performance for $endpoint ($iterations requests)..."
    
    local total_time=0
    local success_count=0
    local min_time=999999
    local max_time=0
    
    for i in $(seq 1 "$iterations"); do
        local start_time
        start_time=$(date +%s%3N)  # milliseconds
        
        local response
        response=$(http_request GET "$base_url$endpoint" \
            -H "Accept: application/json" -s)
        local status_code
        status_code=$(echo "$response" | cut -d'|' -f1)
        
        local end_time
        end_time=$(date +%s%3N)
        local request_time=$((end_time - start_time))
        
        if [[ "$status_code" == "200" ]]; then
            ((success_count++))
            total_time=$((total_time + request_time))
            
            if [[ $request_time -lt $min_time ]]; then
                min_time=$request_time
            fi
            
            if [[ $request_time -gt $max_time ]]; then
                max_time=$request_time
            fi
        fi
        
        echo "Request $i: ${request_time}ms (Status: $status_code)"
    done
    
    echo ""
    print_status $BLUE "📊 Performance Results:"
    echo "  Successful requests: $success_count/$iterations"
    
    if [[ $success_count -gt 0 ]]; then
        local avg_time=$((total_time / success_count))
        echo "  Average response time: ${avg_time}ms"
        echo "  Minimum response time: ${min_time}ms"
        echo "  Maximum response time: ${max_time}ms"
        
        if [[ $avg_time -lt 500 ]]; then
            print_status $GREEN "🚀 Excellent performance (< 500ms)"
        elif [[ $avg_time -lt 1000 ]]; then
            print_status $YELLOW "⚠️ Good performance (< 1s)"
        else
            print_status $RED "🐌 Slow performance (> 1s)"
        fi
    fi
}

# Function to test API data consistency
test_data_consistency() {
    local base_url=${1:-$BACKEND_URL}
    
    print_status $CYAN "🔍 Testing API data consistency on $base_url..."
    
    echo ""
    print_status $BLUE "📊 Data Consistency Checks:"
    
    # Get services and check if they reference valid categories
    local services_response
    services_response=$(http_request GET "$base_url/v1/services" \
        -H "Accept: application/json" -s)
    local services_status
    services_status=$(echo "$services_response" | cut -d'|' -f1)
    
    if [[ "$services_status" == "200" ]]; then
        local services_body
        services_body=$(echo "$services_response" | cut -d'|' -f2-)
        
        # Get categories
        local categories_response
        categories_response=$(http_request GET "$base_url/v1/services/categories" \
            -H "Accept: application/json" -s)
        local categories_status
        categories_status=$(echo "$categories_response" | cut -d'|' -f1)
        
        if [[ "$categories_status" == "200" ]]; then
            local categories_body
            categories_body=$(echo "$categories_response" | cut -d'|' -f2-)
            
            # Check if all services have valid category references
            local service_count
            service_count=$(echo "$services_body" | jq '. | length')
            local category_count
            category_count=$(echo "$categories_body" | jq '. | length')
            
            echo "  Services found: $service_count"
            echo "  Categories found: $category_count"
            
            # Check for orphaned services
            local orphaned_services
            orphaned_services=$(echo "$services_body" | jq --argjson categories "$categories_body" '
                [.[] | select(.categoryId as $catId | $categories | map(.id) | index($catId) | not)]
            ')
            local orphaned_count
            orphaned_count=$(echo "$orphaned_services" | jq '. | length')
            
            if [[ "$orphaned_count" == "0" ]]; then
                print_status $GREEN "  ✅ All services have valid category references"
            else
                print_status $RED "  ❌ Found $orphaned_count services with invalid category references"
            fi
        else
            print_status $YELLOW "  ⚠️ Could not fetch categories for consistency check"
        fi
    else
        print_status $YELLOW "  ⚠️ Could not fetch services for consistency check"
    fi
    
    # Check professionals and their services
    local professionals_response
    professionals_response=$(http_request GET "$base_url/v1/professionals" \
        -H "Accept: application/json" -s)
    local professionals_status
    professionals_status=$(echo "$professionals_response" | cut -d'|' -f1)
    
    if [[ "$professionals_status" == "200" ]]; then
        local professionals_body
        professionals_body=$(echo "$professionals_response" | cut -d'|' -f2-)
        local professional_count
        professional_count=$(echo "$professionals_body" | jq '. | length')
        
        echo "  Professionals found: $professional_count"
        
        # Check if professionals have services
        local professionals_with_services
        professionals_with_services=$(echo "$professionals_body" | jq '[.[] | select(.services and (.services | length > 0))] | length')
        
        echo "  Professionals with services: $professionals_with_services"
        
        if [[ "$professionals_with_services" -gt 0 ]]; then
            print_status $GREEN "  ✅ Some professionals have services assigned"
        else
            print_status $YELLOW "  ⚠️ No professionals have services assigned"
        fi
    else
        print_status $YELLOW "  ⚠️ Could not fetch professionals for consistency check"
    fi
}

# Function to test Service Catalogue pagination
test_service_pagination() {
    local base_url=${1:-$BACKEND_URL}

    print_status $CYAN "📄 Testing Service Catalogue pagination on $base_url..."

    echo ""
    print_status $BLUE "Pagination Tests:"

    # Test 1: Default pagination
    echo "1. Testing default pagination (page=0, size=20):"
    local response
    response=$(curl -s "$base_url/v1/services" | jq '{totalElements, totalPages, size, number, numberOfElements}')
    echo "   $response"

    # Test 2: Custom page size
    echo "2. Testing custom page size (size=3):"
    response=$(curl -s "$base_url/v1/services?size=3" | jq '{totalElements, totalPages, size, number, numberOfElements}')
    echo "   $response"

    # Test 3: Second page
    echo "3. Testing second page (page=1, size=3):"
    response=$(curl -s "$base_url/v1/services?page=1&size=3" | jq '{totalElements, totalPages, size, number, numberOfElements, content: [.content[].title]}')
    echo "   $response"

    # Test 4: Large page size (should be limited to 100)
    echo "4. Testing large page size (size=200, should be limited to 100):"
    response=$(curl -s "$base_url/v1/services?size=200" | jq '{size}')
    echo "   $response"

    print_status $GREEN "✅ Pagination tests completed"
}

# Function to test Service Catalogue filtering (PUBLISHED services only)
test_service_filtering() {
    local base_url=${1:-$BACKEND_URL}

    print_status $CYAN "🔍 Testing Service Catalogue filtering (PUBLISHED only) on $base_url..."

    echo ""
    print_status $BLUE "Public Filtering Tests (PUBLISHED services only):"

    # Test 1: Filter by category (Electrician)
    echo "1. Testing category filter (Electrician):"
    local response
    response=$(curl -s "$base_url/v1/services?category=11111111-2222-3333-4444-555555555555" | jq '{totalElements, content: [.content[] | {title, categoryName, status}]}')
    echo "   $response"

    # Test 2: Filter by price range
    echo "2. Testing price range filter (100-200):"
    response=$(curl -s "$base_url/v1/services?minPrice=100&maxPrice=200" | jq '{totalElements, content: [.content[] | {title, price, status}]}')
    echo "   $response"

    # Test 3: Search functionality
    echo "3. Testing search functionality (emergency):"
    response=$(curl -s "$base_url/v1/services?search=emergency" | jq '{totalElements, content: [.content[] | {title, categoryName, status}]}')
    echo "   $response"

    # Test 4: Combined filters
    echo "4. Testing combined filters (category + price range):"
    response=$(curl -s "$base_url/v1/services?category=11111111-2222-3333-4444-555555555555&minPrice=100&maxPrice=400" | jq '{totalElements, content: [.content[] | {title, price, categoryName, status}]}')
    echo "   $response"

    # Test 5: Verify only PUBLISHED services are returned
    echo "5. Verifying only PUBLISHED services are returned:"
    local all_services_response
    all_services_response=$(curl -s "$base_url/v1/services")
    local draft_count
    draft_count=$(echo "$all_services_response" | jq '[.content[] | select(.status == "DRAFT")] | length')
    local published_count
    published_count=$(echo "$all_services_response" | jq '[.content[] | select(.status == "PUBLISHED")] | length')

    echo "   DRAFT services in public endpoint: $draft_count (should be 0)"
    echo "   PUBLISHED services in public endpoint: $published_count"

    if [[ "$draft_count" == "0" ]]; then
        print_status $GREEN "   ✅ Public endpoint correctly filters DRAFT services"
    else
        print_status $RED "   ❌ Public endpoint showing DRAFT services!"
    fi

    print_status $GREEN "✅ Filtering tests completed"
}

# Function to test Service Catalogue sorting
test_service_sorting() {
    local base_url=${1:-$BACKEND_URL}

    print_status $CYAN "🔄 Testing Service Catalogue sorting on $base_url..."

    echo ""
    print_status $BLUE "Sorting Tests:"

    # Test 1: Sort by price ascending
    echo "1. Testing sort by price ascending:"
    local response
    response=$(curl -s "$base_url/v1/services?sort=price&direction=asc&size=3" | jq '{content: [.content[] | {title, price}]}')
    echo "   $response"

    # Test 2: Sort by price descending
    echo "2. Testing sort by price descending:"
    response=$(curl -s "$base_url/v1/services?sort=price&direction=desc&size=3" | jq '{content: [.content[] | {title, price}]}')
    echo "   $response"

    # Test 3: Sort by title
    echo "3. Testing sort by title:"
    response=$(curl -s "$base_url/v1/services?sort=title&size=3" | jq '{content: [.content[] | {title}]}')
    echo "   $response"

    print_status $GREEN "✅ Sorting tests completed"
}

# Function to test Service Catalogue caching
test_service_caching() {
    local base_url=${1:-$BACKEND_URL}

    print_status $CYAN "⚡ Testing Service Catalogue caching on $base_url..."

    echo ""
    print_status $BLUE "Caching Performance Tests:"

    # Test caching with multiple identical requests
    echo "1. Testing cache performance (5 identical requests):"
    local endpoint="$base_url/v1/services?size=5"

    for i in {1..5}; do
        local start_time
        start_time=$(python3 -c "import time; print(int(time.time() * 1000))" 2>/dev/null || date +%s000)

        curl -s "$endpoint" > /dev/null

        local end_time
        end_time=$(python3 -c "import time; print(int(time.time() * 1000))" 2>/dev/null || date +%s000)
        local request_time=$((end_time - start_time))

        echo "   Request $i: ${request_time}ms"
    done

    print_status $GREEN "✅ Caching tests completed"
}

# Function to test Service Categories
test_service_categories() {
    local base_url=${1:-$BACKEND_URL}

    print_status $CYAN "📂 Testing Service Categories on $base_url..."

    echo ""
    print_status $BLUE "Service Category Tests:"

    # Test 1: Get all categories
    echo "1. Testing get all categories:"
    local response
    response=$(curl -s "$base_url/v1/services/categories" 2>/dev/null | jq '[.[] | {name, description}]' 2>/dev/null || echo "Categories endpoint not available")
    echo "   $response"

    # Test 2: Verify required categories exist
    echo "2. Verifying required categories (Electrician, Plumber, Mover, Locksmith):"
    local services_response
    services_response=$(curl -s "$base_url/v1/services")
    local categories
    categories=$(echo "$services_response" | jq '[.content[].categoryName] | unique | sort')
    echo "   Available categories: $categories"

    # Check for each required category
    local required_categories=("electrician" "plumber" "mover" "locksmith")
    for category in "${required_categories[@]}"; do
        local count
        count=$(echo "$services_response" | jq --arg cat "$category" '[.content[] | select(.categoryName == $cat)] | length')
        if [[ "$count" -gt 0 ]]; then
            print_status $GREEN "   ✅ $category: $count services"
        else
            print_status $RED "   ❌ $category: 0 services"
        fi
    done

    print_status $GREEN "✅ Service category tests completed"
}

# Function to test Availability Domain (Work Schedules)
test_availability_domain() {
    local base_url=${1:-$BACKEND_URL}

    print_status $CYAN "📅 Testing Availability Domain (Work Schedules) on $base_url..."

    echo ""
    print_status $BLUE "🔐 Getting authentication token for test user:"

    # Get token for test client
    local client_token
    client_token=$(get_user_token "test1client1" "test1client1")
    if [[ -z "$client_token" ]]; then
        print_status $RED "❌ Failed to get token for test1client1"
        return 1
    fi
    print_status $GREEN "✅ Client token obtained (test1client1)"

    echo ""
    print_status $BLUE "Work Schedule Tests:"

    # Test 1: Get work schedule for a professional (authenticated access)
    echo "1. Testing get work schedule for professional (test1professional1):"
    local professional_id="00000000-0000-0000-0000-000000000201"
    local response
    response=$(http_request GET "$base_url/v1/availability/professionals/$professional_id" \
        -H "Authorization: Bearer $client_token" \
        -H "Accept: application/json" -s)
    local status_code
    status_code=$(echo "$response" | cut -d'|' -f1)

    if [[ "$status_code" == "200" ]]; then
        local schedule_body
        schedule_body=$(echo "$response" | cut -d'|' -f2-)
        local summary
        summary=$(echo "$schedule_body" | jq -r '.summary // "No summary"' 2>/dev/null || echo "Error parsing")
        local active_days
        active_days=$(echo "$schedule_body" | jq -r '.hasAnyActiveDays // false' 2>/dev/null || echo "false")
        echo "   Status: $status_code"
        echo "   Summary: $summary"
        echo "   Has active days: $active_days"
        print_status $GREEN "   ✅ Work schedule endpoint working"
    else
        local error_body
        error_body=$(echo "$response" | cut -d'|' -f2-)
        print_status $RED "   ❌ Failed to get work schedule (Status: $status_code)"
        echo "   Error: $error_body"
    fi

    # Test 2: Check availability for specific day and time
    echo ""
    echo "2. Testing availability check (Monday 10:00):"
    response=$(http_request GET "$base_url/v1/availability/professionals/$professional_id/check?dayOfWeek=MONDAY&time=10:00" \
        -H "Authorization: Bearer $client_token" \
        -H "Accept: application/json" -s)
    status_code=$(echo "$response" | cut -d'|' -f1)

    if [[ "$status_code" == "200" ]]; then
        local check_body
        check_body=$(echo "$response" | cut -d'|' -f2-)
        echo "   Status: $status_code"
        echo "   Available: $check_body"
        print_status $GREEN "   ✅ Availability check endpoint working"
    else
        print_status $RED "   ❌ Availability check failed (Status: $status_code)"
    fi

    # Test 3: Find professionals available on specific day
    echo ""
    echo "3. Testing find professionals available on Monday:"
    response=$(http_request GET "$base_url/v1/availability/day/MONDAY" \
        -H "Authorization: Bearer $client_token" \
        -H "Accept: application/json" -s)
    status_code=$(echo "$response" | cut -d'|' -f1)

    if [[ "$status_code" == "200" ]]; then
        local day_body
        day_body=$(echo "$response" | cut -d'|' -f2-)
        local count
        count=$(echo "$day_body" | jq '. | length' 2>/dev/null || echo "0")
        echo "   Status: $status_code"
        echo "   Professionals available on Monday: $count"
        print_status $GREEN "   ✅ Day availability endpoint working"
    else
        print_status $RED "   ❌ Day availability failed (Status: $status_code)"
    fi

    # Test 4: Test all professionals' work schedules
    echo ""
    echo "4. Testing work schedules for all professionals:"
    local professional_ids=(
        "00000000-0000-0000-0000-000000000201"  # test1professional1 (Plumber)
        "00000000-0000-0000-0000-000000000202"  # test2professional2 (Electrician)
        "00000000-0000-0000-0000-000000000203"  # test3professional3 (Locksmith)
        "00000000-0000-0000-0000-000000000204"  # test4professional4 (Electrician)
        "00000000-0000-0000-0000-000000000205"  # test5professional5 (Mover)
    )

    for prof_id in "${professional_ids[@]}"; do
        local schedule_response
        schedule_response=$(http_request GET "$base_url/v1/availability/professionals/$prof_id" \
            -H "Authorization: Bearer $client_token" \
            -H "Accept: application/json" -s)
        local status_code
        status_code=$(echo "$schedule_response" | cut -d'|' -f1)

        if [[ "$status_code" == "200" ]]; then
            local schedule_body
            schedule_body=$(echo "$schedule_response" | cut -d'|' -f2-)
            local summary
            summary=$(echo "$schedule_body" | jq -r '.summary // "No schedule"' 2>/dev/null || echo "Error")
            local active_days
            active_days=$(echo "$schedule_body" | jq -r '.hasAnyActiveDays // false' 2>/dev/null || echo "false")
            echo "   Professional $prof_id: $summary (Active: $active_days)"
        else
            echo "   Professional $prof_id: Error (Status: $status_code)"
        fi
    done

    # Test 5: Test different days of the week
    echo ""
    echo "5. Testing availability by day of week:"
    local days=("MONDAY" "TUESDAY" "WEDNESDAY" "THURSDAY" "FRIDAY" "SATURDAY" "SUNDAY")

    for day in "${days[@]}"; do
        local day_response
        day_response=$(http_request GET "$base_url/v1/availability/day/$day" \
            -H "Authorization: Bearer $client_token" \
            -H "Accept: application/json" -s)
        local status_code
        status_code=$(echo "$day_response" | cut -d'|' -f1)

        if [[ "$status_code" == "200" ]]; then
            local day_body
            day_body=$(echo "$day_response" | cut -d'|' -f2-)
            local count
            count=$(echo "$day_body" | jq '. | length' 2>/dev/null || echo "0")
            echo "   $day: $count professionals available"
        else
            echo "   $day: Error (Status: $status_code)"
        fi
    done

    print_status $GREEN "✅ Availability domain tests completed"
}

# Function to test Availability Domain data consistency
test_availability_data_consistency() {
    local base_url=${1:-$BACKEND_URL}

    print_status $CYAN "🔍 Testing Availability Domain data consistency on $base_url..."

    echo ""
    print_status $BLUE "🔐 Getting authentication token for test user:"

    # Get token for test client
    local client_token
    client_token=$(get_user_token "test1client1" "test1client1")
    if [[ -z "$client_token" ]]; then
        print_status $RED "❌ Failed to get token for test1client1"
        return 1
    fi
    print_status $GREEN "✅ Client token obtained (test1client1)"

    echo ""
    print_status $BLUE "Availability Data Consistency Checks:"

    # Test 1: Verify all professionals have work schedules
    echo "1. Checking if all professionals have work schedules:"
    local professionals_response
    professionals_response=$(curl -s "$base_url/v1/professionals" 2>/dev/null)
    local professional_count
    professional_count=$(echo "$professionals_response" | jq '. | length' 2>/dev/null || echo "0")

    echo "   Total professionals: $professional_count"

    local schedules_with_data=0
    local schedules_empty=0

    # Check each professional's work schedule
    local professional_ids
    professional_ids=$(echo "$professionals_response" | jq -r '.[].id' 2>/dev/null)

    while IFS= read -r prof_id; do
        if [[ -n "$prof_id" && "$prof_id" != "null" ]]; then
            local schedule_response
            schedule_response=$(http_request GET "$base_url/v1/availability/professionals/$prof_id" \
                -H "Authorization: Bearer $client_token" \
                -H "Accept: application/json" -s)
            local status_code
            status_code=$(echo "$schedule_response" | cut -d'|' -f1)

            if [[ "$status_code" == "200" ]]; then
                local schedule_body
                schedule_body=$(echo "$schedule_response" | cut -d'|' -f2-)
                local has_active_days
                has_active_days=$(echo "$schedule_body" | jq -r '.hasAnyActiveDays // false' 2>/dev/null)

                if [[ "$has_active_days" == "true" ]]; then
                    ((schedules_with_data++))
                else
                    ((schedules_empty++))
                fi
            else
                ((schedules_empty++))
            fi
        fi
    done <<< "$professional_ids"

    echo "   Professionals with active schedules: $schedules_with_data"
    echo "   Professionals with empty schedules: $schedules_empty"

    if [[ $schedules_with_data -gt 0 ]]; then
        print_status $GREEN "   ✅ Some professionals have active work schedules"
    else
        print_status $RED "   ❌ No professionals have active work schedules"
    fi

    # Test 2: Verify time range consistency (5-minute granularity)
    echo "2. Checking time range consistency (5-minute granularity):"
    local sample_schedule
    sample_schedule=$(http_request GET "$base_url/v1/availability/professionals/00000000-0000-0000-0000-000000000201" \
        -H "Authorization: Bearer $client_token" \
        -H "Accept: application/json" -s)
    local status_code
    status_code=$(echo "$sample_schedule" | cut -d'|' -f1)

    if [[ "$status_code" == "200" ]]; then
        local schedule_body
        schedule_body=$(echo "$sample_schedule" | cut -d'|' -f2-)
        local time_ranges
        time_ranges=$(echo "$schedule_body" | jq -r '.daySchedules[]?.timeRanges[]? | "\(.startTime) - \(.endTime)"' 2>/dev/null)

        if [[ -n "$time_ranges" ]]; then
            echo "   Sample time ranges:"
            while IFS= read -r range; do
                if [[ -n "$range" ]]; then
                    echo "     $range"
                fi
            done <<< "$time_ranges"
            print_status $GREEN "   ✅ Time ranges are properly formatted"
        else
            print_status $YELLOW "   ⚠️ No time ranges found in sample schedule"
        fi
    else
        print_status $RED "   ❌ Failed to get sample schedule (Status: $status_code)"
    fi

    # Test 3: Verify day coverage
    echo "3. Checking day coverage across all professionals:"
    local day_coverage=()
    local days=("MONDAY" "TUESDAY" "WEDNESDAY" "THURSDAY" "FRIDAY" "SATURDAY" "SUNDAY")

    for day in "${days[@]}"; do
        local day_response
        day_response=$(http_request GET "$base_url/v1/availability/day/$day" \
            -H "Authorization: Bearer $client_token" \
            -H "Accept: application/json" -s)
        local status_code
        status_code=$(echo "$day_response" | cut -d'|' -f1)

        if [[ "$status_code" == "200" ]]; then
            local day_body
            day_body=$(echo "$day_response" | cut -d'|' -f2-)
            local count
            count=$(echo "$day_body" | jq '. | length' 2>/dev/null || echo "0")
            day_coverage+=("$day:$count")
        else
            day_coverage+=("$day:0")
        fi
    done

    echo "   Day coverage:"
    for coverage in "${day_coverage[@]}"; do
        local day_name
        day_name=$(echo "$coverage" | cut -d':' -f1)
        local day_count
        day_count=$(echo "$coverage" | cut -d':' -f2)
        echo "     $day_name: $day_count professionals"
    done

    # Check if weekdays have better coverage than weekends
    local weekday_total=0
    local weekend_total=0

    for coverage in "${day_coverage[@]}"; do
        local day_name
        day_name=$(echo "$coverage" | cut -d':' -f1)
        local day_count
        day_count=$(echo "$coverage" | cut -d':' -f2)

        if [[ "$day_name" == "SATURDAY" || "$day_name" == "SUNDAY" ]]; then
            weekend_total=$((weekend_total + day_count))
        else
            weekday_total=$((weekday_total + day_count))
        fi
    done

    echo "   Weekday coverage: $weekday_total total slots"
    echo "   Weekend coverage: $weekend_total total slots"

    if [[ $weekday_total -gt $weekend_total ]]; then
        print_status $GREEN "   ✅ Weekday coverage is higher than weekend (expected pattern)"
    else
        print_status $YELLOW "   ⚠️ Weekend coverage is equal or higher than weekday"
    fi

    print_status $GREEN "✅ Availability data consistency checks completed"
}

# Function to test professional service endpoints (requires authentication)
test_professional_service_endpoints() {
    local base_url=${1:-$BACKEND_URL}
    local token=${2:-""}

    print_status $CYAN "👨‍💼 Testing Professional Service endpoints on $base_url..."

    if [[ -z "$token" ]]; then
        print_status $YELLOW "⚠️ No authentication token provided. Getting token for professional user..."
        token=$(get_user_token "<EMAIL>" "password123")

        if [[ "$token" == "null" ]] || [[ -z "$token" ]]; then
            print_status $RED "❌ Failed to get authentication token. Skipping professional tests."
            return 1
        fi
    fi

    echo ""
    print_status $BLUE "Professional Service Management Tests:"

    # Test 1: Get professional's own services (should include DRAFT + PUBLISHED)
    echo "1. Testing GET /v1/services/my-services (professional's own services):"
    local response
    response=$(http_request GET "$base_url/v1/services/my-services" \
        -H "Authorization: Bearer $token" \
        -H "Accept: application/json" -s)
    local status_code
    status_code=$(echo "$response" | cut -d'|' -f1)

    if [[ "$status_code" == "200" ]]; then
        local body
        body=$(echo "$response" | cut -d'|' -f2-)
        local service_count
        service_count=$(echo "$body" | jq '. | length')
        local draft_count
        draft_count=$(echo "$body" | jq '[.[] | select(.status == "DRAFT")] | length')
        local published_count
        published_count=$(echo "$body" | jq '[.[] | select(.status == "PUBLISHED")] | length')

        echo "   Status: $status_code"
        echo "   Total services: $service_count"
        echo "   DRAFT services: $draft_count"
        echo "   PUBLISHED services: $published_count"

        print_status $GREEN "   ✅ Professional can access their own services"

        # Show service details
        echo "   Services:"
        echo "$body" | jq '[.[] | {title, status, price}]'
    else
        local error_body
        error_body=$(echo "$response" | cut -d'|' -f2-)
        print_status $RED "   ❌ Failed to get professional services (Status: $status_code)"
        echo "   Error: $error_body"
    fi

    # Test 2: Create a new service (should default to DRAFT)
    echo ""
    echo "2. Testing POST /v1/services (create new service):"
    local create_payload='{
        "title": "Test Service - API Script",
        "description": "Test service created by API script",
        "categoryId": "11111111-2222-3333-4444-555555555555",
        "price": 99.99
    }'

    response=$(http_request POST "$base_url/v1/services" \
        -H "Authorization: Bearer $token" \
        -H "Content-Type: application/json" \
        -H "Accept: application/json" \
        -d "$create_payload" -s)
    status_code=$(echo "$response" | cut -d'|' -f1)

    if [[ "$status_code" == "201" ]] || [[ "$status_code" == "200" ]]; then
        local created_service
        created_service=$(echo "$response" | cut -d'|' -f2-)
        local service_id
        service_id=$(echo "$created_service" | jq -r '.id')
        local service_status
        service_status=$(echo "$created_service" | jq -r '.status')

        echo "   Status: $status_code"
        echo "   Created service ID: $service_id"
        echo "   Service status: $service_status"

        if [[ "$service_status" == "DRAFT" ]]; then
            print_status $GREEN "   ✅ New service correctly defaults to DRAFT status"
        else
            print_status $YELLOW "   ⚠️ New service status is $service_status (expected DRAFT)"
        fi

        # Test 3: Update service status to PUBLISHED
        echo ""
        echo "3. Testing PUT /v1/services/$service_id (update service to PUBLISHED):"
        local update_payload
        update_payload=$(echo "$created_service" | jq '.status = "PUBLISHED"')

        response=$(http_request PUT "$base_url/v1/services/$service_id" \
            -H "Authorization: Bearer $token" \
            -H "Content-Type: application/json" \
            -H "Accept: application/json" \
            -d "$update_payload" -s)
        status_code=$(echo "$response" | cut -d'|' -f1)

        if [[ "$status_code" == "200" ]]; then
            local updated_service
            updated_service=$(echo "$response" | cut -d'|' -f2-)
            local updated_status
            updated_status=$(echo "$updated_service" | jq -r '.status')

            echo "   Status: $status_code"
            echo "   Updated service status: $updated_status"

            if [[ "$updated_status" == "PUBLISHED" ]]; then
                print_status $GREEN "   ✅ Service status successfully updated to PUBLISHED"
            else
                print_status $RED "   ❌ Failed to update service status"
            fi
        else
            local error_body
            error_body=$(echo "$response" | cut -d'|' -f2-)
            print_status $RED "   ❌ Failed to update service (Status: $status_code)"
            echo "   Error: $error_body"
        fi

        # Test 4: Delete the test service
        echo ""
        echo "4. Testing DELETE /v1/services/$service_id (cleanup test service):"
        response=$(http_request DELETE "$base_url/v1/services/$service_id" \
            -H "Authorization: Bearer $token" -s)
        status_code=$(echo "$response" | cut -d'|' -f1)

        if [[ "$status_code" == "204" ]] || [[ "$status_code" == "200" ]]; then
            print_status $GREEN "   ✅ Test service successfully deleted"
        else
            print_status $YELLOW "   ⚠️ Failed to delete test service (Status: $status_code)"
        fi
    else
        local error_body
        error_body=$(echo "$response" | cut -d'|' -f2-)
        print_status $RED "   ❌ Failed to create test service (Status: $status_code)"
        echo "   Error: $error_body"
    fi

    print_status $GREEN "✅ Professional service endpoint tests completed"
}

# Function to test professional profile navigation endpoints
test_professional_profile_navigation() {
    local base_url=${1:-$BACKEND_URL}

    print_status $CYAN "👤 Testing Professional Profile Navigation endpoints on $base_url..."

    echo ""
    print_status $BLUE "Professional Profile Navigation Tests:"

    # Test 1: Get a service and extract professional ID
    echo "1. Getting a service to test professional navigation:"
    local service_response
    service_response=$(curl -s "$base_url/v1/services")
    local professional_id
    professional_id=$(echo "$service_response" | jq -r '.content[0].professionalKeycloakId')

    if [[ "$professional_id" == "null" ]] || [[ -z "$professional_id" ]]; then
        print_status $RED "   ❌ No services found to test professional navigation"
        return 1
    fi

    echo "   Professional ID to test: $professional_id"

    # Test 2: Get professional data
    echo ""
    echo "2. Testing GET /v1/professionals/{id} (professional profile):"
    local professional_response
    professional_response=$(curl -s "$base_url/v1/professionals/$professional_id")
    local professional_name
    professional_name=$(echo "$professional_response" | jq -r '.bio // "No bio"')

    if [[ "$professional_name" != "null" ]]; then
        print_status $GREEN "   ✅ Professional profile endpoint working"
        echo "   Professional bio: $professional_name"
    else
        print_status $RED "   ❌ Professional profile endpoint failed"
        echo "   Response: $professional_response"
    fi

    # Test 3: Get user data for professional
    echo ""
    echo "3. Testing GET /v1/users/{keycloakId} (user data):"
    local user_response
    user_response=$(curl -s "$base_url/v1/users/$professional_id")
    local user_name
    user_name=$(echo "$user_response" | jq -r '(.firstName // "") + " " + (.lastName // "")')

    if [[ "$user_name" != " " ]] && [[ "$user_name" != "null null" ]]; then
        print_status $GREEN "   ✅ User data endpoint working"
        echo "   Professional name: $user_name"
    else
        print_status $RED "   ❌ User data endpoint failed"
        echo "   Response: $user_response"
    fi

    # Test 4: Get professional's services
    echo ""
    echo "4. Testing GET /v1/services/professional/{id} (professional's services):"
    local services_response
    services_response=$(curl -s "$base_url/v1/services/professional/$professional_id")
    local service_count
    service_count=$(echo "$services_response" | jq '. | length')

    if [[ "$service_count" -gt 0 ]]; then
        print_status $GREEN "   ✅ Professional services endpoint working"
        echo "   Services found: $service_count"
        echo "   Services:"
        echo "$services_response" | jq '[.[] | {title, status, price}]'
    else
        print_status $YELLOW "   ⚠️ No services found for professional (this is OK if they have no published services)"
        echo "   Response: $services_response"
    fi

    # Test 5: Verify complete navigation flow
    echo ""
    echo "5. Testing complete navigation flow simulation:"
    echo "   Service Card -> Professional Profile navigation:"
    echo "   ✓ Service has professionalKeycloakId: $professional_id"
    echo "   ✓ Professional profile endpoint: /v1/professionals/$professional_id"
    echo "   ✓ User data endpoint: /v1/users/$professional_id"
    echo "   ✓ Professional services endpoint: /v1/services/professional/$professional_id"

    print_status $GREEN "   ✅ All endpoints required for professional profile navigation are working"

    print_status $GREEN "✅ Professional profile navigation tests completed"
}

# Function to test favorites endpoints (requires authentication)
test_favorites_endpoints() {
    local base_url=${1:-$BACKEND_URL}
    local token=${2:-""}

    print_status $CYAN "❤️ Testing Favorites endpoints on $base_url..."

    if [[ -z "$token" ]]; then
        print_status $YELLOW "⚠️ No authentication token provided. Getting token for test user..."
        token=$(get_user_token "<EMAIL>" "password123")

        if [[ "$token" == "null" ]] || [[ -z "$token" ]]; then
            print_status $RED "❌ Failed to get authentication token. Skipping favorites tests."
            return 1
        fi
    fi

    echo ""
    print_status $BLUE "Favorites Management Tests:"

    # Get a test service ID for favorites testing
    local services_response
    services_response=$(curl -s "$base_url/v1/services")
    local test_service_id
    test_service_id=$(echo "$services_response" | jq -r '.content[0].id')

    if [[ "$test_service_id" == "null" ]] || [[ -z "$test_service_id" ]]; then
        print_status $RED "❌ No services found to test favorites functionality"
        return 1
    fi

    echo "Using test service ID: $test_service_id"

    # Test 1: Get initial favorites count
    echo ""
    echo "1. Testing GET /v1/favorites/count (initial count):"
    local response
    response=$(http_request GET "$base_url/v1/favorites/count" \
        -H "Authorization: Bearer $token" \
        -H "Accept: application/json" -s)
    local status_code
    status_code=$(echo "$response" | cut -d'|' -f1)

    if [[ "$status_code" == "200" ]]; then
        local count_body
        count_body=$(echo "$response" | cut -d'|' -f2-)
        local initial_count
        initial_count=$(echo "$count_body" | jq -r '.count')
        echo "   Status: $status_code"
        echo "   Initial favorites count: $initial_count"
        print_status $GREEN "   ✅ Favorites count endpoint working"
    else
        print_status $RED "   ❌ Failed to get favorites count (Status: $status_code)"
        return 1
    fi

    # Test 2: Get initial favorites list
    echo ""
    echo "2. Testing GET /v1/favorites (get favorites list):"
    response=$(http_request GET "$base_url/v1/favorites" \
        -H "Authorization: Bearer $token" \
        -H "Accept: application/json" -s)
    status_code=$(echo "$response" | cut -d'|' -f1)

    if [[ "$status_code" == "200" ]]; then
        local favorites_body
        favorites_body=$(echo "$response" | cut -d'|' -f2-)
        local favorites_count
        favorites_count=$(echo "$favorites_body" | jq '. | length')
        echo "   Status: $status_code"
        echo "   Favorites in list: $favorites_count"
        print_status $GREEN "   ✅ Favorites list endpoint working"

        # Show existing favorites
        if [[ "$favorites_count" -gt 0 ]]; then
            echo "   Existing favorites:"
            echo "$favorites_body" | jq '[.[] | {title, categoryName, professionalFullName}]'
        fi
    else
        print_status $RED "   ❌ Failed to get favorites list (Status: $status_code)"
        return 1
    fi

    # Test 3: Check if test service is favorited
    echo ""
    echo "3. Testing GET /v1/favorites/check/{serviceId} (check favorite status):"
    response=$(http_request GET "$base_url/v1/favorites/check/$test_service_id" \
        -H "Authorization: Bearer $token" \
        -H "Accept: application/json" -s)
    status_code=$(echo "$response" | cut -d'|' -f1)

    if [[ "$status_code" == "200" ]]; then
        local check_body
        check_body=$(echo "$response" | cut -d'|' -f2-)
        local is_favorited
        is_favorited=$(echo "$check_body" | jq -r '.favorited')
        echo "   Status: $status_code"
        echo "   Service favorited: $is_favorited"
        print_status $GREEN "   ✅ Favorite status check endpoint working"
    else
        print_status $RED "   ❌ Failed to check favorite status (Status: $status_code)"
        return 1
    fi

    # Test 4: Add service to favorites
    echo ""
    echo "4. Testing POST /v1/favorites/{serviceId} (add to favorites):"
    response=$(http_request POST "$base_url/v1/favorites/$test_service_id" \
        -H "Authorization: Bearer $token" \
        -H "Content-Type: application/json" \
        -H "Accept: application/json" -s)
    status_code=$(echo "$response" | cut -d'|' -f1)

    if [[ "$status_code" == "201" ]] || [[ "$status_code" == "200" ]]; then
        local add_body
        add_body=$(echo "$response" | cut -d'|' -f2-)
        local favorited_status
        favorited_status=$(echo "$add_body" | jq -r '.favorited')
        echo "   Status: $status_code"
        echo "   Favorited: $favorited_status"
        print_status $GREEN "   ✅ Add to favorites endpoint working"
    else
        local error_body
        error_body=$(echo "$response" | cut -d'|' -f2-)
        print_status $RED "   ❌ Failed to add to favorites (Status: $status_code)"
        echo "   Error: $error_body"
    fi

    # Test 5: Verify favorites count increased
    echo ""
    echo "5. Testing favorites count after adding:"
    response=$(http_request GET "$base_url/v1/favorites/count" \
        -H "Authorization: Bearer $token" \
        -H "Accept: application/json" -s)
    status_code=$(echo "$response" | cut -d'|' -f1)

    if [[ "$status_code" == "200" ]]; then
        local count_body
        count_body=$(echo "$response" | cut -d'|' -f2-)
        local new_count
        new_count=$(echo "$count_body" | jq -r '.count')
        echo "   Status: $status_code"
        echo "   New favorites count: $new_count"

        if [[ "$new_count" -gt "$initial_count" ]]; then
            print_status $GREEN "   ✅ Favorites count increased correctly"
        else
            print_status $YELLOW "   ⚠️ Favorites count did not increase (service may have already been favorited)"
        fi
    else
        print_status $RED "   ❌ Failed to get updated favorites count"
    fi

    # Test 6: Get favorite service IDs
    echo ""
    echo "6. Testing GET /v1/favorites/service-ids (get favorite service IDs):"
    response=$(http_request GET "$base_url/v1/favorites/service-ids" \
        -H "Authorization: Bearer $token" \
        -H "Accept: application/json" -s)
    status_code=$(echo "$response" | cut -d'|' -f1)

    if [[ "$status_code" == "200" ]]; then
        local ids_body
        ids_body=$(echo "$response" | cut -d'|' -f2-)
        local ids_count
        ids_count=$(echo "$ids_body" | jq '. | length')
        echo "   Status: $status_code"
        echo "   Favorite service IDs count: $ids_count"
        print_status $GREEN "   ✅ Favorite service IDs endpoint working"

        # Check if our test service is in the list
        local contains_test_service
        contains_test_service=$(echo "$ids_body" | jq --arg id "$test_service_id" 'index($id) != null')
        if [[ "$contains_test_service" == "true" ]]; then
            print_status $GREEN "   ✅ Test service found in favorites list"
        else
            print_status $YELLOW "   ⚠️ Test service not found in favorites list"
        fi
    else
        print_status $RED "   ❌ Failed to get favorite service IDs (Status: $status_code)"
    fi

    # Test 7: Toggle favorite (should remove it)
    echo ""
    echo "7. Testing PUT /v1/favorites/{serviceId}/toggle (toggle favorite):"
    response=$(http_request PUT "$base_url/v1/favorites/$test_service_id/toggle" \
        -H "Authorization: Bearer $token" \
        -H "Content-Type: application/json" \
        -H "Accept: application/json" -s)
    status_code=$(echo "$response" | cut -d'|' -f1)

    if [[ "$status_code" == "200" ]]; then
        local toggle_body
        toggle_body=$(echo "$response" | cut -d'|' -f2-)
        local favorited_after_toggle
        favorited_after_toggle=$(echo "$toggle_body" | jq -r '.favorited')
        echo "   Status: $status_code"
        echo "   Favorited after toggle: $favorited_after_toggle"
        print_status $GREEN "   ✅ Toggle favorite endpoint working"
    else
        local error_body
        error_body=$(echo "$response" | cut -d'|' -f2-)
        print_status $RED "   ❌ Failed to toggle favorite (Status: $status_code)"
        echo "   Error: $error_body"
    fi

    # Test 8: Remove from favorites (cleanup)
    echo ""
    echo "8. Testing DELETE /v1/favorites/{serviceId} (remove from favorites):"
    response=$(http_request DELETE "$base_url/v1/favorites/$test_service_id" \
        -H "Authorization: Bearer $token" \
        -H "Accept: application/json" -s)
    status_code=$(echo "$response" | cut -d'|' -f1)

    if [[ "$status_code" == "200" ]]; then
        local remove_body
        remove_body=$(echo "$response" | cut -d'|' -f2-)
        local favorited_after_remove
        favorited_after_remove=$(echo "$remove_body" | jq -r '.favorited')
        echo "   Status: $status_code"
        echo "   Favorited after removal: $favorited_after_remove"
        print_status $GREEN "   ✅ Remove from favorites endpoint working"
    else
        local error_body
        error_body=$(echo "$response" | cut -d'|' -f2-)
        print_status $YELLOW "   ⚠️ Remove from favorites returned status $status_code (may not have been favorited)"
        echo "   Response: $error_body"
    fi

    # Test 9: Final favorites count verification
    echo ""
    echo "9. Testing final favorites count:"
    response=$(http_request GET "$base_url/v1/favorites/count" \
        -H "Authorization: Bearer $token" \
        -H "Accept: application/json" -s)
    status_code=$(echo "$response" | cut -d'|' -f1)

    if [[ "$status_code" == "200" ]]; then
        local count_body
        count_body=$(echo "$response" | cut -d'|' -f2-)
        local final_count
        final_count=$(echo "$count_body" | jq -r '.count')
        echo "   Status: $status_code"
        echo "   Final favorites count: $final_count"
        echo "   Count change: $initial_count → $final_count"
        print_status $GREEN "   ✅ Favorites count tracking working correctly"
    else
        print_status $RED "   ❌ Failed to get final favorites count"
    fi

    print_status $GREEN "✅ Favorites endpoint tests completed"
}

# Function to run comprehensive Service Catalogue tests
test_service_catalogue() {
    local base_url=${1:-$BACKEND_URL}

    print_status $CYAN "🛍️ Running comprehensive Service Catalogue tests on $base_url..."

    echo ""
    test_service_categories "$base_url"

    echo ""
    test_service_pagination "$base_url"

    echo ""
    test_service_filtering "$base_url"

    echo ""
    test_service_sorting "$base_url"

    echo ""
    test_service_caching "$base_url"

    echo ""
    test_professional_service_endpoints "$base_url"

    echo ""
    print_status $GREEN "🎉 Service Catalogue comprehensive testing completed!"
}

# Function to test messaging endpoints
test_messaging_endpoints() {
    local base_url=${1:-$BACKEND_URL}
    local token=${2:-}

    print_status $CYAN "💬 Testing messaging endpoints on $base_url..."

    # Get token if not provided
    if [[ -z "$token" ]]; then
        print_status $BLUE "Getting authentication token..."
        token=$(get_user_token "$DEFAULT_USERNAME" "$DEFAULT_PASSWORD")
        if [[ -z "$token" ]]; then
            print_status $RED "❌ Failed to get authentication token"
            return 1
        fi
        print_status $GREEN "✅ Authentication token obtained"
    fi

    echo ""
    print_status $BLUE "📋 Testing conversation listing:"

    # Test 1: Get conversations list
    echo "1. Testing GET /v1/messaging/conversations:"
    response=$(http_request GET "$base_url/v1/messaging/conversations" \
        -H "Authorization: Bearer $token" \
        -H "Accept: application/json" -s)
    status_code=$(echo "$response" | cut -d'|' -f1)

    if [[ "$status_code" == "200" ]]; then
        local conversations_body
        conversations_body=$(echo "$response" | cut -d'|' -f2-)
        local conversation_count
        conversation_count=$(echo "$conversations_body" | jq '. | length')
        echo "   Status: $status_code"
        echo "   Conversations found: $conversation_count"
        print_status $GREEN "   ✅ Conversations list endpoint working"

        # Show conversation details if any exist
        if [[ "$conversation_count" -gt 0 ]]; then
            echo "   Sample conversations:"
            echo "$conversations_body" | jq '[.[] | {bookingId, otherParticipantName, lastMessageContent, unreadCount}]'
        fi
    else
        print_status $YELLOW "   ⚠️ No conversations found or endpoint returned status $status_code"
    fi

    # Test 2: Get unread count
    echo ""
    echo "2. Testing GET /v1/messaging/unread-count:"
    response=$(http_request GET "$base_url/v1/messaging/unread-count" \
        -H "Authorization: Bearer $token" \
        -H "Accept: application/json" -s)
    status_code=$(echo "$response" | cut -d'|' -f1)

    if [[ "$status_code" == "200" ]]; then
        local unread_body
        unread_body=$(echo "$response" | cut -d'|' -f2-)
        local unread_count
        unread_count=$(echo "$unread_body" | jq -r '.unreadCount')
        echo "   Status: $status_code"
        echo "   Unread messages: $unread_count"
        print_status $GREEN "   ✅ Unread count endpoint working"
    else
        print_status $RED "   ❌ Failed to get unread count"
    fi

    echo ""
    print_status $BLUE "📨 Testing message operations:"

    # Test 3: Send a test message using real test user data
    echo "3. Testing POST /v1/messaging (send message):"
    local test_message='{"bookingId":1,"receiverId":"00000000-0000-0000-0000-000000000201","content":"Test message from API testing script"}'

    response=$(http_request POST "$base_url/v1/messaging" \
        -H "Authorization: Bearer $token" \
        -H "Content-Type: application/json" \
        -H "Accept: application/json" \
        -d "$test_message" -s)
    status_code=$(echo "$response" | cut -d'|' -f1)

    if [[ "$status_code" == "201" ]]; then
        echo "   Status: $status_code"
        print_status $GREEN "   ✅ Message sending endpoint working"
    else
        local error_body
        error_body=$(echo "$response" | cut -d'|' -f2-)
        print_status $YELLOW "   ⚠️ Message sending returned status $status_code"
        echo "   Response: $error_body"
        echo "   Note: This may be expected if user doesn't have access to booking 1001"
    fi

    # Test 4: Get messages for a booking (using real booking ID)
    echo ""
    echo "4. Testing GET /v1/messaging/booking/1001:"
    response=$(http_request GET "$base_url/v1/messaging/booking/1001" \
        -H "Authorization: Bearer $token" \
        -H "Accept: application/json" -s)
    status_code=$(echo "$response" | cut -d'|' -f1)

    if [[ "$status_code" == "200" ]]; then
        local messages_body
        messages_body=$(echo "$response" | cut -d'|' -f2-)
        local message_count
        message_count=$(echo "$messages_body" | jq '. | length')
        echo "   Status: $status_code"
        echo "   Messages found: $message_count"
        print_status $GREEN "   ✅ Get messages by booking endpoint working"

        # Show message details if any exist
        if [[ "$message_count" -gt 0 ]]; then
            echo "   Sample messages:"
            echo "$messages_body" | jq '[.[] | {content, sentAt, isRead}]'
        fi
    else
        print_status $YELLOW "   ⚠️ No messages found for booking 1001 or access denied"
    fi

    # Test 5: Mark conversation as read (using real booking ID)
    echo ""
    echo "5. Testing PUT /v1/messaging/conversations/1001/read:"
    response=$(http_request PUT "$base_url/v1/messaging/conversations/1001/read" \
        -H "Authorization: Bearer $token" \
        -H "Accept: application/json" -s)
    status_code=$(echo "$response" | cut -d'|' -f1)

    if [[ "$status_code" == "200" ]]; then
        echo "   Status: $status_code"
        print_status $GREEN "   ✅ Mark conversation as read endpoint working"
    else
        print_status $YELLOW "   ⚠️ Mark as read returned status $status_code (may need valid conversation)"
    fi

    echo ""
    print_status $BLUE "🔌 Testing WebSocket endpoints:"

    # Test 6: Get WebSocket info (using real booking ID)
    echo "6. Testing GET /v1/messaging/websocket/info/1001:"
    response=$(http_request GET "$base_url/v1/messaging/websocket/info/1001" \
        -H "Authorization: Bearer $token" \
        -H "Accept: application/json" -s)
    status_code=$(echo "$response" | cut -d'|' -f1)

    if [[ "$status_code" == "200" ]]; then
        local ws_body
        ws_body=$(echo "$response" | cut -d'|' -f2-)
        local active_sessions
        active_sessions=$(echo "$ws_body" | jq -r '.activeSessions')
        local total_sessions
        total_sessions=$(echo "$ws_body" | jq -r '.totalActiveSessions')
        local websocket_url
        websocket_url=$(echo "$ws_body" | jq -r '.websocketUrl')
        echo "   Status: $status_code"
        echo "   Active sessions for booking 1001: $active_sessions"
        echo "   Total active sessions: $total_sessions"
        echo "   WebSocket URL: $websocket_url"
        print_status $GREEN "   ✅ WebSocket info endpoint working"
    else
        print_status $RED "   ❌ Failed to get WebSocket info"
    fi

    print_status $GREEN "✅ Messaging endpoint tests completed"
}

# Function to test messaging with specific test users
test_messaging_with_test_users() {
    local base_url=${1:-$BACKEND_URL}

    print_status $CYAN "👥 Testing messaging with specific test users on $base_url..."

    echo ""
    print_status $BLUE "🔐 Getting authentication tokens for test users:"

    # Get tokens for both test users
    local client_token
    client_token=$(get_user_token "test1client1" "test1client1")
    if [[ -z "$client_token" ]]; then
        print_status $RED "❌ Failed to get token for test1client1"
        return 1
    fi
    print_status $GREEN "✅ Client token obtained (test1client1)"

    local professional_token
    professional_token=$(get_user_token "test1professional1" "test1professional1")
    if [[ -z "$professional_token" ]]; then
        print_status $RED "❌ Failed to get token for test1professional1"
        return 1
    fi
    print_status $GREEN "✅ Professional token obtained (test1professional1)"

    echo ""
    print_status $BLUE "💬 Testing messaging flow between test users:"

    # Test 1: Client gets conversations
    echo "1. Testing client conversations (test1client1):"
    local response
    response=$(http_request GET "$base_url/v1/messaging/conversations" \
        -H "Authorization: Bearer $client_token" \
        -H "Accept: application/json" -s)
    local status_code
    status_code=$(echo "$response" | cut -d'|' -f1)

    if [[ "$status_code" == "200" ]]; then
        local conversations_body
        conversations_body=$(echo "$response" | cut -d'|' -f2-)
        local conversation_count
        conversation_count=$(echo "$conversations_body" | jq '. | length')
        echo "   Status: $status_code"
        echo "   Client conversations: $conversation_count"
        print_status $GREEN "   ✅ Client can access conversations"
    else
        print_status $YELLOW "   ⚠️ Client conversations returned status $status_code"
    fi

    # Test 2: Professional gets conversations
    echo ""
    echo "2. Testing professional conversations (test1professional1):"
    response=$(http_request GET "$base_url/v1/messaging/conversations" \
        -H "Authorization: Bearer $professional_token" \
        -H "Accept: application/json" -s)
    status_code=$(echo "$response" | cut -d'|' -f1)

    if [[ "$status_code" == "200" ]]; then
        local conversations_body
        conversations_body=$(echo "$response" | cut -d'|' -f2-)
        local conversation_count
        conversation_count=$(echo "$conversations_body" | jq '. | length')
        echo "   Status: $status_code"
        echo "   Professional conversations: $conversation_count"
        print_status $GREEN "   ✅ Professional can access conversations"
    else
        print_status $YELLOW "   ⚠️ Professional conversations returned status $status_code"
    fi

    # Test 3: Get messages for booking 1001 (client perspective)
    echo ""
    echo "3. Testing booking 1001 messages (client perspective):"
    response=$(http_request GET "$base_url/v1/messaging/booking/1001" \
        -H "Authorization: Bearer $client_token" \
        -H "Accept: application/json" -s)
    status_code=$(echo "$response" | cut -d'|' -f1)

    if [[ "$status_code" == "200" ]]; then
        local messages_body
        messages_body=$(echo "$response" | cut -d'|' -f2-)
        local message_count
        message_count=$(echo "$messages_body" | jq '. | length')
        echo "   Status: $status_code"
        echo "   Messages in booking 1001: $message_count"
        print_status $GREEN "   ✅ Client can access booking messages"

        if [[ "$message_count" -gt 0 ]]; then
            echo "   Latest message:"
            echo "$messages_body" | jq '.[-1] | {content, sentAt, isRead}'
        fi
    else
        print_status $YELLOW "   ⚠️ Client booking messages returned status $status_code"
    fi

    # Test 4: Send a new message from client to professional
    echo ""
    echo "4. Testing send message (client to professional):"
    local test_message='{"bookingId":1,"receiverId":"00000000-0000-0000-0000-000000000201","content":"Test message from API script - client to professional"}'

    response=$(http_request POST "$base_url/v1/messaging" \
        -H "Authorization: Bearer $client_token" \
        -H "Content-Type: application/json" \
        -H "Accept: application/json" \
        -d "$test_message" -s)
    status_code=$(echo "$response" | cut -d'|' -f1)

    if [[ "$status_code" == "201" ]]; then
        echo "   Status: $status_code"
        print_status $GREEN "   ✅ Client can send messages to professional"
    else
        local error_body
        error_body=$(echo "$response" | cut -d'|' -f2-)
        print_status $YELLOW "   ⚠️ Send message returned status $status_code"
        echo "   Response: $error_body"
    fi

    # Test 5: WebSocket info for booking 1001
    echo ""
    echo "5. Testing WebSocket info for booking 1001:"
    response=$(http_request GET "$base_url/v1/messaging/websocket/info/1001" \
        -H "Authorization: Bearer $client_token" \
        -H "Accept: application/json" -s)
    status_code=$(echo "$response" | cut -d'|' -f1)

    if [[ "$status_code" == "200" ]]; then
        local ws_body
        ws_body=$(echo "$response" | cut -d'|' -f2-)
        echo "   Status: $status_code"
        echo "   WebSocket info: $ws_body"
        print_status $GREEN "   ✅ WebSocket info endpoint accessible"
    else
        print_status $RED "   ❌ Failed to get WebSocket info"
    fi

    # Test 6: Test conversation initiation (critical for frontend)
    echo ""
    echo "6. Testing conversation initiation (client initiates with professional):"

    # Client initiates conversation with professional (using professional's internal user ID)
    response=$(http_request POST "$base_url/v1/messaging/conversations/initiate/00000000-0000-0000-0000-000000000201" \
        -H "Authorization: Bearer $client_token" \
        -H "Content-Type: application/json")

    status_code=$(echo "$response" | jq -r '.status_code // empty')
    if [[ "$status_code" == "200" || "$status_code" == "201" ]]; then
        booking_id=$(echo "$response" | jq -r '.bookingId // empty')
        print_status $GREEN "   ✅ Conversation initiation successful - Booking ID: $booking_id"

        # Test 7: Get conversation info for the newly created conversation
        echo ""
        echo "7. Testing conversation info retrieval:"
        response=$(http_request GET "$base_url/v1/messaging/conversations/$booking_id/info" \
            -H "Authorization: Bearer $client_token" \
            -H "Content-Type: application/json")

        status_code=$(echo "$response" | jq -r '.status_code // empty')
        if [[ "$status_code" == "200" ]]; then
            other_participant=$(echo "$response" | jq -r '.otherParticipantName // empty')
            print_status $GREEN "   ✅ Conversation info retrieved - Other participant: $other_participant"
        else
            print_status $RED "   ❌ Conversation info retrieval failed - Status: $status_code"
            echo "$response" | jq '.' 2>/dev/null || echo "$response"
        fi
    else
        print_status $RED "   ❌ Conversation initiation failed - Status: $status_code"
        echo "$response" | jq '.' 2>/dev/null || echo "$response"
    fi

    print_status $GREEN "✅ Test user messaging tests completed"
}

# Function to test WebSocket functionality using Node.js
test_websocket_functionality() {
    local base_url=${1:-$BACKEND_URL}
    local booking_id=${2:-1001}
    local user_id=${3:-"f62a04b6-a0a7-4f21-8e3a-7948eee69ed3"}

    print_status $CYAN "🔌 Testing WebSocket functionality on $base_url..."

    # Check if Node.js is available
    if ! command -v node &> /dev/null; then
        print_status $YELLOW "⚠️ Node.js not found. Skipping WebSocket tests."
        return 1
    fi

    # Check if the WebSocket test script exists
    local ws_test_script="scripts/test-websocket.js"
    if [[ ! -f "$ws_test_script" ]]; then
        print_status $BLUE "📝 Creating WebSocket test script..."
        create_websocket_test_script "$ws_test_script"
    fi

    echo ""
    print_status $BLUE "🧪 Running WebSocket connection tests:"

    # Extract host and port from base_url
    local ws_host="localhost"
    local ws_port="8080"
    if [[ "$base_url" =~ http://([^:]+):([0-9]+) ]]; then
        ws_host="${BASH_REMATCH[1]}"
        ws_port="${BASH_REMATCH[2]}"
    fi

    local ws_url="ws://${ws_host}:${ws_port}/v1/messaging/ws/${booking_id}?userId=${user_id}"

    echo "1. Testing WebSocket connection to: $ws_url"

    # Run the WebSocket test
    local test_result
    test_result=$(node "$ws_test_script" "$ws_url" 2>&1)
    local exit_code=$?

    if [[ $exit_code -eq 0 ]]; then
        print_status $GREEN "   ✅ WebSocket connection test passed"
        echo "   $test_result"
    else
        print_status $RED "   ❌ WebSocket connection test failed"
        echo "   Error: $test_result"
    fi

    print_status $GREEN "✅ WebSocket functionality tests completed"
}

# Function to create WebSocket test script
create_websocket_test_script() {
    local script_path=$1

    cat > "$script_path" << 'EOF'
const WebSocket = require('ws');

const wsUrl = process.argv[2];
if (!wsUrl) {
    console.error('Usage: node test-websocket.js <websocket-url>');
    process.exit(1);
}

console.log(`🔄 Connecting to WebSocket: ${wsUrl}`);

const ws = new WebSocket(wsUrl);
let testsPassed = 0;
let totalTests = 3;

// Test timeout
const timeout = setTimeout(() => {
    console.error('❌ WebSocket test timeout');
    ws.close();
    process.exit(1);
}, 10000);

ws.on('open', function open() {
    console.log('✅ WebSocket connection established');
    testsPassed++;

    // Test 1: Send ping message
    console.log('📤 Sending ping message...');
    ws.send(JSON.stringify({
        type: 'ping',
        message: 'ping test',
        data: {}
    }));

    // Test 2: Send test message
    setTimeout(() => {
        console.log('📤 Sending test message...');
        ws.send(JSON.stringify({
            type: 'send_message',
            data: {
                senderId: '00000000-0000-0000-0000-000000000101',
                receiverId: '00000000-0000-0000-0000-000000000201',
                content: 'WebSocket test message from script'
            }
        }));
    }, 1000);

    // Close connection after tests
    setTimeout(() => {
        ws.close();
    }, 3000);
});

ws.on('message', function message(data) {
    try {
        const msg = JSON.parse(data);
        console.log(`📥 Received message: ${msg.type}`);

        if (msg.type === 'pong') {
            console.log('✅ Ping/pong test passed');
            testsPassed++;
        } else if (msg.type === 'new_message') {
            console.log('✅ Message broadcast test passed');
            testsPassed++;
        }

        console.log(`   Data: ${JSON.stringify(msg.data || {})}`);
    } catch (e) {
        console.log(`📥 Received raw message: ${data}`);
    }
});

ws.on('error', function error(err) {
    console.error(`❌ WebSocket error: ${err.message}`);
    clearTimeout(timeout);
    process.exit(1);
});

ws.on('close', function close(code, reason) {
    clearTimeout(timeout);
    console.log(`🔌 WebSocket connection closed (code: ${code}, reason: ${reason})`);

    if (testsPassed >= 2) {
        console.log(`✅ WebSocket tests completed successfully (${testsPassed}/${totalTests} tests passed)`);
        process.exit(0);
    } else {
        console.log(`❌ Some WebSocket tests failed (${testsPassed}/${totalTests} tests passed)`);
        process.exit(1);
    }
});
EOF

    chmod +x "$script_path"
    print_status $GREEN "✅ WebSocket test script created at $script_path"
}

# Function to show API help
api_help() {
    cat << EOF
🌐 API - API endpoint testing

COMMANDS:
    health [base_url]                      Test API health endpoints
    public [base_url]                      Test public endpoints
    protected [base_url]                   Test protected endpoints (should return 401)
    cors [base_url]                        Test CORS configuration
    production                             Full production API test
    performance [base_url] [endpoint] [count]  Test API performance
    consistency [base_url]                 Test data consistency

    SERVICE CATALOGUE TESTS:
    services [base_url]                    Comprehensive Service Catalogue tests
    pagination [base_url]                  Test service pagination
    filtering [base_url]                   Test service filtering (PUBLISHED only)
    sorting [base_url]                     Test service sorting
    caching [base_url]                     Test service caching
    categories [base_url]                  Test service categories
    professional [base_url] [token]        Test professional service endpoints
    navigation [base_url]                  Test professional profile navigation

    FAVORITES TESTS:
    favorites [base_url] [token]           Test favorites endpoints (requires auth)

    MESSAGING TESTS:
    messaging [base_url] [token]           Test messaging endpoints (requires auth)
    messaging-users [base_url]             Test messaging with test1client1 and test1professional1
    websocket [base_url] [booking_id] [user_id]  Test WebSocket functionality

    AVAILABILITY TESTS:
    availability [base_url]                Test availability domain (work schedules)
    availability-consistency [base_url]    Test availability data consistency

EXAMPLES:
    $0 api health
    $0 api public https://api.yotelohago.co
    $0 api production
    $0 api performance http://localhost:8080 /v1/services 20
    $0 api consistency

    SERVICE CATALOGUE EXAMPLES:
    $0 api services                        # Test all service catalogue features
    $0 api pagination http://localhost:8080
    $0 api filtering
    $0 api sorting
    $0 api caching
    $0 api categories

    FAVORITES EXAMPLES:
    $0 api favorites                       # Test favorites with default user
    $0 api favorites http://localhost:8080 [token]

    MESSAGING EXAMPLES:
    $0 api messaging                       # Test messaging with default user
    $0 api messaging http://localhost:8080 [token]
    $0 api messaging-users                 # Test with test1client1 and test1professional1
    $0 api websocket                       # Test WebSocket functionality
    $0 api websocket http://localhost:8080 1001 f62a04b6-a0a7-4f21-8e3a-7948eee69ed3

    AVAILABILITY EXAMPLES:
    $0 api availability                    # Test availability domain
    $0 api availability http://localhost:8080
    $0 api availability-consistency        # Test availability data consistency

EOF
}

# Main API function
api_main() {
    local command=$1
    shift

    case "$command" in
        "health")
            test_health_endpoints "$1"
            ;;
        "public")
            test_public_endpoints "$1"
            ;;
        "protected")
            test_protected_endpoints "$1"
            ;;
        "cors")
            test_cors_configuration "$1"
            ;;
        "production")
            test_production_api
            ;;
        "performance")
            test_api_performance "$1" "$2" "$3"
            ;;
        "consistency")
            test_data_consistency "$1"
            ;;
        "services")
            test_service_catalogue "$1"
            ;;
        "pagination")
            test_service_pagination "$1"
            ;;
        "filtering")
            test_service_filtering "$1"
            ;;
        "sorting")
            test_service_sorting "$1"
            ;;
        "caching")
            test_service_caching "$1"
            ;;
        "categories")
            test_service_categories "$1"
            ;;
        "professional")
            test_professional_service_endpoints "$1" "$2"
            ;;
        "navigation")
            test_professional_profile_navigation "$1"
            ;;
        "favorites")
            test_favorites_endpoints "$1" "$2"
            ;;
        "messaging")
            test_messaging_endpoints "$1" "$2"
            ;;
        "messaging-users")
            test_messaging_with_test_users "$1"
            ;;
        "websocket")
            test_websocket_functionality "$1" "$2" "$3"
            ;;
        "availability")
            test_availability_domain "$1"
            ;;
        "availability-consistency")
            test_availability_data_consistency "$1"
            ;;
        *)
            print_status $RED "❌ Unknown api command: $command"
            api_help
            exit 1
            ;;
    esac
}
