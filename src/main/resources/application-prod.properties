## Database config
quarkus.datasource.jdbc.url=********************************************************************************
quarkus.hibernate-orm.database.generation=drop-and-create

### CORS Configuration for Production ###
quarkus.http.cors=true
quarkus.http.cors.origins=https://yotelohago.co,https://admin.yotelohago.co
quarkus.http.cors.headers=accept,authorization,content-type,x-requested-with
quarkus.http.cors.methods=GET,POST,PUT,DELETE,OPTIONS
quarkus.http.cors.exposed-headers=Content-Disposition
quarkus.http.cors.access-control-max-age=24H
quarkus.http.cors.access-control-allow-credentials=true

### OIDC Configuration ###
quarkus.oidc.auth-server-url=https://keycloak.yotelohago.co/realms/yotelohago
quarkus.oidc.client-id=yotelohago-app-prod
# Using public client for production (no client secret needed)
quarkus.oidc.application-type=service

### Keycloak Admin API Configuration ###
keycloak.admin.server-url=https://keycloak.yotelohago.co
keycloak.admin.client-id=yotelohago-backend
keycloak.admin.client-secret=yotelohago-backend-secret-change-in-production
keycloak.realm=yotelohago

# Enable OpenAPI and Swagger UI
quarkus.swagger-ui.always-include=true
