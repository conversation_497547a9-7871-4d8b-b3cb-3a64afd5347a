import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, FlatList } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Colors from '../constants/Colors';

// Mock cities data - replace with real data
const CITIES = [
  { id: '1', name: 'New York', state: 'NY' },
  { id: '2', name: 'Los Angeles', state: 'CA' },
  { id: '3', name: 'Chicago', state: 'IL' },
  { id: '4', name: 'Houston', state: 'TX' },
  { id: '5', name: 'Phoenix', state: 'AZ' },
  { id: '6', name: 'Philadelphia', state: 'PA' },
  { id: '7', name: 'San Antonio', state: 'TX' },
  { id: '8', name: 'San Diego', state: 'CA' },
  { id: '9', name: 'Dallas', state: 'TX' },
  { id: '10', name: 'San Jose', state: 'CA' },
];

interface CitySelectionProps {
  onSelectCity: (city: { id: string; name: string; state: string }) => void;
}

export default function CitySelection({ onSelectCity }: CitySelectionProps) {
  const renderCity = ({ item }: { item: typeof CITIES[0] }) => (
    <TouchableOpacity
      style={styles.cityItem}
      onPress={() => onSelectCity(item)}
    >
      <View>
        <Text style={styles.cityName}>{item.name}</Text>
        <Text style={styles.stateName}>{item.state}</Text>
      </View>
      <Ionicons name="chevron-forward" size={20} color={Colors.text.secondary} />
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <FlatList
        data={CITIES}
        renderItem={renderCity}
        keyExtractor={(item) => item.id}
        showsVerticalScrollIndicator={false}
        ItemSeparatorComponent={() => <View style={styles.separator} />}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  cityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    paddingHorizontal: 4,
  },
  cityName: {
    fontSize: 16,
    color: Colors.text.primary,
    marginBottom: 4,
  },
  stateName: {
    fontSize: 14,
    color: Colors.text.secondary,
  },
  separator: {
    height: 1,
    backgroundColor: Colors.border,
  },
}); 