import { useState, useEffect, useCallback } from 'react';
import { 
  ServiceDTO, 
  CreateServiceRequest, 
  UpdateServiceRequest,
  ApiError 
} from '../types/api';
import { professionalServiceManagementService } from '../services/api/professionalServiceManagement';

interface ProfessionalServicesState {
  services: ServiceDTO[];
  isLoading: boolean;
  isSaving: boolean;
  error: string | null;
  lastUpdated: Date | null;
}

/**
 * Custom hook for managing professional services
 * Provides CRUD operations for professionals to manage their own services
 */
export const useProfessionalServices = () => {
  const [state, setState] = useState<ProfessionalServicesState>({
    services: [],
    isLoading: false,
    isSaving: false,
    error: null,
    lastUpdated: null,
  });

  // Load professional's services
  const loadServices = useCallback(async () => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      console.log('🗓️ Loading professional services');
      
      const services = await professionalServiceManagementService.getMyServices();

      console.log('🔍 Professional services loaded:', {
        count: services.length,
        services: services.map(s => ({
          id: s.id,
          title: s.title,
          status: s.status,
          professionalUserId: s.professionalUserId
        }))
      });

      setState(prev => ({
        ...prev,
        services,
        isLoading: false,
        lastUpdated: new Date(),
      }));

      console.log('✅ Professional services loaded successfully');
    } catch (error) {
      console.error('❌ Failed to load professional services:', error);
      
      const apiError = error as ApiError;
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: apiError.error || 'Failed to load services',
      }));
    }
  }, []);

  // Create a new service
  const createService = useCallback(async (request: CreateServiceRequest) => {
    setState(prev => ({ ...prev, isSaving: true, error: null }));

    try {
      console.log('🗓️ Creating new service', request);
      
      const newService = await professionalServiceManagementService.createService(request);
      
      setState(prev => ({
        ...prev,
        services: [...prev.services, newService],
        isSaving: false,
        lastUpdated: new Date(),
      }));

      console.log('✅ Service created successfully');
      return newService;
    } catch (error) {
      console.error('❌ Failed to create service:', error);
      
      const apiError = error as ApiError;
      setState(prev => ({
        ...prev,
        isSaving: false,
        error: apiError.error || 'Failed to create service',
      }));
      throw error;
    }
  }, []);

  // Update an existing service
  const updateService = useCallback(async (serviceId: string, request: UpdateServiceRequest) => {
    setState(prev => ({ ...prev, isSaving: true, error: null }));

    try {
      console.log(`🗓️ Updating service ${serviceId}`, request);
      
      const updatedService = await professionalServiceManagementService.updateService(serviceId, request);
      
      setState(prev => ({
        ...prev,
        services: prev.services.map(service => 
          service.id === serviceId ? updatedService : service
        ),
        isSaving: false,
        lastUpdated: new Date(),
      }));

      console.log('✅ Service updated successfully');
      return updatedService;
    } catch (error) {
      console.error('❌ Failed to update service:', error);
      
      const apiError = error as ApiError;
      setState(prev => ({
        ...prev,
        isSaving: false,
        error: apiError.error || 'Failed to update service',
      }));
      throw error;
    }
  }, []);

  // Delete a service
  const deleteService = useCallback(async (serviceId: string) => {
    setState(prev => ({ ...prev, isSaving: true, error: null }));

    try {
      console.log(`🗓️ Deleting service ${serviceId}`);
      
      const success = await professionalServiceManagementService.deleteService(serviceId);
      
      if (success) {
        setState(prev => ({
          ...prev,
          services: prev.services.filter(service => service.id !== serviceId),
          isSaving: false,
          lastUpdated: new Date(),
        }));
        console.log('✅ Service deleted successfully');
      } else {
        setState(prev => ({ ...prev, isSaving: false }));
        console.log('ℹ️ Service not found for deletion');
      }

      return success;
    } catch (error) {
      console.error('❌ Failed to delete service:', error);
      
      const apiError = error as ApiError;
      setState(prev => ({
        ...prev,
        isSaving: false,
        error: apiError.error || 'Failed to delete service',
      }));
      return false;
    }
  }, []);

  // Toggle service status (active/inactive)
  const toggleServiceStatus = useCallback(async (serviceId: string, isActive: boolean) => {
    setState(prev => ({ ...prev, isSaving: true, error: null }));

    try {
      console.log(`🗓️ Toggling service ${serviceId} status to: ${isActive}`);

      // Find the current service data to pass to the toggle function
      const currentService = state.services.find(service => service.id === serviceId);
      if (!currentService) {
        console.error('❌ Service not found in current state:', {
          serviceId,
          availableServices: state.services.map(s => ({ id: s.id, title: s.title }))
        });
        throw new Error('Service not found in current state');
      }

      console.log('🔍 Found service for toggle:', {
        id: currentService.id,
        title: currentService.title,
        status: currentService.status,
        professionalUserId: currentService.professionalUserId
      });

      const updatedService = await professionalServiceManagementService.toggleServiceStatus(
        serviceId,
        isActive,
        currentService
      );

      setState(prev => ({
        ...prev,
        services: prev.services.map(service =>
          service.id === serviceId ? updatedService : service
        ),
        isSaving: false,
        lastUpdated: new Date(),
      }));

      console.log('✅ Service status toggled successfully');
      return updatedService;
    } catch (error) {
      console.error('❌ Failed to toggle service status:', error);

      const apiError = error as ApiError;
      setState(prev => ({
        ...prev,
        isSaving: false,
        error: apiError.error || 'Failed to toggle service status',
      }));
      throw error;
    }
  }, [state.services]);

  // Clear error
  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  // Refresh services
  const refresh = useCallback(() => {
    loadServices();
  }, [loadServices]);

  // Load services on mount
  useEffect(() => {
    loadServices();
  }, [loadServices]);

  // Calculate statistics
  const getStatistics = useCallback(() => {
    const totalServices = state.services.length;
    const publishedServices = state.services.filter(service =>
      service.status === 'published'
    ).length;
    const draftServices = state.services.filter(service =>
      service.status === 'draft' || !service.status
    ).length;

    return {
      total: totalServices,
      published: publishedServices,
      draft: draftServices,
    };
  }, [state.services]);

  return {
    // State
    services: state.services,
    isLoading: state.isLoading,
    isSaving: state.isSaving,
    error: state.error,
    lastUpdated: state.lastUpdated,

    // Actions
    loadServices,
    createService,
    updateService,
    deleteService,
    toggleServiceStatus,
    clearError,
    refresh,

    // Computed properties
    hasServices: state.services.length > 0,
    isEmpty: state.services.length === 0 && !state.isLoading,
    statistics: getStatistics(),
  };
};

export default useProfessionalServices;
