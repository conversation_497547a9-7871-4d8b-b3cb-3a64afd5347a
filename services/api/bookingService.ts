import {
  BookingDTO,
  BookingWithDetailsDTO,
  CreateBookingRequest,
  UpdateBookingRequest,
  PaginatedResponse,
  BookingStatus
} from '../../types/api';
import { apiGet, apiPost, apiPut, apiDelete } from './client';
import { ApiEndpoints } from './types';

/**
 * Booking API Service
 * Handles all booking-related API calls
 */

// Get all bookings for the current user with enriched data (professional and service details)
export const getBookings = async (): Promise<BookingWithDetailsDTO[]> => {
  try {
    const response = await apiGet<PaginatedResponse<BookingWithDetailsDTO>>(
      `${ApiEndpoints.BOOKINGS}?withDetails=true&page=0&size=100`
    );
    return response.content;
  } catch (error) {
    console.error('Failed to fetch bookings:', error);
    throw new Error('Failed to fetch bookings');
  }
};

// Get all bookings with pagination
export const getBookingsPaginated = async (
  page: number = 0,
  size: number = 20,
  status?: BookingStatus,
  withDetails: boolean = true
): Promise<PaginatedResponse<BookingWithDetailsDTO | BookingDTO>> => {
  try {
    const params = new URLSearchParams({
      page: page.toString(),
      size: size.toString(),
      withDetails: withDetails.toString(),
    });

    if (status) {
      params.append('status', status);
    }

    const response = await apiGet<PaginatedResponse<BookingWithDetailsDTO | BookingDTO>>(
      `${ApiEndpoints.BOOKINGS}?${params.toString()}`
    );
    return response;
  } catch (error) {
    console.error('Failed to fetch paginated bookings:', error);
    throw new Error('Failed to fetch paginated bookings');
  }
};

// Get bookings filtered by status with enriched data
export const getBookingsByStatus = async (status: BookingStatus): Promise<BookingWithDetailsDTO[]> => {
  try {
    const response = await apiGet<PaginatedResponse<BookingWithDetailsDTO>>(
      `${ApiEndpoints.BOOKINGS}?status=${status}&withDetails=true&page=0&size=100`
    );
    return response.content;
  } catch (error) {
    console.error(`Failed to fetch ${status} bookings:`, error);
    throw new Error(`Failed to fetch ${status} bookings`);
  }
};



// Get a specific booking by ID
export const getBookingById = async (id: string): Promise<BookingDTO> => {
  try {
    const response = await apiGet<BookingDTO>(`${ApiEndpoints.BOOKINGS}/${id}`);
    return response;
  } catch (error) {
    console.error(`Failed to fetch booking ${id}:`, error);
    throw new Error(`Failed to fetch booking ${id}`);
  }
};

// Create a new booking
export const createBooking = async (bookingData: CreateBookingRequest): Promise<BookingDTO> => {
  try {
    const response = await apiPost<BookingDTO>(ApiEndpoints.BOOKINGS, bookingData);
    return response;
  } catch (error) {
    console.error('Failed to create booking:', error);
    throw new Error('Failed to create booking');
  }
};

// Update an existing booking
export const updateBooking = async (
  id: string, 
  updateData: UpdateBookingRequest
): Promise<BookingDTO> => {
  try {
    const response = await apiPut<BookingDTO>(`${ApiEndpoints.BOOKINGS}/${id}`, updateData);
    return response;
  } catch (error) {
    console.error(`Failed to update booking ${id}:`, error);
    throw new Error(`Failed to update booking ${id}`);
  }
};

// Delete a booking
export const deleteBooking = async (id: string): Promise<void> => {
  try {
    await apiDelete<void>(`${ApiEndpoints.BOOKINGS}/${id}`);
  } catch (error) {
    console.error(`Failed to delete booking ${id}:`, error);
    throw new Error(`Failed to delete booking ${id}`);
  }
};

// Update booking status
export const updateBookingStatus = async (
  id: string, 
  status: BookingStatus
): Promise<BookingDTO> => {
  try {
    const response = await apiPut<BookingDTO>(`${ApiEndpoints.BOOKINGS}/${id}`, { status });
    return response;
  } catch (error) {
    console.error(`Failed to update booking ${id} status:`, error);
    throw new Error(`Failed to update booking status`);
  }
};

// Helper function to convert backend enriched booking to frontend format
export const transformBookingForFrontend = (backendBooking: BookingWithDetailsDTO): any => {
  // Use scheduledAt if available, otherwise use requestedDate, otherwise use createdAt
  const dateToUse = backendBooking.scheduledAt || backendBooking.requestedDate || backendBooking.createdAt;
  const dateObj = new Date(dateToUse);

  const transformedStatus = backendBooking.status.toLowerCase() as 'open' | 'accepted' | 'completed' | 'cancelled';

  const transformed = {
    id: backendBooking.id.toString(),
    serviceId: backendBooking.serviceId,
    providerId: backendBooking.professionalId || '', // Handle null professional
    userId: backendBooking.clientId,
    title: backendBooking.title,
    description: backendBooking.description,
    // Convert ISO date to frontend format
    date: dateObj.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    }),
    time: dateObj.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    }),
    status: transformedStatus,
    price: Number(backendBooking.price), // Convert BigDecimal to number
    notes: backendBooking.description,
    duration: 120, // Default duration, should be calculated from service
    createdAt: backendBooking.createdAt,
    updatedAt: backendBooking.updatedAt,

    // Enriched data for frontend display (no need for mock data lookup)
    professionalName: backendBooking.professionalFullName || 'Unassigned',
    professionalAvatar: backendBooking.professionalProfileImageUrl || null,
    serviceTitle: backendBooking.serviceTitle,
    serviceCategoryName: backendBooking.serviceCategoryName,

    // Client information for professional mode (now available from backend enriched DTO)
    clientName: backendBooking.clientFullName || 'Client',
    clientAvatar: backendBooking.clientProfileImageUrl || null,
  };

  console.log('🔄 Transforming enriched booking:', {
    originalStatus: backendBooking.status,
    transformedStatus: transformedStatus,
    bookingId: backendBooking.id,
    title: backendBooking.title,
    professionalName: backendBooking.professionalFullName,
    clientName: backendBooking.clientFullName,
    serviceTitle: backendBooking.serviceTitle
  });

  return transformed;
};

// Helper function to convert frontend booking to backend format
export const transformBookingForBackend = (frontendBooking: any): Partial<BookingDTO> => {
  return {
    id: frontendBooking.id ? Number(frontendBooking.id) : undefined,
    clientId: frontendBooking.userId,
    professionalId: frontendBooking.providerId || null,
    serviceId: frontendBooking.serviceId,
    title: frontendBooking.title,
    description: frontendBooking.description || frontendBooking.notes,
    scheduledAt: frontendBooking.date && frontendBooking.time
      ? new Date(`${frontendBooking.date} ${frontendBooking.time}`).toISOString()
      : undefined,
    requestedDate: frontendBooking.date && frontendBooking.time
      ? new Date(`${frontendBooking.date} ${frontendBooking.time}`).toISOString()
      : undefined,
    status: frontendBooking.status.toUpperCase(),
    price: frontendBooking.price,
  };
};

export default {
  getBookings,
  getBookingsByStatus,
  getBookingsPaginated,
  getBookingById,
  createBooking,
  updateBooking,
  deleteBooking,
  updateBookingStatus,
  transformBookingForFrontend,
  transformBookingForBackend,
};
