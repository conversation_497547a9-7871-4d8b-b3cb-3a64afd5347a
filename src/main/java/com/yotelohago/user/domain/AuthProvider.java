package com.yotelohago.user.domain;

/**
 * Enum representing supported authentication providers for external identities.
 * This ensures type safety and prevents typos when working with provider names.
 */
public enum AuthProvider {
    KEYCLOAK("keycloak"),
    APPLE("apple"),
    GOOGLE("google");

    private final String value;

    AuthProvider(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    /**
     * Convert string value to enum, case-insensitive
     */
    public static AuthProvider fromString(String value) {
        if (value == null) {
            throw new IllegalArgumentException("Provider value cannot be null");
        }
        
        for (AuthProvider provider : AuthProvider.values()) {
            if (provider.value.equalsIgnoreCase(value)) {
                return provider;
            }
        }
        
        throw new IllegalArgumentException("Unknown provider: " + value);
    }

    @Override
    public String toString() {
        return value;
    }
}
