package com.yotelohago.professional.api;

import com.yotelohago.professional.domain.Professional;
import com.yotelohago.user.domain.User;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

import java.util.UUID;

/**
 * Mapper for Professional domain
 * Follows the same coding convention as Service, Booking, Media, and Favorite domains:
 * - No URL building logic in mapper
 * - URLs are built exclusively in repository SQL queries
 * - Simple entity ↔ DTO conversion only
 */
@ApplicationScoped
public class ProfessionalMapper {

    @PersistenceContext
    EntityManager entityManager;

    /**
     * Convert ProfessionalRequestDTO to entity for create/update operations
     * @param dto Request DTO with professional data
     * @return Professional entity
     */
    public Professional toEntityFromRequest(ProfessionalRequestDTO dto) {
        // Load the user entity
        User user = User.findById(dto.userId);
        if (user == null) {
            throw new IllegalArgumentException("User not found: " + dto.userId);
        }

        return new Professional(
                user,
                dto.bio,
                dto.city,
                null, // avgRating - will be calculated
                0,    // ratingCount - will be calculated
                dto.available
        );
    }

    /**
     * Convert Professional entity to ProfessionalWithDetailsDTO (without URLs)
     * Note: This method is only used for internal operations where URLs are not needed
     * For API responses, use repository methods that build URLs in SQL
     * @param professional Professional entity
     * @return ProfessionalWithDetailsDTO without URLs
     */
    public ProfessionalWithDetailsDTO toWithDetailsDTO(Professional professional) {
        if (professional == null) {
            return null;
        }

        User user = professional.getUser();
        return new ProfessionalWithDetailsDTO(
                user.id,
                professional.getBio(),
                professional.getCity(),
                professional.getAvgRating(),
                professional.getRatingCount(),
                professional.isAvailable(),
                null, // No URL building in mapper - use repository methods with SQL-based URL building
                user.getFirstName(),
                user.getLastName(),
                user.getEmail(),
                user.getPhone(),
                user.getUsername()
        );
    }
}