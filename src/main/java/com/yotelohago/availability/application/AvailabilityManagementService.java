package com.yotelohago.availability.application;

import com.yotelohago.auth.TokenIdentityProvider;
import com.yotelohago.availability.domain.WorkSchedule;
import com.yotelohago.availability.domain.WorkScheduleRepository;
import com.yotelohago.availability.domain.DayTimeRange;
import com.yotelohago.common.dto.PageRequest;
import com.yotelohago.common.dto.PageResponse;
import com.yotelohago.user.application.UserSyncService;
import com.yotelohago.user.domain.User;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;
import jakarta.ws.rs.ForbiddenException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.DayOfWeek;
import java.time.LocalTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Application service for managing professional work schedules.
 * 
 * This service handles the business logic for work schedule management:
 * - Professionals can manage their own work schedules
 * - Clients can view work schedules for any professional
 * - Simple CRUD operations since it's 1:1 relationship
 * - Focus on weekly recurring patterns rather than individual slots
 * 
 * Authorization rules:
 * - Professionals can manage their own work schedule
 * - Clients can view any professional's work schedule
 * - Admins can manage any work schedule
 */
@ApplicationScoped
public class AvailabilityManagementService {

    private static final Logger logger = LoggerFactory.getLogger(AvailabilityManagementService.class);

    @Inject
    WorkScheduleRepository repository;

    @Inject
    TokenIdentityProvider tokenIdentityProvider;

    @Inject
    UserSyncService userSyncService;

    // =====================================================
    // Query Operations (Read)
    // =====================================================

    /**
     * Find work schedule by professional ID - Accessible by authenticated users
     */
    public Optional<WorkSchedule> findById(UUID professionalId) {
        userSyncService.ensureCurrentUserExists();
        return repository.findById(professionalId);
    }



    /**
     * Get or create work schedule for a professional
     * Creates empty schedule if none exists
     */
    public WorkSchedule getOrCreateForProfessional(UUID professionalId) {
        userSyncService.ensureCurrentUserExists();

        // Check if current user can access this professional's data
        if (!tokenIdentityProvider.canAccessProfessionalData(professionalId) && 
            !tokenIdentityProvider.hasRole("admin")) {
            // For read access, anyone can view work schedules
            // For create access, only the professional themselves or admin
            Optional<WorkSchedule> existing = repository.findById(professionalId);
            if (existing.isPresent()) {
                return existing.get();
            } else {
                throw new ForbiddenException("Access denied to create work schedule");
            }
        }

        return repository.getOrCreateForProfessional(professionalId);
    }

    /**
     * Find all work schedules with pagination - Admin only
     */
    public PageResponse<WorkSchedule> findAllPaginated(PageRequest pageRequest) {
        userSyncService.ensureCurrentUserExists();

        if (!tokenIdentityProvider.hasRole("admin")) {
            throw new ForbiddenException("Admin access required");
        }

        return repository.findAllPaginated(pageRequest);
    }

    /**
     * Find work schedules for multiple professionals - Public access
     */
    public List<WorkSchedule> findByProfessionalIds(List<UUID> professionalIds) {
        userSyncService.ensureCurrentUserExists();
        return repository.findByProfessionalIds(professionalIds);
    }

    /**
     * Find professionals available on a specific day - Public access
     */
    public List<WorkSchedule> findByAvailableDay(DayOfWeek dayOfWeek) {
        userSyncService.ensureCurrentUserExists();
        return repository.findByAvailableDay(dayOfWeek);
    }

    // =====================================================
    // Command Operations (Write)
    // =====================================================

    /**
     * Create or update work schedule - Only professionals can manage their own
     */
    @Transactional
    public WorkSchedule createOrUpdate(WorkSchedule workSchedule) {
        userSyncService.ensureCurrentUserExists();

        UUID currentUserId = tokenIdentityProvider.getCurrentUserId();
        if (currentUserId == null) {
            throw new ForbiddenException("Authentication required");
        }

        // For new schedules, ensure professional is current user
        if (workSchedule.getProfessionalId() == null) {
            if (!currentUserId.equals(workSchedule.getProfessionalId())) {
                throw new ForbiddenException("Professionals can only create their own work schedules");
            }

            // Load the professional user entity
            User professional = User.findById(currentUserId);
            if (professional == null || !professional.isProfessional) {
                throw new IllegalArgumentException("User is not a professional");
            }
            workSchedule.setProfessional(professional);

            repository.persist(workSchedule);
            repository.flush();

            logger.info("Work schedule created for professional: {}", currentUserId);
            return workSchedule;
        } else {
            // For updates, check authorization
            WorkSchedule existingSchedule = repository.findById(workSchedule.getProfessionalId())
                    .orElseThrow(() -> new IllegalArgumentException("Work schedule not found"));

            if (!tokenIdentityProvider.canAccessProfessionalData(existingSchedule.getProfessionalId())) {
                throw new ForbiddenException("Access denied to update work schedule");
            }

            // Preserve immutable fields
            workSchedule.setProfessional(existingSchedule.getProfessional());
            workSchedule.setCreatedAt(existingSchedule.getCreatedAt());

            WorkSchedule result = repository.update(workSchedule);
            logger.info("Work schedule updated for professional: {}", workSchedule.getProfessionalId());
            return result;
        }
    }

    /**
     * Update time ranges for a specific day - Only professionals can manage their own
     */
    @Transactional
    public WorkSchedule updateDayTimeRanges(UUID professionalId, DayOfWeek dayOfWeek, List<DayTimeRange> dayTimeRanges) {
        userSyncService.ensureCurrentUserExists();

        // Check authorization
        if (!tokenIdentityProvider.canAccessProfessionalData(professionalId)) {
            throw new ForbiddenException("Access denied to update work schedule");
        }

        // Get or create work schedule
        WorkSchedule workSchedule = repository.getOrCreateForProfessional(professionalId);

        // Update the specific day
        workSchedule.setTimeRangesForDay(dayOfWeek, dayTimeRanges);

        WorkSchedule result = repository.update(workSchedule);
        logger.info("Day time ranges updated for professional: {} on {}", professionalId, dayOfWeek);
        return result;
    }

    /**
     * Add time range to a specific day - Only professionals can manage their own
     */
    @Transactional
    public WorkSchedule addTimeRange(UUID professionalId, DayOfWeek dayOfWeek, 
                                   LocalTime startTime, LocalTime endTime) {
        userSyncService.ensureCurrentUserExists();

        // Check authorization
        if (!tokenIdentityProvider.canAccessProfessionalData(professionalId)) {
            throw new ForbiddenException("Access denied to update work schedule");
        }

        // Get or create work schedule
        WorkSchedule workSchedule = repository.getOrCreateForProfessional(professionalId);
        
        // Add time range to the specific day
        workSchedule.addTimeRange(dayOfWeek, startTime, endTime);
        
        WorkSchedule result = repository.update(workSchedule);
        logger.info("Time range added for professional: {} on {} ({}-{})", 
                   professionalId, dayOfWeek, startTime, endTime);
        return result;
    }

    /**
     * Clear schedule for a specific day - Only professionals can manage their own
     */
    @Transactional
    public WorkSchedule clearDay(UUID professionalId, DayOfWeek dayOfWeek) {
        userSyncService.ensureCurrentUserExists();

        // Check authorization
        if (!tokenIdentityProvider.canAccessProfessionalData(professionalId)) {
            throw new ForbiddenException("Access denied to update work schedule");
        }

        Optional<WorkSchedule> scheduleOpt = repository.findById(professionalId);
        if (scheduleOpt.isEmpty()) {
            // No schedule exists, nothing to clear
            return repository.getOrCreateForProfessional(professionalId);
        }

        WorkSchedule workSchedule = scheduleOpt.get();
        workSchedule.clearDay(dayOfWeek);
        
        WorkSchedule result = repository.update(workSchedule);
        logger.info("Day schedule cleared for professional: {} on {}", professionalId, dayOfWeek);
        return result;
    }

    /**
     * Delete work schedule - Only professionals can delete their own
     */
    @Transactional
    public boolean delete(UUID professionalId) {
        userSyncService.ensureCurrentUserExists();

        // Check if schedule exists
        Optional<WorkSchedule> scheduleOpt = repository.findById(professionalId);
        if (scheduleOpt.isEmpty()) {
            return false;
        }

        // Check authorization
        if (!tokenIdentityProvider.canAccessProfessionalData(professionalId)) {
            throw new ForbiddenException("Access denied to delete work schedule");
        }

        repository.deleteById(professionalId);
        logger.info("Work schedule deleted for professional: {}", professionalId);
        return true;
    }

    // =====================================================
    // Availability Check Methods (for booking integration)
    // =====================================================

    /**
     * Check if professional is available on a specific day and time
     */
    public boolean isAvailableAt(UUID professionalId, DayOfWeek dayOfWeek, LocalTime time) {
        Optional<WorkSchedule> scheduleOpt = repository.findById(professionalId);
        return scheduleOpt.map(schedule -> schedule.isAvailableAt(dayOfWeek, time))
                         .orElse(false);
    }

    /**
     * Check if professional is available on a specific day (any time)
     */
    public boolean isAvailableOn(UUID professionalId, DayOfWeek dayOfWeek) {
        Optional<WorkSchedule> scheduleOpt = repository.findById(professionalId);
        return scheduleOpt.map(schedule -> schedule.isAvailableOn(dayOfWeek))
                         .orElse(false);
    }
}
