import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import {
  MessagingState,
  MessageDTO,
  ConversationSummaryDTO,
  WebSocketConnectionState
} from '../types/api';
import { UserMode } from '../types/auth';
import {
  getConversations,
  getMessagesForBooking,
  getConversationDetail,
  markConversationAsRead,
  getUnreadMessageCount
} from '../services/api/messagingService';
import { WebSocketService, WebSocketServiceConfig } from '../services/messaging/websocketService';
import { useAuth } from './AuthContext';

// Action types
enum MessagingActionType {
  SET_LOADING = 'SET_LOADING',
  SET_ERROR = 'SET_ERROR',
  SET_CONVERSATIONS = 'SET_CONVERSATIONS',
  SET_CURRENT_CONVERSATION = 'SET_CURRENT_CONVERSATION',
  SET_MESSAGES = 'SET_MESSAGES',
  ADD_MESSAGE = 'ADD_MESSAGE',
  UPDATE_MESSAGE = 'UPDATE_MESSAGE',
  SET_UNREAD_COUNT = 'SET_UNREAD_COUNT',
  SET_WEBSOCKET_STATE = 'SET_WEBSOCKET_STATE',
  SET_TYPING_USERS = 'SET_TYPING_USERS',
  CLEAR_CURRENT_CONVERSATION = 'CLEAR_CURRENT_CONVERSATION'
}

// Action interfaces
interface MessagingAction {
  type: MessagingActionType;
  payload?: any;
}

// Initial state
const initialState: MessagingState = {
  conversations: [],
  currentConversation: {
    bookingId: null,
    messages: [],
    isLoading: false,
    error: null
  },
  unreadCount: 0,
  isLoading: false,
  error: null,
  websocket: {
    isConnected: false,
    isConnecting: false,
    error: null,
    lastConnected: null,
    reconnectAttempts: 0
  },
  typingUsers: {}
};

// Reducer
const messagingReducer = (state: MessagingState, action: MessagingAction): MessagingState => {
  switch (action.type) {
    case MessagingActionType.SET_LOADING:
      return {
        ...state,
        isLoading: action.payload
      };

    case MessagingActionType.SET_ERROR:
      return {
        ...state,
        error: action.payload,
        isLoading: false
      };

    case MessagingActionType.SET_CONVERSATIONS:
      return {
        ...state,
        conversations: action.payload,
        isLoading: false,
        error: null
      };

    case MessagingActionType.SET_CURRENT_CONVERSATION:
      return {
        ...state,
        currentConversation: {
          ...state.currentConversation,
          bookingId: action.payload,
          messages: [],
          isLoading: true,
          error: null
        }
      };

    // Removed SET_CURRENT_CONVERSATION_WITH_PARTICIPANT case - no longer needed
    // since we use client/professional fields from conversation list instead of otherParticipant fields

    case MessagingActionType.SET_MESSAGES:
      return {
        ...state,
        currentConversation: {
          ...state.currentConversation,
          messages: action.payload,
          isLoading: false,
          error: null
        }
      };

    case MessagingActionType.ADD_MESSAGE:
      return {
        ...state,
        currentConversation: {
          ...state.currentConversation,
          messages: [...state.currentConversation.messages, action.payload]
        }
      };

    case MessagingActionType.UPDATE_MESSAGE:
      return {
        ...state,
        currentConversation: {
          ...state.currentConversation,
          messages: state.currentConversation.messages.map(msg =>
            msg.id === action.payload.id ? { ...msg, ...action.payload } : msg
          )
        }
      };

    case MessagingActionType.SET_UNREAD_COUNT:
      return {
        ...state,
        unreadCount: action.payload
      };

    case MessagingActionType.SET_WEBSOCKET_STATE:
      return {
        ...state,
        websocket: action.payload
      };

    case MessagingActionType.SET_TYPING_USERS:
      return {
        ...state,
        typingUsers: {
          ...state.typingUsers,
          [action.payload.bookingId]: action.payload.userIds
        }
      };

    case MessagingActionType.CLEAR_CURRENT_CONVERSATION:
      return {
        ...state,
        currentConversation: {
          bookingId: null,
          messages: [],
          isLoading: false,
          error: null
        }
      };

    default:
      return state;
  }
};

// Context interface
interface MessagingContextType {
  state: MessagingState;
  loadConversations: () => Promise<void>;
  loadMessages: (bookingId: number) => Promise<void>;
  sendMessage: (bookingId: number, receiverUserId: string, content: string) => Promise<void>;
  markConversationRead: (bookingId: number) => Promise<void>;
  connectToConversation: (bookingId: number) => void;
  disconnectFromConversation: () => void;
  sendTyping: (isTyping: boolean) => void;
  refreshUnreadCount: () => Promise<void>;
}

// Create context
const MessagingContext = createContext<MessagingContextType | undefined>(undefined);

// Provider props
interface MessagingProviderProps {
  children: ReactNode;
}

// Provider component
export function MessagingProvider({ children }: MessagingProviderProps) {
  const [state, dispatch] = useReducer(messagingReducer, initialState);
  const { state: authState } = useAuth();
  const [wsService, setWsService] = React.useState<WebSocketService | null>(null);

  // Filter conversations based on user mode
  const filterConversationsByMode = (
    conversations: ConversationSummaryDTO[],
    currentMode: UserMode,
    currentUserId?: string
  ): ConversationSummaryDTO[] => {
    if (!currentUserId) {
      console.warn('⚠️ No current user ID available for filtering conversations');
      return conversations;
    }

    const filtered = conversations.filter(conversation => {
      if (currentMode === UserMode.CLIENT) {
        // In client mode, only show conversations where current user is the client
        const isClient = conversation.clientId === currentUserId;
        console.log(`🔍 Conversation ${conversation.bookingId}: clientId=${conversation.clientId}, currentUserId=${currentUserId}, isClient=${isClient}`);
        return isClient;
      } else {
        // In professional mode, only show conversations where current user is the professional
        const isProfessional = conversation.professionalId === currentUserId;
        console.log(`🔍 Conversation ${conversation.bookingId}: professionalId=${conversation.professionalId}, currentUserId=${currentUserId}, isProfessional=${isProfessional}`);
        return isProfessional;
      }
    });

    console.log(`🔍 Mode-based filtering: ${currentMode} mode, ${conversations.length} total → ${filtered.length} filtered`);
    return filtered;
  };

  // Load conversations
  const loadConversations = async (): Promise<void> => {
    if (!authState.isAuthenticated) {
      console.log('⚠️ User not authenticated, skipping conversations load');
      return;
    }

    dispatch({ type: MessagingActionType.SET_LOADING, payload: true });

    try {
      const allConversations = await getConversations();
      console.log('📡 All conversations received:', allConversations.length);

      // Filter conversations based on current user mode
      const filteredConversations = filterConversationsByMode(allConversations, authState.currentMode, authState.user?.internalId);
      console.log('🔍 Filtered conversations for mode', authState.currentMode, ':', filteredConversations.length);

      dispatch({ type: MessagingActionType.SET_CONVERSATIONS, payload: filteredConversations });

      // Update unread count based on filtered conversations
      const totalUnread = filteredConversations.reduce((sum, conv) => sum + conv.unreadCount, 0);
      dispatch({ type: MessagingActionType.SET_UNREAD_COUNT, payload: totalUnread });
    } catch (error) {
      console.error('❌ Failed to load conversations:', error);
      dispatch({ type: MessagingActionType.SET_ERROR, payload: 'Failed to load conversations' });
    }
  };

  // Load messages for a conversation
  const loadMessages = async (bookingId: number): Promise<void> => {
    if (!authState.isAuthenticated) {
      console.log('⚠️ User not authenticated, skipping messages load');
      return;
    }

    console.log('🔄 Loading messages for booking:', bookingId);

    try {
      // Use the enhanced conversation detail API that handles missing conversations
      console.log('🔄 Calling getConversationDetail for booking:', bookingId);
      const { messages, conversation } = await getConversationDetail(bookingId);

      console.log('📋 getConversationDetail response:', {
        messagesCount: messages?.length || 0,
        conversationExists: !!conversation,
        conversation: conversation
      });

      // Set current conversation
      if (conversation) {
        console.log('📝 Setting current conversation with participant info:', conversation);

        // If conversation is not already in the conversations list, add it
        if (!state.conversations.find(conv => conv.bookingId === bookingId)) {
          console.log('📝 Adding conversation to list:', conversation);
          dispatch({
            type: MessagingActionType.SET_CONVERSATIONS,
            payload: [...state.conversations, conversation]
          });
        }
      }

      // Set current conversation booking ID
      dispatch({ type: MessagingActionType.SET_CURRENT_CONVERSATION, payload: bookingId });

      // Update messages AFTER setting participant info
      dispatch({ type: MessagingActionType.SET_MESSAGES, payload: messages });

    } catch (error) {
      console.error('❌ Failed to load messages:', error);
      dispatch({
        type: MessagingActionType.SET_ERROR,
        payload: 'Failed to load messages'
      });
    }
  };

  // Send message
  const sendMessage = async (
    bookingId: number,
    receiverUserId: string,
    content: string
  ): Promise<void> => {
    console.log('🚀 sendMessage called:', { bookingId, receiverUserId, content });

    if (!authState.isAuthenticated || !authState.user) {
      console.error('❌ User not authenticated');
      throw new Error('User not authenticated');
    }

    try {
      console.log('🔍 Checking WebSocket connection...');
      if (wsService && wsService.isConnected()) {
        console.log('✅ WebSocket connected, sending via WebSocket');
        // Send via WebSocket for real-time delivery
        wsService.sendMessage(receiverUserId, content);
      } else {
        console.log('⚠️ WebSocket not connected, falling back to REST API');
        // Fallback to REST API
        const { sendMessage: sendMessageRest } = await import('../services/api/messagingService');
        await sendMessageRest({ bookingId, receiverUserId, content });

        // Reload messages to get the new message
        await loadMessages(bookingId);
      }
      console.log('✅ Message sent successfully');
    } catch (error) {
      console.error('❌ Failed to send message:', error);
      throw error;
    }
  };

  // Mark conversation as read
  const markConversationRead = async (bookingId: number): Promise<void> => {
    try {
      await markConversationAsRead(bookingId);
      
      // Update local state
      const updatedConversations = state.conversations.map(conv =>
        conv.bookingId === bookingId ? { ...conv, unreadCount: 0 } : conv
      );
      dispatch({ type: MessagingActionType.SET_CONVERSATIONS, payload: updatedConversations });
      
      // Refresh unread count
      await refreshUnreadCount();
    } catch (error) {
      console.error('❌ Failed to mark conversation as read:', error);
      throw error;
    }
  };

  // Connect to WebSocket for a conversation
  const connectToConversation = (bookingId: number): void => {
    if (!authState.isAuthenticated || !authState.user) {
      console.log('⚠️ User not authenticated, skipping WebSocket connection');
      return;
    }

    // Disconnect existing connection
    disconnectFromConversation();

    const config: WebSocketServiceConfig = {
      bookingId,
      userId: authState.user.internalId, // Use internal user ID instead of Keycloak ID
      onMessage: (message: MessageDTO) => {
        dispatch({ type: MessagingActionType.ADD_MESSAGE, payload: message });
      },
      onTyping: (bookingId: number, userIds: string[]) => {
        dispatch({
          type: MessagingActionType.SET_TYPING_USERS,
          payload: { bookingId, userIds }
        });
      },
      onConnectionChange: (connectionState: WebSocketConnectionState) => {
        dispatch({ type: MessagingActionType.SET_WEBSOCKET_STATE, payload: connectionState });
      },
      onError: (error: string) => {
        console.error('❌ WebSocket error:', error);
        dispatch({ type: MessagingActionType.SET_ERROR, payload: error });
      }
    };

    const newWsService = new WebSocketService(config);
    setWsService(newWsService);
    newWsService.connect();
  };

  // Disconnect from WebSocket
  const disconnectFromConversation = (): void => {
    if (wsService) {
      wsService.disconnect();
      setWsService(null);
    }
    // Note: Don't clear current conversation here as it's called when switching conversations
    // Only clear when actually leaving the messaging context
  };

  // Send typing indicator
  const sendTyping = (isTyping: boolean): void => {
    if (wsService && wsService.isConnected()) {
      wsService.sendTyping(isTyping);
    }
  };

  // Refresh unread count
  const refreshUnreadCount = async (): Promise<void> => {
    if (!authState.isAuthenticated) {
      return;
    }

    try {
      const count = await getUnreadMessageCount();
      dispatch({ type: MessagingActionType.SET_UNREAD_COUNT, payload: count });
    } catch (error) {
      console.error('❌ Failed to refresh unread count:', error);
    }
  };

  // Load conversations when user authenticates or mode changes
  useEffect(() => {
    if (authState.isAuthenticated) {
      console.log('🔄 Auth state or mode changed - loading conversations for mode:', authState.currentMode);
      loadConversations();
      refreshUnreadCount();
    } else {
      console.log('🔄 Auth state changed - clearing conversations');
      // Clear state when user logs out
      dispatch({ type: MessagingActionType.SET_CONVERSATIONS, payload: [] });
      dispatch({ type: MessagingActionType.SET_UNREAD_COUNT, payload: 0 });
      dispatch({ type: MessagingActionType.CLEAR_CURRENT_CONVERSATION });
      disconnectFromConversation();
    }
  }, [authState.isAuthenticated, authState.currentMode]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      dispatch({ type: MessagingActionType.CLEAR_CURRENT_CONVERSATION });
      disconnectFromConversation();
    };
  }, []);

  const contextValue: MessagingContextType = {
    state,
    loadConversations,
    loadMessages,
    sendMessage,
    markConversationRead,
    connectToConversation,
    disconnectFromConversation,
    sendTyping,
    refreshUnreadCount
  };

  return (
    <MessagingContext.Provider value={contextValue}>
      {children}
    </MessagingContext.Provider>
  );
}

// Custom hook to use messaging context
export function useMessaging(): MessagingContextType {
  const context = useContext(MessagingContext);
  if (context === undefined) {
    throw new Error('useMessaging must be used within a MessagingProvider');
  }
  return context;
}

export default MessagingContext;
