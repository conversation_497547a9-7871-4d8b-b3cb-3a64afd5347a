package com.yotelohago.media.domain;

/**
 * Enum representing different types of media that can be uploaded
 */
public enum MediaType {
    /**
     * Profile picture for client users
     */
    CLIENT_PROFILE_PICTURE("client_profile_picture"),
    
    /**
     * Profile picture for professional users
     */
    PROFESSIONAL_PROFILE_PICTURE("professional_profile_picture"),
    
    /**
     * Photo associated with a service offering
     */
    SERVICE_PHOTO("service_photo"),
    
    /**
     * General document or file
     */
    DOCUMENT("document");

    private final String value;

    MediaType(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    /**
     * Get MediaType from string value
     * @param value String representation
     * @return MediaType enum
     * @throws IllegalArgumentException if value is not valid
     */
    public static MediaType fromValue(String value) {
        for (MediaType type : MediaType.values()) {
            if (type.value.equals(value)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Invalid MediaType value: " + value);
    }

    @Override
    public String toString() {
        return value;
    }
}
