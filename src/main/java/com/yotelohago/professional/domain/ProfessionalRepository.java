package com.yotelohago.professional.domain;

import com.yotelohago.professional.api.ProfessionalWithDetailsDTO;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository interface for Professional domain
 * Follows the same coding convention as Service, Booking, Media, and Favorite domains:
 * - URLs are built exclusively in SQL queries using CONCAT()
 * - DTO-returning methods include pre-built URLs to avoid N+1 problems
 */
public interface ProfessionalRepository {

    // Entity operations (for persistence)
    void persist(Professional professional);
    Optional<Professional> findById(UUID userId);
    List<Professional> findAll();
    Professional update(Professional professional);
    void deleteById(UUID userId);
    void flush();

    // DTO operations with SQL-based URL building (for API responses)
    /**
     * Find professional with details by ID, including profile image URL built in SQL
     * @param userId Professional user ID
     * @return Optional ProfessionalWithDetailsDTO with pre-built URLs
     */
    Optional<ProfessionalWithDetailsDTO> findWithDetailsByIdWithUrl(UUID userId);


}
