import { router } from 'expo-router';
import crossPlatformStorage from '../services/storage/crossPlatformStorage';

interface AuthRequiredParams {
  actionType: 'booking' | 'messaging' | 'general';
  actionName: string;
  returnPath: string;
  professionalId?: string;
  professionalName?: string;
}

/**
 * Navigate to the auth required screen with context about what action requires authentication
 */
export const navigateToAuthRequired = async ({
  actionType,
  actionName,
  returnPath,
  professionalId,
  professionalName,
}: AuthRequiredParams) => {
  // Store auth context using cross-platform storage
  try {
    await Promise.all([
      crossPlatformStorage.setSessionItem('auth_action_type', actionType),
      crossPlatformStorage.setSessionItem('auth_action_name', actionName),
      crossPlatformStorage.setSessionItem('auth_professional_id', professionalId || ''),
      crossPlatformStorage.setSessionItem('auth_professional_name', professionalName || ''),
    ]);
  } catch (error) {
    console.error('Failed to store auth context:', error);
  }

  const params = new URLSearchParams({
    actionType,
    actionName,
    returnPath,
    ...(professionalId && { professionalId }),
    ...(professionalName && { professionalName }),
  });

  console.log('🔄 Navigating to auth required:', {
    actionType,
    actionName,
    returnPath,
    professionalId,
    professionalName,
  });

  router.push(`/auth/required?${params.toString()}`);
};

/**
 * Helper to get the current path for return navigation
 */
export const getCurrentPath = (): string => {
  return crossPlatformStorage.getCurrentPath();
};

/**
 * Helper to build professional detail path
 */
export const getProfessionalPath = (professionalId: string): string => {
  return `/client/professional/${professionalId}`;
};
