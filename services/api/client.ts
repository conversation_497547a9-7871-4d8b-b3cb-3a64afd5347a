import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';
import { ENV } from '../../config/environment';
import { ApiError } from '../../types/api';
import tokenStorage from '../auth/tokenStorage';

// Create axios instance with base configuration
const createApiClient = (): AxiosInstance => {
  const client = axios.create({
    baseURL: ENV.API_BASE_URL,
    timeout: 10000,
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    },
  });

  // Request interceptor - add auth token to requests
  client.interceptors.request.use(
    async (config) => {
      console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`);

      // Skip auth for explicitly marked requests
      if (config.headers?.['X-Skip-Auth']) {
        delete config.headers['X-Skip-Auth'];
        console.log('⏭️ Skipping auth for this request');
        return config;
      }

      // Add authentication token if available
      try {
        const accessToken = await tokenStorage.getAccessToken();
        if (accessToken) {
          config.headers.Authorization = `Bearer ${accessToken}`;
          console.log('🔐 Added auth token to request');
        }
      } catch (error) {
        console.warn('⚠️ Failed to get access token:', error);
      }

      return config;
    },
    (error) => {
      console.error('❌ Request Error:', error);
      return Promise.reject(error);
    }
  );

  // Response interceptor
  client.interceptors.response.use(
    (response: AxiosResponse) => {
      console.log(`✅ API Response: ${response.status} ${response.config.url}`);
      return response;
    },
    (error: AxiosError) => {
      console.error(`❌ API Error: ${error.response?.status} ${error.config?.url}`, error);

      // Handle common error scenarios
      const apiError: ApiError = {
        error: 'Unknown error',
        status: error.response?.status || 0,
      };

      if (error.response) {
        // Backend returned an HTTP error response
        const status = error.response.status;
        const errorData = error.response.data as any;

        apiError.status = status;

        // Handle specific HTTP status codes
        switch (status) {
          case 401:
            apiError.error = 'Authentication required - please login';
            apiError.message = errorData?.error || 'Unauthorized access';
            break;
          case 403:
            apiError.error = 'Access forbidden - insufficient permissions';
            apiError.message = errorData?.error || 'Forbidden';
            break;
          case 404:
            apiError.error = 'Resource not found';
            apiError.message = errorData?.error || 'Not found';
            break;
          case 500:
            apiError.error = 'Internal server error';
            apiError.message = errorData?.error || 'Server error';
            break;
          default:
            apiError.error = errorData?.error || errorData?.message || `HTTP ${status} error`;
            apiError.message = errorData?.message;
        }
      } else if (error.request) {
        // Network error - no response received
        apiError.error = 'Network error - please check your connection';
        apiError.message = 'Unable to reach the server';
      } else {
        // Request setup error
        apiError.error = error.message || 'Request failed';
        apiError.message = 'Failed to setup request';
      }

      return Promise.reject(apiError);
    }
  );

  return client;
};

// Create the main API client instance
export const apiClient = createApiClient();

// Helper function to handle API calls with consistent error handling
export const apiCall = async <T>(
  requestFn: () => Promise<AxiosResponse<T>>
): Promise<T> => {
  try {
    const response = await requestFn();
    return response.data;
  } catch (error) {
    // Error is already processed by the interceptor
    throw error;
  }
};

// Helper functions for different HTTP methods
export const apiGet = <T>(url: string, config?: AxiosRequestConfig): Promise<T> => {
  return apiCall(() => apiClient.get<T>(url, config));
};

// Helper function for public endpoints (no auth required)
export const apiGetPublic = <T>(url: string, config?: AxiosRequestConfig): Promise<T> => {
  const publicConfig = {
    ...config,
    headers: {
      ...config?.headers,
      'X-Skip-Auth': 'true'
    }
  };
  return apiCall(() => apiClient.get<T>(url, publicConfig));
};

export const apiPost = <T>(
  url: string, 
  data?: any, 
  config?: AxiosRequestConfig
): Promise<T> => {
  return apiCall(() => apiClient.post<T>(url, data, config));
};

export const apiPut = <T>(
  url: string, 
  data?: any, 
  config?: AxiosRequestConfig
): Promise<T> => {
  return apiCall(() => apiClient.put<T>(url, data, config));
};

export const apiDelete = <T>(url: string, config?: AxiosRequestConfig): Promise<T> => {
  return apiCall(() => apiClient.delete<T>(url, config));
};

export const apiPatch = <T>(
  url: string, 
  data?: any, 
  config?: AxiosRequestConfig
): Promise<T> => {
  return apiCall(() => apiClient.patch<T>(url, data, config));
};

// Health check function to test API connectivity
export const checkApiHealth = async (): Promise<boolean> => {
  try {
    // Try to hit a public endpoint that should exist
    await apiGet('/services');
    return true;
  } catch (error) {
    console.warn('API health check failed:', error);
    return false;
  }
};

export default apiClient;
