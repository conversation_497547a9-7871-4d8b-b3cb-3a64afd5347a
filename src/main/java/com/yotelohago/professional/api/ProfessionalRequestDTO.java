package com.yotelohago.professional.api;

import java.util.UUID;

/**
 * Request DTO for creating and updating professional profiles
 * Used for POST and PUT operations where we don't need to return URLs
 */
public class ProfessionalRequestDTO {
    
    public UUID userId;
    public String bio;
    public String city;
    public boolean available;

    /**
     * Default constructor for JSON serialization/deserialization
     */
    public ProfessionalRequestDTO() {}

    /**
     * Constructor with all fields
     */
    public ProfessionalRequestDTO(UUID userId, String bio, String city, boolean available) {
        this.userId = userId;
        this.bio = bio;
        this.city = city;
        this.available = available;
    }

    @Override
    public String toString() {
        return "ProfessionalRequestDTO{" +
                "userId=" + userId +
                ", bio='" + bio + '\'' +
                ", city='" + city + '\'' +
                ", available=" + available +
                '}';
    }
}
