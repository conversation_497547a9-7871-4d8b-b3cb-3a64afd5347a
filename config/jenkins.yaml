### JEN<PERSON>INS CONFIGURATION FILE TO INJECT TOE TRRAFORM, HELM, ANSIBLE and JCASC ###

# Terraform
terraform:
  # Shared vars
  region: us-east-1
  profile: kodekloud
  resource_base_name: jen<PERSON>
  bucket_name: terraform-jenkins-kodekloud
  bucket_key_terraform_infra: terraform_infra.tfstate
  bucket_key_terraform_helm: terraform_helm.tfstate
  dynamodb_table_name: terraform-locks

  # TAILSCALE DNS RESOLVER
  tailscale_dns_nameserver: 100.112.156.85


  # EC2 instance
  instance_type: t3.medium
  ec2_key_path: /Users/<USER>/.ssh/aws/key
  ec2_user: ec2-user

  # ECR
  repository_name: yotelohago/backend
  image_tag: dev

  # HELM
  k8s_kubeconfig_path: ./config/ansible_kubeconfig-macmini1-k3s.generated # default is: "./config/ansible_kubeconfig-k3s.generated"
  helm_argocd_version: 5.51.6





