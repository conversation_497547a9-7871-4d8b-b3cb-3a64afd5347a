provider "aws" {
  region = var.region
  #profile = var.profile
}

resource "null_resource" "empty_bucket_on_destroy" {
  # Store the bucket name in triggers so it's available during destroy
  triggers = {
    bucket_name = aws_s3_bucket.terraform_state.bucket
  }

  provisioner "local-exec" {
    when    = destroy
    command = "aws s3 rm s3://${self.triggers.bucket_name} --recursive"
  }
}

resource "aws_s3_bucket" "terraform_state" {
  bucket = var.bucket_name
}

resource "aws_dynamodb_table" "terraform_locks" {
  name         = var.dynamodb_table_name
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "LockID"

  attribute {
    name = "LockID"
    type = "S"
  }

}