package com.yotelohago.review.domain;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface ReviewRepository {
    void persist(Review review);
    Optional<Review> findById(Long id);
    Optional<Review> findByBookingId(Long bookingId);
    List<Review> findByProfessionalUserId(UUID professionalUserId); // TODO: Add pagination for performance
    List<Review> findAll(); // TODO: Add pagination for performance
    void deleteById(Long id);
    void flush();
}
