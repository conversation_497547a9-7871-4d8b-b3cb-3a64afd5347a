package com.yotelohago.media.domain;

import com.yotelohago.media.api.MediaDTO;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository interface for Media domain
 * Follows the same coding convention as Service and Booking domains:
 * - URLs are built exclusively in SQL queries using CONCAT()
 * - All public methods return DTOs with URLs pre-built
 * - No URL construction in service or mapper layers
 */
public interface MediaRepository {

    /**
     * Persist a new media entity
     * @param media The media entity to persist
     */
    void persist(Media media);

    /**
     * Flush pending changes to database
     */
    void flush();

    /**
     * Delete media entity
     * @param media The media entity to delete
     */
    void delete(Media media);

    /**
     * Check if media exists by file path
     * @param filePath The file path in storage
     * @return true if media exists with this path
     */
    boolean existsByFilePath(String filePath);

    /**
     * Count total media for a user
     * @param userId The user ID
     * @return Number of media files owned by the user
     */
    long countByUserId(UUID userId);

    /**
     * Get total storage size used by a user
     * @param userId The user ID
     * @return Total bytes used by the user's media
     */
    long getTotalStorageSizeByUserId(UUID userId);

    // =====================================================
    // Methods that return DTOs with URLs built in SQL
    // Following the same convention as Service and Booking domains
    // =====================================================

    /**
     * Find media by ID and return DTO with full URL built in SQL
     * @param id The media ID
     * @return Optional containing the MediaDTO with publicUrl if found
     */
    Optional<MediaDTO> findByIdWithUrl(UUID id);

    /**
     * Find all media owned by a specific user and return DTOs with full URLs built in SQL
     * @param userId The user ID
     * @return List of MediaDTOs with publicUrls
     */
    List<MediaDTO> findByUserIdWithUrls(UUID userId);

    /**
     * Find media by user ID and media type and return DTOs with full URLs built in SQL
     * @param userId The user ID
     * @param mediaType The type of media
     * @return List of MediaDTOs with publicUrls
     */
    List<MediaDTO> findByUserIdAndTypeWithUrls(UUID userId, MediaType mediaType);

    /**
     * Find media by associated entity ID and return DTOs with full URLs built in SQL
     * @param associatedEntityId The associated entity ID
     * @return List of MediaDTOs with publicUrls
     */
    List<MediaDTO> findByAssociatedEntityIdWithUrls(UUID associatedEntityId);

    /**
     * Find media by user ID, type, and associated entity ID and return DTOs with full URLs built in SQL
     * @param userId The user ID
     * @param mediaType The media type
     * @param associatedEntityId The associated entity ID
     * @return List of MediaDTOs with publicUrls
     */
    List<MediaDTO> findByUserIdTypeAndAssociatedEntityWithUrls(UUID userId, MediaType mediaType, UUID associatedEntityId);

    /**
     * Find media by file path and return DTO with full URL built in SQL
     * @param filePath The file path in storage
     * @return Optional containing the MediaDTO with publicUrl if found
     */
    Optional<MediaDTO> findByFilePathWithUrl(String filePath);

    /**
     * Find the most recent profile photo for a user by media type and return DTO with full URL built in SQL
     * @param userId The user ID
     * @param mediaType The media type (CLIENT_PROFILE_PICTURE or PROFESSIONAL_PROFILE_PICTURE)
     * @return Optional containing the MediaDTO with publicUrl if found
     */
    Optional<MediaDTO> findProfilePhotoByUserAndTypeWithUrl(UUID userId, MediaType mediaType);

    /**
     * Delete media by ID (requires entity lookup first)
     * @param id The media ID
     * @return true if media was deleted, false if not found
     */
    boolean deleteById(UUID id);
}
