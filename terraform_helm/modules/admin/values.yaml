image:
  repository: registry.yotelohago.co/admin/main
  tag: latest
  pullPolicy: Always

replicaCount: 1

service:
  type: ClusterIP
  port: 80
  targetPort: 80

ingress:
  enabled: true
  className: traefik
  host: admin.yotelohago.co
  tlsSecret: admin-yotelohago-co-tls
  annotations:
    kubernetes.io/ingress.class: traefik
    traefik.ingress.kubernetes.io/router.entrypoints: web, websecure
    cert-manager.io/cluster-issuer: letsencrypt-prod

resources:
  limits:
    cpu: 500m
    memory: 512Mi
  requests:
    cpu: 100m
    memory: 128Mi

nodeSelector: {}

tolerations: []

affinity: {}

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 3
  targetCPUUtilizationPercentage: 80

securityContext:
  runAsNonRoot: true
  runAsUser: 101
  fsGroup: 101

podSecurityContext:
  runAsNonRoot: true
  runAsUser: 101
  fsGroup: 101
