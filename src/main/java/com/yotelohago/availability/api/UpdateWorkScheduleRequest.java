package com.yotelohago.availability.api;

import java.util.List;

/**
 * Request DTO for updating work schedules.
 * 
 * Allows updating the entire weekly schedule or individual day schedules.
 * All fields are optional to support partial updates.
 */
public class UpdateWorkScheduleRequest {
    
    public List<DayScheduleDTO> daySchedules;
    public String notes;

    // Default constructor for JSON deserialization
    public UpdateWorkScheduleRequest() {}

    // Constructor for testing and convenience
    public UpdateWorkScheduleRequest(List<DayScheduleDTO> daySchedules, String notes) {
        this.daySchedules = daySchedules;
        this.notes = notes;
    }
}
