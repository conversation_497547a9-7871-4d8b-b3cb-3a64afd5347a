package com.yotelohago.availability.api;

import com.yotelohago.availability.application.AvailabilityManagementService;
import com.yotelohago.availability.domain.WorkSchedule;
import com.yotelohago.auth.TokenIdentityProvider;
import com.yotelohago.common.ApiVersion;
import com.yotelohago.common.dto.PageRequest;
import com.yotelohago.common.dto.PageResponse;
import jakarta.annotation.security.RolesAllowed;
import jakarta.inject.Inject;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;

import java.time.DayOfWeek;
import java.time.LocalTime;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * REST controller for work schedule management.
 * 
 * Provides endpoints for managing professional work schedules:
 * - GET endpoints for viewing schedules (public access)
 * - PUT endpoints for updating schedules (professionals only)
 * - Simple API focused on weekly recurring patterns
 * 
 * Endpoints:
 * - GET /api/v1/availability/professionals/{professionalId} - Get work schedule for professional
 * - PUT /api/v1/availability/professionals/{professionalId} - Update work schedule
 * - GET /api/v1/availability/day/{dayOfWeek} - Find professionals available on specific day
 * - GET /api/v1/availability/{id} - Get specific work schedule by ID
 * - DELETE /api/v1/availability/{id} - Delete work schedule
 */
@Path(ApiVersion.BASE + "/availability")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@RolesAllowed({"user", "professional", "admin"})
public class AvailabilityResource {

    @Inject
    AvailabilityManagementService service;

    @Inject
    WorkScheduleMapper mapper;

    @Inject
    TokenIdentityProvider tokenIdentityProvider;

    // =====================================================
    // Query Endpoints (Public Access)
    // =====================================================

    /**
     * Get work schedule for a professional - Accessible by clients, professionals, and admins
     * Creates empty schedule if none exists for easier UI binding
     */
    @GET
    @Path("/professionals/{professionalId}")
    public Response getWorkScheduleByProfessional(@PathParam("professionalId") UUID professionalId) {
        try {
            WorkSchedule workSchedule = service.getOrCreateForProfessional(professionalId);
            WorkScheduleDTO dto = mapper.toDTO(workSchedule);
            return Response.ok(dto).build();
        } catch (ForbiddenException e) {
            return Response.status(Response.Status.FORBIDDEN)
                    .entity("{\"error\": \"" + e.getMessage() + "\"}")
                    .build();
        } catch (IllegalArgumentException e) {
            return Response.status(Response.Status.BAD_REQUEST)
                    .entity("{\"error\": \"" + e.getMessage() + "\"}")
                    .build();
        } catch (Exception e) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("{\"error\": \"Failed to retrieve work schedule\"}")
                    .build();
        }
    }

    /**
     * Get work schedule by professional ID
     */
    @GET
    @Path("/{professionalId}")
    public Response getById(@PathParam("professionalId") UUID professionalId) {
        try {
            return service.findById(professionalId)
                    .map(mapper::toDTO)
                    .map(dto -> Response.ok(dto).build())
                    .orElse(Response.status(Response.Status.NOT_FOUND)
                            .entity("{\"error\": \"Work schedule not found\"}")
                            .build());
        } catch (ForbiddenException e) {
            return Response.status(Response.Status.FORBIDDEN)
                    .entity("{\"error\": \"" + e.getMessage() + "\"}")
                    .build();
        } catch (Exception e) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("{\"error\": \"Failed to retrieve work schedule\"}")
                    .build();
        }
    }

    /**
     * Find professionals available on a specific day - Accessible by clients, professionals, and admins
     */
    @GET
    @Path("/day/{dayOfWeek}")
    public Response getProfessionalsAvailableOnDay(@PathParam("dayOfWeek") String dayOfWeekStr) {
        try {
            DayOfWeek dayOfWeek = DayOfWeek.valueOf(dayOfWeekStr.toUpperCase());
            List<WorkScheduleDTO> schedules = service.findByAvailableDay(dayOfWeek).stream()
                    .map(mapper::toDTO)
                    .collect(Collectors.toList());

            return Response.ok(schedules).build();
        } catch (IllegalArgumentException e) {
            return Response.status(Response.Status.BAD_REQUEST)
                    .entity("{\"error\": \"Invalid day of week: " + dayOfWeekStr + "\"}")
                    .build();
        } catch (Exception e) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("{\"error\": \"Failed to retrieve available professionals\"}")
                    .build();
        }
    }

    /**
     * Check if professional is available at specific day and time - Public access
     */
    @GET
    @Path("/professionals/{professionalId}/check")
    public Response checkAvailability(@PathParam("professionalId") UUID professionalId,
                                    @QueryParam("dayOfWeek") String dayOfWeekStr,
                                    @QueryParam("time") String timeStr) {
        try {
            if (dayOfWeekStr == null || timeStr == null) {
                return Response.status(Response.Status.BAD_REQUEST)
                        .entity("{\"error\": \"dayOfWeek and time parameters are required\"}")
                        .build();
            }

            DayOfWeek dayOfWeek = DayOfWeek.valueOf(dayOfWeekStr.toUpperCase());
            LocalTime time = LocalTime.parse(timeStr);

            boolean isAvailable = service.isAvailableAt(professionalId, dayOfWeek, time);
            
            return Response.ok("{\"available\": " + isAvailable + "}").build();
        } catch (IllegalArgumentException e) {
            return Response.status(Response.Status.BAD_REQUEST)
                    .entity("{\"error\": \"Invalid dayOfWeek or time format\"}")
                    .build();
        } catch (Exception e) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("{\"error\": \"Failed to check availability\"}")
                    .build();
        }
    }

    /**
     * Get all work schedules with pagination - Admin only
     */
    @GET
    @RolesAllowed({"admin"})
    public Response getAllWorkSchedules(@BeanParam PageRequest pageRequest) {
        try {
            PageResponse<WorkSchedule> schedules = service.findAllPaginated(pageRequest);
            PageResponse<WorkScheduleDTO> dtoResponse = PageResponse.of(
                schedules.getContent().stream().map(mapper::toDTO).collect(Collectors.toList()),
                schedules.getTotalElements(),
                schedules.getNumber(),
                schedules.getSize()
            );
            return Response.ok(dtoResponse).build();
        } catch (ForbiddenException e) {
            return Response.status(Response.Status.FORBIDDEN)
                    .entity("{\"error\": \"" + e.getMessage() + "\"}")
                    .build();
        } catch (Exception e) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("{\"error\": \"Failed to retrieve work schedules\"}")
                    .build();
        }
    }

    // =====================================================
    // Command Endpoints (Professionals Only)
    // =====================================================

    /**
     * Update work schedule for a professional - Professionals only (their own)
     */
    @PUT
    @Path("/professionals/{professionalId}")
    public Response updateWorkSchedule(@PathParam("professionalId") UUID professionalId,
                                     UpdateWorkScheduleRequest request) {
        try {
            if (request == null) {
                return Response.status(Response.Status.BAD_REQUEST)
                        .entity("{\"error\": \"Update data is required\"}")
                        .build();
            }

            // Get existing or create new work schedule
            WorkSchedule existingSchedule = service.getOrCreateForProfessional(professionalId);
            
            // Update the schedule
            WorkSchedule updatedSchedule = mapper.updateEntity(existingSchedule, request);
            WorkSchedule result = service.createOrUpdate(updatedSchedule);
            WorkScheduleDTO dto = mapper.toDTO(result);

            return Response.ok(dto).build();
        } catch (ForbiddenException e) {
            return Response.status(Response.Status.FORBIDDEN)
                    .entity("{\"error\": \"" + e.getMessage() + "\"}")
                    .build();
        } catch (IllegalArgumentException e) {
            return Response.status(Response.Status.BAD_REQUEST)
                    .entity("{\"error\": \"" + e.getMessage() + "\"}")
                    .build();
        } catch (Exception e) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("{\"error\": \"Failed to update work schedule\"}")
                    .build();
        }
    }

    /**
     * Delete work schedule - Professionals only (their own)
     */
    @DELETE
    @Path("/{professionalId}")
    public Response deleteWorkSchedule(@PathParam("professionalId") UUID professionalId) {
        try {
            boolean deleted = service.delete(professionalId);
            if (deleted) {
                return Response.noContent().build();
            } else {
                return Response.status(Response.Status.NOT_FOUND)
                        .entity("{\"error\": \"Work schedule not found\"}")
                        .build();
            }
        } catch (ForbiddenException e) {
            return Response.status(Response.Status.FORBIDDEN)
                    .entity("{\"error\": \"" + e.getMessage() + "\"}")
                    .build();
        } catch (Exception e) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("{\"error\": \"Failed to delete work schedule\"}")
                    .build();
        }
    }
}
