import { apiGetPublic, apiGet } from './client';
import { ProfessionalDTO, ProfessionalWithDetailsDTO, UserWithDetailsDTO, ServiceDTO, PaginatedResponse, ProfessionalSearchParams } from '../../types/api';

/**
 * Professional API Service
 * Handles all professional-related API calls
 */
export class ProfessionalService {
  private readonly basePath = '/professionals';

  /**
   * Get professional by user ID (public endpoint)
   * @param userId Professional's internal user ID
   * @returns Professional data with user information
   */
  async getProfessionalByUserId(userId: string): Promise<ProfessionalWithUser> {
    try {
      console.log(`🔍 Fetching professional profile by user ID: ${userId}`);

      // Use the public Professional domain endpoint that returns ProfessionalWithDetailsDTO
      const professionalData = await apiGetPublic<ProfessionalWithDetailsDTO>(`/professionals/${userId}`);

      console.log('✅ Professional data fetched:', professionalData);

      // Extract professional data from the ProfessionalWithDetailsDTO
      const professional: ProfessionalDTO = {
        userId: professionalData.userId,
        bio: professionalData.bio,
        city: professionalData.city,
        avgRating: professionalData.avgRating,
        ratingCount: professionalData.ratingCount,
        profileImageUrl: professionalData.professionalProfileImageUrl, // Use correct field name from backend
        available: professionalData.available || false,
        serviceIds: [] // Will be populated from services call
      };

      // Create user data from professional data (since ProfessionalWithDetailsDTO includes user fields)
      const user: UserWithDetailsDTO = {
        id: professionalData.userId,
        username: professionalData.username || '',
        firstName: professionalData.firstName || '',
        lastName: professionalData.lastName || '',
        fullName: professionalData.fullName || '',
        email: professionalData.email || '',
        phone: professionalData.phone || '',
        isProfessional: true,
        bio: professionalData.bio,
        city: professionalData.city,
        avgRating: professionalData.avgRating,
        ratingCount: professionalData.ratingCount,
        available: professionalData.available || false,
        clientProfilePhotoUrl: null, // Not available in professional context
        professionalProfilePhotoUrl: professionalData.professionalProfileImageUrl,
        createdAt: '',
        updatedAt: ''
      };

      // Get professional's services
      const services = await apiGetPublic<ServiceDTO[]>(`/services/professional/${userId}`);

      return {
        professional,
        user,
        services: services || []
      };
    } catch (error) {
      console.error('❌ Failed to fetch professional profile:', error);
      throw error;
    }
  }

  /**
   * Get all professionals (public endpoint)
   * @param params Search and pagination parameters
   * @returns Paginated list of professionals
   */
  async getAllProfessionals(params?: ProfessionalSearchParams): Promise<PaginatedResponse<ProfessionalDTO>> {
    try {
      console.log('🔍 Fetching all professionals with params:', params);

      const queryParams = new URLSearchParams();

      // Always filter by isProfessional=true
      queryParams.append('isProfessional', 'true');

      if (params?.page !== undefined) queryParams.append('page', params.page.toString());
      if (params?.size !== undefined) queryParams.append('size', params.size.toString());
      // Note: Other filters like city, available, minRating, serviceCategory would need
      // to be implemented in the User domain if needed

      const url = `/users?${queryParams.toString()}`;

      // Get users with professional filtering
      const response = await apiGetPublic<PaginatedResponse<UserWithDetailsDTO>>(url);

      // Transform UserWithDetailsDTO to ProfessionalDTO format
      const transformedContent = response.content.map(user => ({
        userId: user.id,
        bio: user.bio,
        city: user.city,
        avgRating: user.avgRating,
        ratingCount: user.ratingCount,
        profileImageUrl: user.professionalProfilePhotoUrl,
        available: user.available || false,
        serviceIds: [] // Would need separate call to get service IDs if needed
      }));

      return {
        ...response,
        content: transformedContent
      };
    } catch (error) {
      console.error('❌ Failed to fetch professionals:', error);
      throw error;
    }
  }

  /**
   * Get professional's services (public endpoint)
   * @param userId Professional's internal user ID
   * @returns List of professional's published services
   */
  async getProfessionalServices(userId: string): Promise<ServiceDTO[]> {
    try {
      console.log(`🔍 Fetching services for professional: ${userId}`);

      // This endpoint should return only PUBLISHED services for public access
      return await apiGetPublic<ServiceDTO[]>(`/services/professional/${userId}`);
    } catch (error) {
      console.error('❌ Failed to fetch professional services:', error);
      throw error;
    }
  }

  /**
   * Search professionals by service category (public endpoint)
   * @param categoryId Service category ID
   * @param params Additional search parameters
   * @returns Professionals offering services in the specified category
   */
  async searchProfessionalsByCategory(
    categoryId: string, 
    params?: Omit<ProfessionalSearchParams, 'serviceCategory'>
  ): Promise<PaginatedResponse<ProfessionalDTO>> {
    try {
      console.log(`🔍 Searching professionals by category: ${categoryId}`);
      
      const searchParams: ProfessionalSearchParams = {
        ...params,
        serviceCategory: categoryId
      };
      
      return await this.getAllProfessionals(searchParams);
    } catch (error) {
      console.error('❌ Failed to search professionals by category:', error);
      throw error;
    }
  }

  /**
   * Get current user's professional profile (authenticated endpoint)
   * @returns Current user's professional data
   * @deprecated This method is deprecated. Use getUserById from userService instead.
   */
  async getMyProfessionalProfile(): Promise<ProfessionalDTO> {
    throw new Error('getMyProfessionalProfile is deprecated. Use userService.getUserById with the current user\'s internal ID instead.');
  }

      // Transform UserWithDetailsDTO to ProfessionalDTO
      return {
        userId: userData.id,
        bio: userData.bio,
        city: userData.city,
        avgRating: userData.avgRating,
        ratingCount: userData.ratingCount,
        profileImageUrl: userData.professionalProfilePhotoUrl,
        available: userData.available || false,
        serviceIds: [] // Would need separate call to get service IDs if needed
      };
    } catch (error) {
      console.error('❌ Failed to fetch professional profile:', error);
      throw error;
    }
  }
}

// Combined type for professional with user data and services
export interface ProfessionalWithUser {
  professional: ProfessionalDTO;
  user: UserWithDetailsDTO;
  services: ServiceDTO[];
}

// Create and export singleton instance
export const professionalService = new ProfessionalService();
export default professionalService;
