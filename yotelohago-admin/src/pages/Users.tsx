import React, { useState, useEffect } from 'react';
import { <PERSON>pography, Button, Space, Tag, Avatar, Dropdown, Modal, Descriptions, Switch, message } from 'antd';
import {
  EyeOutlined,
  EditOutlined,
  UserOutlined,
  MoreOutlined,
  MailOutlined,
  PhoneOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { ColumnsType } from 'antd/es/table';
import DataTable from '../components/common/DataTable';
import { User } from '../types';
import apiService from '../services/apiService';
import { useUrlHighlight } from '../hooks/useUrlHighlight';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { confirm } = Modal;

const Users: React.FC = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [detailsVisible, setDetailsVisible] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const highlightId = useUrlHighlight();

  // Load users from API
  useEffect(() => {
    const loadUsers = async () => {
      try {
        setLoading(true);
        const usersData = await apiService.getUsers();
        setUsers(usersData);
      } catch (error) {
        console.error('Failed to load users:', error);
        message.error('Failed to load users');
      } finally {
        setLoading(false);
      }
    };

    loadUsers();
  }, []);

  const handleViewUser = (user: User) => {
    setSelectedUser(user);
    setDetailsVisible(true);
  };

  const handleToggleUserStatus = (user: User) => {
    confirm({
      title: `${user.isProfessional ? 'Suspend' : 'Activate'} User Account?`,
      icon: <ExclamationCircleOutlined />,
      content: `Are you sure you want to ${user.isProfessional ? 'suspend' : 'activate'} ${user.firstName} ${user.lastName}'s account?`,
      onOk() {
        // Toggle user status logic here
        message.success(`User account ${user.isProfessional ? 'suspended' : 'activated'} successfully`);
      },
    });
  };

  const handleResetPassword = (user: User) => {
    confirm({
      title: 'Reset User Password?',
      icon: <ExclamationCircleOutlined />,
      content: `Send password reset email to ${user.email}?`,
      onOk() {
        message.success('Password reset email sent successfully');
      },
    });
  };

  const getUserTypeStyle = (isProfessional: boolean) => {
    if (isProfessional) {
      return {
        backgroundColor: '#D3D3D3', // Light Gray background
        color: '#000000', // Black text
        borderColor: '#000000', // Black border
        border: '1px solid #000000'
      };
    } else {
      return {
        backgroundColor: '#FFE4E1', // Light Coral background
        color: '#FF7F50', // Coral text
        borderColor: '#FF7F50', // Coral border
        border: '1px solid #FF7F50'
      };
    }
  };

  const columns: ColumnsType<User> = [
    {
      title: 'User',
      key: 'user',
      width: 250,
      render: (_, record) => (
        <div className={`flex items-center space-x-3 ${
          highlightId === record.id ? 'bg-coral-100 p-2 rounded animate-pulse' : ''
        }`}>
          <Avatar icon={<UserOutlined />} className="flex-shrink-0" />
          <div className="flex-1 min-w-0">
            <div className="font-medium text-gray-900 truncate">
              {record.firstName} {record.lastName}
            </div>
            <div className="text-sm text-gray-500 truncate">@{record.username}</div>
          </div>
        </div>
      ),
    },
    {
      title: 'Contact',
      key: 'contact',
      width: 200,
      render: (_, record) => (
        <div className="space-y-1">
          <div className="flex items-center space-x-2 text-sm">
            <MailOutlined className="text-gray-400" />
            <span className="truncate">{record.email}</span>
          </div>
          <div className="flex items-center space-x-2 text-sm">
            <PhoneOutlined className="text-gray-400" />
            <span>{record.phone}</span>
          </div>
        </div>
      ),
    },
    {
      title: 'Type',
      dataIndex: 'isProfessional',
      key: 'isProfessional',
      width: 120,
      render: (isProfessional) => (
        <Tag style={getUserTypeStyle(isProfessional)}>
          {isProfessional ? 'Professional' : 'Client'}
        </Tag>
      ),
      filters: [
        { text: 'Professionals', value: true },
        { text: 'Clients', value: false },
      ],
      onFilter: (value, record) => record.isProfessional === value,
    },
    {
      title: 'Joined',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 120,
      render: (createdAt) => (
        <span className="text-sm">
          {dayjs(createdAt).format('MMM DD, YYYY')}
        </span>
      ),
      sorter: (a, b) => dayjs(a.createdAt).unix() - dayjs(b.createdAt).unix(),
    },
    {
      title: 'Last Active',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      width: 120,
      render: (updatedAt) => (
        <span className="text-sm">
          {dayjs(updatedAt).format('MMM DD, YYYY')}
        </span>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 120,
      render: (_, record) => (
        <Dropdown
          menu={{
            items: [
              {
                key: 'view',
                icon: <EyeOutlined />,
                label: 'View Details',
                onClick: () => handleViewUser(record),
              },
              {
                key: 'edit',
                icon: <EditOutlined />,
                label: 'Edit User',
              },
              {
                type: 'divider',
              },
              {
                key: 'reset-password',
                label: 'Reset Password',
                onClick: () => handleResetPassword(record),
              },
              {
                key: 'toggle-status',
                label: 'Suspend Account',
                danger: true,
                onClick: () => handleToggleUserStatus(record),
              },
            ],
          }}
          trigger={['click']}
        >
          <Button type="text" icon={<MoreOutlined />} />
        </Dropdown>
      ),
    },
  ];

  const filterOptions = [
    {
      key: 'isProfessional',
      label: 'User Type',
      options: [
        { value: 'true', label: 'Professionals' },
        { value: 'false', label: 'Clients' },
      ],
    },
  ];

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <Title level={2} className="m-0">User Management</Title>
        <Text type="secondary">Manage all platform users and their accounts</Text>
      </div>

      <DataTable
        columns={columns}
        dataSource={users}
        rowKey="id"
        loading={loading}
        filterOptions={filterOptions}
        onSearch={(value) => {
          console.log('Search:', value);
        }}
        onFilter={(filters) => {
          console.log('Filters:', filters);
        }}
        onExport={() => {
          console.log('Export users');
        }}
      />

      <Modal
        title={`User Details - ${selectedUser?.firstName} ${selectedUser?.lastName}`}
        open={detailsVisible}
        onCancel={() => setDetailsVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailsVisible(false)}>
            Close
          </Button>,
          <Button key="edit" type="primary" icon={<EditOutlined />}>
            Edit User
          </Button>,
        ]}
        width={600}
      >
        {selectedUser && (
          <div className="space-y-6">
            <div className="flex items-center space-x-4">
              <Avatar size={64} icon={<UserOutlined />} />
              <div>
                <Title level={4} className="m-0">
                  {selectedUser.firstName} {selectedUser.lastName}
                </Title>
                <Text type="secondary">@{selectedUser.username}</Text>
                <div className="mt-2">
                  <Tag style={getUserTypeStyle(selectedUser.isProfessional)}>
                    {selectedUser.isProfessional ? 'Professional' : 'Client'}
                  </Tag>
                </div>
              </div>
            </div>

            <Descriptions column={1} bordered>
              <Descriptions.Item label="Email">
                {selectedUser.email}
              </Descriptions.Item>
              <Descriptions.Item label="Phone">
                {selectedUser.phone}
              </Descriptions.Item>
              <Descriptions.Item label="Username">
                @{selectedUser.username}
              </Descriptions.Item>
              <Descriptions.Item label="Account Type">
                <Tag style={getUserTypeStyle(selectedUser.isProfessional)}>
                  {selectedUser.isProfessional ? 'Professional' : 'Client'}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="Member Since">
                {dayjs(selectedUser.createdAt).format('MMM DD, YYYY HH:mm')}
              </Descriptions.Item>
              <Descriptions.Item label="Last Updated">
                {dayjs(selectedUser.updatedAt).format('MMM DD, YYYY HH:mm')}
              </Descriptions.Item>
            </Descriptions>

            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div>
                <Text strong>Account Status</Text>
                <div className="text-sm text-gray-500">
                  Control user account access
                </div>
              </div>
              <Switch defaultChecked />
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default Users;