package com.yotelohago.professional.application;

import com.yotelohago.auth.TokenIdentityProvider;
import com.yotelohago.professional.api.ProfessionalWithDetailsDTO;
import com.yotelohago.professional.domain.Professional;
import com.yotelohago.professional.domain.ProfessionalRepository;
import com.yotelohago.user.application.UserSyncService;
import com.yotelohago.user.domain.User;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;
import jakarta.ws.rs.ForbiddenException;
import jakarta.ws.rs.NotFoundException;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Service for managing Professional domain operations
 * Follows the same coding convention as Service, Booking, Media, and Favorite domains:
 * - No URL building logic in service layer
 * - URLs are built exclusively in repository SQL queries
 * - Uses consolidated ProfessionalWithDetailsDTO for API responses
 */
@ApplicationScoped
public class ProfessionalManagementService {

    @Inject
    ProfessionalRepository repository;

    @Inject
    TokenIdentityProvider tokenIdentityProvider;

    @Inject
    UserSyncService userSyncService;

    /**
     * Find professional by internal user ID - Public access
     */
    public Optional<Professional> findById(UUID userId) {
        return repository.findById(userId);
    }





    /**
     * Get complete professional profile with details including user information - Public access
     * Uses efficient SQL-based URL building following established coding convention
     *
     * @param professionalId the professional user ID
     * @return ProfessionalWithDetailsDTO containing all professional and user data with pre-built URLs
     * @throws NotFoundException if professional not found
     * @throws IllegalArgumentException if professionalId is null
     */
    public ProfessionalWithDetailsDTO getProfessionalWithDetails(UUID professionalId) {
        if (professionalId == null) {
            throw new IllegalArgumentException("Professional ID cannot be null");
        }

        // Use efficient repository method with SQL-based URL building
        return repository.findWithDetailsByIdWithUrl(professionalId)
                        .orElseThrow(() -> new NotFoundException("Professional not found with ID: " + professionalId));
    }



    /**
     * Create professional profile - Only the user themselves or admin
     */
    @Transactional
    public void create(Professional professional) {
        // Ensure current user exists in database
        userSyncService.ensureCurrentUserExists();

        // Check authorization - user can only create their own professional profile
        if (!tokenIdentityProvider.canAccessProfessionalData(professional.getUser().id)) {
            throw new ForbiddenException("Not authorized to create professional profile for this user");
        }

        repository.persist(professional);
    }

    /**
     * Update professional profile - Only the user themselves or admin
     */
    @Transactional
    public Professional update(Professional professional) {
        // Ensure current user exists in database
        userSyncService.ensureCurrentUserExists();

        // Check authorization - user can only update their own professional profile
        if (!tokenIdentityProvider.canAccessProfessionalData(professional.getUser().id)) {
            throw new ForbiddenException("Not authorized to update professional profile for this user");
        }

        return repository.update(professional);
    }

    /**
     * Delete professional profile - Admin only
     */
    @Transactional
    public void delete(UUID userId) {
        // Ensure current user exists in database
        userSyncService.ensureCurrentUserExists();

        // Check authorization - only admin can delete professional profiles
        if (!tokenIdentityProvider.hasRole("admin")) {
            throw new ForbiddenException("Admin access required to delete professional profiles");
        }

        repository.deleteById(userId);
    }
}