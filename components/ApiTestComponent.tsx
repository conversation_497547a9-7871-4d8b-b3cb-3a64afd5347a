import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { apiGet, checkApiHealth } from '../services/api/client';
import { ENV } from '../config/environment';
import { useAuth } from '../contexts/AuthContext';

interface ApiTestResult {
  endpoint: string;
  status: 'success' | 'error' | 'loading';
  message: string;
  timestamp: string;
}

export default function ApiTestComponent() {
  const [testResults, setTestResults] = useState<ApiTestResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { state: authState } = useAuth();

  const addTestResult = (result: Omit<ApiTestResult, 'timestamp'>) => {
    const newResult: ApiTestResult = {
      ...result,
      timestamp: new Date().toLocaleTimeString(),
    };
    setTestResults(prev => [newResult, ...prev.slice(0, 4)]); // Keep last 5 results
  };

  const testApiHealth = async () => {
    setIsLoading(true);
    try {
      console.log('🔄 Testing API connectivity...');

      // Test a simple endpoint that should exist
      const response = await apiGet('/services');

      addTestResult({
        endpoint: '/services',
        status: 'success',
        message: `API is reachable! Received ${Array.isArray(response) ? response.length : 'data'} services`,
      });
    } catch (error: any) {
      console.error('❌ API connectivity test failed:', error);
      const errorMessage = error.status
        ? `HTTP ${error.status}: ${error.error || error.message}`
        : error.error || error.message || 'API not reachable';

      addTestResult({
        endpoint: '/services',
        status: 'error',
        message: errorMessage,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const testPublicEndpoint = async () => {
    setIsLoading(true);
    try {
      console.log('🔄 Testing public endpoint...');

      // Test professionals endpoint (should be public)
      const response = await apiGet('/professionals');

      addTestResult({
        endpoint: '/professionals',
        status: 'success',
        message: `Received ${Array.isArray(response) ? response.length : 'data'} professionals`,
      });
    } catch (error: any) {
      console.error('❌ Public endpoint test failed:', error);
      const errorMessage = error.status
        ? `HTTP ${error.status}: ${error.error || error.message}`
        : error.error || error.message || 'Failed to fetch professionals';

      addTestResult({
        endpoint: '/professionals',
        status: 'error',
        message: errorMessage,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const testProtectedEndpoint = async () => {
    setIsLoading(true);
    try {
      console.log('🔄 Testing protected endpoint...');

      // Test a protected endpoint (will fail without auth for now)
      const response = await apiGet('/bookings');

      addTestResult({
        endpoint: '/bookings',
        status: 'success',
        message: `Received ${Array.isArray(response) ? response.length : 'data'} bookings`,
      });
    } catch (error: any) {
      console.error('❌ Protected endpoint test failed:', error);
      const errorMessage = error.status
        ? `HTTP ${error.status}: ${error.error || error.message}`
        : error.error || error.message || 'Authentication required';

      addTestResult({
        endpoint: '/bookings',
        status: 'error',
        message: errorMessage,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const testUserInfo = async () => {
    setIsLoading(true);
    try {
      console.log('🔄 Testing user info endpoint...');

      // Get current user's internal ID from auth state
      const currentUserId = authState.user?.internalId;
      if (!currentUserId) {
        throw new Error('No internal user ID available');
      }

      // Test the user info endpoint (current user profile)
      const response = await apiGet(`/users/${currentUserId}`);

      const userInfo = response || {};
      const message = authState.isAuthenticated
        ? `User: ${userInfo.email || 'Unknown'} (${userInfo.firstName || ''} ${userInfo.lastName || ''})`.trim()
        : 'User info retrieved (not authenticated)';

      addTestResult({
        endpoint: `/users/${currentUserId}`,
        status: 'success',
        message: message,
      });
    } catch (error: any) {
      console.error('❌ User info test failed:', error);
      const errorMessage = error.status
        ? `HTTP ${error.status}: ${error.error || error.message}`
        : error.error || error.message || 'Failed to fetch user info';

      addTestResult({
        endpoint: '/users/{id}',
        status: 'error',
        message: errorMessage,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const showEnvironmentInfo = () => {
    Alert.alert(
      'Environment Configuration',
      `API Base URL: ${ENV.API_BASE_URL}\n` +
      `Keycloak URL: ${ENV.KEYCLOAK_URL}\n` +
      `Realm: ${ENV.KEYCLOAK_REALM}\n` +
      `Client ID: ${ENV.KEYCLOAK_CLIENT_ID}\n` +
      `Auth Status: ${authState.isAuthenticated ? 'Authenticated' : 'Not Authenticated'}`
    );
  };

  const getStatusIcon = (status: ApiTestResult['status']) => {
    switch (status) {
      case 'success':
        return <Ionicons name="checkmark-circle" size={20} color="#4CAF50" />;
      case 'error':
        return <Ionicons name="close-circle" size={20} color="#F44336" />;
      case 'loading':
        return <Ionicons name="time" size={20} color="#FF9800" />;
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>API Connection Test</Text>
      
      {/* Environment Info */}
      <TouchableOpacity style={styles.infoButton} onPress={showEnvironmentInfo}>
        <Ionicons name="information-circle" size={20} color="#2196F3" />
        <Text style={styles.infoButtonText}>Environment Info</Text>
      </TouchableOpacity>

      {/* Test Buttons */}
      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={[styles.testButton, styles.healthButton]}
          onPress={testApiHealth}
          disabled={isLoading}
        >
          <Ionicons name="pulse" size={20} color="white" />
          <Text style={styles.buttonText}>Test API Connection</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.testButton, styles.publicButton]}
          onPress={testPublicEndpoint}
          disabled={isLoading}
        >
          <Ionicons name="globe" size={20} color="white" />
          <Text style={styles.buttonText}>Test Professionals API</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.testButton, styles.protectedButton]}
          onPress={testProtectedEndpoint}
          disabled={isLoading}
        >
          <Ionicons name="lock-closed" size={20} color="white" />
          <Text style={styles.buttonText}>Test Protected Endpoint</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.testButton, styles.userInfoButton]}
          onPress={testUserInfo}
          disabled={isLoading}
        >
          <Ionicons name="person-circle" size={20} color="white" />
          <Text style={styles.buttonText}>Test User Info</Text>
        </TouchableOpacity>
      </View>

      {/* Loading Indicator */}
      {isLoading && (
        <View style={styles.loadingContainer}>
          <Ionicons name="refresh" size={24} color="#2196F3" />
          <Text style={styles.loadingText}>Testing...</Text>
        </View>
      )}

      {/* Test Results */}
      {testResults.length > 0 && (
        <View style={styles.resultsContainer}>
          <Text style={styles.resultsTitle}>Recent Test Results:</Text>
          {testResults.map((result, index) => (
            <View key={index} style={styles.resultItem}>
              <View style={styles.resultHeader}>
                {getStatusIcon(result.status)}
                <Text style={styles.resultEndpoint}>{result.endpoint}</Text>
                <Text style={styles.resultTime}>{result.timestamp}</Text>
              </View>
              <Text style={[
                styles.resultMessage,
                result.status === 'error' ? styles.errorMessage : styles.successMessage
              ]}>
                {result.message}
              </Text>
            </View>
          ))}
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 20,
    backgroundColor: '#f5f5f5',
    borderRadius: 10,
    margin: 10,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    textAlign: 'center',
    color: '#333',
  },
  infoButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#E3F2FD',
    padding: 10,
    borderRadius: 8,
    marginBottom: 15,
  },
  infoButtonText: {
    marginLeft: 8,
    color: '#2196F3',
    fontWeight: '500',
  },
  buttonContainer: {
    gap: 10,
    marginBottom: 15,
  },
  testButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
    gap: 8,
  },
  healthButton: {
    backgroundColor: '#4CAF50',
  },
  publicButton: {
    backgroundColor: '#2196F3',
  },
  protectedButton: {
    backgroundColor: '#FF9800',
  },
  userInfoButton: {
    backgroundColor: '#9C27B0', // Purple color
  },
  buttonText: {
    color: 'white',
    fontWeight: '600',
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 10,
  },
  loadingText: {
    marginLeft: 8,
    color: '#2196F3',
    fontStyle: 'italic',
  },
  resultsContainer: {
    marginTop: 10,
  },
  resultsTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 10,
    color: '#333',
  },
  resultItem: {
    backgroundColor: 'white',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
    borderLeftWidth: 4,
    borderLeftColor: '#E0E0E0',
  },
  resultHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 5,
  },
  resultEndpoint: {
    flex: 1,
    marginLeft: 8,
    fontWeight: '500',
    color: '#333',
  },
  resultTime: {
    fontSize: 12,
    color: '#666',
  },
  resultMessage: {
    fontSize: 14,
    marginLeft: 28,
  },
  successMessage: {
    color: '#4CAF50',
  },
  errorMessage: {
    color: '#F44336',
  },
});
