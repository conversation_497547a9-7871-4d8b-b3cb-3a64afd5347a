import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { 
  WorkScheduleDTO, 
  DayScheduleDTO, 
  DayOfWeek, 
  UpdateWorkScheduleRequest 
} from '../../../types/api';
import { useMyAvailability } from '../../../hooks/useAvailability';
import DayScheduleEditor from './DayScheduleEditor';

interface WeeklyScheduleEditorProps {
  onClose?: () => void;
  onSave?: (workSchedule: WorkScheduleDTO) => void;
}

/**
 * Weekly Schedule Editor Component for Professional Mode
 * Manages all 7 days of the week and save functionality
 * Integrates with the availability API and follows black/white theme
 */
export const WeeklyScheduleEditor: React.FC<WeeklyScheduleEditorProps> = ({
  onClose,
  onSave,
}) => {
  const {
    workSchedule,
    isLoading,
    isSaving,
    error,
    updateMyWorkSchedule,
    clearError,
  } = useMyAvailability();

  const [localDaySchedules, setLocalDaySchedules] = useState<DayScheduleDTO[]>([]);
  const [hasChanges, setHasChanges] = useState(false);

  // Initialize local state with current work schedule or default empty schedule
  useEffect(() => {
    if (workSchedule) {
      setLocalDaySchedules(workSchedule.daySchedules);
    } else {
      // Create default empty schedule for all 7 days
      const defaultSchedules: DayScheduleDTO[] = Object.values(DayOfWeek).map(day => ({
        dayOfWeek: day,
        timeRanges: [],
        isActive: false,
      }));
      setLocalDaySchedules(defaultSchedules);
    }
    setHasChanges(false);
  }, [workSchedule]);

  // Handle day schedule changes
  const handleDayScheduleChange = (updatedDaySchedule: DayScheduleDTO) => {
    const updatedSchedules = localDaySchedules.map(daySchedule =>
      daySchedule.dayOfWeek === updatedDaySchedule.dayOfWeek
        ? updatedDaySchedule
        : daySchedule
    );
    
    setLocalDaySchedules(updatedSchedules);
    setHasChanges(true);
  };

  // Save work schedule
  const handleSave = async () => {
    try {
      clearError();

      const updateRequest: UpdateWorkScheduleRequest = {
        daySchedules: localDaySchedules,
        notes: workSchedule?.notes || undefined,
      };

      console.log('🗓️ Saving work schedule:', updateRequest);

      const updatedWorkSchedule = await updateMyWorkSchedule(updateRequest);

      setHasChanges(false);

      Alert.alert(
        'Success',
        'Your availability schedule has been saved successfully!',
        [
          {
            text: 'OK',
            onPress: () => {
              onSave?.(updatedWorkSchedule);
              onClose?.();
            },
          },
        ]
      );
    } catch (error) {
      console.error('❌ Failed to save work schedule:', error);
      
      Alert.alert(
        'Error',
        'Failed to save your availability schedule. Please try again.',
        [{ text: 'OK' }]
      );
    }
  };

  // Handle cancel with unsaved changes warning
  const handleCancel = () => {
    if (hasChanges) {
      Alert.alert(
        'Unsaved Changes',
        'You have unsaved changes. Are you sure you want to cancel?',
        [
          { text: 'Keep Editing', style: 'cancel' },
          { 
            text: 'Discard Changes', 
            style: 'destructive',
            onPress: onClose 
          },
        ]
      );
    } else {
      onClose?.();
    }
  };

  // Get ordered days starting with Monday
  const getOrderedDays = (): DayScheduleDTO[] => {
    const dayOrder = [
      DayOfWeek.MONDAY,
      DayOfWeek.TUESDAY,
      DayOfWeek.WEDNESDAY,
      DayOfWeek.THURSDAY,
      DayOfWeek.FRIDAY,
      DayOfWeek.SATURDAY,
      DayOfWeek.SUNDAY,
    ];

    return dayOrder.map(day => 
      localDaySchedules.find(schedule => schedule.dayOfWeek === day) || {
        dayOfWeek: day,
        timeRanges: [],
        isActive: false,
      }
    );
  };

  // Calculate summary stats
  const getScheduleSummary = () => {
    const activeDays = localDaySchedules.filter(day => day.isActive).length;
    const totalTimeRanges = localDaySchedules.reduce(
      (total, day) => total + day.timeRanges.length, 
      0
    );
    
    return { activeDays, totalTimeRanges };
  };

  const { activeDays, totalTimeRanges } = getScheduleSummary();

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#000000" />
        <Text style={styles.loadingText}>Loading your availability...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={handleCancel} style={styles.headerButton}>
          <Text style={styles.headerButtonText}>Cancel</Text>
        </TouchableOpacity>
        
        <View style={styles.headerCenter}>
          <Text style={styles.headerTitle}>Set Availability</Text>
          <Text style={styles.headerSubtitle}>
            {activeDays} days • {totalTimeRanges} time slots
          </Text>
        </View>
        
        <TouchableOpacity 
          onPress={handleSave} 
          style={[styles.headerButton, styles.saveButton]}
          disabled={isSaving || !hasChanges}
        >
          {isSaving ? (
            <ActivityIndicator size="small" color="#FFFFFF" />
          ) : (
            <Text style={[
              styles.headerButtonText, 
              styles.saveButtonText,
              (!hasChanges) && styles.disabledSaveText
            ]}>
              Save
            </Text>
          )}
        </TouchableOpacity>
      </View>

      {/* Error Message */}
      {error && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity onPress={clearError}>
            <Text style={styles.errorDismiss}>Dismiss</Text>
          </TouchableOpacity>
        </View>
      )}

      {/* Days List */}
      <ScrollView style={styles.scrollContainer} showsVerticalScrollIndicator={false}>
        <View style={styles.daysContainer}>
          {getOrderedDays().map((daySchedule) => (
            <DayScheduleEditor
              key={daySchedule.dayOfWeek}
              daySchedule={daySchedule}
              onDayScheduleChange={handleDayScheduleChange}
              disabled={isSaving}
            />
          ))}
        </View>

        {/* Bottom Spacing */}
        <View style={styles.bottomSpacing} />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F8F8',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F8F8F8',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666666',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E5',
  },
  headerButton: {
    paddingVertical: 8,
    paddingHorizontal: 12,
  },
  saveButton: {
    backgroundColor: '#000000',
    borderRadius: 6,
    minWidth: 60,
    alignItems: 'center',
  },
  headerButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
  },
  saveButtonText: {
    color: '#FFFFFF',
  },
  disabledSaveText: {
    color: '#CCCCCC',
  },
  headerCenter: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
  },
  headerSubtitle: {
    fontSize: 12,
    color: '#666666',
    marginTop: 2,
  },
  errorContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFE5E5',
    borderBottomWidth: 1,
    borderBottomColor: '#FFB3B3',
  },
  errorText: {
    flex: 1,
    fontSize: 14,
    color: '#CC0000',
  },
  errorDismiss: {
    fontSize: 14,
    fontWeight: '600',
    color: '#CC0000',
  },
  scrollContainer: {
    flex: 1,
  },
  daysContainer: {
    padding: 16,
  },
  bottomSpacing: {
    height: 32,
  },
});

export default WeeklyScheduleEditor;
