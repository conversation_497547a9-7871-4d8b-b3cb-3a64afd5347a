---

- name: Show home directory
  debug:
    msg: "Home directory on {{ inventory_hostname }} is {{ ansible_env.PWD }}"

- name: Get RHEL VM internal IP
  shell: |
    echo INTERNAL=************
  register: ip_vars
  changed_when: false

- name: Debug ip
  debug:
    var: ip_vars

- name: Set facts for internal Ip
  set_fact:
    internal_ip: "{{ ip_vars.stdout_lines[0].split('=')[1] }}"

- name: Install required networking tools for K3s
  package:
    name:
      - iptables
      - iproute
      - iptables-nft
    state: present

- name: Install K3s (single node)
  shell: |
    curl -sfL https://get.k3s.io | INSTALL_K3S_EXEC="--tls-san {{ internal_ip }}" sh -s - --write-kubeconfig-mode 644
  args:
    creates: /etc/rancher/k3s/k3s.yaml

- name: Wait for /etc/rancher/k3s/k3s.yaml to be created
  wait_for:
    path: /etc/rancher/k3s/k3s.yaml
    timeout: 120

- name: Create directory on RHEL VM
  ansible.builtin.file:
    path: "{{ ansible_env.PWD }}/.kube"
    state: directory
    mode: '0755'  # Optional: Set permissions (rwxr-xr-x)
    owner: "{{ ansible_env.SUDO_USER }}"    # Optional: Set owner
    group: "{{ ansible_env.SUDO_USER }}"    # Optional: Set group

- name: Copy kubeconfig to user home
  copy:
    src: /etc/rancher/k3s/k3s.yaml
    dest: "{{ ansible_env.PWD }}/.kube/config"
    remote_src: true
    owner: "{{ ansible_env.SUDO_USER }}"
    group: "{{ ansible_env.SUDO_USER }}"
    mode: '0600'

- name: Copy kubeconfig to local machine
  fetch:
    src: "{{ ansible_env.PWD }}/.kube/config"
    dest: "../config/ansible_kubeconfig-{{ ansible_env.SUDO_USER }}-k3s.generated"
    flat: true

- name: Display K3s access instructions
  debug:
    msg: |
      ✅ K3s is now running on EC2
      To access it remotely:
        "export KUBECONFIG=./config/ansible_kubeconfig-{{ ansible_env.SUDO_USER }}-k3s.generated && kubectl get all -A"
