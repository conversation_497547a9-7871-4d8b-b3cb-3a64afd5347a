package com.yotelohago.user.application;

import com.yotelohago.user.domain.AuthProvider;
import com.yotelohago.user.domain.User;
import com.yotelohago.user.domain.UserRepository;
import com.yotelohago.user.domain.UserExternalIdentity;
import com.yotelohago.user.domain.UserExternalIdentityRepository;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Instant;
import java.util.Optional;
import java.util.UUID;

@ApplicationScoped
public class UserSyncService {

    private static final Logger logger = LoggerFactory.getLogger(UserSyncService.class);

    @Inject
    UserRepository userRepository;

    @Inject
    UserExternalIdentityRepository externalIdentityRepository;

    @Inject
    JsonWebToken jwt;

    /**
     * Ensures the current user from JWT exists in our database.
     * Database-first approach: claim identity ownership first, then create user.
     * This eliminates race conditions by using PostgreSQL's ON CONFLICT DO NOTHING.
     *
     * @return The user record (existing or newly created)
     */
    @Transactional
    public Optional<User> ensureCurrentUserExists() {
        if (jwt == null || jwt.getSubject() == null) {
            logger.warn("No JWT token or subject found");
            return Optional.empty();
        }

        String externalId = jwt.getSubject();
        AuthProvider provider = AuthProvider.KEYCLOAK;

        // First check if user already exists (fast path)
        Optional<UserExternalIdentity> existingIdentity = externalIdentityRepository.findByProviderAndExternalId(provider, externalId);
        if (existingIdentity.isPresent()) {
            UUID userId = existingIdentity.get().getUserId();
            return userRepository.findEntityById(userId);
        }

        // User doesn't exist, use database-first creation approach
        return createUserDatabaseFirst(provider, externalId);
    }

    /**
     * Database-first user creation approach.
     * 1. Generate UUID for new user
     * 2. Try to claim identity ownership with ON CONFLICT DO NOTHING
     * 3. If successful, create user record
     * 4. If conflict, fetch existing user
     */
    private Optional<User> createUserDatabaseFirst(AuthProvider provider, String externalId) {
        try {
            // Extract user data from JWT claims
            String username = jwt.getClaim("preferred_username");
            String email = jwt.getClaim("email");
            String givenName = jwt.getClaim("given_name");
            String familyName = jwt.getClaim("family_name");

            // Handle missing claims with defaults
            if (username == null) username = email; // fallback to email
            if (givenName == null) givenName = "Unknown";
            if (familyName == null) familyName = "User";
            if (email == null) {
                logger.error("No email claim found in JWT for user: {}", externalId);
                return Optional.empty();
            }

            // Check if user has professional role
            boolean isProfessional = hasRole("professional");

            Instant now = Instant.now();
            UUID internalUserId = UUID.randomUUID();

            // STEP 1: Create user record first
            User newUser = new User(
                internalUserId,
                username,
                givenName,
                familyName,
                email,
                null, // phone will be set later by user
                isProfessional,
                now,
                now
            );

            userRepository.persist(newUser);
            userRepository.flush(); // Ensure user is committed to database before identity insert

            // STEP 2: Try to claim identity ownership with the user that now exists
            UUID identityId = UUID.randomUUID();

            boolean identityClaimSucceeded = externalIdentityRepository.tryInsertIdentity(
                identityId, internalUserId, provider, externalId, now
            );

            if (identityClaimSucceeded) {
                // STEP 3: We won the race, return our user
                logger.info("Created new user from JWT: provider={}, externalId={}, internalId={}, email={}",
                           provider, externalId, internalUserId, email);

                return Optional.of(newUser);
            } else {
                // STEP 4: Another thread won, fetch their user and clean up our orphaned user
                logger.info("Identity already claimed by another thread for externalId={}, cleaning up and fetching existing user", externalId);

                // Clean up our orphaned user record
                try {
                    userRepository.deleteById(internalUserId);
                    logger.debug("Cleaned up orphaned user record: {}", internalUserId);
                } catch (Exception e) {
                    logger.warn("Failed to clean up orphaned user record: {}", internalUserId, e);
                }

                // Fetch the winning user
                Optional<UserExternalIdentity> existingIdentity = externalIdentityRepository.findByProviderAndExternalId(provider, externalId);
                if (existingIdentity.isPresent()) {
                    UUID existingUserId = existingIdentity.get().getUserId();
                    return userRepository.findEntityById(existingUserId);
                } else {
                    logger.error("Identity claim failed but cannot find existing identity for externalId={}", externalId);
                    return Optional.empty();
                }
            }

        } catch (Exception e) {
            logger.error("Failed to create user database-first for provider={}, externalId={}", provider, externalId, e);
            return Optional.empty();
        }
    }

    private boolean hasRole(String role) {
        try {
            // Check realm roles
            if (jwt.getClaimNames().contains("realm_access")) {
                Object realmAccess = jwt.getClaim("realm_access");
                // This is a simplified check - in production you might want to use a JSON library
                String realmAccessStr = realmAccess.toString();
                return realmAccessStr.contains("\"" + role + "\"");
            }
            return false;
        } catch (Exception e) {
            logger.warn("Failed to check role '{}' in JWT", role, e);
            return false;
        }
    }
}
