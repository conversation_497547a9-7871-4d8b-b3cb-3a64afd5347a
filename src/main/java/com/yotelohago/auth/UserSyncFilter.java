package com.yotelohago.auth;

import com.yotelohago.user.application.UserSyncService;
import io.quarkus.security.identity.SecurityIdentity;
import jakarta.annotation.Priority;
import jakarta.inject.Inject;
import jakarta.ws.rs.container.ContainerRequestContext;
import jakarta.ws.rs.container.ContainerRequestFilter;
import jakarta.ws.rs.ext.Provider;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

/**
 * JAX-RS filter that automatically syncs users from Keycloak on authenticated requests.
 * This ensures that any user with a valid JWT token will have a corresponding record
 * in our database, even if it's their first time accessing our API.
 */
@Provider
@Priority(1000) // Run after authentication but before other filters
public class UserSyncFilter implements ContainerRequestFilter {

    private static final Logger logger = LoggerFactory.getLogger(UserSyncFilter.class);

    @Inject
    SecurityIdentity securityIdentity;

    @Inject
    JsonWebToken jwt;

    @Inject
    UserSyncService userSyncService;

    @Override
    public void filter(ContainerRequestContext requestContext) throws IOException {
        // Only sync for authenticated requests
        if (!securityIdentity.isAnonymous() && jwt != null && jwt.getSubject() != null) {
            try {
                // This will create the user if they don't exist, or return existing user
                userSyncService.ensureCurrentUserExists();
            } catch (Exception e) {
                // Log the error but don't fail the request
                // The individual endpoints can handle missing users if needed
                logger.warn("Failed to sync user from JWT: {}", e.getMessage());
            }
        }
    }
}
